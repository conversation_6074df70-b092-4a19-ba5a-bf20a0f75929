# Deep Cognition


## 开发指南

- **开发环境**: 请使用Linux服务器(Ubuntu 24.04.2 LTS)或MacOS进行开发
- **开发工具**: 请使用Cursor辅助编程


### 开发环境搭建

#### Option 1: Ubuntu开发环境

- Step 1: 安装Anaconda（如果已经有conda可以跳过此步骤）

```bash
# 下载Anaconda安装包
wget https://repo.anaconda.com/archive/Anaconda3-2024.10-1-Linux-x86_64.sh
# 根据指示安装Anaconda
sh Anaconda3-2024.10-1-Linux-x86_64.sh
source ~/.bashrc
rm Anaconda3-2024.10-1-Linux-x86_64.sh
```

- Step 2: 创建conda环境

```bash
# 暂时命名为dair，如果命名有冲突可以换个名字
conda create -n dair python=3.11 -y
conda activate dair
```

- Step 3: 安装系统依赖

```bash
sudo apt-get update
sudo apt-get install -y \
    build-essential \
    python3-dev \
    python3-pip \
    python3-setuptools \
    python3-wheel \
    python3-cffi \
    libcairo2 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libgdk-pixbuf2.0-0 \
    libffi-dev \
    shared-mime-info \
    pango1.0-tools \
    libpango1.0-dev \
    libharfbuzz-dev \
    libcairo2-dev \
    fonts-liberation \
    libjpeg-dev \
    libpng-dev \
    libtiff-dev \
    libwebp-dev \
    fonts-noto-cjk \
    libx11-dev \
    libxext-dev \
    libxrender-dev \
    libxt-dev \
    libxft-dev \
    libfreetype6-dev \
    libfontconfig1-dev \
    libxml2-dev \
    libxslt1-dev \
    zlib1g-dev
```

- Step 4: 安装[Node.js](https://nodejs.org/zh-cn/download)

```bash
# Download and install nvm:
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.3/install.sh | bash

# in lieu of restarting the shell
\. "$HOME/.nvm/nvm.sh"

# Download and install Node.js:
nvm install 22

# Verify the Node.js version:
node -v # Should print "v22.15.0".
nvm current # Should print "v22.15.0".

# Verify npm version:
npm -v # Should print "10.9.2".
```

- Step 5: 克隆仓库并安装依赖

```bash
# 克隆仓库
git clone https://github.com/GAIR-NLP/DAIR.git
cd DAIR


# 安装 Python 依赖
pip install -r backend/requirements.txt
```

- Step 6: docker 环境安装 [Ubuntu Docker Install](https://docs.docker.com/engine/install/ubuntu/)

```bash
# 如已安装其他版本docker，可通过以下命令卸载
sudo apt-get remove docker docker-engine docker.io containerd runc
sudo apt-get update

# Add Docker's official GPG key 
sudo apt-get install ca-certificates curl gnupg lsb-release
sudo install -m 0755 -d /etc/apt/keyrings
sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
sudo chmod a+r /etc/apt/keyrings/docker.asc

# Add the repository to Apt sources:
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu $(. /etc/os-release && echo "${UBUNTU_CODENAME:-$VERSION_CODENAME}") stable" | \
  sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

sudo apt-get update

# Install Special Version Docker
VERSION_STRING=5:28.1.1-1~ubuntu.24.04~noble
sudo apt-get install docker-ce=$VERSION_STRING docker-ce-cli=$VERSION_STRING containerd.io docker-buildx-plugin docker-compose-plugin

# Verify Install Version
docker --version
docker compose version

# Enable Docker Service
sudo systemctl enable docker
sudo systemctl start docker
```


#### Option 2: MacOS开发环境搭建

- Step 1: 安装Anaconda（如果已经有conda可以跳过此步骤）

从[Anaconda官网](https://www.anaconda.com/download/success)下载和系统匹配的最新版本并安装。

- Step 2: 创建conda环境

```bash
# 暂时命名为dair，如果命名有冲突可以换个名字
conda create -n dair python=3.11 -y
conda activate dair
```

- Step 3: 安装系统依赖

```bash
conda install -c conda-forge glib
conda install -c conda-forge pango
```

- Step 4: 安装[Node.js](https://nodejs.org/zh-cn/download)

```bash
# Download and install nvm:
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.3/install.sh | bash

# in lieu of restarting the shell
\. "$HOME/.nvm/nvm.sh"

# Download and install Node.js:
nvm install 22

# Verify the Node.js version:
node -v # Should print "v22.15.0".
nvm current # Should print "v22.15.0".

# Verify npm version:
npm -v # Should print "10.9.2".
```

- Step 5: 克隆仓库并安装依赖

```bash
# 克隆仓库
git clone https://github.com/GAIR-NLP/DAIR.git
cd DAIR


# 安装 Python 依赖
pip install -r backend/requirements.txt
```

- Step 6: MacOS Docker DeskTop

安装对应版本的，参考 [在 Mac 上安装 Docker Desktop](https://docs.docker.com/desktop/setup/install/mac-install/)


### 在开发环境运行

运行需要开两个终端:

- Terminal 1: 运行后台服务器

```bash
# 启动 MongoDB（使用 Docker）
docker run -d \
    --name dair-mongodb \
    -p 27017:27017 \
    -e MONGO_INITDB_ROOT_USERNAME=admin \
    -e MONGO_INITDB_ROOT_PASSWORD=admin \
    -e MONGO_INITDB_DATABASE=dair \
    mongo:6.0 --auth

# 启动 Redis（使用 Docker）
docker run -d \
    --name dair-redis \
    -p 6379:6379 \
    --restart unless-stopped \
    redis:7-alpine redis-server \
    --maxmemory 256mb \
    --maxmemory-policy allkeys-lru \
    --save 900 1 \
    --save 300 10 \
    --save 60 10000 \
    --appendonly yes \
    --appendfsync everysec

# 启动后端服务，在项目根目录(DAIR)运行以下命令
python -m uvicorn backend.main:app --reload
```

- Terminal 2: 运行前端

```bash
# 安装 Node.js 依赖
cd frontend
npm install

# 启动开发服务器
npm run dev
```

运行成功后，我们可以在本地浏览器打开Deep Cognition链接[http://localhost:5173/](http://localhost:5173/)
- 如果是在MacOS本地运行，可以直接打开链接
- 如果是在远程服务器运行，需要在Cursor上做端口转发
  - 在下方工作区选择Ports标签页，点击Add Port，将远程服务器localhost的端口服务转发至本机


## 在生产环境用Docker部署


```bash
# 克隆仓库
git clone https://github.com/GAIR-NLP/DAIR.git
cd DAIR

# 启动服务
docker-compose up -d
```

#### 服务说明
- 前端服务：通过 Nginx 暴露在 80 端口（HTTP）和 443 端口（HTTPS）
- 后端服务：在容器内部运行在 8000 端口，通过 Nginx 反向代理
- MongoDB：在容器内部运行在 27017 端口

#### 配置说明
- MongoDB 配置：
  - 用户名：admin
  - 密码：admin
  - 数据库名：dair

## 在开发环境启动docker数据库GUI
```
docker run -d \
--name dair-mongo-express \
--network dair_main-network \
-p 8081:8081 \
-e ME_CONFIG_MONGODB_SERVER=mongodb \
-e ME_CONFIG_MONGODB_PORT=27017 \
-e ME_CONFIG_MONGODB_AUTH_USERNAME=admin \
-e ME_CONFIG_MONGODB_AUTH_PASSWORD=admin \
-e ME_CONFIG_MONGODB_AUTH_DATABASE=admin \
-e ME_CONFIG_BASICAUTH_USERNAME=admin \
-e ME_CONFIG_BASICAUTH_PASSWORD=admin \
mongo-express
```

## API 接口文档

### 文档管理系统接口

#### 认证
所有接口都需要在请求头中携带 Bearer Token：
```
Authorization: Bearer <your_token>
```

#### 1. 创建文档
- **接口**: `POST /documents/`
- **描述**: 创建新文档
- **请求体**:
```json
{
    "title": "文档标题",
    "parent_id": "父文档ID",  // 可选，根文档为null
    "content": "文档内容",
    "tags": ["标签1", "标签2"],  // 可选
    "metadata": {  // 可选
        "type": "文档类型"
    }
}
```
- **响应**: 201 Created
```json
{
    "id": "文档ID",
    "title": "文档标题",
    "parent_id": "父文档ID",
    "content": "文档内容",
    "tags": ["标签1", "标签2"],
    "metadata": {
        "type": "文档类型"
    },
    "created_at": "创建时间",
    "updated_at": "更新时间"
}
```

#### 2. 获取文档树
- **接口**: `GET /documents/tree`
- **描述**: 获取用户的文档树结构
- **响应**: 200 OK
```json
[
    {
        "id": "文档ID",
        "name": "文档标题",
        "parent_id": "父文档ID",
        "is_folder": false,
        "created_at": "创建时间",
        "updated_at": "更新时间"
    }
]
```

#### 3. 获取单个文档
- **接口**: `GET /documents/{document_id}`
- **描述**: 获取指定ID的文档
- **响应**: 200 OK
```json
{
    "id": "文档ID",
    "title": "文档标题",
    "parent_id": "父文档ID",
    "content": "文档内容",
    "tags": ["标签1", "标签2"],
    "metadata": {
        "type": "文档类型"
    },
    "created_at": "创建时间",
    "updated_at": "更新时间"
}
```

#### 4. 更新文档
- **接口**: `PUT /documents/{document_id}`
- **描述**: 更新指定ID的文档
- **请求体**:
```json
{
    "title": "新标题",  // 可选
    "parent_id": "新父文档ID",  // 可选
    "content": "新内容",  // 可选
    "tags": ["新标签1", "新标签2"],  // 可选
    "metadata": {  // 可选
        "type": "新文档类型"
    }
}
```
- **响应**: 200 OK
```json
{
    "id": "文档ID",
    "title": "新标题",
    "parent_id": "新父文档ID",
    "content": "新内容",
    "tags": ["新标签1", "新标签2"],
    "metadata": {
        "type": "新文档类型"
    },
    "created_at": "创建时间",
    "updated_at": "更新时间"
}
```

#### 5. 删除文档
- **接口**: `DELETE /documents/{document_id}`
- **描述**: 删除指定ID的文档
- **参数**:
  - `force`: 是否强制删除（包括子文档），默认为 false
- **响应**: 204 No Content

#### 错误响应
所有接口在发生错误时都会返回相应的错误信息：
```json
{
    "detail": "错误信息"
}
```

常见错误状态码：
- 400 Bad Request: 请求参数错误
- 401 Unauthorized: 未认证或认证失败
- 404 Not Found: 文档不存在
- 500 Internal Server Error: 服务器内部错误

#### 注意事项
1. 文档树结构限制：
   - 不能将文档设置为其自身的父节点
   - 不能将文档移动到其子文档下（包括间接子文档）
2. 删除文档限制：
   - 默认情况下，不能删除包含子文档的文档
   - 使用 `force=true` 参数可以强制删除文档及其所有子文档

