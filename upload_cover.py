import asyncio
import io
import json
import os
import re
from backend.file.storage_factory import get_storage


async def upload_cover(filename: str, file_content:io.BytesIO):
    """测试TOS存储功能"""
    try:
        # 获取存储实例
        storage = get_storage()
        print(f"存储类型: {type(storage).__name__}")
        
        # 测试数据
        user_id = "cognitions_cover"
        
        print(f"开始测试用户 {user_id} 的文件操作...")
        
        # 测试上传文件
        print("1. 测试上传文件...")
        file_info = await storage.upload_file(
            user_id=user_id,
            filename=filename,
            file_content=file_content,
            content_type="text/plain"
        )
        # 测试获取文件URL
        print("3. 测试获取文件URL...")
        file_url = await storage.get_file_url(user_id, file_info.file_id)
        return file_url
        
    except Exception as e:
        print(f"上传失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    cover_dir_path = "/root/xjcai/DAIR/covers"
    cognition_database_file_path = '/root/xjcai/DAIR/cognitions_export.json'
    cognition_list = []
    # 统计各类错误
    error_stats = {
        "cover_id_empty": 0,
        "cover_path_not_exists": 0,
        "upload_exception": 0
    }
    success_count = 0
    with open(cognition_database_file_path, "r") as f:
        cognition_list = json.load(f)
    
    for it in cognition_list:
        cover_id = it.get('id', '') or it.get('_id', '')
        if not cover_id:
            print(f"cover_id 为空, {it}")
            error_stats["cover_id_empty"] += 1
            continue
        cover_path = os.path.join(cover_dir_path, f"cognition_{cover_id}.png")
        if not os.path.exists(cover_path):
            print(f"cover_path 不存在: {cover_path}")
            error_stats["cover_path_not_exists"] += 1
            continue
        try:
            with open(cover_path, "rb") as f:
                file_content = io.BytesIO(f.read())
                file_url = asyncio.run(upload_cover(f"cognition_{cover_id}.png", file_content))
                it['cover_url'] = file_url
                print(f"上传成功: {file_url}")
                success_count += 1
        except Exception as e:
            print(f"上传过程中发生异常: {e}")
            error_stats["upload_exception"] += 1
        
    cognition_database_file_path_with_cover = '/root/xjcai/DAIR/cognitions_export_with_cover.json'
    with open(cognition_database_file_path_with_cover, "w") as f:
        json.dump(cognition_list, f, indent=4, ensure_ascii=False)
    
    print(f"上传完成，成功数量: {success_count}")
    print("各类错误统计：")
    print(f"  cover_id 为空: {error_stats['cover_id_empty']}")
    print(f"  cover_path 不存在: {error_stats['cover_path_not_exists']}")
    print(f"  上传异常: {error_stats['upload_exception']}")

async def test_download_cover(cover_url: str):
    pattern = r"https://[^/]+/[^/]+/users/([^/]+)/([^/]+)/([^/]+)"
    match = re.match(pattern, cover_url)
    
    url_user_id, file_id, filename = match.groups()
    
    # 获取存储服务
    storage = get_storage()

    # 下载文件内容
    file_content = await storage.download_file(
        user_id=url_user_id,
        file_id=file_id
    )
    with open(f"downloaded_{filename}.png", "wb") as f:
        f.write(file_content)

if __name__ == "__main__":
    asyncio.run(test_download_cover("https://tos-ap-southeast-3.volces.com/deepcognition/users/cognitions_cover/09155123-7562-4bb0-bb3a-04fa53c5960d/cognition_2505_11409_1.png"))