#!/bin/bash

# DAIR项目部署脚本 - 支持自动SSL证书获取

set -e

echo "🚀 开始DAIR项目部署..."

# 1. 停止现有服务
echo "📦 停止现有服务..."
docker compose -f docker-compose.deploy.yml down

# 2. 清理Docker缓存
echo "🧹 清理Docker缓存..."
docker system prune -af --volumes=false

# 3. 拉取最新镜像
echo "⬇️ 拉取最新镜像..."
docker compose -f docker-compose.deploy.yml pull

# 4. 自动获取/更新SSL证书
echo "🔒 处理SSL证书..."
if [ -f "ssl-auto-setup.sh" ]; then
    chmod +x ssl-auto-setup.sh
    ./ssl-auto-setup.sh
else
    echo "⚠️ 未找到SSL自动化脚本，跳过证书处理"
fi

# 5. 启动服务
echo "🌟 启动服务..."
docker compose -f docker-compose.deploy.yml up -d

# 6. 等待服务就绪
echo "⏳ 等待服务启动..."
sleep 10

# 7. 健康检查
echo "🏥 执行健康检查..."
if curl -f -k https://dev.q.opensii.ai >/dev/null 2>&1; then
    echo "✅ HTTPS服务正常运行"
elif curl -f http://dev.q.opensii.ai >/dev/null 2>&1; then
    echo "⚠️ HTTP服务正常，但HTTPS可能未配置"
else
    echo "❌ 服务健康检查失败"
fi

echo "🎉 部署完成！"
echo "🌐 HTTP: http://dev.q.opensii.ai"
echo "🔐 HTTPS: https://dev.q.opensii.ai"