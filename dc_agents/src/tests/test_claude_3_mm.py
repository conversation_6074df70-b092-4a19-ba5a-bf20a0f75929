import requests
import json
import base64
from PIL import Image
import io
from openai import OpenAI


def test_claude_3_mm_api():
    """测试调用Claude 3.7 Sonnet Thinking模型API的图像处理能力"""
    
    # API配置
    api_url = "http://35.220.164.252:3888/v1"
    api_key = "sk-kuZad7QdMaiAGwXfSqLt3tjg9dYjueS4Jg6a4tMXTFkrhhA9"
    client = OpenAI(
        api_key=api_key,
        base_url=api_url
    )
    
    # 准备测试图片
    test_image_path = "/root/interact/DAIR/dc_agents/src/tests/test_tmp/194424.png"
    with open(test_image_path, "rb") as img_file:
        img_bytes = img_file.read()
        img_base64 = base64.b64encode(img_bytes).decode()
    
    try:
        completion = client.chat.completions.create(
            model="anthropic/claude-3.7-sonnet:thinking",
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{img_base64}"
                            }
                        },
                        {
                            "type": "text",
                            "text": "请详细描述这张图片的内容，并分析图片中的关键元素。"
                        }
                    ]
                }
            ]
        )
        print(completion.model_dump_json())
        return completion.model_dump_json()
        
    except Exception as e:
        print(f"API调用失败: {str(e)}")
        raise

if __name__ == "__main__":
    test_claude_3_mm_api()





