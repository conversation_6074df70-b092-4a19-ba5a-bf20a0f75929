import os
import asyncio
import pytest
from datetime import datetime
from typing import Dict, Any

from backend.dc_agents.src.agents.mongodb_memory import MongoDBMemory

# 测试用的代理名称
TEST_AGENT_NAME = "TestAgent"

@pytest.fixture
async def mongodb_memory():
    """创建一个测试用的MongoDB内存实例"""
    # 创建内存实例
    memory = MongoDBMemory(agent_name=TEST_AGENT_NAME)
    
    # 清空现有测试数据
    query = {"agent_name": TEST_AGENT_NAME}
    try:
        docs = await memory.db.find_many(query)
        for doc in docs:
            await memory.db.delete(str(doc.id))
    except Exception as e:
        print(f"清空测试数据失败: {e}")
    
    # 返回测试实例
    yield memory
    
    # 测试完成后清理数据
    try:
        docs = await memory.db.find_many(query)
        for doc in docs:
            await memory.db.delete(str(doc.id))
    except Exception as e:
        print(f"清理测试数据失败: {e}")


@pytest.mark.asyncio
async def test_save_and_load(mongodb_memory):
    """测试保存和加载功能"""
    # 设置对话ID
    conversation_id = mongodb_memory.set_conversation_id()
    
    # 记录一些测试事件
    mongodb_memory.record_event("test_event", "测试内容1")
    mongodb_memory.record_event("user_message", "你好，代理")
    mongodb_memory.record_event("agent_response", "您好，有什么可以帮助您的？")
    
    # 保存到MongoDB
    doc_id = await mongodb_memory.save_to_mongodb()
    assert doc_id, "应该成功保存并返回文档ID"
    
    # 清空内存
    mongodb_memory.clear_memory()
    assert len(mongodb_memory.memory_records) == 0, "清空后内存记录应为空"
    
    # 从MongoDB加载
    result = await mongodb_memory.load_from_mongodb(conversation_id)
    assert result, "应该成功从MongoDB加载"
    assert len(mongodb_memory.memory_records) == 3, "应该加载3条记录"
    assert mongodb_memory.memory_records[0]["type"] == "test_event", "第一条记录类型应为test_event"
    assert mongodb_memory.memory_records[1]["content"] == "你好，代理", "第二条记录内容应正确"


@pytest.mark.asyncio
async def test_async_record_event(mongodb_memory):
    """测试异步记录事件"""
    # 设置对话ID
    conversation_id = mongodb_memory.set_conversation_id()
    
    # 异步记录事件
    await mongodb_memory.record_event_async("async_test", "异步测试内容")
    
    # 验证内存中有记录
    assert len(mongodb_memory.memory_records) == 1, "内存中应有1条记录"
    
    # 清空内存
    mongodb_memory.clear_memory()
    
    # 从MongoDB加载验证持久化成功
    result = await mongodb_memory.load_from_mongodb(conversation_id)
    assert result, "应该成功从MongoDB加载"
    assert len(mongodb_memory.memory_records) == 1, "应该加载1条记录"
    assert mongodb_memory.memory_records[0]["type"] == "async_test", "记录类型应为async_test"


@pytest.mark.asyncio
async def test_get_all_conversations(mongodb_memory):
    """测试获取所有对话记录"""
    # 创建两个不同的对话
    for i in range(2):
        # 设置新的对话ID
        mongodb_memory.set_conversation_id(f"{TEST_AGENT_NAME}_test_{i}")
        mongodb_memory.clear_memory()
        
        # 添加一些测试记录
        mongodb_memory.record_event("user_message", f"测试对话{i}")
        mongodb_memory.record_event("agent_response", f"响应测试对话{i}")
        
        # 保存到MongoDB
        await mongodb_memory.save_to_mongodb()
    
    # 获取所有对话记录
    conversations = await mongodb_memory.get_all_conversations()
    
    # 验证结果
    assert len(conversations) >= 2, "应该至少有2个对话记录"
    
    # 验证对话记录的格式
    for conv in conversations:
        assert "conversation_id" in conv, "对话记录应包含conversation_id"
        assert "timestamp" in conv, "对话记录应包含timestamp"
        assert "record_count" in conv, "对话记录应包含record_count"


@pytest.mark.asyncio
async def test_summarize(mongodb_memory):
    """测试摘要功能"""
    # 设置对话ID
    mongodb_memory.set_conversation_id()
    
    # 添加不同类型的记录
    mongodb_memory.record_event("user_message", "用户消息1")
    mongodb_memory.record_event("agent_response", "代理响应1")
    mongodb_memory.record_event("user_message", "用户消息2")
    mongodb_memory.record_event("system", "系统消息")
    
    # 获取摘要
    summary = mongodb_memory.summarize()
    
    # 验证摘要内容
    assert "共有 4 条记忆记录" in summary, "摘要应包含记录总数"
    assert "user_message: 2 条" in summary, "摘要应包含user_message记录数"
    assert "agent_response: 1 条" in summary, "摘要应包含agent_response记录数"
    assert "system: 1 条" in summary, "摘要应包含system记录数"
    assert "最近记录" in summary, "摘要应包含最近记录部分"


if __name__ == "__main__":
    # 直接运行测试
    asyncio.run(pytest.main(["-xvs", __file__])) 