import os
import unittest
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = str(Path(__file__).parent.parent.parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

from dc_agents.src.tools.file_convert import (
    FileConverter,
    ImageFileConverter,
    PDFFileConverter,
    TextFileConverter,
    PowerPointFileConverter,
    WordFileConverter,
    FileConverterFactory,
    convert_file_to_text
)

class TestFileConvert(unittest.TestCase):
    def setUp(self):
        """设置测试环境"""
        self.test_dir = os.path.join(os.path.dirname(__file__), 'test_tmp')
        self.test_files = {
            'txt': '23423.txt',
            'png': '194424.png',
            'pdf': '3715097.pdf',
            'docx': '59485.docx',
            'pptx': '45621.pptx'
        }

    def test_file_converters_initialization(self):
        """测试转换器初始化"""
        converters = [
            ImageFileConverter(),
            PDFFileConverter(),
            TextFileConverter(),
            PowerPointFileConverter(),
            WordFileConverter()
        ]
        
        for converter in converters:
            self.assertIsInstance(converter, FileConverter)

    def test_converter_factory(self):
        """测试转换器工厂"""
        factory = FileConverterFactory()
        
        # 测试所有文件类型都能找到对应的转换器
        for file_type, filename in self.test_files.items():
            file_path = os.path.join(self.test_dir, filename)
            converter = factory.get_converter(file_path)
            self.assertIsNotNone(converter, f"找不到 {file_type} 文件的转换器")

    def test_pdf_file_conversion(self):
        """测试PDF文件转换"""
        file_path = os.path.join(self.test_dir, self.test_files['pdf'])
        
        # 测试纯文本转换
        text_result = convert_file_to_text(file_path)
        
        self.assertIsNotNone(text_result)
        self.assertIsInstance(text_result, str)
        print("\nPDF文件转换结果:")
        print("-" * 50)
        print(text_result[:500] + "..." if len(text_result) > 500 else text_result)
        print("-" * 50)

    def test_text_file_conversion(self):
        """测试文本文件转换"""
        file_path = os.path.join(self.test_dir, self.test_files['txt'])
        result = convert_file_to_text(file_path)
        self.assertIsNotNone(result)
        self.assertIsInstance(result, str)
        print("\n文本文件转换结果:")
        print(result[:200] + "..." if len(result) > 200 else result)

    def test_image_file_conversion(self):
        """测试图片文件转换"""
        file_path = os.path.join(self.test_dir, self.test_files['png'])
        result = convert_file_to_text(file_path)
        self.assertIsNotNone(result)
        self.assertIsInstance(result, str)
        print("\n图片文件转换结果:")
        print(result[:200] + "..." if len(result) > 200 else result)

    def test_word_file_conversion(self):
        """测试Word文件转换"""
        file_path = os.path.join(self.test_dir, self.test_files['docx'])
        result = convert_file_to_text(file_path)
        self.assertIsNotNone(result)
        self.assertIsInstance(result, str)
        print("\nWord文件转换结果:")
        print(result[:200] + "..." if len(result) > 200 else result)

    def test_powerpoint_file_conversion(self):
        """测试PPT文件转换"""
        file_path = os.path.join(self.test_dir, self.test_files['pptx'])
        result = convert_file_to_text(file_path)
        self.assertIsNotNone(result)
        self.assertIsInstance(result, str)
        print("\nPPT文件转换结果:")
        print(result[:200] + "..." if len(result) > 200 else result)

    def test_invalid_file(self):
        """测试无效文件处理"""
        result = convert_file_to_text("nonexistent_file.txt")
        self.assertIsNone(result)

if __name__ == '__main__':
    unittest.main(verbosity=2) 