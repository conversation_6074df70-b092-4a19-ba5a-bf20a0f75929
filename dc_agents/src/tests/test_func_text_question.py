import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from dc_agents.src.deep_cognition.research_agent import ResearchAgent
from dc_agents.src.agents.research_memory import ResearchMemory
from dc_agents.src.services.llm_service import LLMService

@pytest.mark.asyncio
async def test_e2e_text_question():
    pass

@pytest.fixture
def mock_llm_service():
    """创建模拟的LLM服务"""
    mock = AsyncMock()
    mock.get_completion.return_value = [
        MagicMock(
            choices=[
                MagicMock(
                    delta=MagicMock(
                        content="<action>search</action>"
                    )
                )
            ]
        )
    ]
    return mock

@pytest.fixture
def mock_memory():
    """创建模拟的研究内存"""
    memory = MagicMock(spec=ResearchMemory)
    memory.research_question = "测试问题"
    memory.round = 1
    memory.search_times = 0
    memory.max_search_times = 5
    memory.clarification_questions = []
    memory.research_rules = []
    memory.report_draft = ""
    memory.language = "zh"
    memory.research_trajectory = {
        "search_queries": [],
        "useful_urls": [],
        "action_reasoning": [],
        "user_feedbacks": []
    }
    memory.is_paused = False
    memory.is_updating_report = False
    return memory

@pytest.mark.asyncio
async def test_select_action(mock_llm_service, mock_memory):
    """测试select_action函数"""
    # 创建ResearchAgent实例
    with patch('dc_agents.src.services.llm_service.LLMService', return_value=mock_llm_service):
        agent = ResearchAgent(
            name="TestAgent",
            config={
                "action_selection_model": {
                    "name": "claude-3.7-sonnet-thinking",
                    "temperature": 0.6,
                    "max_tokens": 8000
                },
                "url_selection_model": {
                    "name": "gpt-4.1-mini",
                    "temperature": 0.6,
                    "max_tokens": 500
                },
                "report_editing_model": {
                    "name": "claude-3.7-sonnet",
                    "temperature": 0.6,
                    "max_tokens": 8000
                },
                "max_search_times": 4
            }
        )
        
        # 替换依赖
        agent.memory = mock_memory
        
        # 执行select_action
        actions = []
        async for action in agent.select_action():
            actions.append(action)
        
        # 验证结果
        assert len(actions) > 0
        assert any(action.get("action") == "search" for action in actions)
        assert mock_llm_service.get_completion.called

@pytest.mark.asyncio
async def test_update_report_with_prompt(mock_llm_service, mock_memory):
    """测试_update_report_with_prompt函数"""
    # 创建ResearchAgent实例
    with patch('dc_agents.src.services.llm_service.LLMService', return_value=mock_llm_service):
        agent = ResearchAgent(
            name="TestAgent",
            config={
                "action_selection_model": {
                    "name": "claude-3.7-sonnet-thinking",
                    "temperature": 0.6,
                    "max_tokens": 8000
                },
                "url_selection_model": {
                    "name": "gpt-4.1-mini",
                    "temperature": 0.6,
                    "max_tokens": 500
                },
                "report_editing_model": {
                    "name": "claude-3.7-sonnet",
                    "temperature": 0.6,
                    "max_tokens": 8000
                },
                "max_search_times": 4
            }
        )
        
        # 替换依赖
        agent.memory = mock_memory
        
        # 设置模拟的LLM响应
        mock_llm_service.get_completion.return_value = [
            MagicMock(
                choices=[
                    MagicMock(
                        delta=MagicMock(
                            content="<article>测试报告内容</article>"
                        )
                    )
                ]
            )
        ]
        
        # 执行_update_report_with_prompt
        updates = []
        async for update in agent._update_report_with_prompt(""):
            updates.append(update)
        
        # 验证结果
        assert len(updates) > 0
        assert any(update.get("action") == "edit_report" for update in updates)
        assert mock_llm_service.get_completion.called
        assert mock_memory.update_report_draft.called

