import requests
import json
import base64
from PIL import Image
import io
from openai import OpenAI


def test_qwen_vl_api():
    """测试调用通义千问VL模型API"""
    
    # API配置
    api_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    api_key = "sk-f129baeac55548679517444fdf82597b"  # 需要替换为实际的API key
    client = OpenAI(
        api_key=api_key,
        base_url=api_url
    )
    # 准备测试图片
    test_image_path = "/root/interact/DAIR/dc_agents/src/tests/test_tmp/194424.png"
    with open(test_image_path, "rb") as img_file:
        img_bytes = img_file.read()
        img_base64 = base64.b64encode(img_bytes).decode()
    try:
        completion = client.chat.completions.create(
            model="qwen-vl-plus",
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{img_base64}"
                            }
                        },
                        {
                            "type": "text",
                            "text": "请描述这张图片的内容"
                        }
                    ]
                }
            ]
        )
        print(completion.model_dump_json())
        return completion.model_dump_json()
        
    except Exception as e:
        print(f"API调用失败: {str(e)}")
        raise

if __name__ == "__main__":
    test_qwen_vl_api()