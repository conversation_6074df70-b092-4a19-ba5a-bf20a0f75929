import re
from typing import Optional
from datetime import datetime, timezone, timedelta

# 返回东八区（北京时间）的当前时间
def get_cn_time():
    """返回东八区（北京时间）的当前时间"""
    return datetime.now(timezone(timedelta(hours=8)))

def get_content_from_tag(
    content: str,
    tag: str,
    default_value: Optional[str] = None
) -> str:
    # 说明：
    # 1) (.*?) 懒惰匹配，尽量少匹配字符
    # 2) (?=(</tag>|<\w+|$)) 使用前瞻，意味着当后面紧跟 </tag> 或 <任意单词字符开头的标签> 或文本结束时，都停止匹配
    # 3) re.DOTALL 使得点号 . 可以匹配换行符

    if not content:
        return default_value
    
    pattern = rf"<{tag}>(.*?)(?=(</{tag}>|<\w+|$))"
    match = re.search(pattern, content, re.DOTALL)
    if match:
        return match.group(1).strip()
    return default_value

def get_current_date_str():
    return get_cn_time().strftime("%Y-%m-%d")
