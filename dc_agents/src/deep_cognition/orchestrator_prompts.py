SYSTEM_PROMPT_EN = """## Background information 
* Today is {time_str}
* You are Deep Cognition Agent


"""

SYSTEM_PROMPT_ZH = """## 背景信息
* 今天是 {time_str}
* 你是Deep Cognition智能体


"""

system_prompt = {
    "en": SYSTEM_PROMPT_EN,
    "zh": SYSTEM_PROMPT_ZH
}

######################################
INTENT_PROMPT_EN = """You are an orchestrator agent for a research system. Your task is to analyze the user's intent and make a decision on how to proceed. The user's intent can be one of the following:
- **Deep Research**: Request deep research on a specific problem. 
- **Paper Reading**: The user may put URLs of papers in the question, or just put a URL without any other text, then you can assume the user wants to read the paper.
- **Other**: If the question is neither deep research nor paper reading.

Put the intent name in <intent> tags, it should be one of ["deep_research", "paper_reading", "other"]

Here is the user's question:
<question>
{question}
</question>
"""

INTENT_PROMPT_ZH = """你是一个研究系统的协调者。你的任务是分析用户的意图，并决定如何继续。用户的意图可以是以下几种之一：
- **Deep Research**：请求对特定问题进行深入研究。
- **Paper Reading**：用户可能会在问题中放入论文的URL，或者只放一个URL而没有任何其他文本，那么你可以假设用户想要阅读论文。
- **Other**：如果问题既不是深入研究也不是论文阅读。

在<intent>标签中放入意图名称，它应该是["deep_research", "paper_reading", "other"]之一

以下是用户的问题：
<question>
{question}
</question>
"""


intent_prompt = {
    "en": INTENT_PROMPT_EN,
    "zh": INTENT_PROMPT_ZH
}


######################################


CLARIFICATION_PROMPT_EN = """You are an assistant for a research system. The user provides a question which requires deep research, your task is:
1. Analyze the question and think about what the user really wants to know.
2. Guide the user to clearly describe what they want to know.
3. Ask a clarification question to help the user think more clearly.
4. If the question is not clear, ask the user to give more details.

You should return a clarification question for the user.

Here is the user's question:
<question>
{question}
</question>
"""

CLARIFICATION_PROMPT_ZH = """你是一个研究系统的助手。用户提供了一个需要深入研究的问题，你的任务是：
1. 分析问题并思考用户真正想知道什么。
2. 引导用户清晰地描述他们想要了解的内容。
3. 提出一个澄清问题帮助用户更清楚地思考。
4. 如果问题不清晰，请用户提供更多细节。

You should return a clarification question for the user.

Here is the user's question:
<question>
{question}
</question>
"""


clarification_prompt = {
    "en": CLARIFICATION_PROMPT_EN,
    "zh": CLARIFICATION_PROMPT_ZH
}

#######################################


QUESTION_REWRITER_PROMPT_EN = """You just had a conversation with a user. The user first asked a question, then you asked a clarification question, and the user answered the clarification question.

Now rewrite the user's question and requirements based on the conversation history, and put the rewritten question and requirements in <question> tags.

Here is the conversation history:
<conversation>
{conversation}
</conversation>
"""


QUESTION_REWRITER_PROMPT_ZH = """你刚刚与一个用户进行了对话。用户首先问了一个问题，然后你问了一个澄清问题，用户回答了澄清问题。

现在根据对话历史重写用户的需求，并将重写后的问题和需求放在<question>标签中。

以下是对话历史：
<conversation>
{conversation}
</conversation>
"""

question_rewriter_prompt = {
    "en": QUESTION_REWRITER_PROMPT_EN,
    "zh": QUESTION_REWRITER_PROMPT_ZH
}
