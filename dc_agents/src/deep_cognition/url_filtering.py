from typing import List, Dict
from dc_agents.src.deep_cognition.url_filtering_prompts import FILTER_PROMPT_EN
from loguru import logger
from dc_agents.src.services.llm_service import LLMService

def build_prompt(webpages: List[Dict], prompt: str) -> str:
    result = f"{prompt}\n\n"
    for i, webpage in enumerate(webpages):
        simplified_webpage = dict({
            'url': webpage.get('url', ''),
            'title': webpage.get('title', ''),
            'snippet': webpage.get('snippet', ''),
            'query': webpage.get('query', ''),
        })
        result += f"{i}: {simplified_webpage}\n\n"
    return result.strip()
        

async def filter_webpages(webpages: List[Dict], agent: LLMService) -> List[Dict]:
    """
    Filters out webpages that are not harmful and relevant to the query

    Args:
        webpages (List[Dict]): List of webpage dictionaries to filter.

    Returns:
        List[Dict]: Filtered list of webpages
    """
    prompt = build_prompt(webpages, FILTER_PROMPT_EN)
    # logger.info(f"Prompt: {prompt[:100]}")

    try: 
        response = await agent.get_completion_sync(
            messages=[
                {"role": "system", "content": prompt},
            ],
        )
        response = response['response']
        print(f"Response: {response}")
        if response is None or not response.choices:
            logger.error("No response from agent or empty choices.")
            return []
    
        content = response.choices[0].message.content
        
        """
        format:
        index0
        index1
        ...
        """
        
        indices_str = content.strip().split("\n")
        indices = [int(index) for index in indices_str if index.isdigit()]
        return [webpages[i] for i in indices if 0 <= i < len(webpages)]
        
        
    
    except Exception as e:
        logger.error(f"Error getting useful from response: {e}")
        return []