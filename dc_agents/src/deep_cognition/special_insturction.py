user_preference = """
"""

search_edit_special_instruction = """
在本次的search edit阶段当中，我们需要纠正本次搜索的动作为 chat_edit, 而不是 search_edit,本轮不再提供这种动作
你只需要根据用户最新的问题，和当前的研究记录和草稿，在本次回答中进行探讨，并给出最终的选择 "chat_edit" 
当然你也可以选择其他的动作，如果你认为需要一些澄清，可以选择clarify，或者如果你真的认为用户的回答已经被解决了，那么就选择finish
系统会依据你的回答当中的内容，进一步的启动 chat_edit 行为，对报告进行重写，改进本次报告，而不会进行搜索
所以本次只需要输出你的探讨和chat_edit的动作标签, 不用生成新的query（新一轮的报告也是不需要在这个阶段输出的）
"""

clarify_special_instruction = """
"""

update_report_special_instruction = """
这是一个特殊的编辑模式（Chat to Edit），用户希望通过对话的方式来修改和完善当前的研究报告。

用户的具体编辑要求：
{user_edit_instruction}

在这个模式下：
1. 不需要进行额外的网络搜索，专注于根据用户的编辑要求对现有报告进行改进
2. 仔细理解用户的编辑需求和反馈，包括：
   - 内容修改建议
   - 结构调整要求  
   - 风格和语调的改进
   - 添加或删除特定部分
3. 保持报告的整体性和逻辑连贯性
4. 在修改时尽量保留原有的有价值内容
5. 对修改的部分进行适当的说明和解释

请根据用户的具体编辑要求，对研究报告进行相应的修改和完善。
"""