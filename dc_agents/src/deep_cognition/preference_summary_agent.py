import json
import logging
import time
from typing import Dict, List, Optional, Any, AsyncGenerator
from datetime import datetime

from loguru import logger

from dc_agents.src.agents.agent import Agent
from dc_agents.src.agents.research_memory import ResearchMemory
from dc_agents.src.services.llm_service import LLMService
from dc_agents.src.utils import get_content_from_tag, get_current_date_str

from .preference_prompts import preference_prompt


class PreferenceSummaryAgent(Agent):
    """
    用户偏好总结智能体
    
    该智能体负责从用户的点赞内容(user_likes_urls)中提取最新的5条记录，
    结合用户之前的偏好总结(user_preference)，生成新的偏好总结并添加到user_preference列表中。
    """
    
    def __init__(self, 
                 name: str = "PreferenceSummaryAgent",
                 config: Optional[Dict[str, Any]] = None):
        """
        初始化偏好总结智能体
        
        Args:
            name: 智能体名称
            config: 配置字典，应包含偏好分析模型的配置
        """
        super().__init__(name=name, config=config)
        
        # 初始化偏好分析模型
        if config and 'preference_analysis_model' in config:
            self.preference_analysis_model = LLMService(config['preference_analysis_model'])
        else:
            # 使用默认配置
            default_config = {
                'name': 'gpt-4o-mini',
                'temperature': 0.3,
                'max_tokens': 2000
            }
            self.preference_analysis_model = LLMService(default_config)
            
        logger.info(f"初始化偏好总结智能体: {name}")
    
    async def analyze_user_preferences(self, 
                                     memory: ResearchMemory, 
                                     language: str = "zh") -> Optional[str]:
        """
        分析用户偏好并生成新的偏好总结
        
        Args:
            memory: 研究内存对象
            language: 语言（"zh" 或 "en"）
            
        Returns:
            生成的新偏好总结，如果失败则返回None
        """
        try:
            # 1. 获取最后5个点赞的内容
            recent_likes = self._get_last_5_likes(memory)
            
            if not recent_likes:
                logger.info("没有找到用户点赞记录，跳过偏好分析")
                return None
                
            # 2. 获取用户之前的偏好记录
            previous_preferences = self._get_previous_preferences(memory)
            
            # 3. 准备分析数据
            analysis_data = self._prepare_analysis_data(memory, recent_likes, previous_preferences)
            
            # 4. 使用preference_prompts中的提示词
            prompt_template = preference_prompt.get(language, preference_prompt["zh"])
            formatted_prompt = prompt_template.format(**analysis_data)
            
            logger.info(f"开始分析用户偏好，使用最近{len(recent_likes)}条点赞记录")
            logger.debug(f"分析数据: {analysis_data['research_question']}")
            
            # 5. 调用LLM进行分析
            messages = [{"role": "user", "content": formatted_prompt}]
            response = await self.preference_analysis_model.get_completion(messages=messages)
            
            new_preference = ""
            async for chunk in response:
                # 处理token信息
                if "is_token_info" in chunk and chunk["is_token_info"]:
                    token_info = chunk.get("token_info", {})
                    logger.debug(f"偏好分析token使用: {token_info}")
                    continue
                    
                if chunk.choices and hasattr(chunk.choices[0].delta, "content") and chunk.choices[0].delta.content is not None:
                    new_preference += chunk.choices[0].delta.content
                    
            if new_preference.strip():
                # 6. 将新的偏好总结添加到user_preference列表中
                self._add_preference_to_memory(memory, new_preference.strip())
                
                logger.info(f"成功生成新的用户偏好总结，长度: {len(new_preference)}字符")
                logger.debug(f"新偏好总结: {new_preference[:200]}...")
                
                return new_preference.strip()
            else:
                logger.warning("模型返回空的偏好总结")
                return None
                
        except Exception as e:
            logger.error(f"分析用户偏好时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    def _get_last_5_likes(self, memory: ResearchMemory) -> List[Dict[str, Any]]:
        """
        获取最后5个用户点赞的内容
        
        Args:
            memory: 研究内存对象
            
        Returns:
            最后5个点赞记录列表
        """
        if not hasattr(memory, 'user_likes_urls') or not memory.user_likes_urls:
            return []
            
        # 按时间戳排序，获取最新的5个记录
        sorted_likes = sorted(
            memory.user_likes_urls, 
            key=lambda x: x.get('timestamp', 0), 
            reverse=True
        )
        
        return sorted_likes[:5]
    
    def _get_previous_preferences(self, memory: ResearchMemory) -> List[str]:
        """
        获取用户之前的偏好总结记录
        
        Args:
            memory: 研究内存对象
            
        Returns:
            之前的偏好总结列表
        """
        if not hasattr(memory, 'user_preferences') or not memory.user_preferences:
            return []
            
        return memory.user_preferences
    
    def _prepare_analysis_data(self, 
                             memory: ResearchMemory, 
                             recent_likes: List[Dict[str, Any]], 
                             previous_preferences: List[str]) -> Dict[str, str]:
        """
        准备分析数据，构建提示词所需的各个字段
        
        Args:
            memory: 研究内存对象
            recent_likes: 最近的点赞记录
            previous_preferences: 之前的偏好总结
            
        Returns:
            格式化后的分析数据字典
        """
        # 1. 研究问题 - 使用最新的研究问题
        research_question = memory.research_question or "未知研究问题"
        
        # 2. 最后一次用户偏好 - 获取最新的偏好总结
        last_preference = ""
        if previous_preferences:
            last_preference = previous_preferences[-1]  # 获取最后一次偏好
        else:
            last_preference = "暂无历史偏好记录"
        
        # 3. 格式化最近5条点赞记录 - 清晰展示每条记录的详细信息
        recent_likes_records = ""
        if recent_likes:
            record_lines = []
            for i, like in enumerate(recent_likes, 1):
                url = like.get('url', '未知URL')
                search_query = like.get('search_query', '未知查询')
                new_question = like.get('new_question', '无用户反馈')
                page_content = like.get('page_content', '')
                round_num = like.get('round', '未知')
                
                # 截取页面内容的前800字符
                content_preview = page_content[:800] + "..." if len(page_content) > 800 else page_content
                
                record_text = f"""
记录 {i}:
- URL: {url}
- 搜索查询: {search_query}
- 用户反馈/新问题: {new_question}
- 轮次: {round_num}
- 内容预览: {content_preview}
"""
                record_lines.append(record_text.strip())
            
            recent_likes_records = "\n\n".join(record_lines)
        else:
            recent_likes_records = "暂无点赞记录"
        
        return {
            'research_question': research_question,
            'last_preference': last_preference,
            'recent_likes_records': recent_likes_records
        }
    
    def _add_preference_to_memory(self, memory: ResearchMemory, new_preference: str):
        """
        将新的偏好总结添加到memory的user_preferences列表中
        
        Args:
            memory: 研究内存对象
            new_preference: 新的偏好总结
        """
        # 确保user_preferences属性存在
        if not hasattr(memory, 'user_preferences'):
            memory.user_preferences = []
        
        # 添加新的偏好总结
        memory.user_preferences.append(new_preference)
        
        logger.info(f"新偏好已添加到user_preferences，当前总数: {len(memory.user_preferences)}")
    
    def should_generate_new_preference(self, 
                                     memory: ResearchMemory, 
                                     min_new_likes: int = 1) -> bool:
        # 检查是否有足够的点赞记录
        if not hasattr(memory, 'user_likes_urls') or not memory.user_likes_urls:
            return False
            
        # 检查点赞数量是否足够
        if len(memory.user_likes_urls) < min_new_likes:
            return False
            
        return True
    
    async def run(self, input_data: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """
        运行偏好总结智能体
        
        Args:
            input_data: 输入数据，应包含:
                - memory: ResearchMemory对象
                - language: 可选，语言设置，默认"zh"
                
        Yields:
            处理结果
        """
        memory = input_data.get('memory')
        if not memory:
            yield {
                "type": "error",
                "content": "缺少memory参数"
            }
            return
            
        language = input_data.get('language', 'zh')
        
        # 检查是否有足够的点赞记录来生成偏好
        if not self.should_generate_new_preference(memory):
            recent_likes_count = len(getattr(memory, 'user_likes_urls', []))
            yield {
                "type": "info",
                "content": f"点赞记录不足（当前{recent_likes_count}条，需要至少5条），跳过偏好分析"
            }
            return
        
        # 执行偏好分析
        yield {
            "type": "info",
            "content": "开始分析用户偏好..."
        }
        
        try:
            # 获取当前信息用于日志
            recent_likes = self._get_last_5_likes(memory)
            previous_prefs = self._get_previous_preferences(memory)
            
            yield {
                "type": "info",
                "content": f"基于最近{len(recent_likes)}条点赞记录和{len(previous_prefs)}条历史偏好进行分析"
            }
            
            # 生成新的偏好总结
            new_preference = await self.analyze_user_preferences(memory, language)
            
            if new_preference:
                yield {
                    "type": "preference_generated",
                    "content": new_preference,
                    "total_preferences": len(memory.user_preferences),
                    "based_on_likes": len(recent_likes)
                }
                
                yield {
                    "type": "success",
                    "content": f"成功生成新的偏好总结，当前共有{len(memory.user_preferences)}条偏好记录"
                }
            else:
                yield {
                    "type": "error",
                    "content": "偏好分析失败"
                }
                
        except Exception as e:
            logger.error(f"运行偏好总结智能体时出错: {str(e)}")
            yield {
                "type": "error",
                "content": f"偏好分析出错: {str(e)}"
            }
 