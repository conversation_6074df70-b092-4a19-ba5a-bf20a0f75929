import asyncio
import os
import tempfile
from typing import Dict, List, Optional, Any, AsyncGenerator
from datetime import datetime
import uuid
import hashlib
from pathlib import Path

from pptx import Presentation
from pptx.shapes.connector import Connector
from pptx.shapes.shapetree import SlideShapes
import io

from dc_agents.src.agents.agent import Agent
from dc_agents.src.services.llm_service import LLMService
from dc_agents.src.deep_cognition.ppt_review_prompts import (
    ROLE_INITIALIZATION_PROMPT,
    STRUCTURE_ANALYSIS_PROMPT_ZH,
    PAGE_ANALYSIS_PROMPT_ZH,
    COMPREHENSIVE_EVALUATION_PROMPT_ZH,
    FEEDBACK_GENERATION_PROMPT_ZH,
    QA_RESPONSE_PROMPT_ZH,
)
from dc_agents.src.utils import get_current_date_str
from loguru import logger


class PPTReviewAgent(Agent):
    """
    PPT评审Agent - 扮演资深VC投资合伙人角色
    对创业项目的融资PPT进行专业评审，提供投资人视角的建议
    """

    def __init__(
        self,
        name: str = "PPTReviewAgent",
        config: Optional[Dict[str, Any]] = None,
        conversation_id: str = None,
    ):
        """
        Initialize the PPT Review agent.

        Args:
            name: The name of the agent
            config: Optional configuration dictionary for the agent
            conversation_id: Optional conversation ID for session tracking
        """
        super().__init__(name=name, config=config)

        # 初始化模型服务
        self.structure_model = LLMService(self.config["structure_model"])
        self.content_model = LLMService(self.config["content_model"])
        self.design_model = LLMService(self.config["design_model"])

        # 会话管理
        self.conversation_id = conversation_id

        # 评审配置
        self.output_dir = Path("ppt_reviews")
        self.output_dir.mkdir(exist_ok=True)

        # 初始化token使用统计
        self.token_usage = {
            "total_input_tokens": 0,
            "total_output_tokens": 0,
            "total_cost": 0,
            "models_usage": {},
        }

        # 评审会话状态
        self.review_session = {
            "session_id": conversation_id,
            "ppt_content": None,
            "analysis_results": {
                "structure": {},
                "pages": [],
                "comprehensive": {},
            },
            "review_report": {},
        }

        logger.info(
            f"PPT Review Agent initialized with conversation_id: {conversation_id}"
        )

    async def _get_llm_response(
        self, llm_service, prompt: str, temperature: float = 0.7, max_tokens: int = 1500
    ) -> str:
        """通用的LLM响应获取方法"""
        try:
            # 使用同步方式获取完整响应，避免异步生成器问题
            result = await llm_service.get_completion_sync(
                [{"role": "user", "content": prompt}],
                temperature=temperature,
                max_tokens=max_tokens,
            )

            if result and result.get("response"):
                response = result["response"]
                token_info = result.get("token_info", {})

                # 更新token使用统计
                if token_info:
                    self._update_token_usage(llm_service.model_name, token_info)

                # 从response中提取文本内容
                if hasattr(response, "choices") and response.choices and len(response.choices) > 0:
                    message = response.choices[0].message
                    if message is None:
                        logger.warning("LLM响应中message为None")
                        return "抱歉，LLM响应中message为空。"
                    
                    content = ""

                    # 处理reasoning content (如果存在)
                    if (
                        hasattr(message, "reasoning_content")
                        and message.reasoning_content
                    ):
                        content += message.reasoning_content
                    elif hasattr(message, "reasoning") and message.reasoning:
                        content += message.reasoning

                    # 处理主要内容
                    if hasattr(message, "content") and message.content:
                        content += message.content

                    if not content:
                        logger.warning("LLM响应内容为空")
                        return "抱歉，LLM响应内容为空。"

                    return content

            return "抱歉，LLM响应解析失败。"

        except Exception as e:
            logger.error(f"LLM调用失败: {str(e)}")
            return f"抱歉，LLM调用出现错误: {str(e)}"

    def _update_token_usage(self, model_name: str, token_info: Dict[str, Any]) -> None:
        """更新token使用统计"""
        self.token_usage["total_input_tokens"] += token_info.get("input_tokens", 0)
        self.token_usage["total_output_tokens"] += token_info.get("output_tokens", 0)
        self.token_usage["total_cost"] += token_info.get("total_cost", 0)

        if model_name not in self.token_usage["models_usage"]:
            self.token_usage["models_usage"][model_name] = {
                "input_tokens": 0,
                "output_tokens": 0,
                "cost": 0,
            }

        self.token_usage["models_usage"][model_name]["input_tokens"] += token_info.get(
            "input_tokens", 0
        )
        self.token_usage["models_usage"][model_name]["output_tokens"] += token_info.get(
            "output_tokens", 0
        )
        self.token_usage["models_usage"][model_name]["cost"] += token_info.get(
            "total_cost", 0
        )

    async def run(
        self, input_data: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        运行PPT评审Agent的六阶段工作流程

        Args:
            input_data: Dictionary containing:
                - file_path: PPT文件路径
                - language: 语言偏好 (zh/en)
                - review_depth: 评审深度 (basic/standard/comprehensive)
                - additional_context: 额外上下文信息

        Returns:
            A generator of chunks of the response
        """
        try:
            file_path = input_data.get("file_path", "")
            language = input_data.get("language", "zh")
            review_depth = input_data.get("review_depth", "standard")
            additional_context = input_data.get("additional_context", "")

            if not file_path or not os.path.exists(file_path):
                yield {
                    "agent": self.name,
                    "stage": "error",
                    "content_type": "error",
                    "content": "PPT文件路径无效或文件不存在",
                }
                return

            # 阶段1: 角色初始化
            async for result in self._stage_1_role_initialization(language):
                yield result

            # 阶段2: 材料接收和PPT内容提取
            async for result in self._stage_2_material_ingestion(file_path, language):
                yield result

            # 阶段3: 宏观结构分析
            async for result in self._stage_3_macro_structure_analysis(
                language, review_depth
            ):
                yield result

            # 阶段4: 逐页深度剖析
            async for result in self._stage_4_page_by_page_analysis(
                language, review_depth
            ):
                yield result

            # 阶段5: 综合评估与反馈生成
            async for result in self._stage_5_comprehensive_evaluation(language):
                yield result

            # 阶段6: 生成最终报告
            async for result in self._stage_6_generate_final_report(
                language, additional_context
            ):
                yield result

        except Exception as e:
            logger.error(f"PPT Review Agent error: {str(e)}")
            yield {
                "agent": self.name,
                "stage": "error",
                "content_type": "error",
                "content": f"PPT评审失败: {str(e)}",
            }

    async def _stage_1_role_initialization(
        self, language: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """阶段1: 角色初始化"""
        yield {
            "agent": self.name,
            "stage": "initialization",
            "content_type": "text",
            "content": """我是一位拥有超过15年经验的资深VC投资合伙人。我将基于专业的融资项目评审SOP，
            从投资人视角为您的PPT提供犀利但建设性的反馈。我的目标是帮助您发现逻辑漏洞、潜在风险和优化空间，
            提升融资成功率。请上传您的PPT文件，我将进行六个阶段的专业评审。""",
        }

    async def _stage_2_material_ingestion(
        self, file_path: str, language: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """阶段2: 材料接收和PPT内容提取"""
        yield {
            "agent": self.name,
            "stage": "material_ingestion",
            "content_type": "action",
            "action": "extract",
            "content": "正在分析PPT文件结构和内容...",
        }

        try:
            # 提取PPT内容
            ppt_content = await self._extract_ppt_content(file_path)
            self.review_session["ppt_content"] = ppt_content

            # 生成文件基本信息反馈
            file_info = {
                "file_name": os.path.basename(file_path),
                "total_slides": len(ppt_content["slides"]),
                "estimated_duration": len(ppt_content["slides"])
                * 1.5,  # 假设每页1.5分钟
            }

            yield {
                "agent": self.name,
                "stage": "material_ingestion",
                "content_type": "result",
                "content": {
                    "message": f"PPT文件分析完成。共{file_info['total_slides']}张幻灯片，预估路演时长{file_info['estimated_duration']:.1f}分钟。",
                    "file_info": file_info,
                    "extraction_summary": {
                        "total_slides": len(ppt_content["slides"]),
                        "has_title_slide": any(
                            slide.get("slide_type") == "title"
                            for slide in ppt_content["slides"]
                        ),
                        "content_slides": sum(
                            1
                            for slide in ppt_content["slides"]
                            if slide.get("slide_type") == "content"
                        ),
                    },
                },
            }

        except Exception as e:
            yield {
                "agent": self.name,
                "stage": "material_ingestion",
                "content_type": "error",
                "content": f"PPT文件解析失败: {str(e)}",
            }
            return

    async def _stage_3_macro_structure_analysis(
        self, language: str, review_depth: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """阶段3: 宏观结构分析"""
        yield {
            "agent": self.name,
            "stage": "macro_structure_analysis",
            "content_type": "action",
            "action": "analyze",
            "content": "正在分析PPT整体结构和故事线逻辑...",
        }

        ppt_content = self.review_session["ppt_content"]
        if not ppt_content:
            yield {
                "agent": self.name,
                "stage": "macro_structure_analysis",
                "content_type": "error",
                "content": "PPT内容未找到，无法进行结构分析",
            }
            return

        # 构建结构分析提示词
        slides_summary = self._create_slides_summary(ppt_content["slides"])
        prompt = STRUCTURE_ANALYSIS_PROMPT_ZH[language].format(
            ppt_content=slides_summary, current_date=get_current_date_str()
        )

        # 调用LLM进行结构分析
        response = await self._get_llm_response(
            self.structure_model, prompt, temperature=0.7, max_tokens=3000
        )

        # 解析分析结果
        try:
            # 尝试解析JSON结果或直接使用文本
            structure_analysis = {
                "storyline_compliance": response,
                "logical_flow": "待分析",
                "missing_sections": [],
                "overall_assessment": response,
            }

            self.review_session["analysis_results"]["structure"] = structure_analysis

            yield {
                "agent": self.name,
                "stage": "macro_structure_analysis",
                "content_type": "result",
                "content": {
                    "analysis": structure_analysis,
                    "summary": "宏观结构分析完成，发现了故事线和逻辑流的关键要点。",
                },
            }

        except Exception as e:
            logger.error(f"Structure analysis parsing error: {str(e)}")
            yield {
                "agent": self.name,
                "stage": "macro_structure_analysis",
                "content_type": "text",
                "content": response,
            }

    async def _stage_4_page_by_page_analysis(
        self, language: str, review_depth: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """阶段4: 逐页深度剖析"""
        yield {
            "agent": self.name,
            "stage": "page_by_page_analysis",
            "content_type": "action",
            "action": "analyze",
            "content": "正在进行逐页深度分析...",
        }

        ppt_content = self.review_session["ppt_content"]
        if not ppt_content or not isinstance(ppt_content, dict):
            yield {
                "agent": self.name,
                "stage": "page_by_page_analysis",
                "content_type": "error",
                "content": "PPT内容提取失败，无法进行逐页分析",
            }
            return
            
        slides = ppt_content.get("slides", [])
        if not slides or not isinstance(slides, list):
            yield {
                "agent": self.name,
                "stage": "page_by_page_analysis", 
                "content_type": "error",
                "content": "PPT幻灯片数据格式错误",
            }
            return
            
        page_analyses = []

        # 决定分析的幻灯片数量
        max_pages = {"basic": 5, "standard": 10, "comprehensive": len(slides)}.get(
            review_depth, 10
        )
        slides_to_analyze = slides[:max_pages]

        for i, slide in enumerate(slides_to_analyze):
            yield {
                "agent": self.name,
                "stage": "page_by_page_analysis",
                "content_type": "progress",
                "content": f"正在分析第{i+1}页: {slide.get('title', '无标题')}",
                "progress": {"current": i + 1, "total": len(slides_to_analyze)},
            }

            # 为每一页生成分析
            try:
                slide_analysis = await self._analyze_single_slide(
                    slide, i + 1, len(slides), language
                )
                page_analyses.append(slide_analysis)
            except Exception as e:
                logger.error(f"分析第{i+1}页时出错: {str(e)}")
                # 创建一个错误分析结果
                error_analysis = {
                    "slide_number": i + 1,
                    "title": slide.get("title", "无标题"),
                    "analysis": f"分析此页时出现错误: {str(e)}",
                    "strengths": [],
                    "weaknesses": [],
                    "questions": [],
                }
                page_analyses.append(error_analysis)

        self.review_session["analysis_results"]["pages"] = page_analyses

        yield {
            "agent": self.name,
            "stage": "page_by_page_analysis",
            "content_type": "result",
            "content": {
                "analyzed_pages": len(page_analyses),
                "summary": f"已完成{len(page_analyses)}页的详细分析，发现了多个需要优化的要点。",
            },
        }

    async def _stage_5_comprehensive_evaluation(
        self, language: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """阶段5: 综合评估与反馈生成"""
        yield {
            "agent": self.name,
            "stage": "comprehensive_evaluation",
            "content_type": "action",
            "action": "evaluate",
            "content": "正在生成综合评估和投资人问题清单...",
        }

        # 整合所有分析结果
        structure_analysis = self.review_session["analysis_results"].get(
            "structure", {}
        )
        page_analyses = self.review_session["analysis_results"].get("pages", [])

        # 构建综合评估提示词
        analysis_summary = self._create_analysis_summary(
            structure_analysis, page_analyses
        )
        prompt = COMPREHENSIVE_EVALUATION_PROMPT_ZH[language].format(
            analysis_summary=analysis_summary, current_date=get_current_date_str()
        )

        # 调用LLM进行综合评估
        response_text = await self._get_llm_response(
            self.content_model, prompt, temperature=0.6, max_tokens=1500
        )

        comprehensive_evaluation = self._parse_evaluation_response(response_text)

        self.review_session["analysis_results"][
            "comprehensive"
        ] = comprehensive_evaluation

        yield {
            "agent": self.name,
            "stage": "comprehensive_evaluation",
            "content_type": "result",
            "content": {
                "evaluation": comprehensive_evaluation,
                "summary": "综合评估完成，已生成投资人视角的核心反馈。",
            },
        }

    def _parse_evaluation_response(self, response_text: str) -> Dict[str, Any]:
        """尝试从LLM响应中解析出结构化的评估数据"""
        try:
            # 这是一个简化的解析器，实际应用中可能需要更强的健壮性
            # 例如，使用正则表达式提取JSON代码块
            import json
            
            # 去除Markdown代码块标记
            if "```json" in response_text:
                response_text = response_text.split("```json")[1].split("```")[0]

            data = json.loads(response_text)
            
            # 验证关键字段
            required_keys = ["overall_impression", "top_strengths", "top_concerns", "key_questions", "recommendations"]
            if not all(key in data for key in required_keys):
                logger.warning("解析的评估JSON缺少关键字段")
                return {"overall_impression": response_text} # 解析失败，返回原始文本
                
            return data
        except (json.JSONDecodeError, IndexError) as e:
            logger.error(f"解析综合评估响应失败: {e}. 响应: {response_text}")
            # 解析失败，将原始文本放入overall_impression
            return {"overall_impression": response_text}

    async def _stage_6_generate_final_report(
        self, language: str, additional_context: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """阶段6: 生成最终评审报告"""
        yield {
            "agent": self.name,
            "stage": "final_report",
            "content_type": "action",
            "action": "generate",
            "content": "正在生成最终评审报告...",
        }

        # 整合所有分析结果生成最终报告
        final_report = await self._generate_structured_report(
            language, additional_context
        )
        self.review_session["review_report"] = final_report

        # 保存报告到文件
        report_path = await self._save_report_to_file(final_report)

        yield {
            "agent": self.name,
            "stage": "final_report",
            "content_type": "result",
            "content": {
                "report": final_report,
                "report_path": str(report_path),
                "token_usage": self.token_usage,
                "session_id": self.conversation_id,
            },
        }

    async def _extract_ppt_content(self, file_path: str) -> Dict[str, Any]:
        """提取PPT文件内容"""
        try:
            prs = Presentation(file_path)
            slides_data = []

            for i, slide in enumerate(prs.slides):
                slide_data = {
                    "slide_number": i + 1,
                    "title": self._extract_slide_title(slide),
                    "content": self._extract_slide_content(slide),
                    "slide_type": self._identify_slide_type(slide, i),
                    "key_points": self._extract_key_points(slide),
                    "has_images": self._has_images(slide),
                    "has_charts": self._has_charts(slide),
                }
                slides_data.append(slide_data)

            return {
                "file_path": file_path,
                "total_slides": len(slides_data),
                "slides": slides_data,
                "extracted_at": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"PPT content extraction failed: {str(e)}")
            raise Exception(f"PPT文件解析失败: {str(e)}")

    def _extract_slide_title(self, slide) -> str:
        """提取幻灯片标题"""
        title = ""
        for shape in slide.shapes:
            if hasattr(shape, "text") and shape.text.strip():
                # 通常第一个文本框是标题
                if not title or len(shape.text.strip()) < len(title):
                    title = shape.text.strip()
        return title if title else "无标题"

    def _extract_slide_content(self, slide) -> str:
        """提取幻灯片内容"""
        content_parts = []
        for shape in slide.shapes:
            if hasattr(shape, "text") and shape.text.strip():
                content_parts.append(shape.text.strip())
        return "\n".join(content_parts)

    def _identify_slide_type(self, slide, slide_index: int) -> str:
        """识别幻灯片类型"""
        if slide_index == 0:
            return "title"

        # 简单的类型识别逻辑
        title = self._extract_slide_title(slide).lower()
        content = self._extract_slide_content(slide).lower()

        if any(keyword in title for keyword in ["目录", "agenda", "outline"]):
            return "agenda"
        elif any(keyword in title for keyword in ["谢谢", "thank", "questions"]):
            return "thanks"
        elif any(keyword in content for keyword in ["vs", "对比", "比较"]):
            return "comparison"
        elif self._has_charts(slide):
            return "data"
        else:
            return "content"

    def _extract_key_points(self, slide) -> List[str]:
        """提取关键要点"""
        key_points = []
        for shape in slide.shapes:
            if hasattr(shape, "text") and shape.text.strip():
                text = shape.text.strip()
                # 简单的要点提取：按行分割并过滤短文本
                lines = [
                    line.strip() for line in text.split("\n") if len(line.strip()) > 5
                ]
                key_points.extend(lines[:3])  # 最多提取3个要点
        return key_points[:5]  # 最多返回5个要点

    def _has_images(self, slide) -> bool:
        """检查是否包含图片"""
        for shape in slide.shapes:
            if hasattr(shape, "image"):
                return True
        return False

    def _has_charts(self, slide) -> bool:
        """检查是否包含图表"""
        for shape in slide.shapes:
            if hasattr(shape, "chart"):
                return True
        return False

    def _create_slides_summary(self, slides: List[Dict]) -> str:
        """创建幻灯片摘要"""
        summary_parts = []
        for slide in slides[:10]:  # 只摘要前10页
            summary_parts.append(
                f"第{slide['slide_number']}页: {slide['title']}\n"
                f"类型: {slide['slide_type']}\n"
                f"内容摘要: {slide['content'][:200]}...\n"
            )
        return "\n".join(summary_parts)

    async def _analyze_single_slide(
        self, slide: Dict, slide_number: int, total_slides: int, language: str
    ) -> Dict[str, Any]:
        """分析单个幻灯片"""
        prompt = PAGE_ANALYSIS_PROMPT_ZH[language].format(
            slide_number=slide_number,
            total_slides=total_slides,
            slide_title=slide.get("title", "无标题"),
            slide_content=slide.get("content", ""),
            slide_type=slide.get("slide_type", "content"),
            key_points="\n".join(slide.get("key_points", [])),
        )

        response_text = await self._get_llm_response(
            self.content_model, prompt, temperature=0.6, max_tokens=1500
        )

        return {
            "slide_number": slide_number,
            "title": slide.get("title"),
            "analysis": response_text,
            "strengths": [],  # 可以进一步解析
            "weaknesses": [],  # 可以进一步解析
            "questions": [],  # 可以进一步解析
        }

    def _create_analysis_summary(
        self, structure_analysis: Dict, page_analyses: List[Dict]
    ) -> str:
        """创建分析摘要"""
        summary_parts = [
            "## 结构分析摘要",
            structure_analysis.get("overall_assessment", ""),
            "\n## 页面分析摘要",
        ]

        for analysis in page_analyses[:5]:  # 最多包含5页的分析
            summary_parts.append(
                f"第{analysis['slide_number']}页 - {analysis['title']}: {analysis['analysis'][:300]}..."
            )

        return "\n".join(summary_parts)

    def _create_full_summary_for_report(self, analysis_results: Dict) -> str:
        """为最终报告生成一个更完整的文本摘要"""
        structure_summary = analysis_results.get("structure", {}).get("summary", "无结构分析。")
        
        page_summaries = []
        for page in analysis_results.get("pages", []):
            page_summaries.append(f"  - 第{page['slide_number']}页 ({page['title']}): {page['analysis'][:100]}...")
        pages_summary = "\n".join(page_summaries) if page_summaries else "无逐页分析。"

        comprehensive = analysis_results.get("comprehensive", {})
        comprehensive_summary = f"""
        总体印象: {comprehensive.get('overall_impression', 'N/A')}
        主要优点: {', '.join(comprehensive.get('top_strengths', []))}
        主要风险: {', '.join(comprehensive.get('top_concerns', []))}
        """

        return f"""
        ## 结构分析结论
        {structure_summary}

        ## 逐页分析摘要
        {pages_summary}

        ## 综合评估
        {comprehensive_summary}
        """

    async def _generate_structured_report(
        self, language: str, additional_context: str
    ) -> Dict[str, Any]:
        """生成结构化评审报告"""
        analysis_results = self.review_session["analysis_results"]
        comprehensive_eval = analysis_results.get("comprehensive", {})

        # 构建最终报告生成提示词
        all_analysis_summary = self._create_full_summary_for_report(analysis_results)

        prompt = FEEDBACK_GENERATION_PROMPT_ZH[language].format(
            all_analysis=all_analysis_summary,
            additional_context=additional_context,
            current_date=get_current_date_str(),
        )

        response_text = await self._get_llm_response(
            self.design_model, prompt, temperature=0.5, max_tokens=2000 # 增加token以生成完整报告
        )

        return {
            "session_id": self.conversation_id,
            "report_date": datetime.now().isoformat(),
            "overall_impression": comprehensive_eval.get("overall_impression", "评估中..."),
            "top_strengths": comprehensive_eval.get("top_strengths", []),
            "top_concerns": comprehensive_eval.get("top_concerns", []),
            "page_reviews": analysis_results.get("pages", []),
            "key_questions": comprehensive_eval.get("key_questions", []),
            "recommendations": comprehensive_eval.get("recommendations", {}),
            "final_report_text": response_text,
            "generated_at": datetime.now().isoformat(),
        }

    async def _save_report_to_file(self, report: Dict[str, Any]) -> Path:
        """保存评审报告到文件"""
        report_filename = f"ppt_review_{self.conversation_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_path = self.output_dir / report_filename

        import json

        with open(report_path, "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        return report_path

    async def handle_qa_question(self, question: str, context: str = "") -> str:
        """处理交互式问答"""
        review_report = self.review_session.get("review_report", {})

        prompt = QA_RESPONSE_PROMPT_ZH["zh"].format(
            question=question, context=context, review_summary=str(review_report)
        )

        response_text = await self._get_llm_response(
            self.content_model, prompt, temperature=0.6, max_tokens=1500
        )
        return response_text

    def get_token_usage(self) -> Dict[str, Any]:
        """获取Token使用统计"""
        return self.token_usage

    def get_review_session(self) -> Dict[str, Any]:
        """获取评审会话信息"""
        return self.review_session
