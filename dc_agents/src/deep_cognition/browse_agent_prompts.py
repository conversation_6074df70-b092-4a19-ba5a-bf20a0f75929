BROWSING_PROMPT_EN = """You are a research scientist, conducting in-depth research on a given problem and writing a research report. Previously, after analyzing and breaking down the problem, you decided to obtain relevant information through web searches. Below are the problem being researched and the query statement you used in web searches:

<research_question>
{research_question}
</research_question>

<query>
{query}
</query>

Below is your previous reasoning process of analyzing and breaking down the problem:
<reasoning>
{thinking}
</reasoning>

Below are new questions and requirements raised by users during the research process (may be empty at the beginning), and they are in order, with the latest questions and requirements at the end:
<new_questions>
{new_questions}
</new_questions>

Here is the information the user provided to clarify the scope of their research after your inquiry (which may initially be empty).  
<clarification_questions>  
{clarification_questions}  
</clarification_questions>

Below is the retrieved web page content:
<webpage>
{webpage}
</webpage>

- **Important**: Information that is related to the content in the thinking process, *especially the content related to cognition*
Now you need to confirm whether the current web page contains useful information that helps answer the research question. Useful information includes:
- Information that directly answers the research question
- Information that helps understand the relevant background
- Information related to certain nouns or concepts in the question
- Information not completely directly related to the question, but from which users can gain inspiration, thereby gaining a deeper understanding of the question
- Information that is related to the user's preferences in the previous round of interaction, please judge whether to obtain the web page content according to the user's preferences
<user_preferences>
{user_preferences}
</user_preferences>

Place your output in the following tags:
- Place "yes" in the <useful> tags if the webpage contains information that helps answer the research question. Otherwise, place "no" in the <useful> tags.
- Place the directly quoted useful information in the <useful_information> tags. Please keep the original wording and do not add your own interpretations or associations.
- In the <summary> tag, summarize the new information in one or two sentences and directly quote from the webpage. The summary should be strictly based on the webpage content without adding your own speculations or associations. If no new useful information is found on the current page, say "No new useful information found" in the <summary>.
  
"""

BROWSING_PROMPT_ZH = """你是一个研究科学家，正在对给定问题进行深入研究并要撰写一篇研究报告。你在之前已经通过对问题进行分析和拆解后，决定通过网络搜索来获取相关信息。以下是正在研究的问题和你在网络搜索中使用的查询语句：

<research_question>
{research_question}
</research_question>

<query>
{query}
</query>

以下是你之前对问题的分析与拆解过程：
<reasoning>
{thinking}
</reasoning>

以下是用户在研究过程中提出的新的问题和需求（刚开始可能为空），并且是按顺序，最新的问题和需求排在最后面：
<new_questions>
{new_questions}
</new_questions>

以下是用户在研究过程中补充的澄清问题（刚开始可能为空），并且是按顺序，最新的问题和需求排在最后面：
<clarification_questions>
{clarification_questions}
</clarification_questions>

以下是检索到的网页内容：
<webpage>
{webpage}
</webpage>

- **重要** : 和思考过程中的内容相关的信息，*尤其是其中提到的认知相关的内容*
现在你需要确认当前的网页是否包含有助于回答研究问题的有用信息。有用信息包括：
- 直接回答研究问题的信息
- 帮助理解相关背景的信息
- 针对问题的中的某个名词、概念的相关信息
- 和问题不完全直接相关，但是用户可以从中获得启发，从而对问题有更深入的理解
- 用户在上一轮的交互中有这样的偏好，请你根据用户偏好来判断是否需要获取该网页内容
<user_preferences>
{user_preferences}
</user_preferences>

将你的输出放在以下标签中：
- 在<useful>标签中放置"yes"如果网页包含有助于回答研究问题的信息。否则，在<useful>标签中放置"no"。
- 在<useful_information>标签中放置从网页中直接摘录的有用信息原文。请保持原文的表述，不要添加自己的理解和联想。
- 在<summary>标签中用一两句话总结网页中的新信息,并直接引用网页原文。总结时应严格基于网页内容,不要添加自己的推测和联想。如果当前页面没有新的有用信息，请在<summary>说"没有找到新的有用信息"。

"""

browsing_prompt = {
    "en": BROWSING_PROMPT_EN,
    "zh": BROWSING_PROMPT_ZH
}