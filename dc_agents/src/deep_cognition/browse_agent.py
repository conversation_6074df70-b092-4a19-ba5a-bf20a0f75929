from typing import Dict, Any, Optional, List, AsyncGenerator
import asyncio
from concurrent.futures import ThreadPoolExecutor
import time
from collections import defaultdict
import yaml
import os

from dc_agents.src.agents.agent import Agent
from dc_agents.src.services.llm_service import LLMService
from dc_agents.src.deep_cognition.browse_agent_prompts import browsing_prompt
from dc_agents.src.tools.browser import Browser
from dc_agents.src.utils import get_content_from_tag
from firecrawl import FirecrawlApp
from dc_agents.src.tools.web_search_agent import WebSearchAgent
from loguru import logger

import dc_agents.src.tools.text_split as text_split

from dc_agents.src.deep_cognition.url_filtering import filter_webpages

class BrowseAgent(Agent):
    """Browse agent that extracts relevant information from web pages."""

    def __init__(self, 
                 name: str = "Browser", 
                 config: Optional[Dict[str, Any]] = None):
        super().__init__(name=name, config=config)

        self.browsing_model = LLMService(config["browsing_model"])
        self.url_filtering_model = LLMService(config["url_filtering_model"])

        self.browser_config = config.get("browser", {})
        
        # 初始化token使用统计
        self.token_usage = {
            "total_input_tokens": 0,
            "total_output_tokens": 0,
            "total_cost": 0,
            "models_usage": {}  # 按模型名称记录使用情况
        }
            
    def _update_token_usage(self, model_name: str, token_info: Dict[str, Any]) -> None:
        """
        更新token使用统计
        
        Args:
            model_name: 模型名称
            token_info: token信息，包含input_tokens、output_tokens和costs
        """
        # 更新总使用量
        self.token_usage["total_input_tokens"] += token_info.get("input_tokens", 0)
        self.token_usage["total_output_tokens"] += token_info.get("output_tokens", 0)
        self.token_usage["total_cost"] += token_info.get("total_cost", 0)
        
        # 按模型记录
        if model_name not in self.token_usage["models_usage"]:
            self.token_usage["models_usage"][model_name] = {
                "input_tokens": 0,
                "output_tokens": 0,
                "cost": 0
            }
            
        # 更新模型特定使用量
        self.token_usage["models_usage"][model_name]["input_tokens"] += token_info.get("input_tokens", 0)
        self.token_usage["models_usage"][model_name]["output_tokens"] += token_info.get("output_tokens", 0) 
        self.token_usage["models_usage"][model_name]["cost"] += token_info.get("total_cost", 0)
        
        # logger.info(f"浏览代理更新token使用: 模型={model_name}, 输入={token_info.get('input_tokens', 0)}, " +
                #    f"输出={token_info.get('output_tokens', 0)}, 成本={token_info.get('total_cost', 0):.4f}")
            
    async def scrape_and_browse(self, webpage, language, question):
        browser = Browser(
            firecrawl_api_url=self.browser_config["firecrawl_api_url"],
            page_size=self.browser_config["page_size"],
            max_page_number=self.browser_config["max_page_number"]
        )
        logger.info(f"Scraping webpage {webpage['url']}")
        scrape_status = browser.scrape_url(webpage['url'])
        
        if not scrape_status['is_success'] or scrape_status['is_empty']:
            yield {
                "agent": self.name,
                "stage": "browsing",
                "content_type": "observation",
                "content": {
                    "webpage": webpage,
                    "is_useful": False,
                    "page_content": "",
                    "summary": "",
                    "is_scrape_success": False,
                    "is_webpage_empty": scrape_status['is_empty']
                }
            }
        else:
            logger.info(f"Scraped webpage {webpage['url']} successfully")
            
            while browser.current_page_index < len(browser.pages):
                page_content = browser.get_page_content()
                if page_content is None:
                    return
                
                # read current page
                prompt = browsing_prompt[language].format(webpage=page_content, research_question=question, query=webpage['query'])
                response = await self.browsing_model.get_completion(
                    messages=[
                        {"role": "system", "content": prompt},
                    ]
                )
                try:
                    content = ""
                    async for chunk in response:
                        # 处理token信息
                        if hasattr(chunk, "is_token_info") and chunk.get("is_token_info"):
                            token_info = chunk.get("token_info", {})
                            # 更新token使用统计
                            self._update_token_usage("browsing_model", token_info)
                            continue
                            
                        if hasattr(chunk.choices[0].delta, "content") and chunk.choices[0].delta.content is not None:
                            content += chunk.choices[0].delta.content
                
                    is_useful = get_content_from_tag(content, "useful").strip().lower() == "yes"
                    summary = get_content_from_tag(content, "summary")
                    logger.info(f"Query: {webpage['query']}\n Title: {webpage['title']}\n Useful: {is_useful}\n Summary: {summary}")

                    yield {
                        "agent": self.name,
                        "stage": "browsing",
                        "content_type": "observation",
                        "content": {
                            "webpage": webpage,
                            "is_useful": is_useful,
                            "page_content": page_content,
                            "summary": summary, 
                            "is_scrape_success": True,
                            "is_webpage_empty": False
                        }
                    }

                    if browser.page_down():
                        continue
                    else:
                        logger.info(f"Browsing {webpage['url']} finished")
                        return

                except Exception as e:
                    logger.error(f"Error getting useful from response: {e}")
                    if browser.page_down():
                        continue
                    else:
                        return


    async def process_and_enqueue(self, webpage, language, question, queue, semaphore):
        """使用信号量处理数据块并将结果放入队列"""
        async with semaphore:
            async for result in self.scrape_and_browse(webpage, language, question):
                await queue.put(result)
                # 让出控制权给事件循环，确保其他任务能够执行
                await asyncio.sleep(0.1)


    async def browse_webpages_with_concurrency(self, webpages, language, question):
        result_queue = asyncio.Queue()
    
        # 创建信号量限制并发数
        semaphore = asyncio.Semaphore(20)
        
        # 创建一个事件，用于标记所有处理任务完成
        done_event = asyncio.Event()
        
        # 启动所有数据处理任务
        processing_tasks = []
        for i, webpage in enumerate(webpages):
            task = asyncio.create_task(self.process_and_enqueue(webpage, language, question, result_queue, semaphore))
            processing_tasks.append(task)

         # 创建一个任务来等待所有处理完成并设置事件
        async def wait_for_completion():
            await asyncio.gather(*processing_tasks)
            done_event.set()

        # 启动等待完成任务，但不等待它
        asyncio.create_task(wait_for_completion())
        
        # 持续从队列中获取结果，直到所有处理任务完成且队列为空
        while True:
            if result_queue.empty() and done_event.is_set():
                # 队列为空且处理完成，退出循环
                break
                
            try:
                # 使用短超时以便可以检查done_event
                item = await asyncio.wait_for(result_queue.get(), 0.1)
                yield item
                result_queue.task_done()
            except asyncio.TimeoutError:
                # 如果超时，继续循环
                continue


    # async def run(self, input_data: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
    #     """Process URLs and extract relevant information."""
    #     question = input_data['question']
    #     webpages = input_data['webpages']
    #     language = input_data['language']

    #     logger.info(f"\n ++++++++ Browsing webpages: {len(webpages)} ++++++++ \n")

    #     async for result in self.browse_webpages_with_concurrency(webpages, language, question):
    #         yield result


    async def run(self, input_data: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Process URLs and extract relevant information."""
        question = input_data['question']
        webpages = input_data['webpages']
        
        # Filter webpages
        # logger.info(f"webpages before: {[w['url'] for w in webpages[:20]]}...")
        # webpages = await filter_webpages(webpages, self.url_filtering_model)
        # logger.info(f"webpages after: {[w['url'] for w in webpages[:20]]}...")
        
        language = input_data['language']
        article = input_data['article']
        new_questions = input_data.get('new_questions', '')
        clarification_questions = input_data.get('clarification_questions', '')
        search_queries = input_data.get('search_queries', [])
        thinking = input_data.get('thinking', '')
        user_preferences = input_data.get('user_preferences', '暂无用户偏好信息')

        logger.info(f"\n ++++++++ Browsing webpages: {len(webpages)} ++++++++ \n")
        logger.info(f"用户偏好信息: {user_preferences[:200]}..." if len(user_preferences) > 200 else f"用户偏好信息: {user_preferences}")

        # 重置token使用统计
        self.token_usage = {
            "total_input_tokens": 0,
            "total_output_tokens": 0,
            "total_cost": 0,
            "models_usage": {}
        }

        url_to_web = {}
        detailed_web_content = {}
        web_search_agent = WebSearchAgent()
        page_contents = await web_search_agent.scrape_urls([w["url"] for w in webpages])

        for webpage, page_content in zip(webpages, page_contents):
            if not page_content or len(page_content) <= 10:
                logger.warning(f"网页 {webpage['url']} 没有有效的markdown内容，跳过")
                continue
            
            # 不进行真正的scrape_and_browse调用，只保存raw content
            url = webpage['url']
            url_to_web[url] = {
                "webpage": webpage,
                "markdown": page_content,
                "title": webpage.get("title", ""),
                "url": url
            }
            
            # 为每个网页保存详细信息用于偏好记录
            detailed_web_content[url] = {
                "raw_markdown": page_content,
                "title": webpage.get("title", ""),
                "url": url,
                "original_webpage": webpage,
                "page_content_chunks": []  # 初始化，稍后填充
            }
        scrape_result = {}
        for url, webpage in url_to_web.items():
            success = False
            if "markdown" in webpage and len(webpage["markdown"]) > 1:
                success = True
            scrape_result[url] = success
        yield {
            "agent": self.name,
            "stage": "scrape_result",
            "content_type": "observation",
            "content": scrape_result
        }


        url_to_webpage = {w['url']: w for w in webpages}

        page_contents = []
        messages = []
        urls = []
        for url, webpage in url_to_web.items():
            if "markdown" not in webpage or len(webpage["markdown"]) <= 1:
                continue
            words = webpage["markdown"].split(" ")
            page_size = 4000
            # chunks = text_split.split_and_chunk_text_by_tokens(webpage["markdown"], chunk_size=page_size)
            # for i, chunk in enumerate(chunks):
            #     page_content_chunk = chunk.strip()
            for i in range(0, len(words), page_size):
                page_content_chunk = ' '.join(words[i:i+page_size])

                # 保存page_content_chunk到详细内容中
                if url not in detailed_web_content:
                    detailed_web_content[url] = {}
                if "page_content_chunks" not in detailed_web_content[url]:
                    detailed_web_content[url]["page_content_chunks"] = []
                detailed_web_content[url]["page_content_chunks"].append(page_content_chunk)

                # 添加行号
                # chunk_lines = [f"{i}: {line}" for i, line in enumerate(chunk.split("\n"))]
                # chunk = "\n".join(chunk_lines)
                
                messages.append([{
                    "role": "user", 
                    "content": browsing_prompt[language].format(
                        webpage=page_content_chunk, 
                        research_question=question, 
                        query=url_to_webpage[url]['query'],
                        article=article,
                        new_questions=new_questions,
                        clarification_questions=clarification_questions,
                        thinking=thinking,
                        user_preferences=user_preferences
                    ) # todo
                }])
                # logger.info(f"message: {messages[-1]}")
                urls.append(url)
                page_contents.append(page_content_chunk)
                if i >= 5:
                    break


        logger.info(f"number of messages: {len(messages)}")

        # 替换同步的batch_completion为真正的异步实现
        async def get_single_completion(message):
            try:
                # 使用LLMService的get_completion_sync函数获取非流式响应和准确的token计数
                result = await self.browsing_model.get_completion_sync(
                    messages=message,
                    temperature=self.browsing_model.config["temperature"],
                    max_tokens=self.browsing_model.config["max_tokens"]
                )
                
                if result is None:
                    return None
                
                # 从结果中提取响应和token信息
                response = result["response"]
                token_info = result["token_info"]
                
                # 更新token使用统计
                self._update_token_usage("browsing_model", token_info)
                
                return response
                
            except Exception as e:
                logger.error(f"单个请求出错: {str(e)}")
                # 返回空响应对象以保持索引对齐
                return None

        # 分批处理请求，避免一次性发送过多请求
        batch_size = 24  # 每批处理的请求数量
        responses = []
        
        # 使用信号量限制并发请求数量
        semaphore = asyncio.Semaphore(10)  # 最大并发请求数
        
        async def process_with_semaphore(message):
            async with semaphore:
                return await get_single_completion(message)
        
        # 按批次处理
        for i in range(0, len(messages), batch_size):
            batch_messages = messages[i:i+batch_size]
            logger.info(f"处理批次 {i//batch_size + 1}/{(len(messages)+batch_size-1)//batch_size}，包含 {len(batch_messages)} 个请求")
            
            # 发送进度汇报
            total_batches = (len(messages)+batch_size-1)//batch_size
            current_batch = i//batch_size + 1
            progress_percentage = int((current_batch / total_batches) * 100)
            
            # 向客户端汇报进度
            yield {
                "agent": self.name,
                "stage": "browse_progress",
                "content_type": "progress",
                "content": {
                    "current": current_batch,
                    "total": total_batches,
                    "percentage": progress_percentage,
                    "message": f"处理网页内容 {progress_percentage}% ({current_batch}/{total_batches})"
                }
            }
            
            # 创建当前批次的任务
            batch_tasks = [process_with_semaphore(message) for message in batch_messages]
            # 并发执行当前批次的所有请求
            batch_responses = await asyncio.gather(*batch_tasks)
            responses.extend(batch_responses)
            
            # 在批次之间添加短暂延迟，避免API限流
            if i + batch_size < len(messages):
                await asyncio.sleep(0.5)
        
        logger.info(f"number of responses: {len(responses)}")

        url_to_useful_contents = defaultdict(list)

        for url, page_content, response in zip(urls, page_contents, responses):
            try:
                if response is None:
                    # 处理请求失败的情况
                    logger.warning(f"URL {url} 的请求失败")
                    url_to_useful_contents[url].append(None)
                    continue
                    
                content = response.choices[0].message.content
                is_useful = get_content_from_tag(content, "useful").strip().lower() == "yes"
                # 模型提取行号，而不是 summary 
                useful_information = get_content_from_tag(content, "useful_information")
                summary = get_content_from_tag(content, "summary")
                # useful_information_intervals = get_content_from_tag(content, "useful_information_intervals")

                # page_content_lines = page_content.split('\n')
                # logger.info(f"Query: {url_to_webpage[url]['query']}\n Title: {url_to_webpage[url]['title']}\n Total Lines: {len(page_content_lines)}\n Useful: {is_useful}\n Useful Information Intervals: {useful_information_intervals}")
                # summary = get_content_from_tag(content, "summary")
                # logger.info(f"Query: {url_to_webpage[url]['query']}\n Title: {url_to_webpage[url]['title']}\n Useful: {is_useful}\n Summary: {summary}")
                # if is_useful:
                #     useful_information_intervals_int = []
                #     for interval in useful_information_intervals.split('],['):
                #         start, end = interval.strip('][').split(',')
                #         useful_information_intervals_int.append((int(start), int(end)))
                #     useful_information = []
                #     for start, end in useful_information_intervals_int:
                #         line_list = []
                #         for i in range(start, end + 1):
                #             # 删除开头行号
                #             line = page_content_lines[i].split(": ", 1)[-1].strip()
                #             # 删除没有内容的条目
                #             if line:
                #                 line_list.append(line)
                #         useful_information.append('\n'.join(line_list))
                #     print(f"Useful Information: {useful_information}") # Print for debugging
                if is_useful:
                    url_to_useful_contents[url].append({
                        "summary": summary,
                        # "page_content": page_content,
                        "useful_information": useful_information
                    })
                else:
                    url_to_useful_contents[url].append(None)
            except Exception as e:
                logger.error(f"Error getting useful from response: {e}")
                # 打印更详细的错误信息
                if hasattr(e, '__dict__'):
                    logger.error(f"APIError details: {e.__dict__}")
                elif hasattr(response, '__dict__'):
                    logger.error(f"Response details: {response.__dict__}")
                logger.error(f"Response type: {type(response)}")
                url_to_useful_contents[url].append(None)
        
        ret_content = {}
        for url, useful_contents in url_to_useful_contents.items():
            if any(useful_contents):
                item = {
                    "title": url_to_webpage[url]["title"],
                    "query": url_to_webpage[url]["query"],
                    "summaries": [c["summary"] for c in useful_contents if c is not None],
                    # "page_contents": [c["page_content"] for c in useful_contents if c is not None]
                    "useful_information": [c["useful_information"] for c in useful_contents if c is not None],
                    # 新增：包含完整的页面内容块用于偏好记录
                    "page_content_chunks": detailed_web_content.get(url, {}).get("page_content_chunks", []),
                    "raw_markdown": detailed_web_content.get(url, {}).get("raw_markdown", ""),
                    "original_webpage": detailed_web_content.get(url, {}).get("original_webpage", {})
                }
                ret_content[url] = item
        
        yield {
            "agent": self.name,
            "stage": "browse_result",
            "content_type": "observation",
            "content": ret_content
        }
        
        # 在最后返回token使用信息
        yield {
            "agent": self.name,
            "stage": "token_usage",
            "content_type": "info",
            "action": "token_info",
            "token_usage": self.token_usage
        }
