import base64
import logging
import asyncio
import json
import os
import re
import httpx
import time
from typing import Dict, List, Optional, Any, AsyncGenerator, Tuple, Union

from datetime import datetime, timezone, timedelta
from fastapi import HTTPException
from fast_langdetect import detect
from loguru import logger

from dc_agents.src.agents.agent import Agent
from dc_agents.src.agents.context import BaseContext
from dc_agents.src.agents.note_context import NoteContext
from dc_agents.src.agents.research_memory import ResearchMemory
from dc_agents.src.services.llm_service import LLMService
from dc_agents.src.services.search_service import SearchService
from dc_agents.src.deep_cognition.browse_agent import BrowseAgent
from dc_agents.src.utils import get_current_date_str, get_cn_time
from dc_agents.src.utils import get_content_from_tag
from dc_agents.src.agents.file_context import FileContext
from dc_agents.src.services.rag_service import CognitionRAGService
from dc_agents.src.deep_cognition.preference_summary_agent import PreferenceSummaryAgent

# 添加认知数据库导入
import sys
sys.path.append('../../../backend')
try:
    from backend.cognition.database import CognitionFavoritesDatabase
    logger.info("成功导入CognitionFavoritesDatabase")
except ImportError as e:
    logger.error(f"导入CognitionFavoritesDatabase失败: {e}")
    # 备用导入方式
    try:
        sys.path.append('/home/<USER>/DAIR/backend')
        from cognition.database import CognitionFavoritesDatabase
        logger.info("使用备用路径成功导入CognitionFavoritesDatabase")
    except ImportError as e2:
        logger.error(f"备用路径导入也失败: {e2}")
        CognitionFavoritesDatabase = None

from .research_prompts import (
    report_editing_with_reference_prompt,
    user_preference_prompt,
    system_prompt,
    reasoning_prompt,
    action_prompt,
    cognition_query_prompt
)
from dc_agents.src.deep_cognition.special_insturction import search_edit_special_instruction, update_report_special_instruction

# 检查是否启用调试模式
DEBUG_MODE = os.environ.get('DAIR_DEBUG_MODE', 'false').lower() == 'true'
logger.info(f"DAIR 调试模式: {'启用' if DEBUG_MODE else '禁用'}")

class ResearchAgent(Agent):
    """
    Research agent that analyzes questions, generates search queries,
    and determines which URLs to browse.
    """
    
    def __init__(self, 
                 name: str = "Researcher", 
                 conversation_id: str = None,
                 config: Optional[Dict[str, Any]] = None,
                 search_service: Optional[SearchService] = None,
                 browse_agent: Optional[BrowseAgent] = None,
                 ):
        """
        Initialize the Research agent.
        
        Args:
            name: The name of the agent
            config: Optional configuration dictionary for the agent
            llm_services: Dictionary of LLM services
            search_service: Search service for web searches
            browse_agent: Browse agent for browsing webpages
            conversation_id: Optional conversation ID for MongoDB association
        """
        super().__init__(name=name, config=config)

        # 初始化模型服务
        self.action_selection_model = LLMService(self.config['action_selection_model'])

        self.url_selection_model = LLMService(self.config['url_selection_model'])

        self.report_editing_model = LLMService(self.config['report_editing_model'])

        # 初始化服务
        self.search_service = search_service
        self.browse_agent = browse_agent

        self.cognition_rag_service = CognitionRAGService()
        self.cognition_search_model = LLMService(self.config['cognition_search_model'])
        self.cognition_content = ""
        
        # 编辑指令存储
        self.current_edit_instruction = ""
        
        # 创建内存管理器 - 优先使用ResearchMemory研究内存管理器
        try:
            self.memory = ResearchMemory(agent_name=name, conversation_id=conversation_id)
            logger.info(f"使用ResearchMemory研究内存管理器")
        except Exception as e:
            logger.warning(f"ResearchMemory研究内存管理器初始化失败: {str(e)}")
            self.memory = ResearchMemory(agent_name=name, conversation_id=conversation_id)
        
        # 初始化基本状态变量
        self.question = None
        self.report_draft = ""
        self.draft_history = []
        self.draft_version = 0
        self.is_updating_report = False
        self.language = "zh"
        self.round = 0
        self.is_paused = False
        self.user_feedbacks = []
        self.clarification_questions = []
        self.pending_clarification_questions = []
        self.research_state = {}
        # self.research_rules = []
        self.last_browse_result = None
        self.memory.test_run_count = 0
        
        # 配置内存中的状态变量
        self.memory.max_search_times = config.get('max_search_times', 5)

        # 消息队列
        self.page_info_queue = asyncio.Queue()
        # 如果提供了conversation_id，尝试从MongoDB加载会话
        if conversation_id and hasattr(self.memory, "load_from_mongodb"):
            # 设置conversation_id
            self.memory.conversation_id = conversation_id
            logger.info(f"初始化时设置conversation_id: {conversation_id}")
            
            # 创建事件循环处理异步加载
            try:
                loop = asyncio.get_event_loop()
                # 尝试异步加载会话
                if loop.is_running():
                    # 如果已经在事件循环中，创建一个任务但不立即等待
                    # 标记需要后续加载
                    self._needs_loading = True
                    logger.info(f"标记会话需要异步加载: {conversation_id}")
                else:
                    # 如果不在事件循环中，可以直接运行
                    loaded = loop.run_until_complete(self.memory.load_from_mongodb(conversation_id))
                    if loaded:
                        logger.info(f"初始化时成功加载会话: {conversation_id}")
                        self._sync_memory_to_agent()
                        self._needs_loading = False
                    else:
                        logger.warning(f"初始化时未找到会话: {conversation_id}")
                        self._needs_loading = True
            except Exception as e:
                logger.error(f"初始化时加载会话失败: {str(e)}")
                self._needs_loading = True
        else:
            self._needs_loading = False

    async def _load_session(self, conversation_id: str) -> bool:
        """
        从MongoDB加载会话并同步状态到agent
        
        Args:
            conversation_id: 会话ID
            
        Returns:
            加载是否成功
        """
        try:
            loaded = await self.memory.load_from_mongodb(conversation_id)
            if loaded:
                logger.info(f"异步加载会话成功: {conversation_id}")
                self._sync_memory_to_agent()
                self._needs_loading = False  # 标记加载完成
                return True
            else:
                logger.warning(f"异步加载会话失败:  {conversation_id}")
                self._needs_loading = False  # 即使失败也标记为不需要加载
                return False
        except Exception as e:
            logger.error(f"异步加载会话出错: {str(e)}")
            self._needs_loading = False  # 出错时也标记为不需要加载
            return False
    
    def _sync_memory_to_agent(self):
        """同步内存中的状态到类属性"""
        # 基本信息
        logger.error(f"同步内存中的状态到类属性!!!!!!!!!!!!!!!!!!")
        self.question = self.memory.research_question
        self.language = self.memory.language
        self.round = self.memory.round
        self.is_paused = self.memory.is_paused
        
        # 报告相关状态
        self.report_draft = self.memory.report_draft
        self.draft_version = self.memory.draft_version
        self.is_updating_report = self.memory.is_updating_report
        
        # 状态变量
        self.search_times = self.memory.search_times
        
        # 历史记录和轨迹
        self.memory_records = self.memory.memory_records if hasattr(self.memory, "memory_records") else []
        
        # 澄清问题相关
        self.clarification_questions = self.memory.clarification_questions
        self.pending_clarification_questions = self.memory.pending_clarification_questions
        
        # 研究规则
        # self.research_rules = self.memory.research_rules
        
        # 浏览结果
        self.last_browse_result = self.memory.get_last_browse_result()
        
        # 研究轨迹 - 确保充分复制所有相关内容
        if hasattr(self.memory, "research_trajectory"):
            self.research_trajectory = self.memory.research_trajectory
            
            # 恢复用户反馈
            if "user_feedbacks" in self.memory.research_trajectory:
                self.user_feedbacks = [fb.get("content", "") for fb in self.memory.research_trajectory["user_feedbacks"]]
            
        # 研究状态
        self._update_research_state()
        
        logger.info(f"已同步会话状态，问题: {self.question[:50] if self.question else 'None'}...")
        logger.info(f"当前轮次: {self.round}, 搜索次数: {self.search_times if hasattr(self, 'search_times') else 'N/A'}")
        logger.info(f"研究轨迹: {len(self.memory.research_trajectory['rounds']) if hasattr(self.memory, 'research_trajectory') else 0}轮, " 
                    f"{len(self.memory.research_trajectory['search_queries']) if hasattr(self.memory, 'research_trajectory') else 0}次搜索查询")

    async def run(self, input_data: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Run the research agent on the input data.
        
        Args:
            input_data: Dictionary containing:
                - question: The research question
                - browse_results: Optional results from the browse agent
                - stage: Current research stage
                - user_id: Optional user ID for MongoDB storage
                - edit_request: Boolean flag indicating this is an edit request
                - edit_content: Content for editing (if edit_request is True)
                
        Returns:
            Dictionary containing the research agent's output
        """
        # 记录用户ID（如果有）
        user_id = input_data.get("user_id", None)
        
        # 检查是否是编辑请求
        if input_data.get("edit_request", False):
            logger.info("检测到编辑请求，进入编辑模式")
            await self.resume()
            self.memory.increment_round()
            # 确保已设置编辑指令
            edit_content = input_data.get("edit_content", "")
            if edit_content:
                self.set_edit_instruction(edit_content)
            original_enable_search = self.get_enable_search()
            original_edit_mode = self.get_edit_mode()
            # 确保编辑模式已开启
            self.set_edit_mode(True)
            self.set_enable_search(False)
            
            # 直接调用报告更新
            async for chunk in self._update_report_with_prompt(''):
                yield chunk
            
            # 编辑完成后自动pause
            await self.pause()
            self.set_edit_mode(original_edit_mode)
            self.set_enable_search(original_enable_search)
            yield {
                "agent": self.name,
                "stage": "select_action",
                "round": self.memory.round,
                "content_type": "action",
                "action": "think",
                "content": "✅ 已按照编辑请求完成编辑"
            }
            # 发送paused状态消息
            yield {
                "agent": self.name,
                "stage": "paused",
                "content_type": "action",
                "action": "paused",
                "content": "编辑请求处理完成，研究已暂停"
            }
            
            # 保存状态
            if hasattr(self.memory, "save_to_mongodb"):
                metadata = {"user_id": user_id} if user_id else {}
                await self.memory.save(metadata)
            
            return  # 编辑请求处理完成，直接返回
        
        # 检查是否有conversation_id但没有问题，可能是初始化时加载失败的情况
        conversation_id = input_data.get("conversation_id", None)
        if conversation_id and not self.question and hasattr(self.memory, "load_from_mongodb"):
            try:
                # 再次尝试加载会话
                loaded = await self.memory.load_from_mongodb(conversation_id)
                if loaded:
                    logger.info(f"run方法中成功加载会话: {conversation_id}")
                    # 同步内存中的状态到类属性
                    self._sync_memory_to_agent()
                    
                    # 发送加载成功的消息
                    yield {
                        "type": "info",
                        "stage": "init",
                        "action": "load",
                        "content": f"已加载会话 {conversation_id}"
                    }
                else:
                    logger.warning(f"run方法中未找到会话: {conversation_id}")
            except Exception as e:
                logger.error(f"run方法中加载会话失败: {str(e)}")
        
        # 如果没有加载现有会话或加载失败，则初始化新会话
        if not self.question:
            # 获取输入问题
            self.question = input_data.get('question', None)
            if not self.question:
                logger.error("No question provided in input data")
                yield {
                    "type": "error",
                    "stage": "init",
                    "action": "error",
                    "content": "Missing required question in input data"
                }
                return

            # 检测语言并设置语言参数
            try:
                # 检测用户输入的问题语言
                detected_lang = detect(self.question.replace("\n", " "))['lang']
                # 如果不是中文，设置为英文，否则设置为中文
                if detected_lang != "en":
                    self.language = "zh"
                else:
                    self.language = "en"
                logger.info(f"自动检测到语言: {self.language}")
                logger.info(f"问题: '{self.question[:50]}...' 被检测为语言: {detected_lang}, 使用模板语言: {self.language}")
            except Exception as e:
                # 检测失败时使用传入的语言参数，如果没有则默认英文
                self.language = input_data.get('language', 'en')
                logger.warning(f"语言检测失败，使用传入的语言参数: {self.language}, 错误: {str(e)}")
            
            # 设置语言到内存
            self.memory.set_language(self.language)
            logger.info(f"最终设置语言为: {self.language}, 将用于选择提示词模板和生成回应")

            # 设置研究问题
            self.memory.set_research_question(self.question)
            logger.info(f"enable_cognition_search: {self.get_enable_cognition_search()}")
            if self.get_enable_cognition_search():
                # 搜索相关认知
                logger.info(f"开始执行cognition_search，用户ID: {user_id}")
                try:
                    async for cognition_result in self.cognition_search(user_id):
                        # 记录认知搜索的统计信息
                        if cognition_result:
                            search_count = cognition_result.get('search_results_count', 0)
                            favorited_count = cognition_result.get('favorited_count', 0)
                            total_count = cognition_result.get('total_cognitions', 0)
                            logger.info(f"认知搜索完成 - 搜索结果: {search_count}, 收藏: {favorited_count}, 总计: {total_count}")
                except Exception as e:
                    import traceback
                    traceback.print_exc()
                    logger.error(f"[COGNITION DEBUG] 执行cognition_search时发生错误: {str(e)}")
            else:
                logger.info(f"[COGNITION DEBUG] 认知搜索已禁用，跳过cognition_search")
                self.cognition_content = ""  # 确保为空
        
        # 保存研究内存记录，添加用户关联
        if hasattr(self.memory, "save_to_mongodb"):
            metadata = {"user_id": user_id} if user_id else {}
            await self.memory.save(metadata)
            
            # 输出会话ID信息
            yield {
                "type": "info",
                "stage": "init",
                "action": "save",
                "content": f"会话ID: {self.memory.conversation_id}"
            }

        # 兼容处理：确保澄清问题列表中的每一项都是字典格式
        if self.memory.clarification_questions:
            for i, item in enumerate(self.memory.clarification_questions[:]):
                if isinstance(item, str):
                    # 如果是旧格式的字符串，转换为新的问答对格式
                    logger.info(f"将旧格式的澄清问题回答转换为问答对: {item[:50]}...")
                    self.memory.clarification_questions[i] = {
                        "question": "未记录的问题",
                        "answer": item
                    }
                    # 同时添加到内存中
                    self.memory.add_clarification_qa("未记录的问题", item)

        is_finished = False
        
        # 如果有澄清问题，记录一下
        if self.memory.clarification_questions:
            logger.info(f"当前有 {len(self.memory.clarification_questions)} 个澄清问题回答:")
            for i, q in enumerate(self.memory.clarification_questions):
                logger.info(f"  澄清问题回答 {i+1}: {q['question'][:100]}...")
            
        # 如果当前是暂停状态，但现在恢复了，则重置暂停状态
        if input_data.get('resume', False) and self.memory.is_paused:
            # 在恢复前检查是否还有未回答的澄清问题
            pending_questions = self.memory.get_pending_clarification_questions()
            if pending_questions:
                logger.warning(f"尝试恢复研究，但还有{len(pending_questions)}个未回答的澄清问题")
                yield {
                    "type": "error",
                    "stage": "resume",
                    "action": "error",
                    "content": f"无法恢复研究，还有{len(pending_questions)}个未回答的澄清问题，请先回答这些问题"
                }
                return
                
            logger.info("检测到恢复信号，重置暂停状态")
            self.memory.set_paused(False)
            yield {
                "type": "info",
                "stage": "resume",
                "action": "resume",
                "content": "研究已恢复"
            }
        # 重置搜索次数
        self.memory.reset_search_times()

        # 只对每一次单独的 run 进行限制最大搜索次数
        while self.memory.can_search_more():
            # 检查是否需要暂停
            if self.memory.is_paused:
                # 如果处于暂停状态，保存当前状态并中断执行
                logger.info("检测到暂停状态，保存工作并中断执行")
                # 确保保存当前状态
                await self.memory.save()
                
                # 通知前端已暂停
                yield {
                    "agent": self.name,
                    "stage": "paused",
                    "content_type": "action",
                    "action": "paused",
                    "content": "研究已暂停"
                }
                # 直接中断循环，不继续执行
                break
            
            logger.info(f"Round {self.memory.round} start, search times: {self.memory.search_times}")
            
            try:
                preference_agent = PreferenceSummaryAgent(config=self.config)
                if preference_agent.should_generate_new_preference(self.memory):
                    logger.info("检测到足够的用户点赞内容，开始进行偏好总结")
                    
                    try:
                        # 生成偏好总结
                        preference_input = {
                            'memory': self.memory,
                            'language': self.memory.language or 'zh'
                        }
                        
                        # 异步执行偏好总结
                        async for result in preference_agent.run(preference_input):
                            if result.get("type") == "preference_generated":
                                # 通知前端生成了新的偏好总结
                                yield {
                                    "agent": self.name,
                                    "stage": "preference_summary",
                                    "round": self.memory.round,
                                    "content_type": "info",
                                    "action": "preference_updated",
                                    "content": f"基于{result.get('based_on_likes', 0)}条点赞记录更新了用户偏好"
                                }
                            elif result.get("type") == "error":
                                logger.error(f"偏好总结失败: {result.get('content')}")
                                
                        # 保存更新后的偏好到MongoDB
                        await self.memory.save({"is_critical": True})
                        logger.info("偏好总结完成并已保存")
                        
                    except Exception as e:
                        logger.error(f"执行偏好总结时出错: {str(e)}")
                else:
                    logger.info("用户点赞内容不足，跳过偏好总结")
                
                # todo
                async for chunk in self.select_action():
                    # 每次返回数据前检查暂停状态
                    if self.memory.is_paused:
                        # 如果处于暂停状态，保存当前状态并中断执行
                        logger.info("select_action期间检测到暂停状态，保存工作并中断执行")
                        await self.memory.save()
                        # 通知前端已暂停
                        yield {
                            "agent": self.name,
                            "stage": "paused",
                            "content_type": "action",
                            "action": "paused",
                            "content": "研究已暂停"
                        }
                        # 直接退出生成器函数
                        break
                    
                    if "return" in chunk:
                        is_finished = True
                        break
                    else:
                        yield chunk
            except Exception as e:
                import traceback
                traceback.print_exc()
                logger.error(f"执行select_action时发生错误: {str(e)}")
                # 保存当前状态
                await self.memory.save()
                
                # 通知前端发生错误
                yield {
                    "agent": self.name,
                    "stage": "error",
                    "content_type": "action",
                    "action": "error",
                    "content": f"执行过程中发生错误: {str(e)}"
                }
                # 继续下一次循环，而不是直接退出

            if is_finished:
                break
        
        if not self.memory.can_search_more():
            yield {
                "agent": self.name,
                "stage": "system",
                "content_type": "action",
                "action": "system",
                "content": "我已经完成了初步的分析和搜索，接下来您有什么想法或建议吗？如果需要我继续深入探索，也请告诉我。"
            }
            
        yield {
            "agent": self.name,
            "stage": "paused",
            "content_type": "action",
            "action": "paused",
            "content": "研究已暂停"
        }

        # 更新并保存最终报告
        if self.memory.report_draft:
            self.memory.update_final_report(self.memory.report_draft)

        # 在研究结束时保存轨迹
        try:
            # 保存研究轨迹
            metadata = {"user_id": input_data.get("user_id")} if input_data.get("user_id") else {}
            await self.memory.save(metadata)
            logger.info(f"研究结束时保存状态到MongoDB, conversation_id: {self.memory.conversation_id}")
        except Exception as e:
            logger.error(f"保存研究轨迹时出错: {str(e)}")
            
        # 在最后返回token使用信息
        logger.info(f"发送token使用信息：token_usage: {self.memory.token_usage}")
        yield {
            "agent": self.name,
            "stage": "token_usage",
            "content_type": "info",
            "action": "token_info",
            "token_usage": self.memory.token_usage
        }

    async def cognition_search(self, user_id: Optional[str] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Perform cognition search based on the user's question and include user's favorited cognitions.
        
        Args:
            user_id: Optional user ID to fetch favorited cognitions
        
        Returns:
            Dictionary containing the cognition search results
        
        """
        # 基于问题进行认知搜索
        response = await self.cognition_search_model.get_completion([{
            "role": "user",
            "content": cognition_query_prompt[self.language].format(question=self.question)
        }])
        content = ""
        async for chunk in response:
            # 处理token信息
            if "is_token_info" in chunk and chunk["is_token_info"]:
                token_info = chunk.get("token_info", {})
                # 更新token使用统计
                self._update_token_usage("cognition_search_model", token_info)
                continue
                
            if chunk.choices and hasattr(chunk.choices[0].delta, "content") and chunk.choices[0].delta.content is not None:
                content += chunk.choices[0].delta.content
            else:
                logger.error(f"cognition_search_model 返回空响应: {chunk}")
        logger.info(f"cognition_search_model 返回内容: {content}")
        
        # 解析cognition_queries
        cognition_queries = get_content_from_tag(content, "cognition_queries", "").split("\n")
        cognition_queries = [query.strip() for query in cognition_queries if query.strip()]
        logger.info(f"认知搜索查询: {cognition_queries}")

        # 使用cognition_queries进行搜索
        search_results = []
        for query in cognition_queries:
            async for result in self.cognition_rag_service.search(query):
                search_results.extend(result)
        
        # 限制搜索结果数量
        search_results = search_results[:5]
        logger.info(f"基于查询获得的认知搜索结果数量: {len(search_results)}")

        # 获取用户收藏的认知（如果提供了用户ID）
        favorited_cognitions = []
        if user_id and CognitionFavoritesDatabase:
            try:
                logger.info(f"[COGNITION DEBUG] 开始获取用户 {user_id} 的收藏认知")
                logger.info(f"[COGNITION DEBUG] CognitionFavoritesDatabase可用: {CognitionFavoritesDatabase is not None}")
                
                favorites_db = CognitionFavoritesDatabase()
                
                # 采用正确的方法：使用ObjectId格式查询收藏的认知
                try:
                    # 直接查询收藏记录，然后用_id字段查询认知
                    cursor = favorites_db._collection.find({"user_id": user_id}).limit(50)
                    favorite_records = await cursor.to_list(length=50)
                    logger.info(f"[COGNITION DEBUG] 查询到 {len(favorite_records)} 条收藏记录")
                    
                    favorited_items = []
                    if favorite_records:
                        # 提取认知ObjectId列表
                        from bson import ObjectId
                        cognition_object_ids = []
                        for record in favorite_records:
                            cognition_id = record.get("cognition_id")
                            if cognition_id and len(cognition_id) == 24:  # ObjectId长度验证
                                try:
                                    cognition_object_ids.append(ObjectId(cognition_id))
                                except:
                                    logger.warning(f"[COGNITION DEBUG] 无效的ObjectId: {cognition_id}")
                        
                        logger.info(f"[COGNITION DEBUG] 提取到 {len(cognition_object_ids)} 个有效的ObjectId")
                        
                        if cognition_object_ids:
                            # 使用_id字段查询认知详情
                            from backend.cognition.database import CognitionDatabase
                            cognition_db = CognitionDatabase()
                            
                            query = {"_id": {"$in": cognition_object_ids}}
                            cursor = cognition_db._collection.find(query).limit(20)
                            cognition_docs = await cursor.to_list(length=20)
                            logger.info(f"[COGNITION DEBUG] 使用ObjectId查询到 {len(cognition_docs)} 个认知详情")
                            
                            # 转换为标准格式
                            for doc in cognition_docs:
                                # 转换doc id
                                if "_id" in doc:
                                    doc["id"] = str(doc["_id"])
                                favorited_items.append(doc)
                
                except Exception as e:
                    logger.error(f"[COGNITION DEBUG] 使用ObjectId查询收藏认知失败: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    
                    # 如果失败，回退到原来的方法
                    favorites_response = await favorites_db.get_favorited_cognitions(
                        user_id=user_id,
                        skip=0,
                        limit=50
                    )
                    favorited_items = favorites_response.get("items", [])
                
                logger.info(f"[COGNITION DEBUG] 获取到 {len(favorited_items)} 个收藏的认知")
                
                # 转换为与搜索结果相同的格式
                for i, item in enumerate(favorited_items):
                    if i < 3:  # 只记录前3个的详细信息
                        logger.info(f"[COGNITION DEBUG] 收藏项{i+1}: {list(item.keys()) if isinstance(item, dict) else type(item)}")
                    
                    if item.get("question_zh") and item.get("answer_zh"):
                        favorited_cognitions.append({
                            "question": item["question_zh"],
                            "answer": item["answer_zh"],
                            "source": "user_favorite",  # 标记这是用户收藏
                            "cognition_id": item.get("id", "")
                        })
                        if i < 2:
                            logger.info(f"[COGNITION DEBUG] 添加中文收藏: 问题='{item['question_zh'][:50]}...', 答案长度={len(item['answer_zh'])}")
                    elif item.get("question_en") and item.get("answer_en"):
                        favorited_cognitions.append({
                            "question": item["question_en"],
                            "answer": item["answer_en"],
                            "source": "user_favorite",  # 标记这是用户收藏
                            "cognition_id": item.get("id", "")
                        })
                        if i < 2:
                            logger.info(f"[COGNITION DEBUG] 添加英文收藏: 问题='{item['question_en'][:50]}...', 答案长度={len(item['answer_en'])}")
                
                
                logger.info(f"[COGNITION DEBUG] 转换后的收藏认知数量: {len(favorited_cognitions)}")
                
            except Exception as e:
                logger.error(f"获取用户收藏认知失败: {str(e)}")
                import traceback
                traceback.print_exc()
        else:
            if not user_id:
                logger.info("未提供用户ID，跳过获取收藏认知")
            if not CognitionFavoritesDatabase:
                logger.error("CognitionFavoritesDatabase未成功导入，无法获取收藏认知")

        # 合并搜索结果和收藏认知
        all_cognitions = search_results + favorited_cognitions
        logger.info(f"[COGNITION DEBUG] 合并前: 搜索结果={len(search_results)}, 收藏={len(favorited_cognitions)}, 总计={len(all_cognitions)}")
        
        # 去重（基于question内容）
        seen_questions = set()
        unique_cognitions = []
        duplicate_count = 0
        
        for cognition in all_cognitions:
            question = cognition.get("question", "")
            if question and question not in seen_questions:
                seen_questions.add(question)
                unique_cognitions.append(cognition)
            else:
                duplicate_count += 1
        
        logger.info(f"[COGNITION DEBUG] 去重统计: 原始={len(all_cognitions)}, 去重后={len(unique_cognitions)}, 重复={duplicate_count}")
        logger.info(f"[COGNITION DEBUG] 最终认知来源分布:")
        
        # 统计不同来源的认知数量
        source_stats = {}
        for cognition in unique_cognitions:
            source = cognition.get("source", "search_result")
            source_stats[source] = source_stats.get(source, 0) + 1
        
        for source, count in source_stats.items():
            logger.info(f"[COGNITION DEBUG] - {source}: {count}个")
        
        logger.info(f"去重后的认知总数: {len(unique_cognitions)} (搜索结果: {len(search_results)}, 收藏: {len(favorited_cognitions)})")

        # 将搜索到的认知内容结构化
        logger.info(f"[COGNITION DEBUG] 开始构建cognition_content，总认知数: {len(unique_cognitions)}")
        
        self.cognition_content = ""
        for i, result in enumerate(unique_cognitions):
            source_tag = f" [来源: {'用户收藏' if result.get('source') == 'user_favorite' else '搜索结果'}]" if result.get('source') else ""
            cognition_item = f"<cognition_content>\n<question>{result['question']}{source_tag}</question>\n<answer>{result['answer']}</answer>\n</cognition_content>\n"
            self.cognition_content += cognition_item
            
            # 记录前几个认知的详细信息
            if i < 3:
                logger.info(f"[COGNITION DEBUG] 认知{i+1}: 问题='{result['question'][:50]}...', 答案长度={len(result['answer'])}, 来源={result.get('source', 'unknown')}")
        
        logger.info(f"[COGNITION DEBUG] cognition_content构建完成，总长度: {len(self.cognition_content)}")
        logger.info(f"[COGNITION DEBUG] 最终cognition_content前300字符: {self.cognition_content[:300]}...")
        
        yield {
            "cognition_content": self.cognition_content,
            "cognition_queries": cognition_queries,
            "search_results_count": len(search_results),
            "favorited_count": len(favorited_cognitions),
            "total_cognitions": len(unique_cognitions)
        }

    def _update_token_usage(self, model_name: str, token_info: Dict[str, Any]) -> None:
        """
        更新token使用统计
        
        Args:
            model_name: 模型名称
            token_info: token信息，包含input_tokens、output_tokens和costs
        """
        # 使用memory的update_token_usage方法
        try:
            # 创建异步任务
            if asyncio.get_event_loop().is_running():
                asyncio.create_task(self.memory.update_token_usage(model_name, token_info))
            else:
                # 如果不在事件循环中，则创建新的事件循环执行
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(self.memory.update_token_usage(model_name, token_info))
                loop.close()
        except Exception as e:
            logger.error(f"更新token使用统计失败: {str(e)}")
            
            # 更新总使用量（退化方案）
            if not hasattr(self.memory, "token_usage"):
                self.memory.token_usage = {
                    "total_input_tokens": 0,
                    "total_output_tokens": 0,
                    "total_cost": 0,
                    "models_usage": {}  # 按模型名称记录使用情况
                }
            
            # 更新总使用量
            self.memory.token_usage["total_input_tokens"] += token_info.get("input_tokens", 0)
            self.memory.token_usage["total_output_tokens"] += token_info.get("output_tokens", 0)
            self.memory.token_usage["total_cost"] += token_info.get("total_cost", 0)
            
            # 按模型记录
            if model_name not in self.memory.token_usage["models_usage"]:
                self.memory.token_usage["models_usage"][model_name] = {
                    "input_tokens": 0,
                    "output_tokens": 0,
                    "cost": 0
                }
                
            # 更新模型特定使用量
            self.memory.token_usage["models_usage"][model_name]["input_tokens"] += token_info.get("input_tokens", 0)
            self.memory.token_usage["models_usage"][model_name]["output_tokens"] += token_info.get("output_tokens", 0) 
            self.memory.token_usage["models_usage"][model_name]["cost"] += token_info.get("total_cost", 0)

    async def select_action(self) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Select an action for the research agent to take.
        
        Returns:
            Dictionary containing the selected action
        """
        # 增加轮次
        self.memory.increment_round()

        # 创建新的轮次轨迹
        current_round = self.memory.round
        current_round_trajectory = self.memory.start_new_round(current_round)
        
        # 初始化思考和内容变量
        reasoning_content = ""
        action_content = ""
        
        # 检查是否需要暂停 - 不再等待事件恢复，而是检测到暂停直接中断
        if self.memory.is_paused:
            logger.info("select_action开始时检测到暂停状态，中断执行")
            # 保存当前状态
            await self.memory.save()
            # 返回暂停状态的通知
            yield {
                "agent": self.name,
                "stage": "paused",
                "round": current_round,
                "content_type": "action",
                "action": "paused",
                "content": "研究已暂停"
            }
            # 直接返回，不继续执行
            return

        # ! 记录开始进行推理的意图
        self.memory.record_model_action("reasoning", {
            "round": self.memory.round,
            "prompt": ""  
        })

        # 调用LLM进行推理
        messages = self._prepare_action_selection_messages()

        try:
            response = await self.action_selection_model.get_completion(messages=messages)
            if response:
                async for chunk in response:
                    # print(f"chunk: {chunk}")
                    # 处理token信息
                    if "is_token_info" in chunk and chunk["is_token_info"]:
                        token_info = chunk.get("token_info", {})
                        # 更新token使用统计
                        self._update_token_usage("action_selection_model", token_info)
                        continue
                        
                    # 处理reasoning_content和reasoning两种情况
                    if chunk.choices and hasattr(chunk.choices[0].delta, "reasoning_content") and chunk.choices[0].delta.reasoning_content is not None:
                        # 原有的reasoning_content处理逻辑
                        reasoning_chunk = chunk.choices[0].delta.reasoning_content
                        reasoning_content += reasoning_chunk
                        yield {
                            "agent": self.name,
                            "stage": "select_action",
                            "round": current_round,
                            "content_type": "action",
                            "action": "think",
                            "content": reasoning_chunk
                        }
                    # 新增处理reasoning的逻辑
                    elif chunk.choices and hasattr(chunk.choices[0].delta, "reasoning") and chunk.choices[0].delta.reasoning is not None:
                        # 将reasoning视为reasoning_content处理
                        reasoning_chunk = chunk.choices[0].delta.reasoning
                        reasoning_content += reasoning_chunk
                        yield {
                            "agent": self.name,
                            "stage": "select_action",
                            "round": current_round,
                            "content_type": "action",
                            "action": "think",
                            "content": reasoning_chunk
                        }
                    # 原有的content处理逻辑
                    elif chunk.choices and hasattr(chunk.choices[0].delta, "content") and chunk.choices[0].delta.content is not None:
                        reasoning_chunk = chunk.choices[0].delta.content
                        action_content += reasoning_chunk
                        yield {
                            "agent": self.name,
                            "stage": "select_action",
                            "round": current_round,
                            "content_type": "action",
                            "action": "acting",
                            "content": reasoning_chunk
                        }
                    # else:
                        # logger.error(f"reasoning_acting_model 返回空响应: {chunk}")
            else:
                # logger.error("reasoning_acting_model 返回空响应")
                reasoning_content = "无法进行有效的推理，将进行默认动作选择。"
        except Exception as e:
            import traceback
            error_stack = traceback.format_exc()
            logger.error(f"调用 reasoning_acting_model 时出错: {str(e)}\n{error_stack}")
            reasoning_content = f"推理过程中出错: {str(e)}，将进行默认动作选择。"
        # 第二步: 行动选择
        # 获取行动选择提示词
        # action_prompt_template = action_prompt[language]
        # action_prompt_text = action_prompt_template
        
        # 记录开始进行行动选择的意图
        self.memory.record_model_action("action_selection", {
            "round": self.memory.round,
            "prompt": ""
        })

        # # 调用LLM进行行动选择
        # messages = [
        #     {"role": "user", "content": reasoning_prompt_text},
        #     {"role": "assistant", "content": reasoning_content},
        #     {"role": "user", "content": action_prompt_text}
        # ]
        
        # try:
        #     # response = await self.action_selection_model.get_completion(
        #     #     messages=messages,
        #     # )
            
        #     if response:
        #         async for chunk in response:
        #             if hasattr(chunk.choices[0].delta, "reasoning_content") and chunk.choices[0].delta.reasoning_content is not None:
        #                 action_chunk = chunk.choices[0].delta.reasoning_content
        #                 action_content += action_chunk
        #                 # 不将行动选择过程输出给用户，仅记录在内存中
        #             elif hasattr(chunk.choices[0].delta, "content") and chunk.choices[0].delta.content is not None:
        #                 action_chunk = chunk.choices[0].delta.content
        #                 action_content += action_chunk
        #                 # 不将行动选择过程输出给用户，仅记录在内存中
        #     else:
        #         logger.error("action_selection_model 返回空响应")
        #         # 简单的错误处理，默认返回search动作
        #         action_content = "<action>search</action>"
        # except Exception as e:
        #     logger.error(f"调用 action_selection_model 时出错: {str(e)}")
        #     # 出错时默认返回search动作
        #     action_content = "<action>search</action>"
        
        # 解析选择的动作
        selected_action = get_content_from_tag(action_content, "action")
        
        # 检查动作是否有效
        valid_actions = ["search_edit", "chat_edit", "finish", "clarify"]
        
        # 检查生成的内容中是否包含任何有效动作
        action_valid = False
        matched_action = None
        for valid_action in valid_actions:
            if valid_action in selected_action:
                action_valid = True
                matched_action = valid_action
                break
                
        if not selected_action or not action_valid:
            logger.error(f"无效的动作: {selected_action}, 拒绝此轮回复")
            
            # 记录错误到内存
            error_details = {
                "selected_action": selected_action,
                "valid_actions": valid_actions,
                "raw_output": action_content[:500],  # 保存前500字符用于分析
                "reasoning_content": reasoning_content[:200] if reasoning_content else ""
            }
            self.memory.record_model_error("invalid_action", error_details, current_round)
            
            # 保存错误状态
            await self.memory.save()
            
            # 返回错误信息给前端
            yield {
                "agent": self.name,
                "stage": "system",
                "round": current_round,
                "content_type": "action",
                "action": "system",
                "content": f"模型回复无效动作 '{selected_action}'，已拒绝此轮回复，重试中。"
            }
            
            # 直接返回，结束这一轮
            return
            
        # 使用匹配到的有效动作更新内存
        self.memory.update_current_round(current_round, "action", matched_action)
        
        # 将动作相关信息添加到内存中的动作推理部分
        self.memory.research_trajectory["action_reasoning"].append({
            "round": current_round,
            "reasoning": reasoning_content,
            "selected_action": selected_action
        })
        
        yield {
            "agent": self.name,
            "stage": "select_action",
            "round": current_round,
            "content_type": "action",
            "action": "decide",
            "content": f"{selected_action}"
        }
        
        # 处理不同的动作
        if "search_edit" in selected_action:
            # 提取搜索查询
            search_queries = []
            
            # 尝试从<query>标签中提取查询
            query_matches = re.findall(r'<query>(.*?)</query>', action_content, re.DOTALL)
            if query_matches:
                # 处理每个query标签中的内容
                for query_content in query_matches:
                    # 按换行符分割并处理每一行
                    queries = [q.strip() for q in query_content.split('\n')]
                    # 添加非空的查询
                    search_queries.extend([q for q in queries if q])
            
            # 如果没有找到任何查询，使用研究问题作为默认查询
            if not search_queries:
                search_queries = [self.memory.research_question]
            
            yield {
                "agent": self.name,
                "stage": "select_action", 
                "round": current_round,
                "content_type": "action",
                "action": "search",
                "content": ["\n" + "\n".join(search_queries)]
            }
            # 执行Web搜索
            async for chunk in self.web_search(search_queries):
                yield chunk
                
        elif "chat_edit" in selected_action:
            # chat_edit动作：跳过搜索，直接进入报告更新阶段
            logger.info("执行chat_edit动作，跳过搜索步骤，直接更新报告")
            yield {
                "agent": self.name,
                "stage": "select_action",
                "round": current_round,
                "content_type": "action",
                "action": "chat_edit",
                "content": "基于当前信息直接更新报告"
            }
            
            # 直接调用报告更新方法
            async for chunk in self._update_report_with_prompt(''):
                yield chunk
                
        elif "edit" in selected_action:
            
            async for chunk in self._update_report_with_prompt(''):
                yield chunk
                    
        elif "clarify" in selected_action:
            # 提取需要澄清的问题
            print(f"selected_action: {action_content}")
            question_points_str = get_content_from_tag(action_content, "clarification_question_points")
            
            if question_points_str:
                # 处理结构化的问题点
                try:
                    import json
                    question_points_data = json.loads(question_points_str)
                    # 清理之前的问题
                    self.memory.clear_pending_clarification_questions()
                    
                    # 添加问题到待处理列表
                    index = 0
                    for q in question_points_data:
                        q['id'] = index
                        self.memory.add_clarification_question(q)
                        logger.info(f"提出的结构化澄清问题: {q.get('question_content', '')[:100]}")
                        index += 1
                    
                    # 格式化问题点，将其转换为前端期望的格式
                    formatted_questions = []
                    for i, q in enumerate(question_points_data):
                        formatted_question = {
                            "id": i,
                            "content": q
                        }
                        formatted_questions.append(formatted_question)
                    
                    # 设置暂停状态，让出控制权给用户
                    logger.info("模型选择澄清问题，自动暂停研究流程")
                    
                    
                    # 返回结构化问题点
                    yield {
                        "agent": self.name,
                        "stage": "clarify",
                        "round": current_round,
                        "content_type": "action",
                        "action": "clarify",
                        "content": formatted_questions
                    }
                    
                    # 通知前端已暂停
                    yield {
                        "agent": self.name,
                        "stage": "paused",
                        "round": current_round,
                        "content_type": "action",
                        "action": "paused",
                        "content": "研究已暂停，等待澄清问题的回答"
                    }
                    self.memory.set_paused(True)
                    await self.memory.save()
                except Exception as e:
                    logger.error(f"解析结构化问题点时出错: {str(e)}")
                    logger.error(f"原始问题点内容: {question_points_str[:200]}")
            else:
                logger.error("澄清问题内容为空，未找到clarification或clarification_question_points标签")
                
        elif selected_action == "finish":
            yield {
                "agent": self.name,
                "stage": "paused",
                "round": current_round,
                "content_type": "action",
                "action": "paused",
                "content": "研究已暂停，等待澄清问题的回答"
            }
            self.memory.set_paused(True)
            
        # 保存当前轮次的研究轨迹
        await self.memory.save()
    
    def _prepare_action_selection_messages(self) -> List[Dict]:
        """
        准备消息列表，用于与LLM进行交互
        """
        messages = []

        context = {
            "question": self.memory.research_question,
            "round": self.memory.round,
            "search_times": self.memory.search_times,
            "max_search_times": self.memory.max_search_times,
            "clarification_questions": self.memory.clarification_questions,
            # "research_rules": self.memory.research_rules,
            "report_draft": self.memory.report_draft,
            "language": self.memory.language,
            "current_date": get_current_date_str(),
            "new_questions": "",  # 添加new_questions字段，默认为空字符串
            "research_trajectory": self._create_trajectory_summary(),
            "article": self.memory.report_draft, # 添加article字段作为report_draft的别名
            "contexts": self._format_contexts_for_prompt(mm_enabled=self.action_selection_model.is_mm_model),  # 添加上下文内容
            "user_preference": "",  # 添加user_preference字段，默认为空字符串
            "search_edit_special_instruction": "",  # 添加search_edit_special_instruction字段，默认为空字符串
            "error_feedback": self.memory.format_error_feedback()  # 添加错误反馈字段
        }

        # 根据enable_search状态添加特殊指令
        if not self.get_enable_search():
            context["search_edit_special_instruction"] = search_edit_special_instruction
            logger.info("搜索功能已禁用，添加chat_edit特殊指令到action selection context")

        if self.memory.research_trajectory["user_feedbacks"]:
            context["user_feedbacks"] = [fb.get("content", "") for fb in self.memory.research_trajectory["user_feedbacks"]]
            # 将用户反馈也作为new_questions
            context["new_questions"] = "\n".join(context["user_feedbacks"])

        language = self.memory.language or "zh"
        system_prompt_action_selection = system_prompt[language]

        logger.info(f"今天的日期: {get_current_date_str()}")
        logger.info(f"[COGNITION DEBUG] 准备action_selection消息时，cognition_content长度: {len(self.cognition_content)}")
        logger.info(f"[COGNITION DEBUG] cognition_content是否为空: {not bool(self.cognition_content.strip())}")
        
        # 格式化system prompt
        formatted_system_prompt = system_prompt_action_selection.format(
            time_str=get_current_date_str(), 
            cognition=self.cognition_content
        )
        
        logger.info(f"[COGNITION DEBUG] 格式化后的system_prompt长度: {len(formatted_system_prompt)}")
        logger.info(f"[COGNITION DEBUG] system_prompt中包含认知内容: {'<cognition_content>' in formatted_system_prompt}")
        
        messages.append({
            "role": "system",
            "content": formatted_system_prompt
        })

        user_content = None
        image_contexts = self._format_image_contexts_list_for_message()
        if self.action_selection_model.is_mm_model and image_contexts:
            # MM模型，需要将图片和文字形成列表作为Content
            user_content = []
            for image_context in image_contexts:
                user_content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": image_context
                    }
                })
            user_content.append({
                "type": "text",
                "text": action_prompt[language].format(**context)
            })
        else:
            # 非MM模型，直接调用formated Prompt作为字符串型content
            user_content = action_prompt[language].format(**context)
            
        user_message = {
            "role": "user",
            "content": user_content
        }
        messages.append(user_message)

        return messages

    def _prepare_report_editing_with_reference_messages(self) -> List[Dict]:
        """
        准备消息列表，用于与LLM进行交互
        """
        messages = []
        
        context = {
            "question": self.memory.research_question,
            "new_questions": "",
            "clarification_questions": self.memory.clarification_questions,
            "research_trajectory": self._create_trajectory_summary(),
            "article_draft": self.memory.get_report_draft(),
            "contexts": self._format_contexts_for_prompt(mm_enabled=self.report_editing_model.is_mm_model),  # 添加上下文内容
            "user_preferences": self._get_draft_preference_prompt(),
            "user_preference": "",
            "update_report_special_instruction": ""  # 添加search_edit_special_instruction字段，默认为空字符串
        }

        # 检查是否启用编辑模式，如果启用则设置特殊指令
        if self.get_edit_mode():
            # 使用format方法将用户的编辑指令填充到特殊指令中
            formatted_instruction = update_report_special_instruction.format(
                user_edit_instruction=self.get_edit_instruction()
            )
            context["update_report_special_instruction"] = formatted_instruction
        
        if self.memory.research_trajectory["user_feedbacks"]:
            context["user_feedbacks"] = [fb.get("content", "") for fb in self.memory.research_trajectory["user_feedbacks"]]
            # 将用户反馈也作为new_questions
            context["new_questions"] = "\n".join(context["user_feedbacks"])

        language = self.memory.language or "zh"
        system_prompt_report_editing_with_reference = system_prompt[language]
        
        logger.info(f"[COGNITION DEBUG] 准备report_editing消息时，cognition_content长度: {len(self.cognition_content)}")
        
        # 格式化system prompt
        formatted_system_prompt = system_prompt_report_editing_with_reference.format(
            time_str=get_current_date_str(), 
            cognition=self.cognition_content
        )
        
        logger.info(f"[COGNITION DEBUG] report_editing system_prompt中包含认知内容: {'<cognition_content>' in formatted_system_prompt}")
        
        messages.append({
            "role": "system",
            "content": formatted_system_prompt
        })

        report_editing_prompt_template = report_editing_with_reference_prompt[language]

        user_content = None
        image_contexts = self._format_image_contexts_list_for_message()
        if self.report_editing_model.is_mm_model and image_contexts:
            # MM模型，需要将图片和文字形成列表作为Content
            user_content = []
            for image_context in image_contexts:
                user_content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": image_context
                    }
                })
            user_content.append({
                "type": "text",
                "text": report_editing_prompt_template.format(**context)
            })
        else:
            # 非MM模型，直接调用formated Prompt作为字符串型content
            user_content = report_editing_prompt_template.format(**context)
        
        user_message = {
            "role": "user",
            "content": user_content
        }
        messages.append(user_message)
        # import pdb; pdb.set_trace()
        return messages
            
    async def web_search(self, queries: List[str]) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Perform web search with the given queries.
        
        Args:
            queries: List of search queries
            
        Returns:
            Dictionary containing search results
        """
        # 检查暂停状态 - 不再等待事件恢复，而是检测到暂停直接中断
        if self.memory.is_paused:
            logger.info("web_search检测到暂停状态，中断执行")
            # 保存当前状态
            await self.memory.save()
            # 返回暂停状态的通知
            yield {
                "agent": self.name,
                "stage": "paused",
                "round": self.memory.round,
                "content_type": "action",
                "action": "paused",
                "content": "研究已暂停"
            }
            # 直接返回，不继续执行
            return
        
        current_round = self.memory.round
        
        logger.info(f'\n\n++++++++ Start search for {queries} ++++++++')
        
        # 打印搜索查询
        logger.info(f"\n===== Web Search Queries =====")
        for i, query in enumerate(queries):
            logger.info(f"Query {i+1}: {query}")
        logger.info(f"===== End of Queries =====\n")

        # 添加搜索查询到内存
        for query in queries:
            self.memory.add_search_query(query, current_round)
            
        # 记录搜索查询动作
        self.memory.record_model_action("web_search", queries)

        # 正常模式：调用搜索服务
        results = await self.search_service.search(queries)
        
        # 当前轮次的搜索结果，用于记录到内存
        search_results_for_record = []
        for result in results:
            if not DEBUG_MODE:  # 在非调试模式下才需要生成事件，因为调试模式在上面已经生成
                yield {
                    "agent": self.name,
                    "stage": "web_search",
                    "round": current_round,
                    "content_type": "observation",
                    "content": result
                }
            
            # 记录搜索结果到内存
            self.memory.add_search_result(result, result["query"], current_round)
            
            # 添加到临时结果列表
            search_result_info = {
                "url": result.get("url", ""),
                "title": result.get("title", ""),
                "snippet": result.get("snippet", ""),
                "query": result.get("query", ""),
                "round": current_round
            }
            search_results_for_record.append(search_result_info)

        selected_webpages = results

        # !  我们不再在这里对搜索次数进行累加，转而在写到成功的报告的时候进行累加，但是函数名称仍然不变以方便修改和兼容，请务必使用的时候注意
        # self.memory.increment_search_times()

        if len(selected_webpages) > 0:
            # 调用浏览网页方法，但不自动编辑报告
            async for chunk in self.browse_webpages(selected_webpages, queries):
                yield chunk
                
        # 保存研究轨迹
        await self.memory.save()

        # 记录搜索结果
        self.memory.record_action_result("web_search", search_results_for_record)

        # 固定在每次搜索之后，更新报告
        async for chunk in self._update_report_with_prompt(''):
                yield chunk

    async def browse_webpages(self, webpages: List[Dict[str, Any]], queries: List[str]) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Browse webpages and extract useful information.
        
        Args:
            webpages: List of webpages to browse
            queries: List of search queries
            
        Returns:
            Dictionary containing browsing results
        """
        # 检查暂停状态 - 不再等待事件恢复，而是检测到暂停直接中断
        if self.memory.is_paused:
            logger.info("browse_webpages检测到暂停状态，中断执行")
            # 保存当前状态
            await self.memory.save()
            # 返回暂停状态的通知
            yield {
                "agent": self.name,
                "stage": "paused",
                "round": self.memory.round,
                "content_type": "action",
                "action": "paused",
                "content": "研究已暂停"
            }
            # 直接返回，不继续执行
            return
        
        current_round = self.memory.round
        
        logger.info(f'\n\n++++++++ Start browsing webpages, count: {len(webpages)} ++++++++')
        
        # 准备浏览结果
        browse_result = {}
        
        if webpages:
            # 通知开始浏览
            yield {
                "agent": self.name,
                "stage": "browse",
                "round": current_round,
                "content_type": "action",
                "action": "browse",
                "content": webpages
            }

            # 直接将所有网页传递给BrowseAgent，让它内部处理批量请求
            logger.info(f"将所有 {len(webpages)} 个网页一次性传递给浏览代理")
            
            # 准备输入数据
            browse_input = {
                "question": self.memory.research_question,
                "language": self.memory.language or "zh",
                "webpages": webpages,  # 直接传递整个网页列表
                "article": self.memory.report_draft,
                "new_questions": "",
                "clarification_questions": "",
                "search_queries": queries,
                "thinking": self.memory.research_trajectory["rounds"][-1].get("reasoning", "")
            }
            
            # 如果有用户反馈，添加到输入数据
            if self.memory.research_trajectory["user_feedbacks"]:
                browse_input["new_questions"] = "\n".join([fb.get("content", "") for fb in self.memory.research_trajectory["user_feedbacks"]])
            
            # 如果有澄清问题，添加到输入数据
            if self.memory.clarification_questions:
                browse_input["clarification_questions"] = "\n".join([f"问题: {q.get('question', '')}\n回答: {q.get('answer', '')}" for q in self.memory.clarification_questions])
            
            # 添加用户偏好信息
            user_preferences = ""
            if hasattr(self.memory, 'user_preferences') and self.memory.user_preferences:
                # 获取最新的用户偏好总结
                latest_preference = self.memory.user_preferences[-1]
                user_preferences = f"用户最新偏好总结:\n{latest_preference}"
                logger.info(f"传递用户偏好信息给browse_agent，偏好长度: {len(latest_preference)}")
            else:
                user_preferences = "暂无用户偏好信息"
                logger.info("没有用户偏好信息可传递给browse_agent")
            
            browse_input["user_preferences"] = user_preferences
            
            # 调用浏览代理
            try:
                async for result in self.browse_agent.run(browse_input):
                    # 处理 token 使用信息
                    if result.get("stage") == "token_usage" and result.get("content_type") == "info" and result.get("action") == "token_info":
                        browse_token_usage = result.get("token_usage", {})
                        if browse_token_usage:
                            try:
                                # 使用merge_browse_agent_token_usage方法合并token使用情况
                                if asyncio.get_event_loop().is_running():
                                    asyncio.create_task(self.memory.merge_browse_agent_token_usage(browse_token_usage))
                                else:
                                    # 如果不在事件循环中，则创建新的事件循环执行
                                    loop = asyncio.new_event_loop()
                                    asyncio.set_event_loop(loop)
                                    loop.run_until_complete(self.memory.merge_browse_agent_token_usage(browse_token_usage))
                                    loop.close()
                            except Exception as e:
                                logger.error(f"处理BrowseAgent token使用信息时出错: {str(e)}")
                                # 退化方案：直接更新内存中的数据
                                # 更新总数
                                self.memory.token_usage["total_input_tokens"] += browse_token_usage.get("total_input_tokens", 0)
                                self.memory.token_usage["total_output_tokens"] += browse_token_usage.get("total_output_tokens", 0)
                                self.memory.token_usage["total_cost"] += browse_token_usage.get("total_cost", 0)
                                
                                # 合并模型使用情况
                                for model_name, usage in browse_token_usage.get("models_usage", {}).items():
                                    if model_name not in self.memory.token_usage["models_usage"]:
                                        self.memory.token_usage["models_usage"][model_name] = {
                                            "input_tokens": 0,
                                            "output_tokens": 0,
                                            "cost": 0
                                        }
                                    
                                    self.memory.token_usage["models_usage"][model_name]["input_tokens"] += usage.get("input_tokens", 0)
                                    self.memory.token_usage["models_usage"][model_name]["output_tokens"] += usage.get("output_tokens", 0)
                                    self.memory.token_usage["models_usage"][model_name]["cost"] += usage.get("cost", 0)
                                
                            continue
                            
                    if result.get("stage") == "browse_result" and result.get("content_type") == "observation":
                        browse_result_content = result.get("content", {})
                        
                        if browse_result_content:
                            browse_result.update(browse_result_content)
                            
                            # 返回浏览结果
                            for url, content in browse_result_content.items():
                                yield {
                                    "agent": self.name,
                                    "stage": "browse",
                                    "round": current_round,
                                    "content_type": "observation",
                                    "url": url,
                                    "content": content
                                }
            except Exception as e:
                logger.error(f"浏览网页出错: {str(e)}")

            # 存储浏览结果便于后续使用
            self.memory.set_last_browse_result(browse_result)
            
            # 检索并更新URL信息，以便将有用信息添加到研究轨迹
            for url, content in browse_result.items():
                # 检查URL是否已在有用URL列表中
                url_found = False
                
                # 查找已有的URL记录
                for i, url_info in enumerate(self.memory.research_trajectory["useful_urls"]):
                    if url_info.get("url") == url:
                        url_found = True
                        # 如果找到，更新有用信息
                        self.memory.research_trajectory["useful_urls"][i]["summaries"] = content.get("summaries", [])
                        self.memory.research_trajectory["useful_urls"][i]["useful_information"] = content.get("useful_information", [])
                        # logger.info(f"更新URL信息：{url}")
                        break
                
                # 如果未找到，添加新条目
                if not url_found:
                    # 查找原始搜索结果中的标题和摘要
                    title = ""
                    snippet = ""
                    query = ""
                    for webpage in webpages:
                        if webpage.get("url") == url:
                            title = webpage.get("title", "")
                            snippet = webpage.get("snippet", "")
                            query = webpage.get("query", "")
                        break
                
                    new_url_info = {
                        "url": url,
                        "title": title,
                        "snippet": snippet,
                        "query": query,
                        "round": current_round,
                        "summaries": content.get("summaries", []),
                        "useful_information": content.get("useful_information", [])
                    }
                    self.memory.research_trajectory["useful_urls"].append(new_url_info)
                    logger.info(f"添加新的URL信息：{url}")
            
            # 存储浏览结果到当前轮次的研究轨迹
            if self.memory.research_trajectory["rounds"] and self.memory.research_trajectory["rounds"][-1]["round"] == current_round:
                self.memory.research_trajectory["rounds"][-1]["browse_result"] = {
                    "webpage_count": len(browse_result),
                    "urls": list(browse_result.keys())
                }
                
        # 记录浏览请求
        self.memory.record_model_action("browse_webpages", {"webpages": webpages, "queries": queries})
        
        # 记录浏览结果
        self.memory.record_action_result("browse_webpages", browse_result)
        
    async def _update_report_with_prompt(self, prompt: str) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Update the report with a given prompt.
        
        Args:
            prompt: The prompt to update the report with
            
        Returns:
            Dictionary containing the updated report
        """
        # 检查暂停状态 - 不再等待事件恢复，而是检测到暂停直接中断
        if self.memory.is_paused:
            logger.info("_update_report_with_prompt检测到暂停状态，中断执行")
            # 保存当前状态
            await self.memory.save()
            # 返回暂停状态的通知
            yield {
                "agent": self.name,
                "stage": "paused",
                "round": self.memory.round,
                "content_type": "action",
                "action": "paused",
                "content": "研究已暂停"
            }
            # 直接返回，不继续执行
            return
        
        current_round = self.memory.round
        
        logger.info(f"\n\n++++++++ Start updating report  ++++++++")
        
        # 当前报告
        current_report = self.memory.get_report_draft()
        research_trajectory = self._create_trajectory_summary()
        webpages = self.memory.research_trajectory["useful_urls"]
        clarification_questions = self.memory.clarification_questions
        
        # ! 记录开始编辑报告的意图
        self.memory.record_model_action("edit_report", {
            "prompt": prompt,
            "current_length": len(current_report)
        })
        
        # 将状态标记为正在更新
        self.memory.is_updating_report = True
        
        try:
            # 调用报告编辑模型 - 使用get_completion替代generate
            messages = self._prepare_report_editing_with_reference_messages()
            response = await self.report_editing_model.get_completion(messages=messages)
            new_report = ""
            async for chunk in response:
                # 处理token信息
                if "is_token_info" in chunk and chunk["is_token_info"]:
                    token_info = chunk.get("token_info", {})
                    # 更新token使用统计
                    self._update_token_usage("report_editing_model", token_info)
                    continue
                    
                # 处理reasoning_content和reasoning两种情况
                if chunk.choices and hasattr(chunk.choices[0].delta, "reasoning_content") and chunk.choices[0].delta.reasoning_content is not None:
                    # 原有的reasoning_content处理逻辑
                    reasoning_chunk = chunk.choices[0].delta.reasoning_content
                    new_report += reasoning_chunk
                # 新增处理reasoning的逻辑
                elif chunk.choices and hasattr(chunk.choices[0].delta, "reasoning") and chunk.choices[0].delta.reasoning is not None:
                    # 将reasoning视为reasoning_content处理
                    reasoning_chunk = chunk.choices[0].delta.reasoning
                    new_report += reasoning_chunk
                # 原有的content处理逻辑
                elif chunk.choices and hasattr(chunk.choices[0].delta, "content") and chunk.choices[0].delta.content is not None:
                    reasoning_chunk = chunk.choices[0].delta.content
                    new_report += reasoning_chunk
            # 提取新报告
            if new_report:
                logger.info(f"报告编辑模型返回: {new_report[:100]}")
            else:
                logger.error("报告编辑模型未返回有效响应")
                new_report = current_report
            

            new_report = get_content_from_tag(new_report, "article")
            if new_report is None:
                logger.error("报告编辑模型未能提取有效响应, 保持原先版本")
                new_report = current_report
            # 更新报告草稿
            self.memory.update_report_draft(new_report)
            
            # ! 我们不再在这里对搜索次数进行累加，转而在写到成功的报告的时候进行累加，但是函数名称仍然不变以方便修改和兼容，请务必使用的时候注意
            self.memory.increment_search_times()
            
            # 用户反馈
            # yield {
            #     "agent": self.name,
            #     "stage": "edit_report",
            #     "round": current_round,
            #     "content_type": "action",
            #     "action": "edit_report",
            #     "content": "报告已更新"
            # }
            
            # 记录编辑活动到研究轨迹
            if self.memory.research_trajectory["rounds"] and self.memory.research_trajectory["rounds"][-1]["round"] == current_round:
                # 如果当前轮次已经开始记录，添加编辑记录
                self.memory.research_trajectory["rounds"][-1]["edit_action"] = {
                    "time": get_current_date_str(),
                    "draft_version": self.memory.get_draft_version()
                }
            
            # 返回最新的报告内容
            yield {
                "agent": self.name,
                "stage": "edit_report",
                "round": current_round,
                "content_type": "action",
                "action": "edit_report",
                "content": new_report
            }
        
        except Exception as e:
            import traceback
            traceback.print_exc()
            logger.error(f"更新报告时出错: {str(e)}")
            yield {
                "agent": self.name,
                "stage": "edit_report",
                "round": current_round,
                "content_type": "action",
                "action": "edit_report",
                "content": f"更新报告时出错: {str(e)}"
            }
        
        finally:
            # 更新完成后重置状态
            self.memory.is_updating_report = False
        
        # 记录报告编辑结果
        self.memory.record_action_result("edit_report", self.memory.get_report_draft())

    def _split_into_word_chunks(self, text: str, chunk_size: int = 3) -> List[str]:
        """
        Split text into chunks of specified word size, handling both English and Chinese words.
        
        Args:
            text: The text to split
            chunk_size: Number of words per chunk
            
        Returns:
            List of text chunks
        """
        # Split text into words, handling both English and Chinese
        words = []
        current_word = ""
        
        for char in text:
            if char.isspace():
                if current_word:
                    words.append(current_word)
                    current_word = ""
                words.append(char)
            else:
                # For Chinese characters, treat each as a word
                if '\u4e00' <= char <= '\u9fff':
                    if current_word:
                        words.append(current_word)
                        current_word = ""
                    words.append(char)
                else:
                    current_word += char
        
        if current_word:
            words.append(current_word)
        
        # Group words into chunks
        chunks = []
        current_chunk = []
        word_count = 0
        
        for word in words:
            if word.isspace():
                if current_chunk:
                    chunks.append("".join(current_chunk))
                    current_chunk = []
                    word_count = 0
                chunks.append(word)
            else:
                current_chunk.append(word)
                word_count += 1
                if word_count >= chunk_size:
                    chunks.append("".join(current_chunk))
                    current_chunk = []
                    word_count = 0
        
        if current_chunk:
            chunks.append("".join(current_chunk))
            
        return chunks

    def _update_report_with_search_and_replace(self, content: str):
        article = get_content_from_tag(content, "article")
        if article and article != self.report_draft:
            self.report_draft = article
            self.draft_history.append(self.report_draft)
            self.draft_version += 1

            # with open(f"report_draft_{self.draft_version}.md", "w", encoding="utf-8") as f:
            #     f.write(self.report_draft)
            # logger.info(f"Report draft {self.draft_version} saved to report_draft_{self.draft_version}.md")
        else:
            logger.info(f"No revision for report draft {self.draft_version}")

    # 添加一个更新研究状态的方法
    def _update_research_state(self, state_updates=None):
        """更新研究状态"""
        if state_updates:
            self.research_state.update(state_updates)
        else:
            # 如果没有提供更新，则使用当前状态
            self.research_state = {
                "research_query": self.question,
                "current_report": self.report_draft,
                # 其他状态字段可以在需要时添加
            }
        
    async def pause(self):
        """
        暂停代理的执行 - 设置为中断信号
        """
        logger.info("研究代理暂停 - 中断当前执行")
        self.memory.set_paused(True)
        # 保存当前状态，确保中断后可以恢复
        await self.memory.save()
    
    async def resume(self):
        """
        恢复代理的执行
        """
        logger.info("研究代理恢复")
        self.memory.set_paused(False)
        # 保存更新后的状态到MongoDB，确保下次继续时能加载正确状态
        try:
            await self.memory.save({"is_critical": True})
            logger.info(f"恢复执行时保存状态，conversation_id: {self.memory.conversation_id}")
        except Exception as e:
            logger.error(f"恢复执行时保存状态失败: {str(e)}")

    def add_user_feedback(self, feedback: str):
        """
        添加用户反馈
        
        Args:
            feedback: 用户提供的反馈
        """
        # 记录用户反馈
        self.memory.record_user_input(feedback)
        
        self.memory.add_user_feedback(feedback, self.memory.round)
            
        # 保存更新后的研究轨迹 - 使用同步方法调用异步函数
        try:
            # 创建事件循环，如果不存在
            if asyncio.get_event_loop().is_running():
                # 如果在事件循环中，创建任务
                asyncio.create_task(self.memory.save())
            else:
                # 如果不在事件循环中，创建新的事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(self.memory.save())
                loop.close()
            logger.info(f"用户反馈后保存状态，conversation_id: {self.memory.conversation_id}")
        except Exception as e:
            logger.error(f"保存用户反馈后状态失败: {str(e)}")

    def _extract_research_rules_from_answers(self, answers: str):
        """从用户回答中提取可能的研究规则"""
        # 简单实现：查找包含"规则"、"要求"等关键词的句子
        rule_keywords = ["规则", "要求", "限制", "必须", "不能", "应该", "不应该", "需要", "不需要"]
        try:
            sentences = re.split(r'[.。!！?？\n]', answers)
            for sentence in sentences:
                sentence = sentence.strip()
                if not sentence:
                    continue
                    
                # 检查句子是否包含规则关键词
                if any(keyword in sentence for keyword in rule_keywords):
                    # 避免添加重复的规则
                    if sentence not in self.memory.research_rules:
                        self.memory.research_rules.append(sentence)
                        logger.info(f"从用户回答中提取到研究规则: {sentence}")
        except Exception as e:
            logger.error(f"提取研究规则时出错: {str(e)}")
        
    def _create_trajectory_summary(self) -> str:
        """创建研究轨迹的摘要，避免提示词过长"""
        
        # 获取关键信息
        search_queries = self.memory.research_trajectory["search_queries"]
        useful_urls = self.memory.research_trajectory["useful_urls"]
        actions = self.memory.research_trajectory["action_reasoning"]
        
        # 限制数量
        max_items = 10
        recent_queries = search_queries[-max_items:] if search_queries else []
        
        # 只获取最近一次的 action reasoning
        latest_action = actions[-1] if actions else None
        
        # 筛选包含有用信息的URL（具有summaries或useful_information）
        useful_urls_with_info = []
        for url in useful_urls:
            has_useful_info = False
            
            if "summaries" in url and url["summaries"]:
                has_useful_info = True
            if "useful_information" in url and url["useful_information"]:
                has_useful_info = True
                
            if has_useful_info:
                useful_urls_with_info.append(url)
        
        # 如果有用信息的URL太多，限制数量，优先保留最近的
        max_useful_urls = max_items * 3  # 允许更多的URL信息
        selected_urls = useful_urls_with_info[-max_useful_urls:] if len(useful_urls_with_info) > max_useful_urls else useful_urls_with_info
        
        # 创建 markdown 格式的摘要
        summary_parts = []
        
        # 添加最近搜索查询
        if recent_queries:
            summary_parts.append("## 最近搜索查询")
            for i, query in enumerate(recent_queries, 1):
                summary_parts.append(f"{i}. {query}")
            summary_parts.append("")
        
        # 添加最近一次行动推理
        if latest_action:
            summary_parts.append("## 最近行动推理")
            summary_parts.append(f"**轮次:** {latest_action.get('round', 0)}")
            summary_parts.append(f"**选择的行动:** {latest_action.get('selected_action', '')}")
            summary_parts.append("")
        
        # 添加有用的URL信息
        if selected_urls:
            summary_parts.append("## 有用信息来源")
            for url in selected_urls:
                if "summaries" not in url or not url.get("summaries"):
                    continue
                    
                summary_parts.append(f"### {url.get('title', 'Unknown Title')}")
                summary_parts.append(f"**URL:** {url.get('url', '')}")
                
                # 添加摘要信息
                if "summaries" in url and url["summaries"]:
                    summary_parts.append("**摘要:**")
                    for summary in url["summaries"]:
                        summary_parts.append(f"- {summary}")
                
                # 添加有用信息
                if "useful_information" in url and url["useful_information"]:
                    summary_parts.append("**有用信息:**")
                    for info in url["useful_information"]:
                        summary_parts.append(f"- {info}")
                
                summary_parts.append("")
        
        # 组合成最终的 markdown 文本
        try:
            return "\n".join(summary_parts)
        except Exception as e:
            logger.error(f"创建研究轨迹摘要时出错: {e}")
            return f"研究轨迹摘要生成失败: {e}"

    def _format_single_clarification_question(self, question, index=0):
        """
        格式化单个澄清问题和选项，使其更友好地显示
        
        Args:
            question: 单个问题对象，包含问题内容和选项
            index: 问题索引
            
        Returns:
            格式化后的问题字符串
        """
        if not question:
            return ""
            
        question_content = question.get("question_content", "")
        question_options = question.get("question_options", [])
        
        formatted_text = f"【问题{index+1}】{question_content}\n"
        
        if question_options:
            formatted_text += "选项：\n"
            for j, option in enumerate(question_options):
                formatted_text += f"  {j+1}. {option}\n"
            formatted_text += "\n"
        else:
            formatted_text += "（请直接回答）\n\n"
                
        formatted_text += "\n请直接回答此问题。您可以：\n"
        formatted_text += "1. 输入选项编号（如 1 或 2）\n"
        formatted_text += "2. 输入多个选项编号（如 1 2 3）进行多选\n"
        formatted_text += "3. 直接提供您自己的回答\n"
        
        return formatted_text

    # 记录用户输入的方法
    def _record_user_input(self, input_text: str):
        """记录用户输入到研究轨迹"""
        self.memory.record_user_input(input_text)

    # 记录模型思考和动作的方法
    def _record_model_action(self, action_type: str, content: Any):
        """记录模型思考和动作到研究轨迹"""
        self.memory.record_model_action(action_type, content)

    # 记录动作结果的方法
    def _record_action_result(self, action_type: str, result: Any):
        """记录动作结果到研究轨迹"""
        self.memory.record_action_result(action_type, result)

    def get_pending_questions(self) -> List[Dict[str, Any]]:
        """
        获取所有待处理的澄清问题列表
        
        Returns:
            待处理问题列表，每个问题包含问题内容和可能的选项
        """
        # 从内存中获取待处理问题
        questions = self.memory.get_pending_clarification_questions()
        
        # 如果没有待处理问题，返回空列表
        if not questions:
            logger.info("当前没有待处理的澄清问题")
            return []
            
        # 构建前端友好的问题格式
        formatted_questions = []
        for i, q in enumerate(questions):
            question_content = q.get("question_content", "")
            question_options = q.get("question_options", [])
            
            formatted_question = {
                "id": i,
                "question_content": question_content,
                "question_options": question_options,
                "formatted_text": self._format_single_clarification_question(q, i)
            }
            formatted_questions.append(formatted_question)
            
        logger.info(f"返回{len(formatted_questions)}个待处理澄清问题")
        return formatted_questions
        
    async def answer_pending_questions(self, answers: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        处理用户对待处理澄清问题的回答
        
        Args:
            answers: 用户回答列表，每项包含问题ID和回答内容
                例如: [{"id": 0, "answer": "选项1"}, {"id": 1, "answer": "自定义回答"}]
        
        Returns:
            处理结果，包括成功状态和错误信息
        """
        # 获取当前待处理问题
        pending_questions = self.memory.get_pending_clarification_questions()
        
        # 检查是否有待处理问题
        if not pending_questions:
            logger.warning("没有待处理的澄清问题，无法处理回答")
            return {
                "success": False,
                "error": "没有待处理的澄清问题",
                "remaining_questions": 0
            }
            
        # 不再要求提供所有问题的回答，支持部分回答
        if not answers:
            return {
                "success": False,
                "error": "未提供任何问题的回答",
                "remaining_questions": len(pending_questions)
            }
            
        # 处理回答
        formatted_questions = []
        formatted_answers = []
        formatted_qa_pairs = []  # 用于存储详细的问题-回答对
        processed_question_ids = set()  # 记录已处理的问题ID
        
        # 解析每个问题的回答
        for answer_item in answers:
            q_id = answer_item.get("id", -1)
            answer_text = answer_item.get("answer", "")
            
            question = None
            for q in pending_questions:
                if q.get('id') == q_id:
                    question = q
                    break
            if q is None:
                logger.warning(f"问题ID {q_id} 无效")
                continue
                
            # 获取问题信息
            question_content = question.get("question_content", "")
            question_options = question.get("question_options", [])
            
            # 将问题添加到格式化列表
            formatted_questions.append(question_content)
            
            # 创建问题-回答对
            qa_pair = {
                "question_content": question_content,
                "question_options": question_options,
                "answer_type": "custom",  # 默认为自定义回答
                "selected_option_indexes": [],
                "selected_option_text": None,
                "custom_answer": answer_text
            }
            
            # # 判断回答是否匹配某个选项
            # if question_options:
            #     # 尝试判断是否为选项编号
            #     try:
            #         # 检查是否为数字
            #         option_idx = int(answer_text.strip()) - 1  # 选项从1开始
            #         if 0 <= option_idx < len(question_options):
            #             # 是选项编号
            #             qa_pair["answer_type"] = "option"
            #             qa_pair["selected_option_indexes"] = [option_idx]
            #             qa_pair["selected_option_text"] = question_options[option_idx]
            #             qa_pair["custom_answer"] = question_options[option_idx]
            #             logger.info(f"问题{q_id}选择了选项{option_idx+1}: {question_options[option_idx][:50]}...")
            #     except ValueError:
            #         # 不是数字，检查是否匹配选项文本
            #         for idx, option in enumerate(question_options):
            #             if answer_text.strip().lower() == option.lower():
            #                 qa_pair["answer_type"] = "option"
            #                 qa_pair["selected_option_indexes"] = [idx]
            #                 qa_pair["selected_option_text"] = option
            #                 logger.info(f"问题{q_id}匹配选项{idx+1}: {option[:50]}...")
            #                 break
            
            # 添加到回答列表
            formatted_answers.append(f"问题: {question_content}\n回答: {qa_pair['custom_answer']}")
            logger.info(f"问题{question_content}的回答: {qa_pair['custom_answer']}")
            formatted_qa_pairs.append(qa_pair)
            processed_question_ids.add(q_id)
        
        # 只有在有已处理问题时才创建QA记录
        if formatted_questions:
            # 组合所有问题和回答
            combined_question = "\n".join(formatted_questions)
            combined_answer = "\n\n".join(formatted_answers)
            
            # 将问答对添加到澄清问题集合
            clarification_qa_item = {
                "question": combined_question,
                "answer": combined_answer,
                "qa_pairs": formatted_qa_pairs,
                "round": self.memory.round,
                "timestamp": get_cn_time().isoformat()
            }
            
            # 添加到内存模型
            self.memory.clarification_questions.append(clarification_qa_item)
            self.memory.add_clarification_qa(combined_question, combined_answer)
            
            # 根据回答提取可能的研究规则
            # self._extract_research_rules_from_answers(combined_answer)
            
            # 从pending_questions中移除已处理的问题
            new_pending_questions = []
            # for i, q in enumerate(pending_questions):
            #     if i not in processed_question_ids:
            #         new_pending_questions.append(q)
            for q in pending_questions:
                if q.get('id') not in processed_question_ids:
                    new_pending_questions.append(q)
            
            # 更新待处理问题列表
            self.memory.clear_pending_clarification_questions()
            for q in new_pending_questions:
                self.memory.add_clarification_question(q)
            
            # 保存更新后的研究轨迹
            await self.memory.save()
            
            remaining_count = len(new_pending_questions)
            logger.info(f"成功处理了{len(processed_question_ids)}个澄清问题的回答，还有{remaining_count}个待回答")
            
            return {
                "success": True,
                "error": "",
                "remaining_questions": remaining_count,
                "message": f"成功处理了{len(processed_question_ids)}个问题的回答，还有{remaining_count}个待回答"
            }
        else:
            # 没有处理任何问题
            return {
                "success": False,
                "error": "未能处理任何问题的回答",
                "remaining_questions": len(pending_questions)
            }

    def set_models(self, config_updates: Dict[str, Any]):
        """
        更新智能体使用的模型和配置。
        
        Args:
            config_updates: 字典，包含需要更新的模型配置。可以包含以下键:
                - action_selection_model: 字典，包含action_selection_model的配置
                - report_editing_model: 字典，包含report_editing_model的配置
        """
        logger.info(f"更新模型配置: {config_updates}")
        
        # 更新行动选择模型
        if 'action_selection_model' in config_updates:
            model_config = config_updates['action_selection_model']
            model_name = model_config.get('name')

            self.action_selection_model = LLMService(model_config)
            logger.info(f"已更新action_selection_model为: {model_name}")
        
        # 更新报告编辑模型
        if 'report_editing_model' in config_updates:
            model_config = config_updates['report_editing_model']
            model_name = model_config.get('name')
            # 更新模型配置
            self.report_editing_model = LLMService(model_config)
            logger.info(f"已更新report_editing_model为: {model_name}")
                
        # 更新url选择模型
        if 'url_selection_model' in config_updates:
            model_config = config_updates['url_selection_model']
            model_name = model_config.get('name')
            # 更新模型配置
            self.url_selection_model = LLMService(model_config)
            logger.info(f"已更新url_selection_model为: {model_name}")

        
        # 如果有其他配置更新，也可以在这里处理
        # 例如 max_search_times 等
        for key, value in config_updates.items():
            if key not in ['action_selection_model', 'report_editing_model', 'url_selection_model']:
                if hasattr(self, key):
                    setattr(self, key, value)
                    logger.info(f"已更新{key}为: {value}")
                elif hasattr(self.memory, key):
                    setattr(self.memory, key, value)
                    logger.info(f"已更新memory.{key}为: {value}")
    
    # region Contexts Feat
    def _validate_contexts(self) -> List[Dict[str, Any]]:
        """
        检查并清理无效的上下文
        
        Returns:
            List[Dict[str, Any]]: 有效的上下文列表
        """
        contexts = self.memory.get_all_contexts()
        if not contexts:
            return []
            
        valid_contexts = []
        for ctx in contexts:
            # 检查是否是文件上下文
            if isinstance(ctx, FileContext):
                # 检查文件状态
                status = ctx.check_file_status()
                if not status["is_valid"]:
                    logger.warning(f"发现无效的文件上下文 {ctx.context_id}: {status['error']}")
                    # 从内存中移除无效的上下文
                    self.memory.remove_context(ctx["context_id"])
                    continue
                    
            # 如果是有效的上下文，添加到列表
            valid_contexts.append(ctx)
            
        return valid_contexts
    
    def _validate_context_class(self) -> bool:
        """
        检查并清理无效的上下文类
        """
        contexts = self.memory.get_all_context_class()
        if not contexts:
            return []
        valid_contexts = []
        for ctx in contexts:
            if isinstance(ctx, FileContext):
                if ctx.check_file_status()["is_valid"]:
                    valid_contexts.append(ctx)
                else:
                    logger.warning(f"发现无效的文件上下文 {ctx.context_id}: {ctx.check_file_status()['error']}")
                    self.memory.remove_context(ctx.context_id)
            elif isinstance(ctx, NoteContext):
                valid_contexts.append(ctx)
        return valid_contexts
    
    def _format_image_contexts_list_for_message(self) -> List[Any]:
        """
        格式化图片上下文列表，用于消息模板
        """
        valid_contexts = self._validate_context_class()
        if not valid_contexts:
            return []
            
        image_contexts = []
        image_contexts_format = []
        for ctx in valid_contexts:
            if not isinstance(ctx, FileContext) or 'png' not in ctx.metadata.get('file_type', '') or 'jpg' not in ctx.metadata.get('file_type', '') or 'jpeg' not in ctx.metadata.get('file_type', ''):
                continue
            img_bytes = ctx.get_file_binary()
            img_base64 = base64.b64encode(img_bytes).decode()
            image_contexts.append(img_base64)
            if "png" in ctx.metadata.get('file_type', ''):
                image_contexts_format.append('data:image/png;base64,' + img_base64)
            elif "jpg" in ctx.metadata.get('file_type', ''):
                image_contexts_format.append('data:image/jpeg;base64,' + img_base64)
            elif "jpeg" in ctx.metadata.get('file_type', ''):
                image_contexts_format.append('data:image/jpeg;base64,' + img_base64)
            
        return image_contexts_format
    
    def _format_contexts_for_prompt(self, mm_enabled: bool = False) -> str:
        """
        格式化上下文内容，用于提示词模板

        对每一个上下文进行最大大小限制，不超过8000个字符
        
        Returns:
            格式化后的上下文内容字符串
        """
        # 获取并验证上下文
        valid_contexts = self._validate_contexts()
        if not valid_contexts:
            return ""
        if mm_enabled:
            logger.info(f"mm_enabled: {mm_enabled}")
            
        formatted_contexts = []
        for ctx in valid_contexts:
            if mm_enabled and isinstance(ctx, FileContext) and (
                "image" in ctx["metadata"]["file_type"] or 
                "png" in ctx["metadata"]["file_type"] or 
                "jpg" in ctx["metadata"]["file_type"] or
                "jpeg" in ctx["metadata"]["file_type"]
            ):
                logger.info(f"上下文 {ctx['context_id']} 是图片，跳过")
                continue
            context_info = f"【上下文 {ctx['context_id']}】\n"
            if ctx.get('metadata'):
                metadata_str = ", ".join([f"{k}: {v}" for k, v in ctx['metadata'].items()])
                context_info += f"元数据: {metadata_str}\n"
            context_info += f"内容:\n{ctx['content'][:8000]}\n" # 对超长的内容进行硬截断
            formatted_contexts.append(context_info)
            
        return "\n".join(formatted_contexts)

    async def add_file_context(self, file_path: str) -> str:
        """
        添加文件上下文到研究智能体的内存中
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 上下文ID
        """
        try:
            # 创建文件上下文
            file_context = FileContext(file_path)
            
            # 添加到内存中
            self.memory.add_context(file_context)
            
            # 保存到MongoDB
            await self.memory.save()
            
            return file_context.context_id
        except Exception as e:
            logger.error(f"添加文件上下文失败: {str(e)}")
            raise
    
    async def add_note_context(self, note_id: str, user_id: str, content: str, title: str) -> str:
        """
        添加笔记上下文到研究智能体的内存中
        """
        try:
            # 创建笔记上下文
            note_context = NoteContext(note_id, user_id)
            note_context.add_context(content, title)
            self.memory.add_context(note_context)
            await self.memory.save()
            return note_context.context_id
        except Exception as e:
            logger.error(f"添加笔记上下文失败: {str(e)}")
            raise
    
    def request_context(self, context_id: str):
        """
        请求上下文
        """
        try:
            context = self.memory.get_context_by_id(context_id)
            if isinstance(context, FileContext):
                return context.get_file_binary()
            elif isinstance(context, NoteContext):
                return context.get_content()
            else:
                return None
        except Exception as e:
            logger.error(f"请求上下文失败: {str(e)}")

    def remove_context(self, context_id: str, file_path: Optional[str] = None) -> bool:
        """
        安全地删除指定的上下文
        
        Args:
            context_id: 上下文的唯一标识符
            file_path: 可选的文件路径，用于额外验证
            
        Returns:
            bool: 是否成功删除
        """
        try:
            # 获取所有上下文
            contexts = self.memory.get_all_contexts()
            if not contexts:
                logger.warning(f"没有找到任何上下文")
                return False
                
            # 查找匹配的上下文
            target_context = None
            for ctx in contexts:
                if ctx["context_id"] == context_id:
                    # 如果是文件上下文，需要验证文件路径
                    if isinstance(ctx, FileContext):
                        if file_path and ctx["file_path"] != file_path:
                            continue
                        # 检查文件状态
                        status = ctx.check_file_status()
                        if not status["is_valid"]:
                            logger.warning(f"上下文 {context_id} 关联的文件无效: {status['error']}")
                    target_context = ctx
                    break
            
            if not target_context:
                logger.warning(f"未找到匹配的上下文: ID={context_id}, 文件路径={file_path}")
                return False
                
            # 从内存中移除上下文
            self.memory.remove_context(context_id)
            
            # 如果是文件上下文，尝试删除文件
            if isinstance(target_context, FileContext):
                try:
                    if os.path.exists(target_context["file_path"]):
                        os.remove(target_context["file_path"])
                        logger.info(f"已删除上下文文件: {target_context['file_path']}")
                except Exception as e:
                    logger.error(f"删除上下文文件失败: {str(e)}")
            
            logger.info(f"成功删除上下文: {context_id}")
            return True
            
        except Exception as e:
            import traceback
            error_stack = traceback.format_exc()
            logger.error(f"删除上下文时出错: {str(e)}\n{error_stack}")
            return False
    # endregion Contexts Feat
    def set_enable_cognition_search(self, enable: bool) -> None:
        """设置是否启用认知搜索"""
        if hasattr(self, 'memory'):
            self.memory.set_enable_cognition_search(enable)

    def get_enable_cognition_search(self) -> bool:
        """获取是否启用认知搜索"""
        if hasattr(self, 'memory'):
            return self.memory.get_enable_cognition_search()
        return False  # 默认返回True

    # 用户偏好相关方法
    def set_user_preferences(self, preferences: Dict[str, Any]) -> None:
        """
        设置用户偏好
        
        Args:
            preferences: 偏好设置字典，包含各项偏好及其值（1-5）
        """
        self.memory.set_user_preferences(preferences)
        logger.info(f"Research Agent 设置用户偏好: {preferences}")
    
    def get_user_preferences(self) -> Dict[str, Any]:
        """
        获取当前用户偏好
        
        Returns:
            用户偏好设置字典
        """
        return self.memory.get_user_draft_preferences()
    
    def update_user_preference(self, preferences: Dict[str, Any]) -> None:
        """
        更新单个用户偏好
        
        Args:
            key: 偏好键名
            value: 偏好值（1-5）
        """
        print(f"update_user_preference: {preferences}")
        # import pdb; pdb.set_trace()
        for key, value in preferences.items():
            if key in self.memory.user_draft_preferences:
                self.memory.update_user_draft_preference(key, value)
                logger.info(f"Research Agent 更新偏好 {key} = {value}")
            else:
                logger.warning(f"Research Agent 不支持更新偏好 {key}")

    def _get_draft_preference_prompt(self) -> str:
        """
        获取用户draft偏好提示词
        """
        pref = self.get_user_preferences()
        prompt = user_preference_prompt[self.language]
        if self.language == "zh":
            level2text = {
                1: "极低",
                2: "低",
                3: "中",
                4: "高",
                5: "极高"
            }
        else:
            level2text = {
                1: "Very Low",
                2: "Low",
                3: "Medium",
                4: "High",
                5: "Very High"
            }
        pref_contexts = {
            "user_preference_professional": level2text[pref["professional"]],
            "user_preference_critical": level2text[pref["critical"]],
            "user_preference_comparison": level2text[pref["comparison"]],
            "user_preference_organization": level2text[pref["organization"]],
            "user_preference_cutting_edge": level2text[pref["cutting_edge"]],
            "user_preference_coverage": level2text[pref["coverage"]],
            "user_preference_depth": level2text[pref["depth"]]
        }
        prompt = prompt.format(**pref_contexts)
        return prompt

    def set_enable_search(self, enable: bool) -> None:
        """设置是否启用搜索"""
        if hasattr(self, 'memory'):
            self.memory.set_enable_search(enable)

    def get_enable_search(self) -> bool:
        """获取是否启用搜索"""
        if hasattr(self, 'memory'):
            return self.memory.get_enable_search()
        return True  # 默认启用搜索

    def set_edit_mode(self, enable: bool) -> None:
        """设置是否启用编辑模式（chat to edit）"""
        if hasattr(self, 'memory'):
            self.memory.set_edit_mode(enable)
    
    def get_edit_mode(self) -> bool:
        """获取是否启用编辑模式"""
        if hasattr(self, 'memory'):
            return self.memory.get_edit_mode()
        return False  # 默认关闭编辑模式

    def set_edit_instruction(self, instruction: str) -> None:
        """设置当前的编辑指令"""
        self.current_edit_instruction = instruction
        logger.info(f"设置编辑指令: {instruction[:100]}...")
    
    def get_edit_instruction(self) -> str:
        """获取当前的编辑指令"""
        return self.current_edit_instruction

    def add_user_content_preference(self, url: str, metadata: Dict[str, Any]) -> bool:
        """
        添加用户内容偏好（点赞URL）
        
        Args:
            url: 用户点赞的URL
            metadata: 元数据，包含title、timestamp等信息
            
        Returns:
            bool: 是否成功添加
        """
        try:
            # 检查URL是否已经在点赞列表中
            existing_like = None
            for like in self.memory.user_likes_urls:
                if like.get('url') == url:
                    existing_like = like
                    break
            
            if existing_like:
                logger.info(f"URL已存在于点赞列表中，更新元数据: {url}")
                # 更新现有记录的元数据
                existing_like.update(metadata)
                existing_like['timestamp'] = metadata.get('timestamp', get_cn_time().isoformat())
            else:
                # 构建点赞记录
                like_record = {
                    'url': url,
                    'title': metadata.get('title', ''),
                    'timestamp': metadata.get('timestamp', get_cn_time().isoformat()),
                    'user_id': metadata.get('user_id', ''),
                    'round': self.memory.round,
                    'search_query': '',  # 从research_trajectory中查找对应的搜索查询
                    'new_question': '',  # 如果有用户反馈则添加
                    'page_content': ''   # 从浏览结果中获取页面内容
                }
                
                # 尝试从研究轨迹中找到对应URL的搜索查询和内容
                for url_info in self.memory.research_trajectory.get("useful_urls", []):
                    if url_info.get("url") == url:
                        like_record['search_query'] = url_info.get('query', '')
                        like_record['page_content'] = self._extract_page_content_from_url_info(url_info)
                        break
                
                # 添加最近的用户反馈作为new_question
                if self.memory.research_trajectory.get("user_feedbacks"):
                    latest_feedback = self.memory.research_trajectory["user_feedbacks"][-1]
                    like_record['new_question'] = latest_feedback.get('content', '')
                
                # 添加到用户点赞列表
                self.memory.user_likes_urls.append(like_record)
                
                logger.info(f"成功添加用户点赞URL: {url}, 标题: {like_record['title']}")
            
            # 异步保存到MongoDB
            try:
                if asyncio.get_event_loop().is_running():
                    asyncio.create_task(self.memory.save())
                else:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(self.memory.save())
                    loop.close()
            except Exception as e:
                logger.error(f"保存用户偏好到MongoDB失败: {str(e)}")
            
            return True
            
        except Exception as e:
            logger.error(f"添加用户内容偏好失败: {str(e)}")
            return False

    def remove_user_content_preference(self, url: str) -> bool:
        """
        移除用户内容偏好（取消点赞URL）
        
        Args:
            url: 要取消点赞的URL
            
        Returns:
            bool: 是否成功移除
        """
        try:
            # 查找并移除对应的点赞记录
            original_count = len(self.memory.user_likes_urls)
            self.memory.user_likes_urls = [
                like for like in self.memory.user_likes_urls 
                if like.get('url') != url
            ]
            
            removed_count = original_count - len(self.memory.user_likes_urls)
            
            if removed_count > 0:
                logger.info(f"成功移除用户点赞URL: {url}, 移除了{removed_count}条记录")
                
                # 异步保存到MongoDB
                try:
                    if asyncio.get_event_loop().is_running():
                        asyncio.create_task(self.memory.save())
                    else:
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        loop.run_until_complete(self.memory.save())
                        loop.close()
                except Exception as e:
                    logger.error(f"保存用户偏好到MongoDB失败: {str(e)}")
                
                return True
            else:
                logger.warning(f"未找到要移除的点赞URL: {url}")
                return False
                
        except Exception as e:
            logger.error(f"移除用户内容偏好失败: {str(e)}")
            return False

    def _extract_page_content_from_url_info(self, url_info: Dict[str, Any]) -> str:
        """
        从URL信息中提取页面内容
        
        Args:
            url_info: URL信息字典
            
        Returns:
            str: 提取的页面内容
        """
        content_parts = []
        
        # 添加摘要信息
        summaries = url_info.get('summaries', [])
        if summaries:
            content_parts.extend(summaries)
        
        # 添加有用信息
        useful_info = url_info.get('useful_information', [])
        if useful_info:
            content_parts.extend(useful_info)
        
        # 如果没有内容，使用snippet作为fallback
        if not content_parts:
            snippet = url_info.get('snippet', '')
            if snippet:
                content_parts.append(snippet)
        
        # 合并内容，限制长度
        combined_content = '\n'.join(content_parts)
        
        # 限制内容长度为1000字符
        if len(combined_content) > 1000:
            combined_content = combined_content[:1000] + "..."
        
        return combined_content
