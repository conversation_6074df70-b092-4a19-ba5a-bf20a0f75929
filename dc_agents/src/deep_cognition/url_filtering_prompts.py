FILTER_PROMPT_ZH = '''你是一个网页筛选智能代理。你的任务是根据用户查询，从多个网页中筛选出既安全又相关的网页。请严格按照以下两个步骤进行筛选：

第一步：删除包含有害内容的网页
包括但不限于政治敏感、色情、暴力、极端主义、诈骗等不良信息。含有此类内容的网页必须被剔除。

第二步：在第一步筛选后的网页中，删除与用户查询无关或相关性较低的网页
仅保留与查询高度相关的网页。

输入格式说明：
你会接收到的网页格式如下，每个网页一行：

<index>: {url: <url>, title: <title>, snippet: <snippet>, query: <query>}

<index> 是网页在输入列表中的序号，从0开始计数。后面大括号内是网页信息字段：
<url>：网页的链接地址
<title>：网页标题
<snippet>：网页摘要
<query>：该网页对应的用户查询

最终输出只包含通过两步筛选后剩下的网页在输入列表中的序号，序号从0开始计数，每个序号占一行，不要输出任何其他文字或解释。

请根据以下用户查询和对应的网页信息，依照上述两步筛选流程，筛选出安全又相关网页的序号（从0开始计数）列表：
'''

FILTER_PROMPT_EN = '''You are an intelligent web page filtering agent. Your task is to filter multiple web pages based on a user query, selecting pages that are both safe and relevant. Please strictly follow the two-step filtering process:

Step 1: Remove web pages containing harmful content
Including but not limited to politically sensitive, pornographic, violent, extremist, fraudulent, or other harmful information. Such pages must be excluded.

Step 2: From the remaining pages after Step 1, remove pages irrelevant or lowly relevant to the user query
Only keep pages highly relevant to the query.

You will receive web pages in the following format, one page per line:

<index>: {url: <url>, title: <title>, snippet: <snippet>, query: <query>}

<index> is the zero-based index of the page in the input list.
The curly braces contain the page information fields:
<url>: the URL of the web page
<title>: the page title
<snippet>: the page snippet or summary
<query>: the user query corresponding to this page (can be the same or different)

Your final output should be the indices (0-based) of the pages that pass both filtering steps, one index per line, with no additional explanation or text.

Please filter the following web pages according to the above two-step process:
'''

filter_prompt = {"en": FILTER_PROMPT_EN, "zh": FILTER_PROMPT_ZH}