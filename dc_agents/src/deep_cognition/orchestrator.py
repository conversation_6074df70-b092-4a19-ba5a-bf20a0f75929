from typing import Dict, Any, AsyncGenerator
import asyncio
from fast_langdetect import detect

from dc_agents.src.utils import get_content_from_tag, get_current_date_str
from dc_agents.src.services.llm_service import LLMService
from dc_agents.src.services.search_service import SearchService
from dc_agents.src.deep_cognition.research_agent import ResearchAgent
from dc_agents.src.deep_cognition.browse_agent import BrowseAgent
from dc_agents.src.deep_cognition.orchestrator_prompts import (
    intent_prompt, clarification_prompt, question_rewriter_prompt,
    system_prompt
)
from loguru import logger


class Orchestrator:
    """
    Orchestrator agent that analyzes questions, asks for clarifications, 
    and refines questions for the research agent.
    """
    
    def __init__(self, 
                 service_config: str,
                 agents_config: str):
        """
        Initialize the Orchestrator agent.
        
        Args:
            llm_config: The path to the LLM configuration file
            agents_config: The path to the agents configuration file
        """
        self.name = "Orchestrator"

        self.llms = {}
        for model, _config in service_config["llms"].items():
            self.llms[model] = LLMService(_config)
        
        search_service = SearchService(config=service_config["search_service"])
        
        self.intent_model_config = agents_config["orchestrator"]["intent_model"]
        self.clarification_model_config = agents_config["orchestrator"]["clarification_model"]
        self.question_rewriter_model_config = agents_config["orchestrator"]["question_rewriter_model"]

        self.intent_model = self.llms[self.intent_model_config["name"]]
        self.clarification_model = self.llms[self.clarification_model_config["name"]]
        self.question_rewriter_model = self.llms[self.question_rewriter_model_config["name"]]

        browse_agent = BrowseAgent(name="Browse", config=agents_config["browse_agent"], llm_services=self.llms)

        self.research_agent = ResearchAgent(
            name="Research", 
            config=agents_config["research_agent"], 
            llm_services=self.llms, 
            search_service=search_service, 
            browse_agent=browse_agent
        )

        self.language = "zh"
        self.turns = []

        self.rewritten_question = ""

        self.is_researching = False
        self.is_paused = asyncio.Event()
    
    async def run(self, input_data: str) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Run the orchestrator agent on the input data.
        
        Args:
            input_data: Dictionary containing:
                - question: The user's original question
                
        Returns:
            A generator of chunks of the response
        """
        print(f"orchestrator is paused: {self.is_paused.is_set()}")
        if self.is_paused.is_set():
            yield {
                "agent": self.name,
                "stage": "orchestrator",
                "content_type": "action",
                "action": "pause",
                "content": "The research is paused."
            }
            return

        question = input_data['content']
        if len(self.turns) == 0:
            # initial stage
            try:
                language = detect(question.replace("\n", " "))['lang']
                if language != "zh":
                    self.language = "en"
                else:
                    self.language = language
            except Exception as e:
                logger.error(f"Error detecting language: {e}")
                self.language = "zh"
            # intent = await self.get_intent(question)

            self.turns.append({
                "role": "user",
                "content": question,
                # "intent": intent
            })
        
            # if intent == "deep_research":
            #     async for chunk in self.ask_for_clarification(question):
            #         yield chunk
            # elif intent == "paper_reading":
            #     pass
            # else:
            #     pass

            async for chunk in self.ask_for_clarification(question):
                if self.is_paused.is_set():
                    return
                yield chunk
        
        elif self.turns[-1]["action"] == "ask_for_clarification":
            # Rewrite the question
            self.rewritten_question = await self.rewrite_question(question)

            async for chunk in self.start_deep_research(self.rewritten_question):
                if self.is_paused.is_set():
                    return
                yield chunk


    async def get_intent(self, question: str) -> str:
        """
        Get the intent of the question.
        """
        prefix_prompt = system_prompt[self.language].format(time_str=get_current_date_str())
        prompt = prefix_prompt + intent_prompt[self.language].format(question=question)
        
        response = await self.intent_model.get_completion(
            messages=[{"role": "user", "content": prompt}],
            temperature=0.6,
            max_tokens=15
        )
        content = ""
        async for chunk in response:
            if hasattr(chunk.choices[0].delta, "content") and chunk.choices[0].delta.content is not None:
                content += chunk.choices[0].delta.content

        intent = "other"
        if content is not None:
            intent = get_content_from_tag(content, "intent").lower()
            if intent not in ["deep_research", "paper_reading", "other"]:
                intent = "other"
        return intent

    
    async def ask_for_clarification(self, question: str) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Ask for clarification of the question.
        """
        prefix_prompt = system_prompt[self.language].format(time_str=get_current_date_str())
        prompt = prefix_prompt + clarification_prompt[self.language].format(question=question)

        response = await self.clarification_model.get_completion(
            messages=[{"role": "user", "content": prompt}],
            temperature=0.6,
            max_tokens=1000
        )

        clarification_question = ""
        async for chunk in response:
            if self.is_paused.is_set():
                return
            if chunk.choices[0].delta is None:
                continue
            if hasattr(chunk.choices[0].delta, "reasoning_content") and chunk.choices[0].delta.reasoning_content is not None:
                yield {
                    "agent": self.name,
                    "stage": "clarification",
                    "content_type": "action",
                    "action": "think",
                    "content": chunk.choices[0].delta.reasoning_content
                }
            elif hasattr(chunk.choices[0].delta, "content") and chunk.choices[0].delta.content is not None:
                yield {
                    "agent": self.name,
                    "stage": "clarification",
                    "content_type": "action",
                    "action": "answer",
                    "content": chunk.choices[0].delta.content
                }
                clarification_question += chunk.choices[0].delta.content

        self.turns.append({
            "role": "assistant",
            "content": clarification_question,
            "action": "ask_for_clarification"
        })

    async def rewrite_question(self, question: str) -> str:
        """
        Rewrite the question.
        """
        messages = [
            {"role": "user", "content": self.turns[-2]["content"]},
            {"role": "assistant", "content": self.turns[-1]["content"]},
            {"role": "user", "content": question}
        ]
        question_conversation_str = ""
        for message in messages:
            question_conversation_str += f"**{message['role']}**: {message['content']}\n"

        prefix_prompt = system_prompt[self.language].format(time_str=get_current_date_str())
        prompt = prefix_prompt + question_rewriter_prompt[self.language].format(conversation=question_conversation_str)

        response = await self.question_rewriter_model.get_completion(
            messages=[{"role": "user", "content": prompt}],
            temperature=self.question_rewriter_model_config["temperature"],
            max_tokens=self.question_rewriter_model_config["max_tokens"]
        )

        content = ""
        async for chunk in response:
            if self.is_paused.is_set():
                return
            if hasattr(chunk.choices[0].delta, "content") and chunk.choices[0].delta.content is not None:
                content += chunk.choices[0].delta.content
        rewritten_question = get_content_from_tag(content, "question")
        return rewritten_question

    async def start_deep_research(self, question: str) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Start the deep research process.
        """
        if self.is_paused.is_set():
            return

        logger.info(f"Starting deep research with question: {question}")
        self.is_researching = True
        
        async for chunk in self.research_agent.run(input_data={"question": question, "language": self.language}):
            if self.is_paused.is_set():
                return
            yield chunk

    async def pause_research(self):
        """
        Pause the research process.
        """

        if not self.is_researching:
            return
        
        if self.is_paused.is_set():
            return

        self.is_paused.set()
        await self.research_agent.pause()
        
    async def resume_research(self, user_feedback: str):
        """
        Resume the research process.
        """
        if not self.is_researching:
            return
        
        if not self.is_paused.is_set():
            return

        logger.info(f"Resuming research with feedback: {user_feedback}")
        self.is_paused.clear()
        async for chunk in self.research_agent.resume(user_feedback):
            if self.is_paused.is_set():
                break
            yield chunk
