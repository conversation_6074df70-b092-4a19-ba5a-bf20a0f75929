import os
from typing import Dict, Any, Optional
from loguru import logger
from .context import BaseContext
from ..tools.file_convert import FileConverterFactory, convert_file_to_text

class FileContext(BaseContext):
    """
    基于文件的上下文实现
    """
    
    def __init__(self, file_path: str):
        """
        初始化文件上下文
        
        Args:
            file_path: 文件路径
        """
        super().__init__(os.path.basename(file_path))
        self.file_path = file_path
        self.content = ""
        self.file_converter_factory = FileConverterFactory()
        self.metadata.update({
            "file_path": file_path,
            "file_size": 0,
            "file_type": os.path.splitext(file_path)[1],
            "last_modified": None,
            "type": "file"
        })
        
    def check_file_status(self) -> Dict[str, Any]:
        """
        检查文件状态
        
        Returns:
            Dict[str, Any]: 包含文件状态信息的字典
            {
                "is_valid": bool,  # 文件是否有效
                "exists": bool,    # 文件是否存在
                "is_readable": bool,  # 文件是否可读
                "error": str,      # 如果有错误，错误信息
                "file_size": int,  # 文件大小（字节）
                "last_modified": float  # 最后修改时间戳
            }
        """
        status = {
            "is_valid": False,
            "exists": False,
            "is_readable": False,
            "error": None,
            "file_size": 0,
            "last_modified": None
        }
        
        try:
            # 检查文件是否存在
            if not os.path.exists(self.file_path):
                status["error"] = "文件不存在"
                return status
                
            status["exists"] = True
            
            # 检查文件是否可读
            if not os.access(self.file_path, os.R_OK):
                status["error"] = "文件不可读"
                return status
                
            status["is_readable"] = True
            
            # 获取文件信息
            file_stat = os.stat(self.file_path)
            status["file_size"] = file_stat.st_size
            status["last_modified"] = file_stat.st_mtime
            
            # 如果文件存在且可读，则认为是有效的
            status["is_valid"] = True
            
            return status
            
        except Exception as e:
            status["error"] = f"检查文件状态时出错: {str(e)}"
            return status
            
    def get_context(self) -> str:
        """
        获取文件内容作为上下文，支持多种文件格式
        
        Returns:
            文件内容字符串
        """
        if not self.content:
            try:
                # 使用文件转换器工厂处理文件
                content = convert_file_to_text(self.file_path)
                if content is None:
                    logger.error(f"无法转换文件内容: {self.file_path}")
                    return ""
                    
                self.content = content
                
                # 更新元数据
                self.metadata.update({
                    "file_size": len(self.content),
                    "last_modified": os.path.getmtime(self.file_path)
                })
                
                # 添加文件类型信息
                converter = self.file_converter_factory.get_converter(self.file_path)
                if converter:
                    self.metadata["converter_type"] = converter.__class__.__name__
                
            except Exception as e:
                logger.error(f"读取文件失败: {str(e)}")
                return ""
                
        return self.content
        
    def add_context(self, content: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        添加内容到文件
        
        Args:
            content: 要添加的内容
            metadata: 可选的元数据
            
        Returns:
            是否添加成功
        """
        try:
            with open(self.file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            self.content = content
            
            # 更新元数据
            self.metadata.update({
                "file_size": len(content),
                "last_modified": os.path.getmtime(self.file_path)
            })
            
            if metadata:
                self.update_metadata(metadata)
                
            return True
        except Exception as e:
            logger.error(f"写入文件失败: {str(e)}")
            return False
    
    def get_file_binary(self) -> bytes:
        """
        获取文件的二进制内容
        """
        file_path = self.metadata["file_path"] or self.file_path
        with open(file_path, 'rb') as f:
            return f.read()
