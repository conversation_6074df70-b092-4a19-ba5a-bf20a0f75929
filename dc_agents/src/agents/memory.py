from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timezone, timedelta
import os
import json
import hashlib
from abc import ABC, abstractmethod
from loguru import logger

from dc_agents.src.models.memory import MemoryRecord
from dc_agents.src.utils import get_cn_time


class BaseMemory(ABC):
    """
    代理系统的基础内存管理类

    管理代理的记忆、历史记录和状态追踪
    """

    def __init__(self, agent_name: str = "Generic", conversation_id: str = None):
        """
        初始化基础内存类

        Args:
            agent_name: 使用此内存的代理名称
        """
        self.agent_name = agent_name
        # 通用记录列表，用于记录所有轨迹和历史
        self.memory_records = []
        # 每次对话的唯一ID
        self.conversation_id = conversation_id
        # 保存路径的基础目录
        self._base_save_dir = None

    def record_event(self, event_type: str, content: Any, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        记录一个事件到内存记录中

        Args:
            event_type: 事件类型
            content: 事件内容
            metadata: 可选的元数据

        Returns:
            记录的事件数据
        """
        record = MemoryRecord(
            type=event_type,
            timestamp=get_cn_time().isoformat(),
            content=content,
            metadata=metadata
        )
            
        self.memory_records.append(record)
        return record.model_dump()

    def get_recent_events(self, event_type: Optional[str] = None, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取最近的事件记录

        Args:
            event_type: 可选的事件类型过滤器
            limit: 要返回的最大记录数

        Returns:
            最近的事件记录列表
        """
        if event_type:
            filtered_records = [r for r in self.memory_records if r.type == event_type]
            result = filtered_records[-limit:] if filtered_records else []
        else:
            result = self.memory_records[-limit:] if self.memory_records else []
            
        # 转换为字典格式返回
        return [record.model_dump() if hasattr(record, 'model_dump') else record for record in result]

    def clear_memory(self) -> None:
        """
        清除内存中的所有记录
        """
        self.memory_records = []

    @property
    def base_save_dir(self) -> str:
        """
        获取内存记录的基本保存目录
        """
        if not self._base_save_dir:
            self._base_save_dir = os.path.join(
                os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                f"{self.agent_name.lower()}_memories"
            )
            os.makedirs(self._base_save_dir, exist_ok=True)
        return self._base_save_dir

    def set_conversation_id(self, conversation_id: Optional[str] = None) -> str:
        """
        设置对话ID

        Args:
            conversation_id: 可选的自定义对话ID

        Returns:
            实际使用的对话ID
        """
        if not conversation_id:
            timestamp = get_cn_time().strftime("%Y%m%d%H%M%S")
            conversation_id = f"{self.agent_name}_{timestamp}"
        
        self.conversation_id = conversation_id
        return conversation_id

    def save_memory(self, filepath: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        保存内存记录到文件

        Args:
            filepath: 可选的自定义文件路径
            metadata: 可选的附加元数据

        Returns:
            保存的文件路径
        """
        if not self.memory_records:
            logger.warning("没有内存记录可保存")
            return ""

        # 如果没有提供保存路径，则生成一个
        if not filepath:
            # if not self.conversation_id:
            #     self.set_conversation_id()
                
            filepath = os.path.join(self.base_save_dir, f"{self.conversation_id}.json")

        # 准备保存数据
        save_data = {
            "metadata": {
                "agent_name": self.agent_name,
                "timestamp": get_cn_time().isoformat(),
                "conversation_id": self.conversation_id,
            },
            "memory_records": [r.model_dump() for r in self.memory_records]
        }
        
        # 添加任何附加元数据
        if metadata:
            save_data["metadata"].update(metadata)
            
        # 保存到文件
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            logger.info(f"内存记录已保存至: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"保存内存记录失败: {str(e)}")
            return ""
            
    def load_memory(self, filepath: str) -> bool:
        """
        从文件加载内存记录

        Args:
            filepath: 要加载的文件路径

        Returns:
            是否成功加载
        """
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            if "memory_records" in data:
                self.memory_records = [MemoryRecord(**r) for r in data["memory_records"]]
                
                # 获取元数据中的对话ID
                if "metadata" in data and "conversation_id" in data["metadata"]:
                    self.conversation_id = data["metadata"]["conversation_id"]
                    
                logger.info(f"从 {filepath} 加载了 {len(self.memory_records)} 条内存记录")
                return True
            else:
                logger.warning(f"文件 {filepath} 中没有找到内存记录")
                return False
                
        except Exception as e:
            logger.error(f"加载内存记录失败: {str(e)}")
            return False

    @abstractmethod
    def summarize(self) -> str:
        """
        总结内存记录内容

        Returns:
            内存记录的摘要
        """
        pass 