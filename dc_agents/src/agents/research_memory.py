from typing import Dict, Any, List, Optional, Union, AsyncGenerator
from datetime import datetime, timezone, timedelta
import os
import json
import hashlib
import asyncio
import time
from loguru import logger

from backend.db.mongodb import MongoDBBase
from dc_agents.src.models.research_memory import ResearchMemoryModel
from dc_agents.src.agents.memory import BaseMemory
from dc_agents.src.models.memory import MemoryRecord
from dc_agents.src.utils import get_cn_time
from dc_agents.src.agents.context import BaseContext
from dc_agents.src.agents.file_context import FileContext

# 尝试导入Redis客户端，如果失败则禁用Redis功能
try:
    from backend.redis.dependencies import get_redis_client
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logger.warning("Redis客户端不可用，将仅使用MongoDB存储")


class ResearchMemory(BaseMemory):
    """
    研究代理的内存管理类
    
    扩展基本内存管理功能，专门用于研究轨迹的记录、存储和检索
    基于MongoDB实现持久化存储，使用Redis进行分层缓存优化
    """
    
    def __init__(self, conversation_id: str, agent_name: str = "Researcher"):
        """
        初始化研究型代理的内存管理类
        
        Args:
            conversation_id: 对话ID
            agent_name: 代理名称
        """
        # 初始化基础内存类
        super().__init__(agent_name, conversation_id)
        
        # 存储格式版本控制
        self.STORAGE_VERSION_SINGLE = "v1.0"      # 传统单文档存储版本
        self.STORAGE_VERSION_SEPARATED = "v2.0"   # 分离存储版本
        self.current_storage_version = self.STORAGE_VERSION_SEPARATED  # 默认使用新版本
        self.detected_storage_version = None      # 从数据库检测到的版本
        
        # Redis分层缓存相关初始化
        self._init_redis_cache()
        
        # 数据分离存储策略
        self._init_data_separation()
        
        # 研究轨迹相关属性（核心数据）
        self.research_question = ""              # 研究问题
        self.final_report = ""                   # 最终报告
        
        # 完整研究轨迹（保留所有历史数据，通过分层缓存优化性能）
        self.research_trajectory = {
            "initial_question": "",                # 初始问题
            "action_reasoning": [],                # 每轮select_action的思考过程（完整历史）
            "useful_urls": [],                     # 有用的URL及其信息（完整历史）
            "user_feedbacks": [],                  # 用户反馈（完整历史）
            "clarification_qa": [],                # 澄清问题与回答（完整历史）
            "search_queries": [],                  # 搜索查询（完整历史）
            "rounds": [],                           # 每轮详细信息（完整历史）
            "enable_cognition_search": True,        # 默认启用认知搜索
            "enable_search": True                  # 默认启用搜索
        }
        
        # token使用统计
        self.token_usage = {
            "total_input_tokens": 0,
            "total_output_tokens": 0,
            "total_cost": 0,
            "models_usage": {}  # 按模型名称记录使用情况
        }
        
        # 上下文管理（轻量级）
        self.contexts: List[BaseContext] = []      # 仅保留上下文元数据，内容按需加载
        
        # 研究状态控制变量
        self.language = None                     # 语言
        self.round = 0                           # 当前轮次
        self.search_times = 0                    # 搜索次数
        self.max_search_times = 5              # 最大搜索次数
        self.is_paused = False                   # 是否暂停
        
        # 报告相关状态
        self.report_draft = ""                   # 报告草稿
        self.draft_history = []                  # 草稿历史（完整版本历史）
        self.draft_version = 0                   # 草稿版本
        self.is_updating_report = False          # 是否正在更新报告
        
        # 澄清问题相关状态
        self.clarification_questions = []        # 存储完整的问题-回答对（完整历史）
        self.pending_clarification_questions = [] # 记录模型提出的问题
        
        # 浏览结果相关
        self.last_browse_result = None           # 最后一次浏览结果
        
        # 用户的偏好
        self.user_preferences = []            # 用户偏好
        self.user_likes_urls = []             # 用户点赞的URL
        
        # 用户偏好相关
        self.user_draft_preferences = {
            "professional": 3,      # 专业性 (1-5)
            "critical": 3,          # 批判性 (1-5) 
            "comparison": 3,        # 表格对比 (1-5)
            "organization": 3,      # 组织性 (1-5)
            "cutting_edge": 3,      # 前沿性 (1-5)
            "coverage": 3,          # 覆盖面 (1-5)
            "depth": 3              # 深度 (1-5)
        }

        # MongoDB连接相关
        self.collection_name = f"{agent_name.lower()}_research_memories"
        try:
            self.db = MongoDBBase(self.collection_name, ResearchMemoryModel)
            self.mongodb_available = True
        except Exception as e:
            logger.error(f"MongoDB连接失败: {str(e)}")
            self.mongodb_available = False
            
        # 确保 memory_records 初始化为空列表
        self.memory_records = []
            
        # 当前文档在MongoDB中的ID
        self.memory_document_id = None
        
        # 数据加载状态标记
        self._is_loaded_from_storage = False
        self._is_loading = False
    
    def _init_data_separation(self):
        """初始化数据分离存储策略"""
        # 数据分离键前缀
        self._core_data_prefix = f"core:{self.agent_name}:{self.conversation_id}"
        self._messages_prefix = f"messages:{self.agent_name}:{self.conversation_id}"
        self._contexts_prefix = f"contexts:{self.agent_name}:{self.conversation_id}"
        self._history_prefix = f"history:{self.agent_name}:{self.conversation_id}"
        
        # 移除数据大小限制配置 - 不应该限制历史数据
        # 分页配置（仅用于懒加载，不限制总量）
        self._message_page_size = 50         # 消息分页大小
        self._context_page_size = 10         # 上下文分页大小
        
        # 自动迁移配置
        self._auto_migrate_enabled = False   # 默认关闭自动迁移，保证数据安全
    
    def _limit_list_size(self, data_list: list, max_size: int) -> list:
        """移除数据大小限制 - 保持完整历史数据"""
        # 不再限制数据大小，返回完整列表
        return data_list
    
    def _cleanup_trajectory_data(self):
        """移除数据清理逻辑 - 保持完整历史数据"""
        # 不再清理数据，保持完整的研究轨迹
        pass
    
    def _init_redis_cache(self):
        """初始化Redis分层缓存相关配置"""
        self._redis_enabled = REDIS_AVAILABLE
        self._redis_client = None
        self._redis_key_prefix = f"research_memory:{self.agent_name}:{self.conversation_id}"
        
        # 分层缓存配置
        self._l1_cache_ttl = 30 * 60          # L1缓存30分钟过期（热数据）
        self._l2_cache_ttl = 4 * 60 * 60      # L2缓存4小时过期（温数据）
        self._persistent_cache_ttl = 24 * 60 * 60  # 持久缓存24小时过期（核心数据）
        
        # 保存控制参数（针对公网MongoDB优化）
        self._auto_save_interval = 1800       # 30分钟自动保存一次到MongoDB（增加间隔）
        self._batch_save_threshold = 512       # 累积512次变更后强制保存（提高阈值）
        self._critical_save_threshold = 128     # 累积128次关键变更后强制保存（提高阈值）
        
        # 防抖控制参数（新增）
        self._debounce_delay = 30             # 防抖延迟30秒
        self._last_save_request_time = 0      # 上次保存请求时间
        self._pending_save_task = None        # 待执行的保存任务
        self._max_debounce_time = 300         # 最大防抖时间5分钟，超过后强制保存
        
        # 分离存储和增量更新策略（替代数据压缩）
        self._enable_data_compression = False  # 禁用数据压缩，保持数据完整性
        self._enable_incremental_save = True   # 启用增量保存
        self._enable_separated_storage = True  # 启用分离存储
        
        # 分离存储的MongoDB集合配置
        self._collections = {
            "core": f"{self.agent_name.lower()}_sessions",           # 核心会话信息
            "trajectory": f"{self.agent_name.lower()}_trajectory",   # 研究轨迹
            "memory": f"{self.agent_name.lower()}_memory_records",   # 内存记录
            "contexts": f"{self.agent_name.lower()}_contexts",       # 上下文信息
            "preferences": f"{self.agent_name.lower()}_preferences"  # 用户偏好
        }
        
        # 增量更新追踪
        self._dirty_fields = {
            "core": set(),          # 核心信息变更字段
            "trajectory": set(),    # 轨迹变更字段  
            "memory": [],          # 新增的内存记录
            "contexts": [],        # 新增/更新的上下文
            "preferences": []      # 新增/更新的偏好
        }
        
        # 最后同步时间戳
        self._last_sync_timestamps = {
            "core": 0,
            "trajectory": 0, 
            "memory": 0,
            "contexts": 0,
            "preferences": 0
        }
        
        # 保存控制状态
        self._changes_count = 0              # 记录未保存的变更次数
        self._critical_changes_count = 0     # 记录关键变更次数
        self._last_mongodb_save = time.time() # 上次保存到MongoDB的时间
        self._save_timer = None              # 定时保存任务
        self._is_saving_to_mongodb = False   # 是否正在保存到MongoDB中
        
        # MongoDB操作优化（针对公网访问优化）
        self._mongodb_connection_timeout = 8000    # 8秒连接超时（略增加）
        self._mongodb_query_timeout = 35000        # 35秒查询超时（大幅减少）
        self._mongodb_retry_count = 3              # 重试3次
        self._mongodb_retry_delay = 2              # 重试间隔2秒
        
        if self._redis_enabled:
            try:
                self._redis_client = get_redis_client()
                logger.debug(f"Redis分层缓存已启用: {self._redis_key_prefix}")
                # 启动定时保存任务
                self._start_auto_save_timer()
            except Exception as e:
                logger.warning(f"Redis客户端初始化失败，禁用缓存功能: {str(e)}")
                self._redis_enabled = False
    
    async def _get_redis_key(self, key_type: str = "core", suffix: str = "") -> str:
        """获取Redis键名"""
        if key_type == "core":
            base_key = self._core_data_prefix
        elif key_type == "messages":
            base_key = self._messages_prefix
        elif key_type == "contexts":
            base_key = self._contexts_prefix
        elif key_type == "history":
            base_key = self._history_prefix
        else:
            base_key = f"{self._redis_key_prefix}:{key_type}"
            
        if suffix:
            return f"{base_key}:{suffix}"
        return base_key
    
    async def _save_core_data_to_redis(self) -> bool:
        """将核心数据保存到Redis L1缓存"""
        if not self._redis_enabled or not self._redis_client:
            return False
            
        try:
            # 清理数据
            self._cleanup_trajectory_data()
            
            # 准备核心数据（不包含大容量数据）
            core_data = {
                "agent_name": self.agent_name,
                "conversation_id": self.conversation_id,
                "timestamp": get_cn_time().isoformat(),
                "research_question": self.research_question,
                "final_report": self.final_report,
                "research_trajectory": self.research_trajectory,
                "token_usage": self.token_usage,
                "language": self.language,
                "round": self.round,
                "search_times": self.search_times,
                "max_search_times": self.max_search_times,
                "is_paused": self.is_paused,
                "report_draft": self.report_draft,
                "draft_version": self.draft_version,
                "is_updating_report": self.is_updating_report,
                "clarification_questions": self.clarification_questions,
                "pending_clarification_questions": self.pending_clarification_questions,
                "user_preferences": self.user_preferences,
                "user_likes_urls": self.user_likes_urls,
                "user_draft_preferences": self.user_draft_preferences,
                "research_rules": getattr(self, 'research_rules', []),
                "last_browse_result": self.last_browse_result,
                "cached_at": time.time()
            }
            
            # 保存到Redis L1缓存
            core_key = await self._get_redis_key("core")
            success = await self._redis_client.set(core_key, core_data, ex=self._l1_cache_ttl)
            
            if success:
                # logger.debug(f"核心数据已保存到Redis L1缓存: {core_key}")
                return True
            else:
                logger.error(f"保存核心数据到Redis失败: {core_key}")
                return False
                
        except Exception as e:
            logger.error(f"保存核心数据到Redis时出错: {str(e)}")
            return False
    
    async def _save_messages_to_redis(self) -> bool:
        """将完整消息数据保存到Redis L2缓存（分页存储，但不限制总量）"""
        if not self._redis_enabled or not self._redis_client:
            return False
            
        try:
            # 消息数据分页存储（不限制总量）
            messages_data = [r.model_dump() if hasattr(r, 'model_dump') else r for r in self.memory_records]
            
            # 按页存储
            total_pages = (len(messages_data) + self._message_page_size - 1) // self._message_page_size
            
            for page in range(total_pages):
                start_idx = page * self._message_page_size
                end_idx = min(start_idx + self._message_page_size, len(messages_data))
                page_data = messages_data[start_idx:end_idx]
                
                page_key = await self._get_redis_key("messages", f"page_{page}")
                await self._redis_client.set(page_key, {
                    "page": page,
                    "total_pages": total_pages,
                    "data": page_data,
                    "cached_at": time.time()
                }, ex=self._l2_cache_ttl)
            
            # 保存消息元数据
            meta_key = await self._get_redis_key("messages", "meta")
            await self._redis_client.set(meta_key, {
                "total_messages": len(messages_data),
                "total_pages": total_pages,
                "page_size": self._message_page_size,
                "last_updated": time.time()
            }, ex=self._l2_cache_ttl)
            
            logger.debug(f"完整消息数据已保存到Redis L2缓存: {total_pages}页, 共{len(messages_data)}条消息")
            return True
            
        except Exception as e:
            logger.error(f"保存消息数据到Redis时出错: {str(e)}")
            return False
    
    async def _save_contexts_to_redis(self) -> bool:
        """将上下文元数据保存到Redis（内容按需加载）"""
        if not self._redis_enabled or not self._redis_client:
            return False
            
        try:
            # 只保存上下文元数据，不保存内容
            contexts_meta = []
            for ctx in self.contexts:
                meta = {
                    "context_id": ctx.context_id,
                    "metadata": ctx.get_metadata(),
                    "content_size": len(ctx.get_context()) if hasattr(ctx, 'get_context') else 0
                }
                contexts_meta.append(meta)
            
            contexts_key = await self._get_redis_key("contexts")
            success = await self._redis_client.set(contexts_key, {
                "contexts_meta": contexts_meta,
                "total_contexts": len(contexts_meta),
                "cached_at": time.time()
            }, ex=self._l2_cache_ttl)
            
            if success:
                logger.debug(f"上下文元数据已保存到Redis: {len(contexts_meta)}个")
            return success
            
        except Exception as e:
            logger.error(f"保存上下文元数据到Redis时出错: {str(e)}")
            return False
    
    async def _load_core_data_from_redis(self) -> bool:
        """从Redis L1缓存加载核心数据"""
        if not self._redis_enabled or not self._redis_client:
            return False
            
        try:
            core_key = await self._get_redis_key("core")
            cache_data = await self._redis_client.get(core_key)
            
            if not cache_data:
                logger.debug(f"Redis L1缓存中没有找到核心数据: {core_key}")
                return False
            
            # 检查缓存是否过期
            cached_at = cache_data.get("cached_at", 0)
            if time.time() - cached_at > self._l1_cache_ttl:
                logger.warning(f"Redis L1缓存已过期: {core_key}")
                await self._redis_client.delete(core_key)
                return False
            
            # 加载核心数据到当前实例
            self.agent_name = cache_data.get("agent_name", self.agent_name)
            self.conversation_id = cache_data.get("conversation_id", self.conversation_id)
            self.research_question = cache_data.get("research_question", "")
            self.final_report = cache_data.get("final_report", "")
            self.research_trajectory = cache_data.get("research_trajectory", {
                "initial_question": "",
                "action_reasoning": [],
                "useful_urls": [],
                "user_feedbacks": [],
                "clarification_qa": [],
                "search_queries": [],
                "rounds": [],
                "enable_cognition_search": True,
                "enable_search": True
            })
            
            # 加载其他核心属性
            self.token_usage = cache_data.get("token_usage", {
                "total_input_tokens": 0,
                "total_output_tokens": 0,
                "total_cost": 0,
                "models_usage": {}
            })
            self.language = cache_data.get("language")
            self.round = cache_data.get("round", 0)
            self.search_times = cache_data.get("search_times", 0)
            self.max_search_times = cache_data.get("max_search_times", 5)
            self.is_paused = cache_data.get("is_paused", False)
            self.report_draft = cache_data.get("report_draft", "")
            self.draft_version = cache_data.get("draft_version", 0)
            self.is_updating_report = cache_data.get("is_updating_report", False)
            self.clarification_questions = cache_data.get("clarification_questions", [])
            self.pending_clarification_questions = cache_data.get("pending_clarification_questions", [])
            self.user_preferences = cache_data.get("user_preferences", [])
            self.user_likes_urls = cache_data.get("user_likes_urls", [])
            self.user_draft_preferences = cache_data.get("user_draft_preferences", {})
            self.research_rules = cache_data.get("research_rules", [])
            self.last_browse_result = cache_data.get("last_browse_result")
            
            logger.info(f"从Redis L1缓存加载核心数据成功: {core_key}")
            return True
            
        except Exception as e:
            logger.error(f"从Redis L1缓存加载核心数据时出错: {str(e)}")
            return False
    
    async def _load_messages_from_redis_lazy(self) -> bool:
        """懒加载消息数据（仅在需要时加载）"""
        if not self._redis_enabled or not self._redis_client:
            return False
            
        try:
            # 先检查消息元数据
            meta_key = await self._get_redis_key("messages", "meta")
            meta_data = await self._redis_client.get(meta_key)
            
            if not meta_data:
                logger.debug("Redis中没有找到消息元数据")
                return False
            
            total_messages = meta_data.get("total_messages", 0)
            total_pages = meta_data.get("total_pages", 0)
            
            if total_messages == 0:
                self.memory_records = []
                return True
            
            # 只加载最近的一页消息
            if total_pages > 0:
                last_page = total_pages - 1
                page_key = await self._get_redis_key("messages", f"page_{last_page}")
                page_data = await self._redis_client.get(page_key)
                
                if page_data and "data" in page_data:
                    # 重建内存记录
                    self.memory_records = []
                    for record in page_data["data"]:
                        if isinstance(record, dict):
                            self.memory_records.append(MemoryRecord(**record))
                        else:
                            self.memory_records.append(record)
                    
                    logger.debug(f"懒加载最近的消息数据成功: {len(self.memory_records)}条")
                    return True
                    
            return False
            
        except Exception as e:
            logger.error(f"懒加载消息数据时出错: {str(e)}")
            return False
    
    async def _save_to_redis(self) -> bool:
        """将当前内存状态保存到Redis分层缓存"""
        if not self._redis_enabled or not self._redis_client:
            return False
            
        try:
            # 并行保存到不同的缓存层
            core_task = self._save_core_data_to_redis()
            messages_task = self._save_messages_to_redis()
            contexts_task = self._save_contexts_to_redis()
            
            # 等待所有保存任务完成
            results = await asyncio.gather(core_task, messages_task, contexts_task, return_exceptions=True)
            
            success = any(isinstance(r, bool) and r for r in results)
            if success:
                logger.debug(f"分层缓存保存完成")
            
            return success
                
        except Exception as e:
            logger.error(f"保存到Redis分层缓存时出错: {str(e)}")
            return False
    
    async def _load_from_redis(self) -> bool:
        """从Redis分层缓存加载内存状态"""
        if not self._redis_enabled or not self._redis_client:
            return False
            
        try:
            # 优先加载核心数据
            core_loaded = await self._load_core_data_from_redis()
            
            if core_loaded:
                # 懒加载消息数据
                await self._load_messages_from_redis_lazy()
                # 上下文数据按需加载，这里只加载元数据
                logger.info(f"从Redis分层缓存加载成功: {self.conversation_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"从Redis分层缓存加载时出错: {str(e)}")
            return False

    def _increment_changes(self, force_save: bool = False, is_critical: bool = False):
        """增加变更计数，使用智能防抖机制控制MongoDB保存频率"""
        # 检查是否正在保存中，避免重复触发
        if hasattr(self, '_is_saving_to_mongodb') and self._is_saving_to_mongodb:
            logger.debug("MongoDB保存进行中，跳过新的保存触发")
            return
            
        self._changes_count += 1
        if is_critical:
            self._critical_changes_count += 1
        
        # 立即保存到Redis缓存（异步执行，不阻塞）
        if self._redis_enabled:
            asyncio.create_task(self._save_core_data_to_redis())
        logger.info(f"force_save: {force_save}, changes_count: {self._changes_count}, critical_changes_count: {self._critical_changes_count}")
        
        # 检查是否需要立即保存到MongoDB（无防抖）
        should_force_save = (
            force_save or 
            self._critical_changes_count >= self._critical_save_threshold or
            self._changes_count >= self._batch_save_threshold
        )
        
        # 检查是否超过最大防抖时间
        time_since_last_save = time.time() - self._last_mongodb_save
        max_debounce_exceeded = time_since_last_save > self._max_debounce_time
        
        if should_force_save or max_debounce_exceeded:
            # 立即保存，取消任何待执行的防抖保存
            if self._pending_save_task and not self._pending_save_task.done():
                self._pending_save_task.cancel()
            
            # 立即清零计数器，防止重复触发
            current_changes = self._changes_count
            current_critical = self._critical_changes_count
            self._changes_count = 0
            self._critical_changes_count = 0
            
            logger.debug(f"立即保存到MongoDB: force={should_force_save}, max_debounce={max_debounce_exceeded}, 清零计数器: changes={current_changes}, critical={current_critical}")
            asyncio.create_task(self._background_save_to_mongodb())
        else:
            # 使用防抖机制延迟保存
            self._debounced_save_to_mongodb()
    
    def _debounced_save_to_mongodb(self):
        """防抖保存到MongoDB"""
        # 如果正在保存中，跳过防抖保存
        if self._is_saving_to_mongodb:
            logger.debug("MongoDB保存进行中，跳过防抖保存")
            return
            
        current_time = time.time()
        self._last_save_request_time = current_time
        
        # 如果已经有待执行的保存任务，取消它
        if self._pending_save_task and not self._pending_save_task.done():
            self._pending_save_task.cancel()
        
        # 创建新的延迟保存任务
        async def delayed_save():
            await asyncio.sleep(self._debounce_delay)
            # 检查是否在等待期间又有新的保存请求，或者正在保存中
            if (time.time() - self._last_save_request_time >= self._debounce_delay - 1 
                and not self._is_saving_to_mongodb):
                # 在防抖保存时也需要清零计数器
                current_changes = self._changes_count
                current_critical = self._critical_changes_count
                self._changes_count = 0
                self._critical_changes_count = 0
                
                logger.debug(f"防抖保存到MongoDB执行，延迟{self._debounce_delay}秒，清零计数器: changes={current_changes}, critical={current_critical}")
                await self._background_save_to_mongodb()
            else:
                logger.debug("防抖保存被新的请求取消或正在保存中")
        
        self._pending_save_task = asyncio.create_task(delayed_save())
    
    async def _background_save_to_mongodb(self):
        """后台保存到MongoDB"""
        # 设置保存状态，防止重复触发
        if self._is_saving_to_mongodb:
            logger.debug("MongoDB保存已在进行中，跳过重复保存")
            return
            
        self._is_saving_to_mongodb = True
        try:
            await self.save_to_mongodb({"operation_type": "conversation_end", "force_save": True})
            # 注意：计数器在_increment_changes中已经清零，这里不需要再次清零
            self._last_mongodb_save = time.time()
            logger.debug(f"后台保存到MongoDB完成: {self.conversation_id}")
        except Exception as e:
            logger.error(f"后台保存到MongoDB失败: {str(e)}")
            # 保存失败时，恢复一些计数以便重试（可选）
            # 这里可以考虑是否需要恢复计数器
        finally:
            self._is_saving_to_mongodb = False
    
    def set_research_question(self, question: str) -> None:
        """
        设置研究问题
        
        Args:
            question: 研究问题
        """
        self.research_question = question
        logger.info(f"设置研究问题: {question}")
        self.research_trajectory["initial_question"] = question
        
        # 记录初始问题
        self.record_event("question", question)
        self._increment_changes(is_critical=True)  # 研究问题是关键变更
    
    def record_user_input(self, input_text: str) -> Dict[str, Any]:
        """
        记录用户输入
        
        Args:
            input_text: 用户输入文本
            
        Returns:
            记录的事件
        """
        result = self.record_event("user_input", input_text)
        
        # 标记新增内存记录
        self._mark_memory_dirty(result)
        
        self._increment_changes()
        return result
    
    def record_model_action(self, action_type: str, content: Any) -> Dict[str, Any]:
        """
        记录模型动作
        
        Args:
            action_type: 动作类型
            content: 动作内容
            
        Returns:
            记录的事件
        """
        result = self.record_event("model_action", content, {"action": action_type})
        self._increment_changes()
        return result
    
    def record_action_result(self, action_type: str, result: Any) -> Dict[str, Any]:
        """
        记录动作结果
        
        Args:
            action_type: 动作类型
            result: 动作结果
            
        Returns:
            记录的事件
        """
        result = self.record_event("action_result", result, {"action": action_type})
        self._increment_changes()
        return result
    
    def add_search_query(self, query: str, round_num: int) -> None:
        """
        添加搜索查询到研究轨迹
        
        Args:
            query: 搜索查询
            round_num: 轮次编号
        """
        query_data = {
            "round": round_num,
            "query": query
        }
        self.research_trajectory["search_queries"].append(query_data)
        
        # 如果当前轮次已在研究轨迹中
        if self.research_trajectory["rounds"] and self.research_trajectory["rounds"][-1]["round"] == round_num:
            if "search_query" not in self.research_trajectory["rounds"][-1]:
                self.research_trajectory["rounds"][-1]["search_query"] = query
            elif isinstance(self.research_trajectory["rounds"][-1]["search_query"], str):
                self.research_trajectory["rounds"][-1]["search_query"] = [
                    self.research_trajectory["rounds"][-1]["search_query"], 
                    query
                ]
            elif isinstance(self.research_trajectory["rounds"][-1]["search_query"], list):
                self.research_trajectory["rounds"][-1]["search_query"].append(query)
        
        # 标记研究轨迹更新
        self._mark_field_dirty("trajectory", "research_trajectory")
        
        self._increment_changes()
    
    def add_search_result(self, result: Dict[str, Any], query: str, round_num: int) -> None:
        """
        添加搜索结果到研究轨迹
        
        Args:
            result: 搜索结果
            query: 搜索查询
            round_num: 轮次编号
        """
        # 记录搜索结果到研究轨迹
        search_result_info = {
            "url": result.get("url", ""),
            "title": result.get("title", ""),
            "snippet": result.get("snippet", ""),
            "query": query,
            "round": round_num
        }
        
        # 添加到当前轮次的轨迹
        if self.research_trajectory["rounds"] and self.research_trajectory["rounds"][-1]["round"] == round_num:
            if "search_results" not in self.research_trajectory["rounds"][-1]:
                self.research_trajectory["rounds"][-1]["search_results"] = []
            self.research_trajectory["rounds"][-1]["search_results"].append(search_result_info)
        
        # 将URL添加到有用URL列表
        self.research_trajectory["useful_urls"].append(search_result_info)
        self._increment_changes()
    
    def start_new_round(self, round_num: int) -> Dict[str, Any]:
        """
        开始新的研究轮次
        
        Args:
            round_num: 轮次编号
            
        Returns:
            新轮次的轨迹数据
        """
        round_trajectory = {
            "round": round_num,
            "timestamp": get_cn_time().isoformat(),
            "reasoning": "",                   # 思考过程
            "action": "",                      # 选择的动作
            "search_results": [],              # 搜索结果
            "browse_results": [],              # 浏览结果
            "report_edits": []                 # 报告编辑
        }
        
        self.research_trajectory["rounds"].append(round_trajectory)
        
        # 记录开始新轮次的事件
        self.record_event("round_start", {"round": round_num})
        self._increment_changes()
        
        return round_trajectory
    
    def update_current_round(self, round_num: int, field: str, value: Any) -> None:
        """
        更新当前轮次的特定字段
        
        Args:
            round_num: 轮次编号
            field: 要更新的字段
            value: 新值
        """
        # 找到当前轮次
        current_round = None
        for r in self.research_trajectory["rounds"]:
            if r["round"] == round_num:
                current_round = r
                break
        
        # 如果找到当前轮次，更新字段
        if current_round:
            current_round[field] = value
            self._increment_changes()
        else:
            logger.warning(f"轮次 {round_num} 不存在，无法更新字段 {field}")
    
    def add_clarification_qa(self, question: str, answer: str) -> None:
        """
        添加澄清问题和回答到研究轨迹
        
        Args:
            question: 澄清问题
            answer: 用户回答
        """
        qa_data = {
            "timestamp": get_cn_time().isoformat(),
            "question": question,
            "answer": answer
        }
        
        self.research_trajectory["clarification_qa"].append(qa_data)
        
        # 记录澄清问答事件
        self.record_event("clarification_qa", qa_data)
        self._increment_changes()
    
    def add_user_feedback(self, feedback: str, round_num: Optional[int] = None) -> None:
        """
        添加用户反馈到研究轨迹
        
        Args:
            feedback: 用户反馈内容
            round_num: 可选的轮次编号
        """
        feedback_data = {
            "timestamp": get_cn_time().isoformat(),
            "round": round_num or self.round,
            "content": feedback
        }
        
        self.research_trajectory["user_feedbacks"].append(feedback_data)
        
        # 记录用户反馈事件
        self.record_event("user_feedback", feedback_data)
        self._increment_changes()
    
    def update_final_report(self, report: str) -> None:
        """
        更新最终报告
        
        Args:
            report: 更新后的报告内容
        """
        self.final_report = report
        
        # 记录报告更新事件
        self.record_event("final_report_update", {"content": report})
        self._increment_changes()
    
    def add_context(self, context: BaseContext) -> None:
        """
        添加上下文
        
        Args:
            context: 上下文对象
        """
        self.contexts.append(context)
        logger.info(f"添加上下文: {context.context_id}")
        
        # 标记新增/更新上下文
        self._mark_context_dirty(context)
        
        self._increment_changes()
            
    def get_all_contexts(self) -> List[Dict[str, Any]]:
        """
        获取所有上下文的内容和元数据
        
        Returns:
            上下文信息列表
        """
        context_info = []
        for context in self.contexts:
            context_info.append({
                "context_id": context.context_id,
                "content": context.get_context(),
                "metadata": context.get_metadata()
            })
        return context_info

    def get_all_context_class(self) -> List[BaseContext]:
        """
        获取所有上下文类
        """
        return self.contexts
        
    async def save_to_mongodb(self, metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        将研究内存记录保存到MongoDB（自动选择存储格式，支持版本兼容）
        
        Args:
            metadata: 可选的附加元数据
            
        Returns:
            MongoDB中的文档ID
        """
        if not self.mongodb_available:
            logger.warning("MongoDB不可用，无法保存")
            return ""
        
        # 智能选择存储格式：根据检测到的版本或默认配置
        use_separated = await self._should_use_separated_storage(self.conversation_id)
        
        if use_separated:
            # 使用分离存储策略（v2.0）
            return await self._save_with_separated_storage(metadata)
        else:
            # 使用传统单文档保存（v1.0 兼容性）
            return await self._save_single_document(metadata)
    
    def _prepare_save_data(self, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """准备要保存的数据"""
        memory_data = {
            "agent_name": self.agent_name,
            "conversation_id": self.conversation_id,
            "timestamp": get_cn_time().isoformat(),
            "memory_records": [r.model_dump() if hasattr(r, 'model_dump') else r for r in self.memory_records],
            "research_question": self.research_question,
            "final_report": self.final_report,
            "research_trajectory": self.research_trajectory,
            "token_usage": self.token_usage,
            "contexts": [{
                "context_id": ctx.context_id,
                "content": ctx.get_context(),
                "metadata": ctx.get_metadata()
            } for ctx in self.contexts],
            "language": self.language,
            "round": self.round,
            "search_times": self.search_times,
            "max_search_times": self.max_search_times,
            "is_paused": self.is_paused,
            "report_draft": self.report_draft,
            "draft_version": self.draft_version,
            "is_updating_report": self.is_updating_report,
            "clarification_questions": self.clarification_questions,
            "pending_clarification_questions": self.pending_clarification_questions,
            "user_preferences": self.user_preferences,
            "user_likes_urls": self.user_likes_urls,
            "user_draft_preferences": self.user_draft_preferences,
            "storage_version": self.STORAGE_VERSION_SINGLE,  # 标识传统存储版本
            "user_id": metadata.get("user_id") if metadata else None,
        }
        
        # 添加元数据
        if metadata:
            memory_data["metadata"] = metadata
            
        return memory_data
    
    def _estimate_data_size(self, data: Dict[str, Any]) -> int:
        """估算数据大小（字节）"""
        try:
            import json
            return len(json.dumps(data, ensure_ascii=False).encode('utf-8'))
        except Exception:
            # 粗略估算
            return len(str(data).encode('utf-8'))
    
    async def _save_with_separated_storage(self, metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        使用分离存储策略保存数据
        """
        try:
            # 保存核心会话信息
            core_id = await self._save_core_data(metadata)
            
            # 保存研究轨迹（如果有更新）
            if self._dirty_fields["trajectory"]:
                await self._save_trajectory_data()
            
            # 保存新增的内存记录
            if self._dirty_fields["memory"]:
                await self._save_memory_records()
            
            # 保存新增/更新的上下文
            if self._dirty_fields["contexts"]:
                await self._save_contexts_data()
            
            # 保存新增/更新的用户偏好
            if self._dirty_fields["preferences"]:
                await self._save_preferences_data()
            
            # 更新状态
            self._last_mongodb_save = time.time()
            # 注意：如果是通过_increment_changes触发的保存，计数器已经在那里清零了
            # 只有直接调用save_to_mongodb时才需要在这里清零
            
            # 清空脏数据标记
            for key in self._dirty_fields:
                if isinstance(self._dirty_fields[key], set):
                    self._dirty_fields[key].clear()
                else:
                    self._dirty_fields[key].clear()
            
            logger.info(f"分离存储保存成功: {core_id}")
            return core_id
            
        except Exception as e:
            logger.error(f"分离存储保存失败: {str(e)}")
            # 如果分离存储失败，回退到传统方式
            logger.info("回退到传统单文档保存方式")
            return await self._save_single_document(metadata)
    
    async def _save_single_document(self, metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        传统单文档保存方式（回退兼容性）
        """
        # 准备数据
        memory_data = self._prepare_save_data(metadata)
        
        # 使用重试机制保存
        for attempt in range(self._mongodb_retry_count):
            try:
                # 设置超时保存
                result = await asyncio.wait_for(
                    self._perform_mongodb_save(memory_data),
                    timeout=self._mongodb_query_timeout / 1000  # 转换为秒
                )
                
                # 保存成功
                # 注意：计数器可能已经在_increment_changes中清零了
                # 这里确保清零，防止直接调用save_to_mongodb的情况
                self._changes_count = 0
                self._critical_changes_count = 0
                self._last_mongodb_save = time.time()
                
                return result
                
            except asyncio.TimeoutError:
                logger.error(f"MongoDB保存超时 (尝试 {attempt + 1}/{self._mongodb_retry_count})")
                if attempt < self._mongodb_retry_count - 1:
                    await asyncio.sleep(self._mongodb_retry_delay)
                    
            except Exception as e:
                logger.error(f"MongoDB保存失败 (尝试 {attempt + 1}/{self._mongodb_retry_count}): {str(e)}")
                if attempt < self._mongodb_retry_count - 1:
                    await asyncio.sleep(self._mongodb_retry_delay)
                else:
                    # 最后一次尝试失败，记录详细错误
                    logger.error(f"MongoDB保存最终失败")
        
        return ""
    
    async def _perform_mongodb_save(self, memory_data: Dict[str, Any]) -> str:
        """执行实际的MongoDB保存操作"""
        if self.conversation_id:
            # 更新现有文档
            documents = await self.db.find_many({"conversation_id": self.conversation_id}, skip=0, limit=1)
            if documents:
                document = documents[0]
                document = await self.db.update(document.id, memory_data)
                self.memory_document_id = str(document.id)
                logger.info(f"更新MongoDB研究内存记录: {self.memory_document_id}")
            else:
                # 创建新文档
                document = await self.db.create(memory_data)
                self.memory_document_id = str(document.id)
                logger.info(f"创建MongoDB研究内存记录: {self.memory_document_id}")
        
        return self.memory_document_id
    
    async def load_from_mongodb(self, conversation_id: str) -> bool:
        """
        从MongoDB或Redis加载研究内存记录（自动识别存储格式，支持版本兼容）
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            是否成功加载
        """
        # 防止并发加载
        # if self._is_loading:
        #     logger.warning(f"正在加载中，跳过重复加载: {conversation_id}")
        #     return self._is_loaded_from_storage
            
        self._is_loading = True
        
        try:
            # 首先尝试从Redis分层缓存加载
            if await self._load_from_redis():
                logger.info(f"从Redis分层缓存加载会话成功: {conversation_id}")
                self._is_loaded_from_storage = True
                return True
                
            # Redis没有数据，从MongoDB加载，智能检测存储格式
            if not self.mongodb_available:
                logger.warning("MongoDB不可用，无法加载")
                return False
            
            # 检测存储版本并选择加载策略
            use_separated = await self._should_use_separated_storage(conversation_id)
            
            if use_separated:
                # 使用分离存储策略加载（v2.0）
                logger.debug(f"使用分离存储格式加载: {conversation_id}")
                success = await self._load_from_separated_storage(conversation_id)
                if success:
                    self._is_loaded_from_storage = True
                    # 加载成功后，保存到Redis缓存以加速后续访问
                    if self._redis_enabled:
                        await self._save_to_redis()
                    return True
            else:
                # 使用传统单一集合加载（v1.0 兼容性）
                logger.debug(f"使用传统存储格式加载: {conversation_id}")
                
            # 如果分离存储加载失败，或者检测为传统格式，尝试从传统单一集合加载
            try:
                documents = await asyncio.wait_for(
                    self.db.find_many(
                        {"conversation_id": conversation_id}, 
                        skip=0, 
                        limit=1
                    ),
                    timeout=self._mongodb_query_timeout / 1000  # 转换为秒
                )
                
                if documents:
                    document = documents[0]
                    # 从传统单一文档加载
                    self._load_core_data_from_document(document)
                    
                    # 记录检测到的存储版本
                    self.detected_storage_version = self.STORAGE_VERSION_SINGLE
                    
                    # 加载成功后，保存到Redis缓存以加速后续访问
                    if self._redis_enabled:
                        await self._save_to_redis()
                    
                    logger.info(f"从传统单一集合加载研究内存记录成功: {str(document.id)}")
                    self.memory_document_id = str(document.id)
                    self._is_loaded_from_storage = True
                    return True
                else:
                    logger.warning(f"找不到对话ID为 {conversation_id} 的研究内存记录")
                    return False
                    
            except asyncio.TimeoutError:
                logger.error(f"MongoDB查询超时: {conversation_id}")
                return False
            except Exception as mongo_e:
                logger.error(f"MongoDB查询失败: {str(mongo_e)}")
                return False
                
        except Exception as e:
            logger.error(f"加载失败: {str(e)}")
            return False
        finally:
            self._is_loading = False
    
    async def _load_from_separated_storage(self, conversation_id: str) -> bool:
        """
        从分离存储的MongoDB集合中加载数据
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            是否成功加载
        """
        try:
            from backend.db.mongodb import MongoDBBase
            
            # 1. 加载核心会话信息（必须成功）
            core_db = MongoDBBase(self._collections["core"])
            core_doc = await core_db.find_one({"conversation_id": conversation_id})
            
            if not core_doc:
                logger.warning(f"未找到核心会话数据: {conversation_id}")
                return False
            
            # 加载核心数据
            self._load_core_data_from_separated_document(core_doc)
            
            # 2. 加载研究轨迹（可选）
            try:
                trajectory_db = MongoDBBase(self._collections["trajectory"])
                trajectory_doc = await trajectory_db.find_one({"conversation_id": conversation_id})
                if trajectory_doc:
                    self.research_trajectory = trajectory_doc.get("research_trajectory", [])
                    logger.debug("已加载研究轨迹数据")
            except Exception as e:
                logger.warning(f"加载研究轨迹失败: {str(e)}")
                # 轨迹数据失败不影响核心功能
            
            # 3. 加载内存记录（可选，分页加载）
            try:
                await self._load_memory_records_from_separated_storage(conversation_id)
            except Exception as e:
                logger.warning(f"加载内存记录失败: {str(e)}")
                self.memory_records = []
            
            # 4. 加载上下文数据（可选）
            try:
                await self._load_contexts_from_separated_storage(conversation_id)
            except Exception as e:
                logger.warning(f"加载上下文数据失败: {str(e)}")
                self.contexts = []
            
            # 5. 加载用户偏好（可选）
            try:
                preferences_db = MongoDBBase(self._collections["preferences"])
                preferences_doc = await preferences_db.find_one({"conversation_id": conversation_id})
                if preferences_doc:
                    self.user_preferences = getattr(preferences_doc, 'user_preferences', [])
                    self.user_likes_urls = getattr(preferences_doc, 'user_likes_urls', [])
                    self.user_draft_preferences = getattr(preferences_doc, 'user_draft_preferences', {})
                    self.clarification_questions = getattr(preferences_doc, 'clarification_questions', [])
                    self.pending_clarification_questions = getattr(preferences_doc, 'pending_clarification_questions', [])
                    logger.debug("已加载用户偏好数据")
            except Exception as e:
                logger.warning(f"加载用户偏好失败: {str(e)}")
                # 偏好数据失败不影响核心功能
            
            logger.info(f"从分离存储加载会话成功: {conversation_id}")
            # 获取文档ID - 处理dict和对象两种情况
            doc_id = core_doc.id if hasattr(core_doc, 'id') else core_doc.get('_id') or core_doc.get('id')
            self.memory_document_id = str(doc_id)
            return True
            
        except Exception as e:
            logger.error(f"从分离存储加载失败: {str(e)}")
            return False
    
    def _load_core_data_from_separated_document(self, core_doc):
        """从分离存储的核心文档加载数据"""
        try:
            # 保存文档ID - 处理dict和对象两种情况
            doc_id = core_doc.id if hasattr(core_doc, 'id') else core_doc.get('_id') or core_doc.get('id')
            self.memory_document_id = str(doc_id)
            
            # 加载基本属性
            self.agent_name = core_doc.get("agent_name", "")
            self.conversation_id = core_doc.get("conversation_id", "")
            
            # 加载核心研究数据
            self.research_question = core_doc.get("research_question", '')
            self.final_report = core_doc.get("final_report", '')
            
            # 加载token使用统计
            self.token_usage = core_doc.get("token_usage", {
                "total_input_tokens": 0,
                "total_output_tokens": 0,
                "total_cost": 0,
                "models_usage": {}
            })
                
            # 加载研究状态控制变量
            self.language = core_doc.get("language", None)
            self.round = core_doc.get("round", 0)
            self.search_times = core_doc.get("search_times", 0)
            self.max_search_times = core_doc.get("max_search_times", 5)
            self.is_paused = core_doc.get("is_paused", False)
            
            # 加载报告相关状态
            self.report_draft = core_doc.get("report_draft", '')
            self.draft_version = core_doc.get("draft_version", 0)
            self.is_updating_report = core_doc.get("is_updating_report", False)
            
            # 加载浏览结果
            self.last_browse_result = core_doc.get("last_browse_result", None)
            
        except Exception as e:
            logger.error(f"从核心文档加载数据失败: {str(e)}")
    
    async def _load_memory_records_from_separated_storage(self, conversation_id: str):
        """从分离存储加载内存记录（分页加载，仅加载最近的记录）"""
        try:
            from backend.db.mongodb import MongoDBBase
            
            memory_db = MongoDBBase(self._collections["memory"])
            
            # 只加载最近的50条记录，按创建时间倒序
            memory_docs = await memory_db.find_many(
                {"conversation_id": conversation_id}, 
                skip=0, 
                limit=50
            )
            
            # 重建内存记录（因为是倒序查询，需要反转）
            self.memory_records = []
            for doc in reversed(memory_docs):
                record_data = doc.get("record_data", {})
                if isinstance(record_data, dict):
                    self.memory_records.append(MemoryRecord(**record_data))
                else:
                    self.memory_records.append(record_data)
            
            logger.debug(f"从分离存储加载{len(self.memory_records)}条内存记录")
            
        except Exception as e:
            logger.error(f"从分离存储加载内存记录失败: {str(e)}")
            self.memory_records = []
    
    async def _load_contexts_from_separated_storage(self, conversation_id: str):
        """从分离存储加载上下文数据"""
        try:
            from backend.db.mongodb import MongoDBBase
            
            contexts_db = MongoDBBase(self._collections["contexts"])
            
            # 加载上下文数据
            context_docs = await contexts_db.find_many({"conversation_id": conversation_id})
            
            self.contexts = []
            for doc in context_docs:
                try:
                    context_id = doc.context_id
                    metadata = getattr(doc, 'metadata', {})
                    
                    # 根据元数据类型创建对应的上下文对象
                    if metadata.get('file_path'):
                        file_context = FileContext(metadata['file_path'])
                        file_context.set_context_id(context_id)
                        file_context.update_metadata(metadata)
                        # 不立即加载内容，保持懒加载
                        self.contexts.append(file_context)
                    else:
                        # 其他类型的上下文，暂时跳过或创建基础上下文
                        logger.debug(f"跳过未知类型的上下文: {context_id}")
                        
                except Exception as ctx_e:
                    logger.warning(f"加载单个上下文失败: {str(ctx_e)}")
                    continue
            
            logger.debug(f"从分离存储加载{len(self.contexts)}个上下文")
            
        except Exception as e:
            logger.error(f"从分离存储加载上下文失败: {str(e)}")
            self.contexts = []
    
    def _load_core_data_from_document(self, document):
        """从MongoDB文档加载核心数据（传统单一集合兼容性）"""
        try:
            # 保存文档ID
            self.memory_document_id = str(document.id)
            
            # 加载基本属性
            self.agent_name = document.agent_name
            self.conversation_id = document.conversation_id
            
            # 加载核心研究数据
            self.research_question = document.research_question
            self.final_report = document.final_report
            
            # 加载完整研究轨迹（不限制大小）
            if hasattr(document, "research_trajectory") and document.research_trajectory:
                self.research_trajectory = document.research_trajectory
            
            # 加载token使用统计
            if hasattr(document, "token_usage"):
                self.token_usage = document.token_usage
            else:
                self.token_usage = {
                    "total_input_tokens": 0,
                    "total_output_tokens": 0,
                    "total_cost": 0,
                    "models_usage": {}
                }
                
            # 加载研究状态控制变量
            self.language = document.language
            self.round = document.round
            self.search_times = document.search_times
            self.max_search_times = document.max_search_times
            self.is_paused = document.is_paused
            
            # 加载报告相关状态
            self.report_draft = document.report_draft
            self.draft_version = document.draft_version
            self.is_updating_report = document.is_updating_report
            
            # 加载完整澄清问题（不限制数量）
            self.clarification_questions = document.clarification_questions
            self.pending_clarification_questions = document.pending_clarification_questions
            
            # 加载完整用户偏好和点赞（不限制数量）
            self.user_preferences = getattr(document, 'user_preferences', [])
            self.user_likes_urls = getattr(document, 'user_likes_urls', [])
            
            # 加载研究规则
            self.research_rules = getattr(document, 'research_rules', [])
            # 延迟加载上下文和消息记录（不限制数量）
            self._delayed_load_contexts(document)
            self._delayed_load_memory_records(document)
            # 加载用户偏好
            self.user_draft_preferences = getattr(document, 'user_draft_preferences', {})
            print(f"加载用户偏好: {self.user_draft_preferences}")
            # import pdb; pdb.set_trace();
            # 加载上下文
            self.contexts = []
            for ctx_data in document.contexts:
                logger.info(f"存在的上下文数据记录:{str(ctx_data)}")
                if ctx_data.metadata["file_path"]:
                    file_context = FileContext(ctx_data.metadata["file_path"])
                    file_context.set_context_id(ctx_data.context_id)
                    file_context.set_content(ctx_data.content)
                    file_context.update_metadata(ctx_data.metadata)
                    self.contexts.append(file_context)
                    
            logger.info(f"从MongoDB加载研究内存记录成功: {self.memory_document_id}")
            return True
        except Exception as e:
            logger.error(f"从文档加载核心数据失败: {str(e)}")
    
    def _delayed_load_contexts(self, document):
        """延迟加载完整上下文（不限制数量）"""
        try:
            self.contexts = []
            for ctx_data in getattr(document, 'contexts', []):
                logger.debug(f"加载上下文元数据: {ctx_data.get('context_id', 'unknown')}")
                if ctx_data.get('metadata', {}).get('file_path'):
                    file_context = FileContext(ctx_data['metadata']['file_path'])
                    file_context.set_context_id(ctx_data['context_id'])
                    # 不立即加载内容，只设置元数据
                    file_context.update_metadata(ctx_data['metadata'])
                    self.contexts.append(file_context)
        except Exception as e:
            logger.error(f"延迟加载上下文失败: {str(e)}")
            self.contexts = []
    
    def _delayed_load_memory_records(self, document):
        """延迟加载完整内存记录（不限制数量）"""
        try:
            if hasattr(document, 'memory_records') and isinstance(document.memory_records, list):
                # 加载所有内存记录，不限制数量
                self.memory_records = []
                for record in document.memory_records:
                    if isinstance(record, dict):
                        self.memory_records.append(MemoryRecord(**record))
                    else:
                        self.memory_records.append(record)
            else:
                self.memory_records = []
        except Exception as e:
            logger.error(f"延迟加载内存记录失败: {str(e)}")
            self.memory_records = []

    def summarize(self) -> str:
        """
        生成研究轨迹的摘要
        
        Returns:
            研究轨迹摘要字符串
        """
        # 格式化研究轨迹摘要
        summary = ["## 研究轨迹摘要"]
        
        # 初始问题
        summary.append(f"### 初始问题\n{self.research_trajectory['initial_question']}\n")
        
        # 轮次摘要
        summary.append("### 研究进展")
        for round_data in self.research_trajectory["rounds"]:
            round_num = round_data.get("round", "?")
            timestamp = round_data.get("timestamp", "")
            action = round_data.get("action", "未知动作")
            
            # 格式化时间
            try:
                dt = datetime.fromisoformat(timestamp)
                formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")
            except (ValueError, TypeError):
                formatted_time = timestamp
                
            summary.append(f"\n#### 轮次 {round_num} ({formatted_time})")
            summary.append(f"- 动作: {action}")
            
            # 搜索查询
            search_query = round_data.get("search_query", "")
            if search_query:
                if isinstance(search_query, list):
                    search_query = ", ".join(search_query)
                summary.append(f"- 搜索查询: {search_query}")
                
            # 搜索结果
            search_results = round_data.get("search_results", [])
            if search_results:
                summary.append("- 搜索结果:")
                for i, result in enumerate(search_results[:3]):  # 仅显示前3个结果
                    title = result.get("title", "无标题")
                    summary.append(f"  - {title}")
                if len(search_results) > 3:
                    summary.append(f"  - ... 及其他 {len(search_results) - 3} 个结果")
                    
            # 浏览结果
            browse_results = round_data.get("browse_results", [])
            if browse_results:
                summary.append(f"- 浏览了 {len(browse_results)} 个网页")
                
            # 报告编辑
            report_edits = round_data.get("report_edits", [])
            if report_edits:
                summary.append(f"- 编辑报告: {len(report_edits)} 次修改")
        
        # 用户反馈
        user_feedbacks = self.research_trajectory["user_feedbacks"]
        if user_feedbacks:
            summary.append("\n### 用户反馈")
            for i, feedback in enumerate(user_feedbacks[-3:]):  # 仅显示最近3条反馈
                content = feedback.get("content", "")
                if len(content) > 100:
                    content = content[:100] + "..."
                summary.append(f"- {content}")
            if len(user_feedbacks) > 3:
                summary.append(f"- ... 及其他 {len(user_feedbacks) - 3} 条反馈")
        
        # 澄清问题与回答
        clarification_qa = self.research_trajectory["clarification_qa"]
        if clarification_qa:
            summary.append("\n### 澄清问题与回答")
            for i, qa in enumerate(clarification_qa):
                question = qa.get("question", "")
                answer = qa.get("answer", "")
                if len(question) > 100:
                    question = question[:100] + "..."
                if len(answer) > 100:
                    answer = answer[:100] + "..."
                summary.append(f"- Q: {question}")
                summary.append(f"  A: {answer}")
        
        return "\n".join(summary)
    
    def add_validation_result(self, validation_result: Dict[str, Any], hypothesis: str, research_context: str, round_num: int) -> None:
        """
        添加验证结果
        
        Args:
            validation_result: 验证结果
            hypothesis: 假设
            research_context: 研究上下文
            round_num: 轮次编号
        """
        validation_data = {
            "timestamp": get_cn_time().isoformat(),
            "round": round_num,
            "hypothesis": hypothesis,
            "research_context": research_context,
            "validation_result": validation_result
        }
        
        # 在当前轮次中添加验证结果
        for round_data in self.research_trajectory["rounds"]:
            if round_data["round"] == round_num:
                if "validation_results" not in round_data:
                    round_data["validation_results"] = []
                round_data["validation_results"].append(validation_data)
                break
        
        # 记录验证事件
        self.record_event("validation", validation_data)
    
    def record_model_error(self, error_type: str, error_details: Dict[str, Any], round_num: Optional[int] = None) -> None:
        """
        记录模型错误
        
        Args:
            error_type: 错误类型，如 "invalid_action", "parsing_error", etc.
            error_details: 错误详情
            round_num: 可选的轮次编号，如果没有提供则使用当前轮次
        """
        if round_num is None:
            round_num = self.round
            
        error_record = {
            "error_type": error_type,
            "round": round_num,
            "timestamp": get_cn_time().isoformat(),
            "details": error_details
        }
        
        # 确保research_trajectory中有model_errors字段
        if "model_errors" not in self.research_trajectory:
            self.research_trajectory["model_errors"] = []
            
        self.research_trajectory["model_errors"].append(error_record)
        
        # 记录错误事件
        self.record_event("model_error", error_record)
        
        logger.warning(f"记录模型错误: {error_type} - {error_details}")

    def get_recent_model_errors(self, limit: int = 3) -> List[Dict[str, Any]]:
        """
        获取最近的模型错误记录
        
        Args:
            limit: 返回的错误记录数量限制
            
        Returns:
            最近的错误记录列表
        """
        if "model_errors" not in self.research_trajectory:
            return []
            
        # 按时间戳倒序排列，返回最近的错误
        errors = self.research_trajectory["model_errors"]
        return sorted(errors, key=lambda x: x["timestamp"], reverse=True)[:limit]

    def get_last_round_error(self) -> Optional[Dict[str, Any]]:
        """
        获取上一轮的错误信息，并且只有在当前轮次正好是错误轮次+1时才返回
        
        Returns:
            上一轮的错误信息，如果没有或不满足条件则返回None
        """
        if "model_errors" not in self.research_trajectory or not self.research_trajectory["model_errors"]:
            return None
            
        # 查找上一轮的错误
        last_round = self.round - 1
        for error in reversed(self.research_trajectory["model_errors"]):
            if error.get("round") == last_round:
                # 只有当前轮次是错误轮次的下一轮时才返回错误信息
                return error
                
        return None

    def format_error_feedback(self) -> str:
        """
        格式化错误反馈信息，用于在下次提示中给模型参考
        只有当前轮次刚好是错误发生后的下一轮时才返回错误反馈
        
        Returns:
            格式化的错误反馈字符串，包含标题。如果没有错误则返回空字符串
        """
        last_error = self.get_last_round_error()
        if not last_error:
            return ""
            
        error_type = last_error.get("error_type", "unknown")
        error_details = last_error.get("details", {})
        
        # 根据错误类型生成特定的反馈信息
        feedback_lines = ["## 上一轮操作出现错误，请注意以下反馈："]
        
        if error_type == "invalid_action":
            feedback_lines.extend([
                f"**错误类型**: 无效动作选择",
                f"**问题**: 选择了无效的动作 '{error_details.get('selected_action', 'unknown')}'",
                f"**可用动作**: {', '.join(error_details.get('valid_actions', []))}",
                f"**要求**: 请确保选择有效的动作，动作必须完全匹配可用动作列表中的选项"
            ])
        elif error_type == "parsing_error":
            feedback_lines.extend([
                f"**错误类型**: 解析错误",
                f"**问题**: 输出格式无法正确解析",
                f"**要求**: 请严格按照要求的格式输出，确保所有标签正确闭合"
            ])
        else:
            feedback_lines.extend([
                f"**错误类型**: {error_type}",
                f"**详细信息**: {error_details}",
                f"**要求**: 请仔细检查输出格式和内容"
            ])
            
        return "\n".join(feedback_lines) + "\n"
    
    def increment_round(self) -> int:
        """
        增加研究轮次并记录
        
        Returns:
            新的轮次号
        """
        self.round += 1
        self.record_event("round_increment", {"new_round": self.round})
        self._increment_changes()
        return self.round
    
    def increment_search_times(self) -> int:
        """
        增加搜索次数
        我们可能要稍微改一下这里，改成写好报告的次数（包括首次，但是为了兼容性，先不改这个函数名，请务必使用的时候注意）
        
        Returns:
            新的搜索次数
        """
        self.search_times += 1
        self.record_event("search_increment", {"new_search_times": self.search_times})
        self._increment_changes()
        return self.search_times

    def reset_search_times(self) -> None:
        """重置搜索次数"""
        self.search_times = 0
        self._increment_changes()
    
    def can_search_more(self) -> bool:
        """
        检查是否还能继续搜索
        
        Returns:
            是否还能搜索
        """
        return self.search_times < self.max_search_times
    
    def set_paused(self, is_paused: bool) -> None:
        """
        设置暂停状态
        
        Args:
            is_paused: 是否暂停
        """
        self.is_paused = is_paused
        self.record_event("pause_state_change", {
            "is_paused": is_paused,
            "round": self.round
        })
        # 暂停状态变更比较重要，强制保存
        self._increment_changes(force_save=True, is_critical=True)
        
        logger.info(f"研究状态设置为: {'暂停' if is_paused else '运行'}")
    
    def update_report_draft(self, new_draft: str) -> None:
        """
        更新报告草稿
        
        Args:
            new_draft: 新的报告草稿内容
        """
        # 检查是否有实际变化
        if new_draft != self.report_draft:
            # 保存历史版本
            if self.report_draft:
                self.draft_history.append({
                    "version": self.draft_version,
                    "content": self.report_draft,
                    "timestamp": get_cn_time().isoformat()
                })
            
            # 更新到新版本
            self.report_draft = new_draft
            self.draft_version += 1
            
            # 记录更新事件
            self.record_event("report_draft_update", {
                "version": self.draft_version,
                "length": len(new_draft),
                "round": self.round
            })
            
            self._increment_changes()
            logger.info(f"报告草稿已更新到版本 {self.draft_version}")
    
    def get_report_draft(self) -> str:
        """
        获取报告草稿
        
        Returns:
            当前报告草稿内容
        """
        return self.report_draft
    
    def get_draft_version(self) -> int:
        """
        获取草稿版本
        
        Returns:
            当前草稿版本号
        """
        return self.draft_version
    
    def set_language(self, language: str) -> None:
        """
        设置研究语言
        
        Args:
            language: 语言代码
        """
        logger.info(f"设置研究内存语言为: {language}")
        self.language = language
        self._increment_changes()
    
    def get_language(self) -> str:
        """
        获取研究语言
        
        Returns:
            语言代码
        """
        lang = self.language or "zh"  # 默认中文
        logger.info(f"获取研究内存语言: {lang}")
        return lang
    
    def set_last_browse_result(self, browse_result: Dict[str, Any]) -> None:
        """
        设置最后一次浏览结果
        
        Args:
            browse_result: 浏览结果
        """
        self.last_browse_result = browse_result
        self._increment_changes()
    
    def get_last_browse_result(self) -> Optional[Dict[str, Any]]:
        """
        获取最后一次浏览结果
        
        Returns:
            浏览结果
        """
        return self.last_browse_result
    
    def add_clarification_question(self, question: Dict[str, Any]) -> None:
        """
        添加澄清问题
        
        Args:
            question: 问题信息
        """
        self.pending_clarification_questions.append(question)
        self._increment_changes()
    
    def get_pending_clarification_questions(self) -> List[Any]:
        """
        获取待处理的澄清问题
        
        Returns:
            澄清问题列表
        """
        return self.pending_clarification_questions
    
    def clear_pending_clarification_questions(self) -> None:
        """
        清除待处理的澄清问题
        """
        self.pending_clarification_questions = []
        self._increment_changes()
    
    def add_research_rule(self, rule: str) -> None:
        """
        添加研究规则
        
        Args:
            rule: 规则内容
        """
        if rule and rule not in self.research_rules:
            self.research_rules.append(rule)
            self._increment_changes()
    
    def get_research_rules(self) -> List[str]:
        """
        获取研究规则
        
        Returns:
            研究规则列表
        """
        return self.research_rules
    
    def remove_context(self, context_id: str) -> bool:
        """
        从内存中移除指定的上下文
        
        Args:
            context_id: 要移除的上下文ID
            
        Returns:
            bool: 是否成功移除
        """
        try:
            # 查找要移除的上下文
            for i, ctx in enumerate(self.contexts):
                if ctx.context_id == context_id:
                    # 从列表中移除
                    self.contexts.pop(i)
                    logger.info(f"已移除上下文: {context_id}")
                    self._increment_changes()
                    return True
                    
            logger.warning(f"未找到要移除的上下文: {context_id}")
            return False
            
        except Exception as e:
            logger.error(f"移除上下文时出错: {str(e)}")
            return False
    
    def set_enable_cognition_search(self, enable: bool) -> None:
        """设置是否启用认知搜索"""
        self.research_trajectory["enable_cognition_search"] = enable
        self._increment_changes()

    def get_enable_cognition_search(self) -> bool:
        """获取是否启用认知搜索"""
        return self.research_trajectory.get("enable_cognition_search", False)

    def set_enable_search(self, enable: bool) -> None:
        """设置是否启用搜索"""
        self.research_trajectory["enable_search"] = enable
        self._increment_changes()

    def get_enable_search(self) -> bool:
        """获取是否启用搜索"""
        return self.research_trajectory.get("enable_search", True)  # 默认启用搜索

    def set_edit_mode(self, enable: bool) -> None:
        """设置是否启用编辑模式（chat to edit）"""
        self.research_trajectory["edit_mode"] = enable
        self._increment_changes()

    def get_edit_mode(self) -> bool:
        """获取是否启用编辑模式"""
        return self.research_trajectory.get("edit_mode", False)

    async def update_token_usage(self, model_name: str, token_info: Dict[str, Any]) -> None:
        """
        更新token使用统计
        
        Args:
            model_name: 模型名称
            token_info: token信息，包含input_tokens、output_tokens和costs
        """
        # 更新总使用量
        self.token_usage["total_input_tokens"] += token_info.get("input_tokens", 0)
        self.token_usage["total_output_tokens"] += token_info.get("output_tokens", 0)
        self.token_usage["total_cost"] += token_info.get("total_cost", 0)
        
        # 按模型记录
        if model_name not in self.token_usage["models_usage"]:
            self.token_usage["models_usage"][model_name] = {
                "input_tokens": 0,
                "output_tokens": 0,
                "cost": 0
            }
        
        # 更新模型特定使用量
        self.token_usage["models_usage"][model_name]["input_tokens"] += token_info.get("input_tokens", 0)
        self.token_usage["models_usage"][model_name]["output_tokens"] += token_info.get("output_tokens", 0) 
        self.token_usage["models_usage"][model_name]["cost"] += token_info.get("total_cost", 0)
        
        # 记录token使用事件
        self.record_event("token_usage_update", {
            "model_name": model_name,
            "token_info": token_info,
            "total_cost": self.token_usage["total_cost"]
        })
        
        self._increment_changes()

    async def merge_browse_agent_token_usage(self, browse_token_usage: Dict[str, Any]) -> None:
        """
        合并浏览代理的token使用统计
        
        Args:
            browse_token_usage: 浏览代理的token使用统计
        """
        # 更新总数
        self.token_usage["total_input_tokens"] += browse_token_usage.get("total_input_tokens", 0)
        self.token_usage["total_output_tokens"] += browse_token_usage.get("total_output_tokens", 0)
        self.token_usage["total_cost"] += browse_token_usage.get("total_cost", 0)
        
        # 合并模型使用情况
        for model_name, usage in browse_token_usage.get("models_usage", {}).items():
            if model_name not in self.token_usage["models_usage"]:
                self.token_usage["models_usage"][model_name] = {
                    "input_tokens": 0,
                    "output_tokens": 0,
                    "cost": 0
                }
            
            self.token_usage["models_usage"][model_name]["input_tokens"] += usage.get("input_tokens", 0)
            self.token_usage["models_usage"][model_name]["output_tokens"] += usage.get("output_tokens", 0)
            self.token_usage["models_usage"][model_name]["cost"] += usage.get("cost", 0)
        
        self._increment_changes()

    # 新增的缓存管理方法
    
    async def cleanup(self) -> None:
        """
        清理资源（在会话结束时调用）
        确保所有未保存的数据都写入MongoDB，并清理相关资源
        """
        try:
            # 停止定时器和待执行的保存任务
            self._stop_auto_save_timer()
            
            # 取消待执行的防抖保存任务
            if self._pending_save_task and not self._pending_save_task.done():
                self._pending_save_task.cancel()
                logger.debug("取消待执行的防抖保存任务")
            
            # 如果有未保存的更改，强制保存到MongoDB
            if self._changes_count > 0:
                logger.info(f"会话结束时有{self._changes_count}个未保存的更改，执行最终保存")
                try:
                    # 使用较短的超时时间进行最终保存
                    original_timeout = self._mongodb_query_timeout
                    self._mongodb_query_timeout = 20000  # 20秒超时
                    
                    await self.save_to_mongodb({"operation_type": "conversation_end", "force_save": True})
                    logger.info(f"会话结束时最终保存成功: {self.conversation_id}")
                    
                    self._mongodb_query_timeout = original_timeout
                except Exception as e:
                    logger.error(f"会话结束时最终保存失败: {str(e)}")
            
            # 清理Redis缓存（可选）
            if self._redis_enabled and self._redis_client:
                try:
                    # 清理所有相关的缓存键
                    keys_to_delete = [
                        await self._get_redis_key("core"),
                        await self._get_redis_key("messages", "meta"),
                        await self._get_redis_key("contexts")
                    ]
                    
                    # 删除消息分页
                    meta_key = await self._get_redis_key("messages", "meta")
                    meta_data = await self._redis_client.get(meta_key)
                    if meta_data and "total_pages" in meta_data:
                        total_pages = meta_data["total_pages"]
                        for page in range(total_pages):
                            page_key = await self._get_redis_key("messages", f"page_{page}")
                            keys_to_delete.append(page_key)
                    
                    # 批量删除
                    if keys_to_delete:
                        await self._redis_client.delete(*keys_to_delete)
                        logger.debug(f"清理Redis缓存: {len(keys_to_delete)}个键")
                except Exception as e:
                    logger.warning(f"清理Redis缓存失败: {str(e)}")
        
            logger.info(f"清理研究内存管理器完成: {self.conversation_id}")
        except Exception as e:
            logger.error(f"清理研究内存管理器失败: {str(e)}")

    def __del__(self):
        """析构函数：确保资源被正确清理"""
        try:
            # 停止定时器
            if hasattr(self, '_save_timer') and self._save_timer:
                self._save_timer.cancel()
                
            # 停止防抖保存任务
            if hasattr(self, '_pending_save_task') and self._pending_save_task and not self._pending_save_task.done():
                self._pending_save_task.cancel()
                
            # 如果有未保存的更改，记录警告
            if hasattr(self, '_changes_count') and self._changes_count > 0:
                logger.warning(f"研究内存管理器销毁时还有{self._changes_count}个未保存的更改: {getattr(self, 'conversation_id', 'unknown')}")
        except:
            pass  # 析构函数中忽略所有异常

    async def get_all_conversations(self, user_id: Optional[str] = None, skip: int = 0, limit: int = 20) -> List[Dict[str, Any]]:
        """
        获取所有对话记录的摘要（适配分离存储架构）
        
        Args:
            user_id: 可选的用户ID过滤
            skip: 跳过数量，默认0
            limit: 返回数量限制，默认20个
            
        Returns:
            对话记录摘要列表
        """
        if not self.mongodb_available:
            logger.warning("MongoDB不可用，无法获取对话记录")
            return []
            
        try:
            # 如果启用分离存储，从核心集合查询
            if self._enable_separated_storage:
                return await self._get_conversations_from_separated_storage(user_id, skip, limit)
            else:
                # 回退到传统单一集合查询
                return await self._get_conversations_from_single_collection(user_id, skip, limit)
                
        except Exception as e:
            logger.error(f"获取对话记录失败: {str(e)}")
            return []
    
    async def _get_conversations_from_separated_storage(self, user_id: Optional[str] = None, skip: int = 0, limit: int = 20) -> List[Dict[str, Any]]:
        """从分离存储的核心集合获取对话记录摘要"""
        try:
            from backend.db.mongodb import MongoDBBase
            
            # 使用核心会话集合
            core_db = MongoDBBase(self._collections["core"])
            
            # 构建查询条件
            query = {}
            if user_id:
                query["user_id"] = user_id
            
            # 直接查询核心集合，按更新时间倒序
            core_docs = await core_db.find_many(
                query, 
                skip=skip, 
                limit=limit,
                sort=[("updated_at", -1)]
            )
            
            # 格式化返回结果
            conversations = []
            for doc in core_docs:
                conversations.append({
                    "conversation_id": doc.conversation_id,
                    "prompt": getattr(doc, 'research_question', ''),
                    "created_at": doc.created_at.isoformat() if hasattr(doc, 'created_at') and isinstance(doc.created_at, datetime) else str(getattr(doc, 'created_at', '')),
                    "updated_at": doc.updated_at.isoformat() if hasattr(doc, 'updated_at') and isinstance(doc.updated_at, datetime) else str(getattr(doc, 'updated_at', '')),
                    "user_id": getattr(doc, 'user_id', None),
                    "report_draft": getattr(doc, 'report_draft', '')
                })
                
            logger.debug(f"从分离存储获取{len(conversations)}个对话记录")
            return conversations
            
        except Exception as e:
            logger.error(f"从分离存储获取对话记录失败: {str(e)}")
            return []
    
    async def _get_conversations_from_single_collection(self, user_id: Optional[str] = None, skip: int = 0, limit: int = 20) -> List[Dict[str, Any]]:
        """从传统单一集合获取对话记录摘要（兼容性）"""
        try:
            # 构建查询条件
            query = {}
            if user_id:
                query["user_id"] = user_id
                
            # 查询唯一的conversation_id，添加分页支持
            pipeline = [
                {"$match": query},
                {"$sort": {"timestamp": -1}},
                {"$group": {
                    "_id": "$conversation_id",
                    "doc": {"$first": "$$ROOT"}
                }},
                {"$replaceRoot": {"newRoot": "$doc"}},
                {"$sort": {"timestamp": -1}},  # 再次排序以确保正确的分页顺序
                {"$skip": skip},
                {"$limit": limit},
                {"$project": {
                    "conversation_id": 1,
                    "research_question": 1,
                    "created_at": "$created_at",
                    "updated_at": "$updated_at",
                    "user_id": 1,
                    "report_draft": 1
                }}
            ]
            
            # 执行聚合查询
            result = await self.db.aggregate(pipeline)
            
            # 返回格式化的对话记录摘要
            conversations = []
            for doc in result:
                conversations.append({
                    "conversation_id": doc["conversation_id"],
                    "prompt": doc["research_question"],
                    "created_at": doc["created_at"].isoformat() if isinstance(doc["created_at"], datetime) else doc["created_at"],
                    "updated_at": doc["updated_at"].isoformat() if isinstance(doc["updated_at"], datetime) else doc["updated_at"],
                    "user_id": doc["user_id"],
                    "report_draft": doc.get("report_draft", "")
                })
                
            logger.debug(f"从传统单一集合获取{len(conversations)}个对话记录")
            return conversations
            
        except Exception as e:
            logger.error(f"从传统单一集合获取对话记录失败: {str(e)}")
            return []
            
    async def save(self, metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        智能保存研究内存记录（推荐使用的保存接口）
        优先使用缓存策略和防抖机制，避免频繁MongoDB操作
        
        Args:
            metadata: 可选的附加元数据
            
        Returns:
            MongoDB的文档ID
        """
        # 立即保存到Redis缓存
        if self._redis_enabled:
            await self._save_to_redis()
        
        # 检查是否需要立即保存到MongoDB
        force_save = metadata and metadata.get("force_save", False)
        
        if force_save:
            # 强制立即保存，跳过防抖
            return await self.save_to_mongodb(metadata)
        else:
            # 使用防抖机制，增加变更计数
            critical = metadata and metadata.get("is_critical", False)
            self._increment_changes(is_critical=critical)
            return self.memory_document_id or ""
    
    async def save_immediately(self, metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        立即保存到MongoDB（跳过防抖，慎用）
        
        Args:
            metadata: 可选的附加元数据
            
        Returns:
            MongoDB的文档ID
        """
        if not metadata:
            metadata = {}
        metadata["force_save"] = True
        return await self.save_to_mongodb(metadata)
    
    async def force_save_to_mongodb(self, metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        强制立即保存到MongoDB
        
        Args:
            metadata: 可选的附加元数据
            
        Returns:
            MongoDB的文档ID
        """
        if not metadata:
            metadata = {}
        metadata["force_save"] = True
        return await self.save_to_mongodb(metadata)

    def _start_auto_save_timer(self):
        """启动自动保存定时器"""
        if not self._redis_enabled:
            return
            
        def auto_save_callback():
            if self._changes_count > 0:
                asyncio.create_task(self._background_save_to_mongodb())
            # 重新设置定时器
            self._save_timer = asyncio.get_event_loop().call_later(
                self._auto_save_interval, auto_save_callback
            )
        
        self._save_timer = asyncio.get_event_loop().call_later(
            self._auto_save_interval, auto_save_callback
        )
    
    def _stop_auto_save_timer(self):
        """停止自动保存定时器"""
        if self._save_timer:
            self._save_timer.cancel()
            self._save_timer = None
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息（用于监控和调试）"""
        return {
            "conversation_id": self.conversation_id,
            "redis_enabled": self._redis_enabled,
            "is_loaded": self._is_loaded_from_storage,
            "is_loading": self._is_loading,
            "changes_count": self._changes_count,
            "critical_changes_count": self._critical_changes_count,
            "last_mongodb_save": self._last_mongodb_save,
            "storage_version_info": {
                "current_version": self.current_storage_version,
                "detected_version": self.detected_storage_version,
                "separated_storage_enabled": self._enable_separated_storage
            },
            "data_sizes": {
                "memory_records": len(self.memory_records),
                "research_trajectory_rounds": len(self.research_trajectory.get("rounds", [])),
                "useful_urls": len(self.research_trajectory.get("useful_urls", [])),
                "user_feedbacks": len(self.research_trajectory.get("user_feedbacks", [])),
                "contexts": len(self.contexts),
                "clarification_questions": len(self.clarification_questions)
            }
        } 
    
    async def _save_core_data(self, metadata: Optional[Dict[str, Any]] = None) -> str:
        """保存核心会话信息到分离的集合"""
        try:
            from backend.db.mongodb import MongoDBBase
            
            # 创建核心数据集合连接
            core_db = MongoDBBase(self._collections["core"])
            
            # 准备核心数据（不包含大容量字段）
            core_data = {
                "conversation_id": self.conversation_id,
                "agent_name": self.agent_name,
                "research_question": self.research_question,
                "final_report": self.final_report,
                "language": self.language,
                "round": self.round,
                "search_times": self.search_times,
                "max_search_times": self.max_search_times,
                "is_paused": self.is_paused,
                "report_draft": self.report_draft,
                "draft_version": self.draft_version,
                "is_updating_report": self.is_updating_report,
                "token_usage": self.token_usage,
                "last_browse_result": self.last_browse_result,
                "updated_at": get_cn_time(),
                "storage_version": self.STORAGE_VERSION_SEPARATED,  # 标识存储版本
                "user_id": metadata.get("user_id") if metadata else None
            }
            
            # 检查是否存在现有记录
            existing = await core_db.find_one({"conversation_id": self.conversation_id})
            
            if existing:
                # 增量更新，只更新变更的字段
                update_data = {}
                for field in self._dirty_fields["core"]:
                    if field in core_data:
                        update_data[field] = core_data[field]
                
                if update_data:
                    update_data["updated_at"] = get_cn_time()
                    update_data["storage_version"] = self.STORAGE_VERSION_SEPARATED  # 确保版本标识更新
                    # 获取文档ID - 处理dict和对象两种情况
                    doc_id = existing.id if hasattr(existing, 'id') else existing.get('_id') or existing.get('id')
                    await core_db.update(str(doc_id), update_data)
                    logger.debug(f"增量更新核心数据: {list(update_data.keys())}")
                
                # 返回文档ID
                doc_id = existing.id if hasattr(existing, 'id') else existing.get('_id') or existing.get('id')
                return str(doc_id)
            else:
                # 创建新记录
                core_data["created_at"] = get_cn_time()
                document = await core_db.create(core_data)
                doc_id = document.id if hasattr(document, 'id') else document.get('_id') or document.get('id')
                self.memory_document_id = str(doc_id)
                logger.info(f"创建新的核心数据记录: {self.memory_document_id}")
                return self.memory_document_id
                
        except Exception as e:
            logger.error(f"保存核心数据失败: {str(e)}")
            raise
    
    async def _save_trajectory_data(self):
        """保存研究轨迹数据到分离的集合"""
        try:
            from backend.db.mongodb import MongoDBBase
            
            trajectory_db = MongoDBBase(self._collections["trajectory"])
            
            # 准备轨迹数据
            trajectory_data = {
                "conversation_id": self.conversation_id,
                "research_trajectory": self.research_trajectory,
                "updated_at": get_cn_time()
            }
            
            # 检查是否存在
            existing = await trajectory_db.find_one({"conversation_id": self.conversation_id})
            
            if existing:
                # 增量更新轨迹
                update_data = {}
                for field in self._dirty_fields["trajectory"]:
                    if field == "research_trajectory":
                        update_data["research_trajectory"] = self.research_trajectory
                
                if update_data:
                    update_data["updated_at"] = get_cn_time()
                    # 获取文档ID - 处理dict和对象两种情况
                    doc_id = existing.id if hasattr(existing, 'id') else existing.get('_id') or existing.get('id')
                    await trajectory_db.update(str(doc_id), update_data)
                    logger.debug(f"增量更新研究轨迹")
            else:
                trajectory_data["created_at"] = get_cn_time()
                await trajectory_db.create(trajectory_data)
                logger.debug(f"创建新的研究轨迹记录")
                
        except Exception as e:
            logger.error(f"保存研究轨迹失败: {str(e)}")
            raise
    
    async def _save_memory_records(self):
        """保存新增的内存记录到分离的集合"""
        try:
            if not self._dirty_fields["memory"]:
                return
                
            from backend.db.mongodb import MongoDBBase
            
            memory_db = MongoDBBase(self._collections["memory"])
            
            # 批量插入新的内存记录
            new_records = []
            for record in self._dirty_fields["memory"]:
                record_data = {
                    "conversation_id": self.conversation_id,
                    "record_data": record.model_dump() if hasattr(record, 'model_dump') else record,
                    "created_at": get_cn_time()
                }
                new_records.append(record_data)
            
            if new_records:
                # 批量插入
                for record_data in new_records:
                    await memory_db.create(record_data)
                logger.debug(f"批量插入{len(new_records)}条内存记录")
                
        except Exception as e:
            logger.error(f"保存内存记录失败: {str(e)}")
            raise
    
    async def _save_contexts_data(self):
        """保存新增/更新的上下文到分离的集合"""
        try:
            if not self._dirty_fields["contexts"]:
                return
                
            from backend.db.mongodb import MongoDBBase
            
            contexts_db = MongoDBBase(self._collections["contexts"])
            
            # 处理新增/更新的上下文
            for context in self._dirty_fields["contexts"]:
                context_data = {
                    "conversation_id": self.conversation_id,
                    "context_id": context.context_id,
                    "content": context.get_context(),
                    "metadata": context.get_metadata(),
                    "updated_at": get_cn_time()
                }
                
                # 检查是否存在
                existing = await contexts_db.find_one({
                    "conversation_id": self.conversation_id,
                    "context_id": context.context_id
                })
                
                if existing:
                    # 更新现有上下文
                    doc_id = existing.id if hasattr(existing, 'id') else existing.get('_id') or existing.get('id')
                    await contexts_db.update(str(doc_id), context_data)
                else:
                    # 创建新上下文
                    context_data["created_at"] = get_cn_time()
                    await contexts_db.create(context_data)
                    
            logger.debug(f"处理{len(self._dirty_fields['contexts'])}个上下文")
                
        except Exception as e:
            logger.error(f"保存上下文失败: {str(e)}")
            raise
    
    async def _save_preferences_data(self):
        """保存新增/更新的用户偏好到分离的集合"""
        try:
            if not self._dirty_fields["preferences"]:
                return
                
            from backend.db.mongodb import MongoDBBase
            
            preferences_db = MongoDBBase(self._collections["preferences"])
            
            # 准备偏好数据
            preferences_data = {
                "conversation_id": self.conversation_id,
                "user_preferences": self.user_preferences,
                "user_likes_urls": self.user_likes_urls,
                "user_draft_preferences": self.user_draft_preferences,
                "clarification_questions": self.clarification_questions,
                "pending_clarification_questions": self.pending_clarification_questions,
                "updated_at": get_cn_time()
            }
            
            # 检查是否存在
            existing = await preferences_db.find_one({"conversation_id": self.conversation_id})
            
            if existing:
                # 更新现有偏好
                doc_id = existing.id if hasattr(existing, 'id') else existing.get('_id') or existing.get('id')
                await preferences_db.update(str(doc_id), preferences_data)
            else:
                # 创建新偏好记录
                preferences_data["created_at"] = get_cn_time()
                await preferences_db.create(preferences_data)
                
            logger.debug(f"更新用户偏好数据")
                
        except Exception as e:
            logger.error(f"保存用户偏好失败: {str(e)}")
            raise
    
    def _mark_field_dirty(self, collection: str, field: str):
        """标记字段为脏数据，需要更新"""
        if collection in self._dirty_fields:
            if isinstance(self._dirty_fields[collection], set):
                self._dirty_fields[collection].add(field)
            else:
                # 对于列表类型，直接添加对象引用
                pass  # 在具体的添加方法中处理
    
    def _mark_memory_dirty(self, record):
        """标记新增的内存记录"""
        self._dirty_fields["memory"].append(record)
    
    def _mark_context_dirty(self, context):
        """标记新增/更新的上下文"""
        self._dirty_fields["contexts"].append(context)
    
    def _mark_preference_dirty(self):
        """标记用户偏好需要更新"""
        self._dirty_fields["preferences"].append("updated")
    
    async def _detect_storage_version(self, conversation_id: str) -> str:
        """
        检测现有数据的存储版本格式
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            检测到的存储版本 (v1.0 或 v2.0)
        """
        try:
            # 首先检查分离存储格式（v2.0）
            if self._enable_separated_storage:
                from backend.db.mongodb import MongoDBBase
                
                try:
                    core_db = MongoDBBase(self._collections["core"])
                    core_doc = await core_db.find_one({"conversation_id": conversation_id})
                    
                    if core_doc:
                        # 检查是否有storage_version字段
                        version = getattr(core_doc, 'storage_version', None)
                        if version:
                            logger.info(f"检测到存储版本标识: {version}")
                            return version
                        else:
                            # 如果在分离存储集合中找到数据但没有版本标识，说明是早期v2.0版本
                            logger.info(f"检测到分离存储格式但无版本标识，判定为v2.0")
                            return self.STORAGE_VERSION_SEPARATED
                            
                except Exception as e:
                    logger.debug(f"检查分离存储失败: {str(e)}")
            
            # 检查传统单文档存储格式（v1.0）
            if self.mongodb_available:
                try:
                    documents = await self.db.find_many(
                        {"conversation_id": conversation_id}, 
                        skip=0, 
                        limit=1
                    )
                    
                    if documents:
                        document = documents[0]
                        # 检查是否有storage_version字段
                        version = getattr(document, 'storage_version', None)
                        if version:
                            logger.info(f"检测到存储版本标识: {version}")
                            return version
                        else:
                            # 如果在传统集合中找到数据但没有版本标识，说明是v1.0版本
                            logger.info(f"检测到传统存储格式，判定为v1.0")
                            return self.STORAGE_VERSION_SINGLE
                            
                except Exception as e:
                    logger.debug(f"检查传统存储失败: {str(e)}")
            
            # 如果都没有找到数据，返回当前默认版本
            logger.info(f"未找到现有数据，使用默认存储版本: {self.current_storage_version}")
            return self.current_storage_version
            
        except Exception as e:
            logger.error(f"检测存储版本失败: {str(e)}")
            return self.current_storage_version
    
    async def _should_use_separated_storage(self, conversation_id: str) -> bool:
        """
        根据检测到的存储版本决定是否使用分离存储
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            是否应该使用分离存储
        """
        if not self.detected_storage_version:
            self.detected_storage_version = await self._detect_storage_version(conversation_id)
        
        # 根据检测到的版本决定存储策略
        if self.detected_storage_version == self.STORAGE_VERSION_SEPARATED:
            return True
        elif self.detected_storage_version == self.STORAGE_VERSION_SINGLE:
            return False
        else:
            # 未知版本，使用默认策略
            return self._enable_separated_storage
    
    async def migrate_to_separated_storage(self, conversation_id: str) -> bool:
        """
        将传统存储格式（v1.0）迁移到分离存储格式（v2.0）
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            是否迁移成功
        """
        try:
            logger.info(f"开始迁移存储格式: {conversation_id} (v1.0 -> v2.0)")
            
            # 首先从传统格式加载数据
            if not self.mongodb_available:
                logger.error("MongoDB不可用，无法执行迁移")
                return False
            
            # 查询传统集合中的数据
            documents = await self.db.find_many(
                {"conversation_id": conversation_id}, 
                skip=0, 
                limit=1
            )
            
            if not documents:
                logger.warning(f"未找到需要迁移的数据: {conversation_id}")
                return False
            
            document = documents[0]
            
            # 临时加载数据到当前实例
            old_storage_version = self.detected_storage_version
            self._load_core_data_from_document(document)
            
            # 强制使用分离存储格式保存
            self.detected_storage_version = self.STORAGE_VERSION_SEPARATED
            self.current_storage_version = self.STORAGE_VERSION_SEPARATED
            
            # 保存到分离存储格式
            result = await self._save_with_separated_storage({
                "migration": True,
                "source_version": self.STORAGE_VERSION_SINGLE,
                "target_version": self.STORAGE_VERSION_SEPARATED
            })
            
            if result:
                # 迁移成功，可以选择删除旧数据（可选）
                # await self.db.delete(str(document.id))
                logger.info(f"迁移成功: {conversation_id} -> {result}")
                
                # 在传统文档中标记已迁移（而不是删除，保证数据安全）
                await self.db.update(str(document.id), {
                    "migrated_to_separated": True,
                    "migrated_at": get_cn_time(),
                    "new_storage_id": result
                })
                
                return True
            else:
                # 迁移失败，恢复原状态
                self.detected_storage_version = old_storage_version
                logger.error(f"迁移失败: {conversation_id}")
                return False
                
        except Exception as e:
            logger.error(f"迁移过程中发生错误: {str(e)}")
            return False
    
    async def check_and_auto_migrate(self, conversation_id: str) -> bool:
        """
        检查并自动迁移旧格式数据（可选功能）
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            是否需要迁移或迁移是否成功
        """
        try:
            # 检测存储版本
            detected_version = await self._detect_storage_version(conversation_id)
            
            # 如果检测到v1.0格式且当前默认使用v2.0，询问是否迁移
            if (detected_version == self.STORAGE_VERSION_SINGLE and 
                self.current_storage_version == self.STORAGE_VERSION_SEPARATED):
                
                logger.info(f"检测到旧格式数据，建议迁移: {conversation_id}")
                
                # 这里可以添加配置选项来决定是否自动迁移
                auto_migrate = getattr(self, '_auto_migrate_enabled', False)
                
                if auto_migrate:
                    logger.info(f"自动迁移已启用，开始迁移: {conversation_id}")
                    return await self.migrate_to_separated_storage(conversation_id)
                else:
                    logger.info(f"自动迁移未启用，保持兼容模式: {conversation_id}")
                    return True
            
            return True
            
        except Exception as e:
            logger.error(f"检查和自动迁移失败: {str(e)}")
            return False
    
    def enable_auto_migration(self, enable: bool = True) -> None:
        """
        启用或禁用自动迁移功能
        
        Args:
            enable: 是否启用自动迁移
        """
        self._auto_migrate_enabled = enable
        logger.info(f"自动迁移功能已{'启用' if enable else '禁用'}")
    
    def get_storage_version_info(self) -> Dict[str, str]:
        """
        获取存储版本信息
        
        Returns:
            包含当前版本和检测版本的字典
        """
        return {
            "current_version": self.current_storage_version,
            "detected_version": self.detected_storage_version or "未检测",
            "single_version": self.STORAGE_VERSION_SINGLE,
            "separated_version": self.STORAGE_VERSION_SEPARATED
        }

    # 用户偏好相关方法
    def set_user_draft_preferences(self, preferences: Dict[str, Any]) -> None:
        """
        设置用户偏好
        
        Args:
            preferences: 偏好设置字典，包含各项偏好及其值（1-5）
        """
        self.user_draft_preferences = preferences
        logger.info(f"设置用户偏好: {preferences}")
        
        # 标记偏好数据需要更新
        self._mark_preference_dirty()
        self._increment_changes(is_critical=True)  # 用户偏好是关键变更
    
    def get_user_draft_preferences(self) -> Dict[str, Any]:
        """
        获取当前用户偏好
        
        Returns:
            用户偏好设置字典
        """
        return self.user_draft_preferences.copy()  # 返回副本以防止外部修改
    
    def update_user_draft_preference(self, key: str, value: Any) -> None:
        """
        更新单个用户偏好
        
        Args:
            key: 偏好键名
            value: 偏好值（1-5）
        """
        if key in self.user_draft_preferences:
            old_value = self.user_draft_preferences[key]
            self.user_draft_preferences[key] = value
            
            # 标记偏好数据需要更新
            self._mark_preference_dirty()
            self._increment_changes(is_critical=True)  # 用户偏好是关键变更
            
            logger.info(f"更新用户偏好 {key}: {old_value} -> {value}")
        else:
            logger.warning(f"不支持的偏好键: {key}")