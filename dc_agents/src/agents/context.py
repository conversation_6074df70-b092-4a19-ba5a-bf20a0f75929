import os
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from datetime import datetime
from loguru import logger

class BaseContext(ABC):
    """
    上下文基类，定义了上下文管理的基本接口
    """
    
    def __init__(self, context_id: str):
        """
        初始化上下文
        
        Args:
            context_id: 上下文的唯一标识符
        """
        self.context_id = context_id
        self.created_at = datetime.now()
        self.metadata: Dict[str, Any] = {}
        
    @abstractmethod
    def get_context(self) -> str:
        """
        获取上下文内容
        
        Returns:
            上下文内容字符串
        """
        pass
        
    @abstractmethod
    def add_context(self, content: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        添加上下文内容
        
        Args:
            content: 上下文内容
            metadata: 可选的元数据
            
        Returns:
            是否添加成功
        """
        pass
        
    def get_metadata(self) -> Dict[str, Any]:
        """
        获取上下文元数据
        
        Returns:
            元数据字典
        """
        return self.metadata
        
    def update_metadata(self, metadata: Dict[str, Any]) -> None:
        """
        更新上下文元数据
        
        Args:
            metadata: 新的元数据
        """
        self.metadata.update(metadata)

    def set_context_id(self, context_id: str) -> None:
        """
        设置上下文的唯一标识符
        
        Args:
            context_id: 新的上下文标识符
        """
        self.context_id = context_id
        
    def set_content(self, content: str) -> None:
        """
        设置上下文内容
        
        Args:
            content: 新的上下文内容
        """
        self.content = content