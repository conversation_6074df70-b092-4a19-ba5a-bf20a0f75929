from typing import Dict
from .context import BaseContext

class NoteContext(BaseContext):
    """
    基于笔记的上下文实现
    """
    
    def __init__(self, note_id: str, user_id: str):
        """
        初始化笔记上下文
        """
        super().__init__(f"note_{note_id}:user_{user_id}")
        self.content = ""
        self.title = ""
        self.metadata = {
            "type": "note",
            "note_id": note_id,
            "user_id": user_id
        }
    
    def get_context(self) -> str:
        return self.content
    
    def add_context(self, content: str, title: str) -> bool:
        self.content = content
        self.title = title
        return True
    