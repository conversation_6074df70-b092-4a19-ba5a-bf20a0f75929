from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from dc_agents.src.services.llm_service import LLMService


class Agent(ABC):
    """Base class for all agents in the multi-agent system."""
    
    def __init__(self, 
                 name: str, 
                 config: Optional[Dict[str, Any]] = None,
                 instruction: Optional[str] = None,
                 tools: Optional[List[str]] = None):
        """
        Initialize a base agent.
        
        Args:
            name: The name of the agent
            config: Optional configuration dictionary for the agent
            llm_config: Optional configuration dictionary for the LLM service
            instruction: Optional instruction for the agent
            tools: Optional list of tools for the agent
        """
        self.name = name
        self.config = config or {}
        self.instruction = instruction
        self.tools = tools

    @abstractmethod
    async def run(self, input_data: Any) -> Any:
        """
        Run the agent with the given input data.
        
        Args:
            input_data: The input data for the agent to process
            
        Returns:
            The output of the agent's processing
        """
        pass
