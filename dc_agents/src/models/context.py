from pydantic import BaseModel, Field
from typing import Dict, Any, Optional
from datetime import datetime
from bson import ObjectId

class BaseContextModel(BaseModel):
    """基础上下文模型"""
    context_id: str
    content: Any
    metadata: Optional[Dict[str, Any]] = None
    
    def model_dump(self, **kwargs):
        """重写模型转换为字典的方法，确保内容可序列化"""
        dump_data = super().model_dump(**kwargs)
        
        # 处理可能无法序列化的 content
        if isinstance(dump_data["content"], (dict, list)):
            try:
                # 尝试 JSON 序列化测试
                import json
                json.dumps(dump_data["content"])
            except (TypeError, OverflowError):
                dump_data["content"] = str(dump_data["content"])
        else:
            dump_data["content"] = str(dump_data["content"])
            
        return dump_data
    
    model_config = {
        "arbitrary_types_allowed": True,
        "json_encoders": {ObjectId: str},
        "json_schema_extra": {
            "example": {
                "context_id": "ctx_123",
                "content": "上下文内容",
                "metadata": {"source": "file", "file_path": "/path/to/file"}
            }
        }
    }

class FileContextModel(BaseContextModel):
    """文件上下文模型"""
    metadata: Dict[str, Any] = Field(
        default_factory=lambda: {"type": "file"},
        description="文件上下文元数据"
    )
    
    @classmethod
    def from_file_context(cls, file_path: str, content: Any = None):
        """从文件路径创建文件上下文模型"""
        return cls(
            context_id=f"file_{file_path}",
            content=content or "",
            metadata={
                "type": "file",
                "file_path": file_path,
                "created_at": datetime.now().isoformat()
            }
        ) 

class NoteContextModel(BaseContextModel):
    """笔记上下文模型"""
    metadata: Dict[str, Any] = Field(
        default_factory=lambda: {"type": "note"},
        description="笔记上下文元数据"
    )
    
    @classmethod
    def from_note_context(cls, note_id: str, user_id: str, content: Any = None):
        return cls(
            context_id=f"note_{note_id}:user_{user_id}",
            content=content or "",
            metadata={
                "type": "note",
                "note_id": note_id,
                "user_id": user_id,
                "created_at": datetime.now().isoformat()
            }
        )