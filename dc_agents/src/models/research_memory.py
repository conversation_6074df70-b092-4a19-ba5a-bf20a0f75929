from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from datetime import datetime, timezone, timedelta
from bson import ObjectId

from backend.db.mongodb import PyObjectId
from dc_agents.src.models.memory import MemoryRecord
from dc_agents.src.models.context import BaseContextModel, FileContextModel

# 返回东八区（北京时间）的当前时间
def get_cn_time():
    """返回东八区（北京时间）的当前时间"""
    return datetime.now(timezone(timedelta(hours=8)))


class ResearchMemoryModel(BaseModel):
    """研究记忆的MongoDB模型"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    agent_name: str
    conversation_id: str
    timestamp: datetime = Field(default_factory=get_cn_time)
    
    # 内存记录
    memory_records: List[MemoryRecord] = []
    
    # context记录
    contexts: List[BaseContextModel] = []

    # 研究轨迹相关属性
    research_question: str = ""
    final_report: str = ""
    research_trajectory: Dict[str, Any] = Field(default_factory=dict)
    
    # token使用统计
    token_usage: Dict[str, Any] = Field(
        default_factory=lambda: {
            "total_input_tokens": 0,
            "total_output_tokens": 0,
            "total_cost": 0,
            "models_usage": {}
        }
    )
    
    # 研究状态控制变量
    language: Optional[str] = None
    round: int = 0
    search_times: int = 0
    max_search_times: int = 10
    is_paused: bool = False
    
    # 报告相关状态
    report_draft: str = ""
    draft_version: int = 0
    is_updating_report: bool = False
    
    # 澄清问题相关状态
    clarification_questions: List[Dict[str, Any]] = Field(default_factory=list)
    pending_clarification_questions: List[Any] = Field(default_factory=list)

    # 用户偏好
    user_preferences: List[Any] = Field(default_factory=list)
    user_likes_urls: List[Dict[str, Any]] = Field(default_factory=list)
    
    # 研究规则
    research_rules: List[str] = Field(default_factory=list)
    
    # 用户偏好设置 - 对话级别的偏好
    user_preferences: Dict[str, Any] = Field(
        default_factory=lambda: {
            "professional": 3,      # 专业性 (1-5)
            "critical": 3,          # 批判性 (1-5) 
            "comparison": 3,        # 表格对比 (1-5)
            "organization": 3,      # 组织性 (1-5)
            "cutting_edge": 3,      # 前沿性 (1-5)
            "coverage": 3,          # 覆盖面 (1-5)
            "depth": 3              # 深度 (1-5)
        }
    )
    
    # 最后浏览结果
    last_browse_result: Optional[Dict[str, Any]] = None
    
    # 用户关联
    user_id: Optional[str] = None
    
    # 元数据
    metadata: Optional[Dict[str, Any]] = None
    
    # 时间戳
    created_at: datetime = Field(default_factory=get_cn_time)
    updated_at: datetime = Field(default_factory=get_cn_time)
    
    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
        "json_encoders": {ObjectId: str},
        "json_schema_extra": {
            "example": {
                "agent_name": "ResearchAgent",
                "conversation_id": "conv_123",
                "memory_records": [],
                "contexts": [
                    {
                        "context_id": "file_/path/to/file",
                        "content": "文件内容",
                        "metadata": {
                            "type": "file",
                            "file_path": "/path/to/file"
                        }
                    }
                ],
                "metadata": {
                    "session_info": "研究会话详情"
                }
            }
        }
    } 