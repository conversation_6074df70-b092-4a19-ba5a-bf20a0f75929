from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone, timedelta
from bson import ObjectId
import json

from backend.db.mongodb import PyObjectId

# 返回东八区（北京时间）的当前时间
def get_cn_time():
    """返回东八区（北京时间）的当前时间"""
    return datetime.now(timezone(timedelta(hours=8)))


class MemoryRecord(BaseModel):
    """内存记录单条事件的模型"""
    type: str
    timestamp: str
    content: Any
    metadata: Optional[Dict[str, Any]] = None
    
    def model_dump(self, **kwargs):
        """重写模型转换为字典的方法，确保内容可序列化"""
        dump_data = super().model_dump(**kwargs)
        
        # 处理可能无法序列化的 content
        try:
            # 尝试 JSON 序列化测试
            json.dumps(dump_data["content"])
        except (TypeError, OverflowError):
            # 如果是字典类型，遍历处理每个值
            if isinstance(dump_data["content"], dict):
                serialized_content = {}
                for k, v in dump_data["content"].items():
                    try:
                        # 测试值是否可序列化
                        json.dumps(v)
                        serialized_content[k] = v
                    except (TypeError, OverflowError):
                        # 不可序列化的值转为字符串
                        serialized_content[k] = str(v)
                dump_data["content"] = serialized_content
            else:
                # 非字典类型转为字符串
                dump_data["content"] = str(dump_data["content"])
        
        # 处理可能无法序列化的 metadata
        if dump_data["metadata"]:
            try:
                json.dumps(dump_data["metadata"])
            except (TypeError, OverflowError):
                # 如果是字典类型，遍历处理每个值
                if isinstance(dump_data["metadata"], dict):
                    serialized_metadata = {}
                    for k, v in dump_data["metadata"].items():
                        try:
                            json.dumps(v)
                            serialized_metadata[k] = v
                        except (TypeError, OverflowError):
                            serialized_metadata[k] = str(v)
                    dump_data["metadata"] = serialized_metadata
                else:
                    dump_data["metadata"] = str(dump_data["metadata"])
            
        return dump_data
    
    model_config = {
        "arbitrary_types_allowed": True,
        "json_encoders": {ObjectId: str},
        "json_schema_extra": {
            "example": {
                "type": "user_message",
                "timestamp": "2023-08-01T15:22:33.123456",
                "content": "Hello, agent!",
                "metadata": {"source": "chat"}
            }
        }
    }


class MemoryModel(BaseModel):
    """内存记录的MongoDB模型"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    agent_name: str
    conversation_id: str
    timestamp: datetime = Field(default_factory=get_cn_time)
    memory_records: List[MemoryRecord] = []
    metadata: Optional[Dict[str, Any]] = None
    created_at: datetime = Field(default_factory=get_cn_time)
    updated_at: datetime = Field(default_factory=get_cn_time)
    
    class Config:
        populate_by_name = True
        json_schema_extra = {
            "example": {
                "agent_name": "DeepAgent",
                "conversation_id": "DeepAgent_20230801152233",
                "memory_records": [
                    {
                        "type": "user_message",
                        "timestamp": "2023-08-01T15:22:33.123456",
                        "content": "Hello, agent!",
                        "metadata": {"source": "chat"}
                    }
                ],
                "metadata": {
                    "session_info": "用户会话详情"
                }
            }
        } 