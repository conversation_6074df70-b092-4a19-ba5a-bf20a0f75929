import requests
from typing import List, Dict, AsyncGenerator, Any
import logging
import yaml


logger = logging.getLogger(__name__)


class RAGService:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.endpoint = config["endpoint"]
        self.k = config["k"]
        self.user = config["user"]
        self.password = config["password"]
        self.index = config["index"]
        self.search_field = config["search_field"]
        self.search_pipeline = config["search_pipeline"]


    async def search(self, query: str) -> AsyncGenerator[List[Dict[str, Any]], None]:
        """
        执行RAG搜索，默认使用混合搜索
        
        Args:
            query: 搜索查询字符串
            
        Returns:
            List[Dict[str, Any]]: 搜索结果列表，每个结果包含文档ID、标题、摘要和相关性得分
        """
        if not query:
            yield []
        
        request_body = {
            "size": self.k,
            "query": {
                "hybrid": {
                    "queries": [
                        {
                            "match": {
                                self.search_field: query
                            }
                        },
                        {
                            "remote_neural": {
                                f"{self.search_field}_knn": {
                                    "query_text": query,
                                    "k": self.k
                                }
                            }
                        }
                    ]
                }
            }
        }
        headers = {"Content-Type": "application/json"}

        try:
            response = requests.post(
                f"{self.endpoint}/{self.index}/_search?search_pipeline={self.search_pipeline}", 
                json=request_body, 
                headers=headers,
                auth=(self.user, self.password),
                verify=False
            )
            response.raise_for_status()
            results = response.json()
            hits = results.get("hits", {}).get("hits", [])
            yield [hit["_source"] for hit in hits]
        except Exception as e:
            logger.error(f"RAG搜索失败: {str(e)}")
            yield []

    # TODO: 添加_msearch方法，当前的使用了火山云的opensearch，但其只支持2.9版本，这个版本的_msearch方法不支持search_pipeline参数
    # async def _msearch(self, queries: List[str]) -> AsyncGenerator[List[Dict[str, Any]], None]:


class CognitionRAGService(RAGService):
    def __init__(self):
        config = yaml.load(open("dc_agents/config/service_config.yaml"), Loader=yaml.FullLoader)
        super().__init__(config["cognition_base_opensearch"])
