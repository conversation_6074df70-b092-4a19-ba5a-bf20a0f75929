from typing import Dict, Any, Optional, List, AsyncGenerator
from openai import AsyncOpenAI, AsyncAzureOpenAI
from openai.types.chat import Chat<PERSON><PERSON>ple<PERSON>, ChatCompletionChunk, ChatCompletionMessage
from openai.types.chat.chat_completion import Choice as CompletionChoice
from openai.types.chat.chat_completion_chunk import Choice as ChunkChoice, ChoiceDelta
from openai.types.completion_usage import CompletionUsage
import anthropic
from loguru import logger
import yaml
import tiktoken  # 用于计算token
import json
import threading
import asyncio
import aiohttp
import time
import uuid


class TokenizerSingleton:
    """Claude tokenizer单例类，避免重复加载，异步安全"""

    _instance = None
    _async_lock = asyncio.Lock()
    _thread_lock = threading.Lock()
    _claude_tokenizer = None
    _loaded = False

    def __new__(cls):
        if cls._instance is None:
            with cls._thread_lock:
                if cls._instance is None:
                    cls._instance = super(TokenizerSingleton, cls).__new__(cls)
        return cls._instance

    async def get_claude_tokenizer_async(self):
        """异步获取Claude tokenizer，如果未加载则加载"""
        if not self._loaded:
            async with self._async_lock:
                if not self._loaded:
                    try:
                        # 在后台线程中加载tokenizer，避免阻塞异步循环
                        def load_tokenizer():
                            try:
                                from transformers import GPT2TokenizerFast

                                return GPT2TokenizerFast.from_pretrained(
                                    "Xenova/claude-tokenizer"
                                )
                            except ImportError:
                                logger.warning(
                                    "未安装transformers库，无法加载Claude tokenizer"
                                )
                                return None
                            except Exception as e:
                                logger.warning(f"加载Claude tokenizer失败: {e}")
                                return None

                        logger.info(
                            "开始异步加载Claude tokenizer (Xenova/claude-tokenizer)..."
                        )
                        loop = asyncio.get_event_loop()
                        self._claude_tokenizer = await loop.run_in_executor(
                            None, load_tokenizer
                        )
                        self._loaded = True
                        if self._claude_tokenizer:
                            logger.info("Claude tokenizer异步加载完成")
                        else:
                            logger.warning("Claude tokenizer加载失败")
                    except Exception as e:
                        logger.error(f"异步加载Claude tokenizer出错: {e}")
                        self._claude_tokenizer = None
                        self._loaded = True

        return self._claude_tokenizer

    def get_claude_tokenizer(self):
        """同步获取Claude tokenizer，如果未加载则加载"""
        if not self._loaded:
            with self._thread_lock:
                if not self._loaded:
                    try:
                        from transformers import GPT2TokenizerFast

                        logger.info(
                            "开始加载Claude tokenizer (Xenova/claude-tokenizer)..."
                        )
                        self._claude_tokenizer = GPT2TokenizerFast.from_pretrained(
                            "Xenova/claude-tokenizer"
                        )
                        self._loaded = True
                        logger.info("Claude tokenizer加载完成")
                    except ImportError:
                        logger.warning("未安装transformers库，无法加载Claude tokenizer")
                        self._claude_tokenizer = None
                        self._loaded = True
                    except Exception as e:
                        logger.warning(f"加载Claude tokenizer失败: {e}")
                        self._claude_tokenizer = None
                        self._loaded = True

        return self._claude_tokenizer


class AnthropicNativeAdapter:
    """适配器，用于处理使用Anthropic官方SDK但返回OpenAI格式"""

    def __init__(self, api_key: str, api_url: str, timeout: float = 300.0):
        self.api_key = api_key
        self.api_url = api_url
        self.timeout = timeout
        self._chunk_counter = 0  # 用于生成唯一的chunk ID

        # 创建Anthropic客户端
        self.client = anthropic.Anthropic(
            api_key=api_key, base_url=api_url, timeout=timeout
        )

    async def chat_completions_create(
        self,
        model: str,
        messages: List[Dict[str, Any]],
        temperature: float = 0.7,
        max_tokens: int = 1000,
        stream: bool = False,
        is_reasoning_model: bool = False,
        **kwargs,
    ):
        """创建聊天完成请求，模拟OpenAI接口"""

        # 对于这个特殊的Anthropic API，temperature必须为1.0
        temperature = 1.0

        # 提取system prompt
        system_prompt = ""
        messages_without_system = []
        for message in messages:
            if message["role"] == "system":
                system_prompt = message["content"]
            else:
                messages_without_system.append(message)

        # 构建请求参数
        request_params = {
            "model": model,
            "max_tokens": max_tokens,
            "stream": stream,
            "system": system_prompt,
            "temperature": temperature,
            "messages": messages_without_system,
        }

        # 只有当is_reasoning_model为True时才添加thinking字段
        if is_reasoning_model:
            # 确保max_tokens足够大，至少要比budget_tokens大
            budget_tokens = 1025
            if (
                max_tokens < budget_tokens + 500
            ):  # 给thinking留出足够空间，再给实际回答留500tokens
                max_tokens = budget_tokens + 1000  # 总共给2025 tokens
                request_params["max_tokens"] = max_tokens

            request_params["thinking"] = {
                "type": "enabled",
                "budget_tokens": budget_tokens,
            }

        logger.info(f"使用Anthropic SDK调用: {model}")
        # 只在debug模式下记录详细请求参数
        if logger.level <= 10:  # DEBUG level
            logger.debug(
                f"请求参数: {json.dumps(request_params, ensure_ascii=False, indent=2)}"
            )

        if stream:
            logger.info("进入流式模式")
            return self._handle_stream_response(request_params)
        else:
            logger.info("进入同步模式")
            return await self._handle_sync_response(request_params)

    async def _handle_sync_response(self, request_params: dict):
        """处理非流式响应"""
        try:
            # 使用Anthropic SDK进行同步调用
            response = self.client.messages.create(**request_params)

            # 只在debug模式下记录详细响应
            if logger.level <= 10:  # DEBUG level
                logger.debug(f"Anthropic API响应: {response}")
            else:
                logger.info("Anthropic API调用成功")

            # 转换为OpenAI格式
            openai_response = self._convert_anthropic_message_to_openai_format(response)
            return openai_response

        except Exception as e:
            logger.error(f"Anthropic API调用失败: {e}")
            raise

    async def _handle_stream_response(self, request_params: dict):
        """处理流式响应 - 修复为正确的异步生成器"""
        try:
            # 重置chunk计数器
            self._chunk_counter = 0

            # 使用Anthropic SDK进行流式调用
            stream = self.client.messages.create(**request_params)

            for chunk in stream:
                # 只在debug模式下记录详细的chunk信息
                if logger.level <= 10:  # DEBUG level
                    logger.debug(f"Anthropic流式响应: {chunk}")

                # 转换为OpenAI格式的流式chunk
                openai_chunk = self._convert_anthropic_chunk_to_openai_format(chunk)
                if openai_chunk:
                    yield openai_chunk

        except Exception as e:
            logger.error(f"Anthropic流式API调用失败: {e}")
            raise

    def _convert_anthropic_message_to_openai_format(self, anthropic_message):
        """将Anthropic Message对象转换为OpenAI格式"""

        # 提取thinking和content
        thinking_content = ""
        text_content = ""

        for content_block in anthropic_message.content:
            block_type = getattr(content_block, "type", None)
            if block_type == "thinking":
                thinking_content = getattr(content_block, "thinking", "")
            elif block_type == "text":
                text_content = getattr(content_block, "text", "")

        # 合并thinking和content（如果有thinking的话）
        full_content = ""
        if thinking_content:
            full_content += thinking_content
        if text_content:
            full_content += text_content

        # 创建ChatCompletionMessage
        message = ChatCompletionMessage(content=full_content, role="assistant")
        # 添加自定义属性以保存thinking内容
        if thinking_content:
            message.reasoning_content = thinking_content

        # 创建Choice
        choice = CompletionChoice(
            finish_reason=anthropic_message.stop_reason or "stop",
            index=0,
            message=message,
        )

        # 创建Usage
        usage = CompletionUsage(
            prompt_tokens=(
                anthropic_message.usage.input_tokens if anthropic_message.usage else 0
            ),
            completion_tokens=(
                anthropic_message.usage.output_tokens if anthropic_message.usage else 0
            ),
            total_tokens=(
                (
                    anthropic_message.usage.input_tokens
                    + anthropic_message.usage.output_tokens
                )
                if anthropic_message.usage
                else 0
            ),
        )

        # 构建ChatCompletion对象
        return ChatCompletion(
            id=anthropic_message.id or f"chatcmpl-{uuid.uuid4().hex[:29]}",
            choices=[choice],
            created=int(time.time()),
            model=(
                anthropic_message.model if hasattr(anthropic_message, "model") else ""
            ),
            object="chat.completion",
            usage=usage,
        )

    def _convert_anthropic_chunk_to_openai_format(self, anthropic_chunk):
        """将Anthropic流式chunk转换为OpenAI格式 - 优化版本"""

        # 获取chunk类型，避免重复的hasattr调用
        chunk_type = getattr(anthropic_chunk, "type", None)
        if not chunk_type:
            return None

        # 递增chunk计数器，确保ID唯一
        self._chunk_counter += 1

        # 处理content_block_delta事件
        if chunk_type == "content_block_delta":
            delta = anthropic_chunk.delta
            delta_type = getattr(delta, "type", None)

            content = ""
            reasoning_content = ""

            # 处理thinking内容
            if delta_type == "thinking_delta":
                reasoning_content = getattr(delta, "thinking", "")
            # 处理普通文本内容
            elif delta_type == "text_delta":
                content = getattr(delta, "text", "")

            # 只有当有实际内容时才返回chunk
            if content or reasoning_content:
                # 创建ChoiceDelta
                choice_delta = ChoiceDelta(
                    content=content if content else None,
                    function_call=None,
                    refusal=None,
                    role=None,
                    tool_calls=None,
                )
                # 添加自定义属性以保存thinking内容
                if reasoning_content:
                    choice_delta.reasoning_content = reasoning_content

                # 创建ChunkChoice
                choice = ChunkChoice(
                    delta=choice_delta, finish_reason=None, index=0, logprobs=None
                )

                # 构建ChatCompletionChunk对象，使用唯一ID
                return ChatCompletionChunk(
                    id=f"chatcmpl-delta-{self._chunk_counter}",
                    choices=[choice],
                    created=int(time.time()),
                    model="",
                    object="chat.completion.chunk",
                )

        # 处理content_block_start事件
        elif chunk_type == "content_block_start":
            # 开始事件，返回一个空的delta
            choice_delta = ChoiceDelta(
                content=None,
                function_call=None,
                refusal=None,
                role="assistant",
                tool_calls=None,
            )

            choice = ChunkChoice(
                delta=choice_delta, finish_reason=None, index=0, logprobs=None
            )

            return ChatCompletionChunk(
                id=f"chatcmpl-start-{self._chunk_counter}",
                choices=[choice],
                created=int(time.time()),
                model="",
                object="chat.completion.chunk",
            )

        # 处理content_block_stop事件
        elif chunk_type == "content_block_stop":
            choice_delta = ChoiceDelta(
                content=None,
                function_call=None,
                refusal=None,
                role=None,
                tool_calls=None,
            )

            choice = ChunkChoice(
                delta=choice_delta, finish_reason="stop", index=0, logprobs=None
            )

            return ChatCompletionChunk(
                id=f"chatcmpl-stop-{self._chunk_counter}",
                choices=[choice],
                created=int(time.time()),
                model="",
                object="chat.completion.chunk",
            )

        # 处理message_start事件
        elif chunk_type == "message_start":
            # 消息开始事件
            choice_delta = ChoiceDelta(
                content=None,
                function_call=None,
                refusal=None,
                role="assistant",
                tool_calls=None,
            )

            choice = ChunkChoice(
                delta=choice_delta, finish_reason=None, index=0, logprobs=None
            )

            # 获取模型名称
            model_name = ""
            if hasattr(anthropic_chunk, "message"):
                model_name = getattr(anthropic_chunk.message, "model", "")

            return ChatCompletionChunk(
                id=f"chatcmpl-msg-start-{self._chunk_counter}",
                choices=[choice],
                created=int(time.time()),
                model=model_name,
                object="chat.completion.chunk",
            )

        # 处理message_stop事件
        elif chunk_type == "message_stop":
            # 消息结束事件
            choice_delta = ChoiceDelta(
                content=None,
                function_call=None,
                refusal=None,
                role=None,
                tool_calls=None,
            )

            choice = ChunkChoice(
                delta=choice_delta, finish_reason="stop", index=0, logprobs=None
            )

            return ChatCompletionChunk(
                id=f"chatcmpl-msg-stop-{self._chunk_counter}",
                choices=[choice],
                created=int(time.time()),
                model="",
                object="chat.completion.chunk",
            )

        # 对于其他类型的事件，只在debug模式下记录
        else:
            if logger.level <= 10:  # DEBUG level
                logger.debug(f"未处理的流式事件类型: {chunk_type}")
            return None


class LLMService:
    """Service for interacting with language models."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the LLM service.

        Args:
            config: Configuration for the LLM service
        """

        self.llm_config = yaml.load(
            open("dc_agents/config/service_config.yaml", "r", encoding="utf-8"),
            Loader=yaml.FullLoader,
        )["llms"]
        self.config = config
        self.model_name = self.llm_config[self.config["name"]]["model"]

        self.api_key = self.llm_config[self.config["name"]]["api_key"]
        self.api_url = self.llm_config[self.config["name"]]["api_url"]
        self.is_reasoning_model = self.llm_config[self.config["name"]][
            "is_reasoning_model"
        ]
        self.is_mm_model = self.llm_config[self.config["name"]]["is_mm_model"]
        self.provider = self.llm_config[self.config["name"]]["provider"]

        # 读取价格信息
        self.input_price = self.llm_config[self.config["name"]].get("input_price", 0)
        self.output_price = self.llm_config[self.config["name"]].get("output_price", 0)

        # 默认超时设置（秒），可以在配置中覆盖
        self.timeout = self.llm_config[self.config["name"]].get(
            "timeout", 300.0
        )  # 5分钟超时

        if self.provider == "azure":
            self.async_client = AsyncAzureOpenAI(
                azure_endpoint=self.api_url,
                api_version=self.llm_config[self.config["name"]]["api_version"],
                api_key=self.api_key,
                timeout=self.timeout,
            )
        elif self.provider == "openai":
            self.async_client = AsyncOpenAI(
                api_key=self.api_key, base_url=self.api_url, timeout=self.timeout
            )
        elif self.provider == "anthropic_native":
            # 使用特殊的Anthropic原生适配器
            self.async_client = AnthropicNativeAdapter(
                api_key=self.api_key, api_url=self.api_url, timeout=self.timeout
            )
        else:
            raise ValueError(f"Unsupported provider: {self.provider}")

        # 获取对应的token encoder
        try:
            # 对于Claude模型，使用单例tokenizer
            if "claude" in self.model_name.lower():
                self.is_claude_model = True
                self.tokenizer_singleton = TokenizerSingleton()
                self.claude_tokenizer = self.tokenizer_singleton.get_claude_tokenizer()
                self.encoding = None  # Claude使用不同的tokenizer
                logger.info("Claude模型已配置使用单例tokenizer")
            # 对于OpenAI模型，使用tiktoken库
            elif self.provider == "azure" or "gpt" in self.model_name.lower():
                self.encoding = tiktoken.encoding_for_model("gpt-4")
                self.is_claude_model = False
                self.claude_tokenizer = None
            else:
                # 默认使用cl100k_base编码，适用于多数新模型
                self.encoding = tiktoken.get_encoding("cl100k_base")
                self.is_claude_model = False
                self.claude_tokenizer = None
        except Exception as e:
            logger.warning(f"Failed to get token encoder: {e}. Using default encoding.")
            self.encoding = tiktoken.get_encoding("cl100k_base")
            self.is_claude_model = False
            self.claude_tokenizer = None

        logger.info(
            f"LLM Service initialized with model {self.config['name']}, timeout {self.timeout}s"
        )

    def _count_tokens(self, messages: List[Dict[str, Any]]) -> int:
        """计算消息列表的token数量 - 优化版本"""
        try:
            if not messages:
                return 0

            # 对于Claude模型，使用单例的Claude tokenizer
            if self.is_claude_model:
                if self.claude_tokenizer is None:
                    logger.warning("Claude tokenizer未加载，无法计算token数量")
                    return 0

                try:
                    # 优化：直接拼接所有内容，减少循环开销
                    all_content = []
                    for msg in messages:
                        role = msg.get("role", "")
                        content = str(msg.get("content", ""))
                        all_content.append(f"{role}: {content}")

                    messages_text = "\n".join(all_content)

                    # 使用单例的Claude tokenizer计算token数量
                    tokens = self.claude_tokenizer.encode(messages_text)
                    return int(len(tokens) * 1.1)  # 增加10%的冗余并转换为整数

                except Exception as e:
                    logger.warning(f"使用Claude tokenizer计算token失败: {e}")
                    return 0
            else:
                # 对于GPT和其他模型，使用tiktoken
                if self.encoding is None:
                    logger.warning("没有可用的encoding，无法计算token数量")
                    return 0

                # 优化：使用更高效的json序列化
                try:
                    messages_str = json.dumps(
                        messages, separators=(",", ":"), ensure_ascii=False
                    )
                    tokens = len(self.encoding.encode(messages_str))
                    return tokens
                except Exception as e:
                    logger.warning(f"使用tiktoken计算token失败: {e}")
                    return 0

        except Exception as e:
            logger.error(f"计算token时发生错误: {e}")
            return 0

    async def _count_tokens_async(self, messages: List[Dict[str, Any]]) -> int:
        """异步计算消息列表的token数量"""
        # 对于Claude模型，如果tokenizer未加载，尝试异步加载
        if self.is_claude_model and self.claude_tokenizer is None:
            try:
                self.claude_tokenizer = (
                    await self.tokenizer_singleton.get_claude_tokenizer_async()
                )
            except Exception as e:
                logger.warning(f"异步加载Claude tokenizer失败: {e}")

        # 由于tokenizer计算相对轻量，直接使用同步方法
        return self._count_tokens(messages)

    def _calculate_cost(
        self, input_tokens: int, output_tokens: int
    ) -> Dict[str, float]:
        """计算token使用成本"""
        input_cost = (input_tokens / 1000) * self.input_price
        output_cost = (output_tokens / 1000) * self.output_price
        total_cost = input_cost + output_cost

        return {
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "input_cost": input_cost,
            "output_cost": output_cost,
            "total_cost": total_cost,
            "currency": "CNY",  # 默认使用人民币
        }

    async def get_completion(
        self,
        messages: List[Dict[str, Any]],
        temperature: float = 0.7,
        max_tokens: int = 1000,
    ) -> AsyncGenerator[str, None]:
        """
        Generate text using the language model.

        Args:
            messages: The messages to send to the model
            stream: Whether to stream the response
        Returns:
            The generated text
        """

        if "temperature" in self.config:
            temperature = self.config["temperature"]

        if "max_tokens" in self.config:
            max_tokens = self.config["max_tokens"]

        # 计算输入tokens
        input_tokens = await self._count_tokens_async(messages)
        output_tokens = 0  # 将在流式响应中累计

        try:
            logger.info(
                f"Calling LLM API with model {self.config['name']}, temperature {temperature}, max_tokens {max_tokens}"
            )

            if self.provider == "anthropic_native":
                # 使用特殊的Anthropic原生适配器
                response = await self.async_client.chat_completions_create(
                    model=self.model_name,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    stream=True,
                    is_reasoning_model=self.is_reasoning_model,
                    timeout=self.timeout,
                )
            elif isinstance(self.async_client, AsyncAzureOpenAI):
                response = await self.async_client.chat.completions.create(
                    model=self.model_name,
                    messages=messages,
                    max_completion_tokens=self.config["max_tokens"],
                    stream=True,
                    timeout=self.timeout,  # 使用服务级别的超时设置
                )
            else:
                response = await self.async_client.chat.completions.create(
                    model=self.model_name,
                    messages=messages,
                    temperature=self.config["temperature"],
                    max_tokens=self.config["max_tokens"],
                    stream=True,
                    timeout=self.timeout,  # 使用服务级别的超时设置
                )

            # 创建自定义生成器，包装原始响应并计算token - 优化版本
            async def token_counting_generator():
                nonlocal output_tokens
                content_parts = []  # 使用列表收集内容，最后一次性拼接

                async for chunk in response:
                    # 优化：减少属性检查的次数
                    if hasattr(chunk, "choices") and chunk.choices:
                        delta = chunk.choices[0].delta

                        # 收集内容
                        content = None
                        if (
                            hasattr(delta, "reasoning_content")
                            and delta.reasoning_content is not None
                        ):
                            content = delta.reasoning_content
                        elif (
                            hasattr(delta, "reasoning") and delta.reasoning is not None
                        ):
                            content = delta.reasoning
                        elif hasattr(delta, "content") and delta.content is not None:
                            content = delta.content

                        if content:
                            content_parts.append(content)

                    # 将原始chunk返回给调用者
                    yield chunk

                # 流式响应结束后，一次性计算输出token
                if content_parts:
                    full_content = "".join(content_parts)
                    output_tokens = self._count_tokens([{"content": full_content}])

                # 计算成本信息
                token_info = self._calculate_cost(input_tokens, output_tokens)

                # 只在info级别记录token使用情况，避免过度日志
                logger.info(
                    f"流式请求完成: 输入tokens={input_tokens}, 输出tokens={output_tokens}, 成本={token_info['total_cost']:.4f}"
                )

                # 返回特殊的最后一个chunk，包含token信息
                token_chunk = {"token_info": token_info, "is_token_info": True}
                yield token_chunk

            return token_counting_generator()

        except Exception as e:
            # Fallback to a simple response if the API call fails
            logger.error(f"Error calling LLM API: {str(e)}")

            async def error_generator():
                yield {
                    "is_token_info": True,
                    "token_info": {"total_cost": 0, "error": str(e)},
                }

            return error_generator()

    async def get_completion_sync(
        self,
        messages: List[Dict[str, Any]],
        temperature: float = 0.7,
        max_tokens: int = 1000,
    ) -> Dict[str, Any]:
        """
        以非流式方式生成文本，并返回完整响应和token信息

        Args:
            messages: 发送给模型的消息
            temperature: 温度参数
            max_tokens: 最大生成token数

        Returns:
            包含响应内容和token信息的字典
        """

        if "temperature" in self.config:
            temperature = self.config["temperature"]

        if "max_tokens" in self.config:
            max_tokens = self.config["max_tokens"]

        # 计算输入tokens
        input_tokens = await self._count_tokens_async(messages)

        try:
            # logger.info(f"调用LLM API（非流式）模型 {self.config['name']}，温度 {temperature}，最大token {max_tokens}")

            # 使用非流式API调用
            if self.provider == "anthropic_native":
                # 使用特殊的Anthropic原生适配器
                response = await self.async_client.chat_completions_create(
                    model=self.model_name,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    stream=False,
                    is_reasoning_model=self.is_reasoning_model,
                    timeout=self.timeout,
                )
            elif isinstance(self.async_client, AsyncAzureOpenAI):
                response = await self.async_client.chat.completions.create(
                    model=self.model_name,
                    messages=messages,
                    max_completion_tokens=max_tokens,
                    stream=False,
                    timeout=self.timeout,
                )
            else:
                response = await self.async_client.chat.completions.create(
                    model=self.model_name,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    stream=False,
                    timeout=self.timeout,
                )

            # 获取完整响应内容
            content = response.choices[0].message.content or ""
            if (
                hasattr(response.choices[0].message, "reasoning_content")
                and response.choices[0].message.reasoning_content
            ):
                content = response.choices[0].message.reasoning_content + content
            elif (
                hasattr(response.choices[0].message, "reasoning")
                and response.choices[0].message.reasoning
            ):
                content = response.choices[0].message.reasoning + content
            # 计算输出tokens
            output_tokens = self._count_tokens([{"content": content}])

            # 计算成本
            token_info = self._calculate_cost(input_tokens, output_tokens)

            # 记录token使用情况
            # logger.info(f"非流式请求完成: 输入tokens={input_tokens}, 输出tokens={output_tokens}, 成本={token_info['total_cost']:.4f}")

            # 返回结果和token信息
            return {"response": response, "token_info": token_info}

        except Exception as e:
            logger.error(f"调用LLM API错误: {str(e)}")
            return None
