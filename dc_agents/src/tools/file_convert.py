import os
from abc import ABC, abstractmethod
from typing import Optional, Dict, Type, Union
from pptx import Presentation
from docx import Document
from .mdconvert import PdfConverter
from PIL import Image
import base64
import yaml
from openai import OpenAI
from loguru import logger

logger = logger.bind(name="file_convert")
class FileConverter(ABC):
    """文件转换器的抽象基类"""
    
    @abstractmethod
    def can_handle(self, file_path: str) -> bool:
        """判断是否可以处理该文件类型"""
        pass
    
    @abstractmethod
    def convert_to_text(self, file_path: str, format: str = 'text') -> str:
        """
        将文件转换为文本内容
        Args:
            file_path: 文件路径
            format: 输出格式，'text' 或 'markdown'
        Returns:
            str: 转换后的文本内容
        """
        pass

class ImageFileConverter(FileConverter):
    """处理图片文件的转换器"""
    
    def can_handle(self, file_path: str) -> bool:
        return file_path.lower().endswith(('.jpg', '.jpeg', '.png'))
    
    def convert_to_text(self, file_path: str, format: str = 'text') -> str:
        """
        处理图片文件
        Args:
            file_path: 图片文件路径
            format: 输出格式（目前只支持text）
        Returns:
            str: 图片信息文本
        Raises:
            Exception: 文件处理失败
        """
        try:
            # 获取图片基本信息
            config = yaml.load(open("dc_agents/config/config.yaml"), Loader=yaml.FullLoader)
            client = OpenAI(
                api_key=config["image_api"]["api_key"],
                base_url=config["image_api"]["base_url"]
            )
            with open(file_path, "rb") as img_file:
                img_base64 = img_file.read()
                img_base64 = base64.b64encode(img_base64).decode()
            completion = client.chat.completions.create(
                model=config["image_api"]["model"],
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{img_base64}"
                                }
                            },
                            {
                                "type": "text",
                                "text": "请详细描述这张图片的内容"
                            }
                        ]
                    }
                ]
            )
            logger.info(completion.choices[0].message.content)
            return completion.choices[0].message.content
        except Exception as e:
            raise Exception(f"图片文件处理失败: {str(e)}")

class PDFFileConverter(FileConverter):
    """处理PDF文件的转换器"""
    
    def can_handle(self, file_path: str) -> bool:
        return file_path.lower().endswith('.pdf')
    
    def convert_to_text(self, file_path: str, format: str = 'text') -> str:
        """
        使用PdfConverter提取PDF文件中的文本
        Args:
            file_path: PDF文件路径
            format: 输出格式（目前只支持text）
        Returns:
            str: 提取的文本内容
        Raises:
            Exception: 文件处理失败
        """
        try:
            # 使用PdfConverter处理PDF
            pdf_converter = PdfConverter()
            
            # 设置转换参数
            kwargs = {
                "file_extension": ".pdf",
                "max_pages": 15,  # 最多处理15页
                "max_chars_per_page": 5000,  # 每页最多5000字符
                "total_max_chars": 100000,  # 总共最多10万字符
                "use_threading": True,  # 使用多线程
                "pdf_timeout": 30  # 30秒超时
            }
            
            # 转换为文本
            result = pdf_converter.convert(file_path, **kwargs)
            
            if result and hasattr(result, 'text_content'):
                return result.text_content
            else:
                return "无法提取文本内容"
                
        except Exception as e:
            raise Exception(f"PDF文件处理失败: {str(e)}")

class TextFileConverter(FileConverter):
    """处理纯文本文件的转换器"""
    
    def can_handle(self, file_path: str) -> bool:
        is_txt = file_path.lower().endswith('.txt')
        is_md = file_path.lower().endswith('.md')
        return is_txt or is_md
    
    def convert_to_text(self, file_path: str, format: str = 'text') -> str:
        """
        读取文本文件内容
        Args:
            file_path: 文本文件路径
            format: 输出格式（目前只支持text）
        Returns:
            str: 文件内容
        Raises:
            FileNotFoundError: 文件不存在
            UnicodeDecodeError: 文件编码错误
        """
        try:
            # 首先尝试使用 UTF-8 编码读取
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            # 如果 UTF-8 失败，尝试使用 GBK 编码（常用于中文 Windows 系统）
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    return f.read()
            except UnicodeDecodeError:
                # 如果 GBK 也失败，尝试使用 latin-1（可以读取任何字节）
                with open(file_path, 'r', encoding='latin-1') as f:
                    return f.read()

class PowerPointFileConverter(FileConverter):
    """处理PPT文件的转换器"""
    
    def can_handle(self, file_path: str) -> bool:
        return file_path.lower().endswith(('.ppt', '.pptx'))
    
    def convert_to_text(self, file_path: str, format: str = 'text') -> str:
        """
        使用python-pptx提取PPT文件中的文本
        Args:
            file_path: PPT文件路径
            format: 输出格式（目前只支持text）
        Returns:
            str: 提取的文本内容
        Raises:
            Exception: 文件处理失败
        """
        try:
            # 加载PPT文件
            prs = Presentation(file_path)
            text_content = []
            
            # 遍历所有幻灯片
            for slide in prs.slides:
                # 遍历幻灯片中的所有形状
                for shape in slide.shapes:
                    if hasattr(shape, "text"):
                        text = shape.text.strip()
                        if text:
                            text_content.append(text)
            
            # 合并所有文本
            result = "\n".join(text_content)
            return result if result else "无法提取文本内容"
        except Exception as e:
            raise Exception(f"PPT文件处理失败: {str(e)}")

class WordFileConverter(FileConverter):
    """处理Word文档的转换器"""
    
    def can_handle(self, file_path: str) -> bool:
        return file_path.lower().endswith(('.doc', '.docx'))
    
    def convert_to_text(self, file_path: str, format: str = 'text') -> str:
        """
        使用python-docx提取Word文件中的文本
        Args:
            file_path: Word文件路径
            format: 输出格式（目前只支持text）
        Returns:
            str: 提取的文本内容
        Raises:
            Exception: 文件处理失败
        """
        try:
            # 加载Word文件
            doc = Document(file_path)
            text_content = []
            
            # 提取段落文本
            for para in doc.paragraphs:
                text = para.text.strip()
                if text:
                    text_content.append(text)
            
            # 提取表格文本
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        text = cell.text.strip()
                        if text:
                            row_text.append(text)
                    if row_text:
                        text_content.append(" | ".join(row_text))
            
            # 合并所有文本
            result = "\n".join(text_content)
            return result if result else "无法提取文本内容"
        except Exception as e:
            raise Exception(f"Word文件处理失败: {str(e)}")

class FileConverterFactory:
    """文件转换器工厂类"""
    
    def __init__(self):
        self._converters: Dict[str, Type[FileConverter]] = {}
        self._register_default_converters()
    
    def _register_default_converters(self):
        """注册默认的转换器"""
        self.register_converter('image', ImageFileConverter())
        self.register_converter('pdf', PDFFileConverter())
        self.register_converter('text', TextFileConverter())
        self.register_converter('powerpoint', PowerPointFileConverter())
        self.register_converter('word', WordFileConverter())
    
    def register_converter(self, name: str, converter: FileConverter):
        """注册新的转换器"""
        self._converters[name] = converter
    
    def get_converter(self, file_path: str) -> Optional[FileConverter]:
        """获取适合处理指定文件的转换器"""
        for converter in self._converters.values():
            if converter.can_handle(file_path):
                return converter
        return None

def convert_file_to_text(file_path: str, format: str = 'text') -> Optional[str]:
    """
    将文件转换为文本的便捷函数
    Args:
        file_path: 文件路径
        format: 输出格式，'text' 或 'markdown'
    Returns:
        Optional[str]: 转换后的文本内容，如果转换失败则返回None
    """
    if not os.path.exists(file_path):
        return None
    
    factory = FileConverterFactory()
    converter = factory.get_converter(file_path)
    
    if converter is None:
        return None
    
    try:
        return converter.convert_to_text(file_path, format)
    except Exception as e:
        print(f"转换文件时发生错误: {str(e)}")
        return None