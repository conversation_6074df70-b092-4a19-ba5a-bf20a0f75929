from firecrawl import FirecrawlApp
import time
import re
import threading
import concurrent.futures
import traceback

class Browser:
    def __init__(self, 
                 firecrawl_api_url: str,
                 page_size: int = 1024 * 10,
                 max_page_number: int = 3,
                 pdf_max_content_size: int = 200000,  # 限制PDF内容大小
                 request_timeout: int = 30):  # 添加请求超时参数
        self.firecrawl_app = FirecrawlApp(api_url=firecrawl_api_url)

        self.page_size = page_size
        self.max_page_number = max_page_number
        self.pdf_max_content_size = pdf_max_content_size
        self.request_timeout = request_timeout  # 请求超时时间（秒）

        self.pages = []
        self.current_page_index = 0
        self.current_page_content = None
        
    def scrape_url(self, url: str) -> dict:
        """
        抓取URL并返回结果。
        增加了超时处理和错误恢复机制。
        """
        # 处理arxiv链接
        if "arxiv.org/abs/" in url:
            url = url.replace("abs", "pdf")
            
        # 判断是否为PDF文件
        is_pdf = url.lower().endswith('.pdf') or 'pdf' in url.lower() or '/pdf/' in url.lower()
        
        # 在开始抓取前，先设置默认错误页面，避免发生异常时返回None
        self._create_error_page("正在抓取内容，请稍候...")
        
        try:
            # 使用带超时的处理机制
            result = self._scrape_with_timeout(url, is_pdf)
            return result if result else {
                "is_success": False,
                "is_empty": False,
                "error": "无法获取内容",
            }
        except Exception as e:
            # 捕获并记录所有异常
            error_msg = str(e)
            trace = traceback.format_exc()
            print(f"抓取URL时发生错误 {url}: {error_msg}\n{trace}")
            
            # 创建错误页面
            self._create_error_page(f"抓取内容失败: {error_msg[:200]}")
            
            return {
                "is_success": False,
                "is_empty": False,
                "error": error_msg,
            }
    
    def _create_error_page(self, message: str):
        """创建一个错误页面"""
        self.pages = [f"### 处理错误\n\n{message}"]
        self.current_page_index = 0
        self.current_page_content = self.pages[0]
    
    def _scrape_with_timeout(self, url: str, is_pdf: bool) -> dict:
        """使用线程执行带超时的抓取"""
        # 设置超时时间
        timeout = self.request_timeout * 2 if is_pdf else self.request_timeout
        
        # 使用线程执行实际抓取，以便能够设置超时
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future = executor.submit(self._perform_scrape, url, is_pdf)
            try:
                return future.result(timeout=timeout)
            except concurrent.futures.TimeoutError:
                print(f"抓取URL超时: {url}")
                # 当发生超时时，返回一个特殊的错误页面
                self._create_error_page(
                    "内容抓取超时。该资源（特别是PDF）可能过大或访问缓慢，无法在规定时间内完成处理。\n\n"
                    "建议：\n"
                    "- 尝试使用较小的PDF文件\n"
                    "- 对于学术论文，尝试查找摘要版本而不是完整PDF\n"
                    "- 考虑直接引用论文的DOI链接而不是PDF链接"
                )
                return {
                    "is_success": True,
                    "is_empty": False,
                    "timeout": True,
                }
    
    def _perform_scrape(self, url: str, is_pdf: bool) -> dict:
        """执行实际的抓取操作"""
        # 调用异步批处理API
        job = self.firecrawl_app.async_batch_scrape_urls([url], params={'formats': ['markdown']})

        # 轮询等待结果
        done = False
        retry_count = 0
        max_retries = 3
        wait_time = 2
        
        while not done and retry_count < max_retries:
            time.sleep(wait_time)
            retry_count += 1
            wait_time *= 1.5  # 指数退避增加等待时间
            
            try:
                status = self.firecrawl_app.check_batch_scrape_status(job["id"])
                if status["status"] == "completed":
                    done = True
                    markdown = status["data"][0]["markdown"]
                elif status["status"] == "failed":
                    return {
                        "is_success": False,
                        "is_empty": False,
                        "error": "抓取失败",
                    }
            except Exception as e:
                print(f"检查批量抓取状态时出错: {e}")
                # 继续尝试
            
        if not done:
            print("超过最大重试次数，抓取失败")
            return {
                "is_success": False,
                "is_empty": False,
                "error": "抓取超时，超过最大重试次数",
            }
            
        # 检查是否获取到内容
        if not markdown or len(markdown.strip()) < 50:
            print(f"抓取的内容太短或为空: {url}")
            return {
                "is_success": False,
                "is_empty": True,
                "error": "抓取的内容为空或太短",
            }
            
        # 处理内容过长的情况（尤其是PDF）
        if is_pdf and len(markdown) > self.pdf_max_content_size:
            # 提取关键部分
            markdown = self._extract_key_pdf_sections(markdown)
            
        # 使用更智能的分页方式
        self._split_into_pages(markdown)
        
        if len(self.pages) == 0:
            return {
                "is_success": True,
                "is_empty": True,
            }

        # 设置当前页
        self.current_page_index = 0
        self.current_page_content = self.pages[self.current_page_index]

        return {
            "is_success": True,
            "is_empty": False,
        }
        
    def _extract_key_pdf_sections(self, markdown: str) -> str:
        """从PDF中提取关键部分"""
        try:
            # 提取标题 (通常在文档开头)
            title_section = ""
            first_lines = markdown.split('\n\n', 3)[0] if '\n\n' in markdown else markdown[:500]
            title_section = first_lines
            
            # 提取摘要部分 (通常包含Abstract关键词)
            abstract_section = ""
            abstract_match = re.search(r'(?i)abstract[:\s]*(.*?)(?=\n\n|\n[A-Z]+\s*\n)', markdown, re.DOTALL)
            if abstract_match:
                abstract_section = abstract_match.group(0)
                
            # 提取介绍部分
            intro_section = ""
            introduction_match = re.search(r'(?i)introduction[:\s]*(.*?)(?=\n\n|\n[A-Z]+\s*\n)', markdown, re.DOTALL)
            if introduction_match:
                intro_section = introduction_match.group(0)
                
            # 提取结论部分
            conclusion_section = ""
            conclusion_patterns = [
                r'(?i)conclusion[s]?[:\s]*(.*?)(?=\n\n|\n[A-Z]+\s*\n|\Z)',
                r'(?i)discussion[:\s]*(.*?)(?=\n\n|\n[A-Z]+\s*\n|\Z)',
                r'(?i)summary[:\s]*(.*?)(?=\n\n|\n[A-Z]+\s*\n|\Z)',
                r'(?i)future work[:\s]*(.*?)(?=\n\n|\n[A-Z]+\s*\n|\Z)'
            ]
            
            for pattern in conclusion_patterns:
                match = re.search(pattern, markdown, re.DOTALL)
                if match:
                    conclusion_section = match.group(0)
                    break
                    
            # 组合关键部分
            key_sections = []
            
            # 总是添加标题部分
            if title_section:
                key_sections.append(title_section)
                
            # 添加摘要（如果有）
            if abstract_section and abstract_section not in title_section:
                key_sections.append(f"\n\n## 摘要\n\n{abstract_section}")
                
            # 添加介绍（如果有）
            if intro_section and intro_section not in title_section and intro_section not in abstract_section:
                key_sections.append(f"\n\n## 介绍\n\n{intro_section}")
                
            # 添加结论（如果有）
            if conclusion_section:
                key_sections.append(f"\n\n## 结论\n\n{conclusion_section}")
                
            # 添加截断信息
            key_sections.append("\n\n...\n\n[PDF内容已截断，仅显示关键部分]")
            
            # 合并所有部分并限制总长度
            result = "\n\n".join(key_sections)
            result = result[:self.pdf_max_content_size]
            
            return result
        except Exception as e:
            print(f"提取PDF关键部分时出错: {e}")
            # 如果提取失败，简单截断内容
            return markdown[:self.pdf_max_content_size] + "\n\n...\n\n[内容已截断]"
    
    def _split_into_pages(self, markdown: str) -> None:
        """将markdown内容分割成页面"""
        self.pages = []
        
        # 检查内容是否为空
        if not markdown or len(markdown.strip()) == 0:
            self.pages = ["[内容为空]"]
            return
            
        # 尝试在段落边界分页
        paragraphs = markdown.split('\n\n')
        current_page = ""
        
        for para in paragraphs:
            # 如果当前段落为空，跳过
            if not para.strip():
                continue
                
            # 如果当前段落加上当前页面内容不超过页面大小，则添加到当前页面
            if len(current_page) + len(para) + 2 <= self.page_size:
                if current_page:
                    current_page += "\n\n" + para
                else:
                    current_page = para
            else:
                # 如果当前页面不为空，则添加到页面列表
                if current_page:
                    self.pages.append(current_page)
                
                # 检查单个段落是否超过页面大小
                if len(para) > self.page_size:
                    # 分割超长段落
                    words = para.split(" ")
                    temp_para = ""
                    for word in words:
                        if len(temp_para) + len(word) + 1 <= self.page_size:
                            if temp_para:
                                temp_para += " " + word
                            else:
                                temp_para = word
                        else:
                            self.pages.append(temp_para)
                            temp_para = word
                    
                    if temp_para:
                        current_page = temp_para
                    else:
                        current_page = ""
                else:
                    current_page = para
        
        # 添加最后一页
        if current_page:
            self.pages.append(current_page)
            
        # 确保至少有一页
        if len(self.pages) == 0:
            self.pages = ["[内容处理后为空]"]
            
        # 限制页面数量
        self.pages = self.pages[:self.max_page_number]

    def get_page_content(self) -> str:
        """获取当前页面内容"""
        if not self.current_page_content:
            return "[当前无内容]"
        return self.current_page_content

    def page_down(self) -> str:
        """翻到下一页"""
        if self.current_page_index < len(self.pages) - 1:
            self.current_page_index += 1
            self.current_page_content = self.pages[self.current_page_index]
            return self.current_page_content
        else:
            return self.current_page_content # 返回当前内容而不是None，避免空指针

    def page_up(self) -> str:
        """翻到上一页"""
        if self.current_page_index > 0:
            self.current_page_index -= 1
            self.current_page_content = self.pages[self.current_page_index]
            return self.current_page_content
        else:
            return self.current_page_content # 返回当前内容而不是None，避免空指针
