import requests
import json
import http.client



def web_search(query, config):
    if not query:
        raise ValueError("Search query cannot be empty")
    if config['search_engine'] == 'google':
        return serper_google_search(
            query=query,
            serper_api_key=config['serper_api_key'],
            top_k=config['search_top_k'],
            region=config['search_region'],
            lang=config['search_lang']
        )
    elif config['search_engine'] == 'bing':
        return azure_bing_search(
            query=query,
            subscription_key=config['azure_bing_search_subscription_key'],
            mkt=config['azure_bing_search_mkt'],
            top_k=config['search_top_k']
        )


def azure_bing_search(query, subscription_key, mkt, top_k):
    params = {'q': query, 'mkt': mkt, 'count': top_k}
    headers = {'Ocp-Apim-Subscription-Key': subscription_key}

    results = []

    try:
        response = requests.get("https://api.bing.microsoft.com/v7.0/search", headers=headers, params=params)
        json_response = response.json()
        for e in json_response['webPages']['value']:
            results.append({
                "title": e['name'],
                "link": e['url'],
                "snippet": e['snippet']
            })
    except Exception as e:
        print(f"Bing search API error: {e}")

    return results


def serper_google_search(
        query, 
        serper_api_key,
        top_k,
        region,
        lang
    ):
    conn = http.client.HTTPSConnection("google.serper.dev")
    payload = json.dumps({
            "q": query,
            "num": top_k,
            "gl": region,
            "hl": lang,
        })
    headers = {
        'X-API-KEY': serper_api_key,
        'Content-Type': 'application/json'
    }
    conn.request("POST", "/search", payload, headers)
    res = conn.getresponse()
    data = json.loads(res.read().decode("utf-8"))

    # TODO handle this raise
    if not data:
        raise Exception("The google search API is temporarily unavailable, please try again later.")

    if "organic" not in data:
        raise Exception(f"No results found for query: '{query}'. Use a less specific query.")
    else:
        results = data["organic"]
        return results