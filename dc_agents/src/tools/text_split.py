import re
import tiktoken
from typing import List

def count_tokens(text, encoding):
    return len(encoding.encode(text))

def smart_sentence_split(text):
    # 正则分句
    pattern = re.compile(r'([^。！？；,，,.!?\n]*[。！？；,，,.!?\n])')
    sentences = pattern.findall(text)
    # 检查剩余部分（无标点残句）
    tail = pattern.sub('', text)
    if tail.strip():
        sentences.append(tail)
    # 合并形如3.4, No.5等
    fixed = []
    # print(f"Initial sentence count: {len(sentences)}")
    # print(sentences)
    i, j = 0, 0
    while i < len(sentences):
        s = sentences[i]
        j = i
        while (
            j + 1 < len(sentences)
            and sentences[j][-1] == '.'
            and re.search(r'(\d+|No|no|Dr|dr|Mr|mr|Ms|ms)\.$', sentences[j].strip())
            and re.match(r'^\s*\w+', sentences[j + 1])
        ):
            s += sentences[j + 1].lstrip()
            j += 1
        fixed.append(s)
        i = j + 1
    # print(f"Fixed sentence count: {len(fixed)}")
    # print(fixed)
    return fixed


def split_and_chunk_text_by_tokens(
    text, 
    chunk_size=4000, 
    min_line_tokens=100, 
    token_encoding="cl100k_base"
) -> List[str]:
    encoding = tiktoken.get_encoding(token_encoding)
    sentences = smart_sentence_split(text)

    # 合并短句，保证每行不少于min_line_tokens
    lines = []
    buf = ""
    for s in sentences:
        buf_new = buf + s
        if count_tokens(buf_new, encoding) < min_line_tokens:
            buf = buf_new
        else:
            if buf:
                lines.append(buf.strip())
            buf = s
    if buf:
        lines.append(buf.strip())

    # chunk 合并：尽量不让逗号前后两句分到不同 chunk
    chunks = []
    cur_chunk = ""
    cur_tokens = 0
    i = 0
    while i < len(lines):
        line = lines[i]
        line_tokens = count_tokens(line, encoding)
        # 正常加不超限
        if cur_tokens + line_tokens <= chunk_size:
            cur_chunk += ("\n" + line) if cur_chunk else line
            cur_tokens += line_tokens
            i += 1
        else:
            # 如果当前chunk为空，强行放入，防止死循环
            if not cur_chunk:
                cur_chunk = line
                cur_tokens = line_tokens
                i += 1
            else:
                # 检查cur_chunk最后一句是否以逗号、分号等弱分隔符结尾
                weak_sep = (cur_chunk.rstrip()[-1] in "，,；;")
                # 检查下一个line存在，并且加一起不会超过1.2*chunk_size
                if weak_sep and i < len(lines):
                    next_tokens = count_tokens(lines[i], encoding)
                    if cur_tokens + next_tokens <= int(chunk_size * 1.2):
                        # 允许稍微超限，和下句一起纳入
                        cur_chunk += ("\n" + lines[i])
                        cur_tokens += next_tokens
                        i += 1
                        continue
                # 否则正常分块
                chunks.append(cur_chunk.strip())
                cur_chunk = ""
                cur_tokens = 0
    if cur_chunk:
        chunks.append(cur_chunk.strip())
    return chunks

if __name__ == "__main__":
    text = (
        "这里有一个数字3.4和一个序号No.5，还有Dr.Smith博士。"
        "This is a long sentence without any breaks, and it should be split appropriately. "
        "This is the last line without ending punctuation."
        "这个例子里，我们可以看到逗号的前后两句，应该尽量不要分开。"
        "如果句子很长，包含多个逗号，也应该尽可能保留其连续性。"
        "而句号是强断句点，优先在句号处分chunk。"
        "最后一行不带标点"
    )
    results = split_and_chunk_text_by_tokens(text, chunk_size=40, min_line_tokens=8)
    for i, chunk in enumerate(results, 1):
        print(f"Chunk {i}:\n{chunk}\n{'-'*20}")