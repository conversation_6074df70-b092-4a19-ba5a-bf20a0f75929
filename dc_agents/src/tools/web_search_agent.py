import asyncio
import logging
import json
import os
import random
import time
import threading
from typing import List, Dict, Any, Optional, AsyncGenerator, Union
from loguru import logger
from concurrent.futures import ThreadPoolExecutor
import requests
import http.client
import yaml
import re

# -------------------------
# Import your global executor
# E.g., in utils.py:
#   import concurrent.futures
#   global_executor = concurrent.futures.ThreadPoolExecutor(max_workers=50)
# Then here:

from dc_agents.src.tools.text_web_browser import SimpleTextBrowser

# def is_pdf_url(url: str) -> bool:
#     url_base = url.lower().split('?')[0]

#     # 1. 明确的 .pdf 后缀
#     if url_base.endswith('.pdf'):
#         return True

#     # 2. ArXiv PDF 链接规则
#     if re.match(r'^https?://arxiv\.org/pdf/(\d{4}\.\d{4,5}|[a-z\-]+/\d{7})(\.pdf)?$', url_base):
#         return True

#     return False

class WebSearchAgent:
    def __init__(self):
        self.user_agent = (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
            "(KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0"
        )

        self.BROWSER_CONFIG = {
            "viewport_size": 500000,
            "downloads_folder": "downloads_folder",
            "request_kwargs": {
                "headers": {"User-Agent": self.user_agent},
                "timeout": (5, 10),
            },
        }
        downloads_folder_path = f"./{self.BROWSER_CONFIG['downloads_folder']}"
        if not os.path.exists(downloads_folder_path):
            logger.info(f"保存目录不存在，创建目录：{os.path.abspath(downloads_folder_path)}")
            os.makedirs(downloads_folder_path, exist_ok=True)

        # Thread-safe dictionary for visited URLs
        self.search_url_history: Dict[str, Dict[str, Any]] = {}
        self._url_history_lock = threading.Lock()

        self.executor = ThreadPoolExecutor(max_workers=128)

        self.search_service_config = yaml.load(open("dc_agents/config/service_config.yaml", "r"), Loader=yaml.FullLoader)["search_service"]

    # -------------------------
    # Helper: actual synchronous scrape - to be run in a thread
    # -------------------------
    def _sync_scrape(self, url: str) -> Union[str, None]:
        """Blocking call to browse a URL and return the textual content."""
        try:
            browser = SimpleTextBrowser(**self.BROWSER_CONFIG)
            browser.visit_page(url)
            return browser.page_content
        except Exception as e:
            logger.error(f"浏览页面{url}时出错: {str(e)}")
            return None
    
    # def _sync_scrape_serper(self, url: str) -> Union[str, None]:
    #     try:
    #         conn = http.client.HTTPSConnection("scrape.serper.dev")
    #         payload = json.dumps({
    #         "url": url,
    #         "includeMarkdown": True
    #         })
    #         headers = {
    #         'X-API-KEY': self.search_service_config['api_key'],
    #         'Content-Type': 'application/json'
    #         }
    #         conn.request("POST", "/", payload, headers)
    #         res = conn.getresponse()
    #         data = res.read()
    #         data_json = json.loads(data.decode("utf-8"))
    #         return data_json["text"]
    #     except Exception as e:
    #         logger.error(f"Serper抓取页面{url}时出错: {str(e)}")
    #         return None

    async def scrape(self, url: str) -> Union[str, None]:
        """
        Asynchronously scrape the webpage by offloading the actual visit
        to the global executor.
        """
        loop = asyncio.get_running_loop()
        try:
            # 检查URL类型
            # if is_pdf_url(url):
            #     result = await loop.run_in_executor(self.executor, self._sync_scrape, url)
            # else:
            #     result = await loop.run_in_executor(self.executor, self._sync_scrape_serper, url)
            result = await loop.run_in_executor(self.executor, self._sync_scrape, url)
            # 确保返回的是字符串类型
            if result is not None and not isinstance(result, str):
                if isinstance(result, bytes):
                    # 如果是字节，尝试解码为UTF-8
                    try:
                        result = result.decode('utf-8')
                    except UnicodeDecodeError:
                        # 如果UTF-8解码失败，尝试使用其他编码或忽略错误
                        result = result.decode('utf-8', errors='ignore')
                else:
                    # 对于其他类型，强制转换为字符串
                    result = str(result)
                    
            return result
        except Exception as e:
            logger.error(f"抓取URL时出错: {str(e)}")
            return None

    async def scrape_urls(self, urls: List[str]) -> List[Union[str, None]]:
        """
        Given a list of URLs, concurrently scrape them all (async).
        Store the resulting browser in `web_page_info.browser`.
        """
        tasks = [asyncio.create_task(self.scrape(url)) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常情况，确保每个结果要么是字符串，要么是None
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"抓取过程中发生异常: {str(result)}")
                processed_results.append(None)
            elif not isinstance(result, (str, type(None))):
                # 如果结果不是字符串或None，尝试转换
                try:
                    if isinstance(result, bytes):
                        processed_results.append(result.decode('utf-8', errors='ignore'))
                    else:
                        processed_results.append(str(result))
                except Exception as e:
                    logger.error(f"转换结果时出错: {str(e)}")
                    processed_results.append(None)
            else:
                processed_results.append(result)
                
        return processed_results

if __name__ == "__main__":
    url = "https://www.voronoiapp.com/geopolitics/Landlocked-Countries-3056"
    # url = "https://unsouthsouth.org/wp-content/uploads/2024/07/Review-of-South-South-Cooperation-in-the-implementation-of-the-Vienna-Programme-of-Action-for-LDCs-in-the-decade-2014-2024.pdf"
    # url = "https://study.com/learn/lesson/landlocked-countries-africa-south-america.html"
    # url = "https://arxiv.org/pdf/2104.09864"
    # url = "https://www.reddit.com/r/Maps/comments/12ca6yj/countries_that_are_surrounded_by_landlocked/"
    agent = WebSearchAgent()
    # Example usage
    result = asyncio.run(agent.scrape(url))
    print(result)