# This is copied from Magentic-one's great repo: https://github.com/microsoft/autogen/blob/v0.4.4/python/packages/autogen-magentic-one/src/autogen_magentic_one/markdown_browser/mdconvert.py
# Thanks to Microsoft researchers for open-sourcing this!
# type: ignore
import base64
import copy
import html
import json
import mimetypes
import os
import re
import shutil
import subprocess
import sys
import tempfile
import traceback
import zipfile
from typing import Any, Dict, List, Optional, Union
from urllib.parse import parse_qs, quote, unquote, urlparse, urlunparse

import mammoth
import markdownify
import pandas as pd
import pdfminer
import pdfminer.high_level
import pptx

# File-format detection
import puremagic
import pydub
import requests
import speech_recognition as sr
from bs4 import BeautifulSoup
from youtube_transcript_api import YouTubeTranscriptApi
from youtube_transcript_api.formatters import SRTFormatter

import functools
import inspect


class _CustomMarkdownify(markdownify.MarkdownConverter):
    """
    A custom version of markdownify's MarkdownConverter. Changes include:

    - Altering the default heading style to use '#', '##', etc.
    - Removing javascript hyperlinks.
    - Truncating images with large data:uri sources.
    - Ensuring URIs are properly escaped, and do not conflict with Markdown syntax
    """

    def __init__(self, **options: Any):
        options["heading_style"] = options.get("heading_style", markdownify.ATX)
        # Monkey patch标准转换方法，确保兼容性
        self._monkey_patch_converter_methods()
        # Explicitly cast options to the expected type if necessary
        super().__init__(**options)

    def _monkey_patch_converter_methods(self):
        """在初始化时修补所有转换方法以确保兼容性"""
        # 获取所有继承的convert_*方法
        for name in dir(markdownify.MarkdownConverter):
            if name.startswith('convert_') and callable(getattr(markdownify.MarkdownConverter, name)):
                # 应用装饰器到每个转换方法
                orig_method = getattr(markdownify.MarkdownConverter, name)
                patched_method = self._make_compatible_converter(orig_method)
                # 替换原始方法
                setattr(markdownify.MarkdownConverter, name, patched_method)

    def _make_compatible_converter(self, method):
        """创建一个能处理不同参数格式的兼容转换方法"""
        @functools.wraps(method)
        def wrapper(*args, **kwargs):
            """兼容装饰器，处理不同版本markdownify的参数差异"""
            try:
                # 首先尝试直接调用
                return method(*args, **kwargs)
            except TypeError as e:
                # 参数不匹配，尝试修复
                error_msg = str(e)
                
                # 检查是否缺少convert_as_inline
                if "convert_as_inline" in error_msg and len(args) >= 3:
                    # 可能是版本差异，尝试添加默认的convert_as_inline=False
                    new_args = list(args)
                    if len(new_args) == 3:  # self, el, text
                        new_args.append(False)  # 添加convert_as_inline参数
                    
                    try:
                        return method(*new_args, **kwargs)
                    except Exception as inner_e:
                        print(f"修复convert_as_inline参数仍然失败: {inner_e}")
                        
                # 如果仍然失败，尝试更通用的异常处理
                el = args[1] if len(args) > 1 else None
                text = args[2] if len(args) > 2 else ""
                
                # 确保返回一些文本内容
                try:
                    if hasattr(el, 'get_text'):
                        return el.get_text()
                    return text or str(el)
                except:
                    # 最后的回退选项
                    return text or ""
        return wrapper

    def convert_hn(self, n: int, el: Any, text: str, convert_as_inline: bool) -> str:
        """Same as usual, but be sure to start with a new line"""
        try:
            if not convert_as_inline:
                if not re.search(r"^\n", text):
                    return "\n" + super().convert_hn(n, el, text, convert_as_inline)  # type: ignore
            return super().convert_hn(n, el, text, convert_as_inline)  # type: ignore
        except Exception as e:
            print(f"转换标题出错: {e}")
            return f"\n{'#' * n} {text}\n"

    def convert_a(self, el: Any, text: str, convert_as_inline: bool = False) -> str:
        """Same as usual converter, but removes Javascript links and escapes URIs."""
        try:
            prefix, suffix, text = markdownify.chomp(text)  # type: ignore
            if not text:
                return ""
            href = el.get("href")
            title = el.get("title")

            # Escape URIs and skip non-http or file schemes
            if href:
                try:
                    parsed_url = urlparse(href)  # type: ignore
                    if parsed_url.scheme and parsed_url.scheme.lower() not in ["http", "https", "file"]:  # type: ignore
                        return "%s%s%s" % (prefix, text, suffix)
                    href = urlunparse(parsed_url._replace(path=quote(unquote(parsed_url.path))))  # type: ignore
                except ValueError:  # It's not clear if this ever gets thrown
                    return "%s%s%s" % (prefix, text, suffix)

            # For the replacement see #29: text nodes underscores are escaped
            if (
                self.options["autolinks"]
                and text.replace(r"\_", "_") == href
                and not title
                and not self.options["default_title"]
            ):
                # Shortcut syntax
                return "<%s>" % href
            if self.options["default_title"] and not title:
                title = href
            title_part = ' "%s"' % title.replace('"', r"\"") if title else ""
            return "%s[%s](%s%s)%s" % (prefix, text, href, title_part, suffix) if href else text
        except Exception as e:
            print(f"转换链接出错: {e}")
            return text or ""

    def convert_img(self, el: Any, text: str, convert_as_inline: bool = False) -> str:
        """Same as usual converter, but removes data URIs"""
        try:
            alt = el.attrs.get("alt", None) or ""
            src = el.attrs.get("src", None) or ""
            title = el.attrs.get("title", None) or ""
            title_part = ' "%s"' % title.replace('"', r"\"") if title else ""
            
            # 确保parent属性存在
            if not hasattr(el, 'parent'):
                return alt
                
            if convert_as_inline and el.parent.name not in self.options["keep_inline_images_in"]:
                return alt

            # Remove dataURIs
            if src.startswith("data:"):
                src = src.split(",")[0] + "..."

            return "![%s](%s%s)" % (alt, src, title_part)
        except Exception as e:
            print(f"转换图片出错: {e}")
            return alt or "[图片]"

    def convert_soup(self, soup: Any) -> str:
        """转换整个HTML文档为Markdown"""
        try:
            return super().convert_soup(soup)  # type: ignore
        except Exception as e:
            print(f"转换HTML出错: {e}")
            # 尝试一个简单的回退方案
            try:
                if hasattr(soup, 'get_text'):
                    return soup.get_text()
                return str(soup)
            except Exception as inner_e:
                print(f"HTML回退方案也失败: {inner_e}")
                return "无法转换HTML内容"

    def __getattr__(self, name: str):
        """处理动态调用缺失的方法"""
        if name.startswith('convert_'):
            # 创建一个通用的转换函数，能够处理各种参数情况
            def generic_converter(*args, **kwargs):
                # 最少需要两个参数 (self, el)
                el = args[1] if len(args) > 1 else None
                text = args[2] if len(args) > 2 else ""
                convert_as_inline = args[3] if len(args) > 3 else False
                
                # 打印函数调用信息，帮助调试
                if el is not None:
                    # 尝试提取元素文本
                    try:
                        if hasattr(el, 'get_text'):
                            return el.get_text()
                        if hasattr(el, 'string') and el.string:
                            return str(el.string)
                    except:
                        pass
                        
                # 如果无法提取，返回传入的文本或空字符串
                return text or ""
            return generic_converter
            
        # 对于非转换方法，抛出正常的AttributeError
        raise AttributeError(f"{self.__class__.__name__} has no attribute '{name}'")


class DocumentConverterResult:
    """The result of converting a document to text."""

    def __init__(self, title: Union[str, None] = None, text_content: str = ""):
        self.title: Union[str, None] = title
        self.text_content: str = text_content


class DocumentConverter:
    """Abstract superclass of all DocumentConverters."""

    def convert(self, local_path: str, **kwargs: Any) -> Union[None, DocumentConverterResult]:
        raise NotImplementedError()


class PlainTextConverter(DocumentConverter):
    """Anything with content type text/plain"""

    def convert(self, local_path: str, **kwargs: Any) -> Union[None, DocumentConverterResult]:
        # Guess the content type from any file extension that might be around
        content_type, _ = mimetypes.guess_type("__placeholder" + kwargs.get("file_extension", ""))

        # Only accept text files
        if content_type is None:
            return None
        # elif "text/" not in content_type.lower():
        #     return None

        text_content = ""
        encodings = ["utf-8", "latin1", "gbk", "gb2312", "cp1252", "iso-8859-1"]
        success = False
        
        for encoding in encodings:
            try:
                with open(local_path, "rt", encoding=encoding) as fh:
                    text_content = fh.read()
                success = True
                # print(f"成功使用 {encoding} 编码读取文件")
                break
            except UnicodeDecodeError as e:
                print(f"使用 {encoding} 编码读取失败: {e}")
                continue
        
        if not success:
            # 如果所有编码都失败，尝试二进制方式读取并强制解码
            try:
                with open(local_path, "rb") as fh:
                    binary_content = fh.read()
                    # 使用errors='replace'参数来替换无法解码的字符
                    text_content = binary_content.decode("utf-8", errors="replace")
                print("使用二进制模式读取并强制UTF-8解码（替换无法解码的字符）")
            except Exception as e:
                print(f"二进制读取失败: {e}")
                return None
        
        return DocumentConverterResult(
            title=None,
            text_content=text_content,
        )


class HtmlConverter(DocumentConverter):
    """Anything with content type text/html"""

    def convert(self, local_path: str, **kwargs: Any) -> Union[None, DocumentConverterResult]:
        # Bail if not html
        extension = kwargs.get("file_extension", "")
        if extension.lower() not in [".html", ".htm"]:
            return None

        result = None
        encodings = ["utf-8", "latin1", "gbk", "gb2312", "cp1252", "iso-8859-1"]
        html_content = None
        
        for encoding in encodings:
            try:
                with open(local_path, "rt", encoding=encoding) as fh:
                    html_content = fh.read()
                # print(f"成功使用 {encoding} 编码读取HTML文件")
                break
            except UnicodeDecodeError as e:
                print(f"使用 {encoding} 编码读取HTML失败: {e}")
                continue
        
        if html_content is None:
            # 如果所有编码都失败，尝试二进制方式读取并强制解码
            try:
                with open(local_path, "rb") as fh:
                    binary_content = fh.read()
                    # 使用errors='replace'参数来替换无法解码的字符
                    html_content = binary_content.decode("utf-8", errors="replace")
                print("使用二进制模式读取HTML并强制UTF-8解码（替换无法解码的字符）")
            except Exception as e:
                print(f"二进制读取HTML失败: {e}")
                return None
        
        result = self._convert(html_content)
        return result

    def _convert(self, html_content: str) -> Union[None, DocumentConverterResult]:
        """Helper function that converts and HTML string."""
        try:
            # Parse the string
            soup = BeautifulSoup(html_content, "html.parser")

            # Remove javascript and style blocks
            for script in soup(["script", "style"]):
                script.extract()

            # Get the title
            title = None if soup.title is None else soup.title.string

            # Print only the main content using safe converter
            body_elm = soup.find("body")
            if body_elm:
                webpage_text = safe_html_to_markdown(str(body_elm))
            else:
                webpage_text = safe_html_to_markdown(str(soup))

            return DocumentConverterResult(
                title=title,
                text_content=webpage_text,
            )
        except Exception as e:
            print(f"HTML转换异常: {e}")
            # 紧急回退方案 - 提取纯文本
            try:
                soup = BeautifulSoup(html_content, "html.parser")
                text = soup.get_text()
                return DocumentConverterResult(
                    title="提取的HTML文本",
                    text_content=text
                )
            except:
                # 最终回退 - 返回部分原始HTML
                excerpt = html_content[:5000] + "..." if len(html_content) > 5000 else html_content
                return DocumentConverterResult(
                    title="无法解析的HTML",
                    text_content=f"HTML解析失败。原始内容片段:\n\n{excerpt}"
                )


class WikipediaConverter(DocumentConverter):
    """Handle Wikipedia pages separately, focusing only on the main document content."""

    def convert(self, local_path: str, **kwargs: Any) -> Union[None, DocumentConverterResult]:
        # Bail if not Wikipedia
        extension = kwargs.get("file_extension", "")
        if extension.lower() not in [".html", ".htm"]:
            return None
        url = kwargs.get("url", "")
        if not re.search(r"^https?:\/\/[a-zA-Z]{2,3}\.wikipedia.org\/", url):
            return None

        # Parse the file
        soup = None
        encodings = ["utf-8", "latin1", "gbk", "gb2312", "cp1252", "iso-8859-1"]
        html_content = None
        
        for encoding in encodings:
            try:
                with open(local_path, "rt", encoding=encoding) as fh:
                    html_content = fh.read()
                    soup = BeautifulSoup(html_content, "html.parser")
                # print(f"成功使用 {encoding} 编码读取维基百科文件")
                break
            except UnicodeDecodeError as e:
                print(f"使用 {encoding} 编码读取维基百科失败: {e}")
                continue
        
        if html_content is None:
            # 如果所有编码都失败，尝试二进制方式读取并强制解码
            try:
                with open(local_path, "rb") as fh:
                    binary_content = fh.read()
                    # 使用errors='replace'参数来替换无法解码的字符
                    html_content = binary_content.decode("utf-8", errors="replace")
                    soup = BeautifulSoup(html_content, "html.parser")
                print("使用二进制模式读取维基百科并强制UTF-8解码（替换无法解码的字符）")
            except Exception as e:
                print(f"二进制读取维基百科失败: {e}")
                return None

        # Remove javascript and style blocks
        for script in soup(["script", "style"]):
            script.extract()

        # Print only the main content
        body_elm = soup.find("div", {"id": "mw-content-text"})
        title_elm = soup.find("span", {"class": "mw-page-title-main"})

        webpage_text = ""
        main_title = None if soup.title is None else soup.title.string

        if body_elm:
            # What's the title
            if title_elm and len(title_elm) > 0:
                main_title = title_elm.string  # type: ignore
                assert isinstance(main_title, str)

            # Convert the page
            webpage_text = f"# {main_title}\n\n" + _CustomMarkdownify().convert_soup(body_elm)
        else:
            webpage_text = _CustomMarkdownify().convert_soup(soup)

        return DocumentConverterResult(
            title=main_title,
            text_content=webpage_text,
        )


class YouTubeConverter(DocumentConverter):
    """Handle YouTube specially, focusing on the video title, description, and transcript."""

    def convert(self, local_path: str, **kwargs: Any) -> Union[None, DocumentConverterResult]:
        # Bail if not YouTube
        extension = kwargs.get("file_extension", "")
        if extension.lower() not in [".html", ".htm"]:
            return None
        url = kwargs.get("url", "")
        if not url.startswith("https://www.youtube.com/watch?"):
            return None

        # Parse the file
        soup = None
        encodings = ["utf-8", "latin1", "gbk", "gb2312", "cp1252", "iso-8859-1"]
        html_content = None
        
        for encoding in encodings:
            try:
                with open(local_path, "rt", encoding=encoding) as fh:
                    html_content = fh.read()
                    soup = BeautifulSoup(html_content, "html.parser")
                # print(f"成功使用 {encoding} 编码读取YouTube文件")
                break
            except UnicodeDecodeError as e:
                print(f"使用 {encoding} 编码读取YouTube失败: {e}")
                continue
        
        if html_content is None:
            # 如果所有编码都失败，尝试二进制方式读取并强制解码
            try:
                with open(local_path, "rb") as fh:
                    binary_content = fh.read()
                    # 使用errors='replace'参数来替换无法解码的字符
                    html_content = binary_content.decode("utf-8", errors="replace")
                    soup = BeautifulSoup(html_content, "html.parser")
                print("使用二进制模式读取YouTube并强制UTF-8解码（替换无法解码的字符）")
            except Exception as e:
                print(f"二进制读取YouTube失败: {e}")
                return None

        # Read the meta tags
        assert soup.title is not None and soup.title.string is not None
        metadata: Dict[str, str] = {"title": soup.title.string}
        for meta in soup(["meta"]):
            for a in meta.attrs:
                if a in ["itemprop", "property", "name"]:
                    metadata[meta[a]] = meta.get("content", "")
                    break

        # We can also try to read the full description. This is more prone to breaking, since it reaches into the page implementation
        try:
            for script in soup(["script"]):
                content = script.text
                if "ytInitialData" in content:
                    lines = re.split(r"\r?\n", content)
                    obj_start = lines[0].find("{")
                    obj_end = lines[0].rfind("}")
                    if obj_start >= 0 and obj_end >= 0:
                        data = json.loads(lines[0][obj_start : obj_end + 1])
                        attrdesc = self._findKey(data, "attributedDescriptionBodyText")  # type: ignore
                        if attrdesc:
                            metadata["description"] = str(attrdesc["content"])
                    break
        except Exception:
            pass

        # Start preparing the page
        webpage_text = "# YouTube\n"

        title = self._get(metadata, ["title", "og:title", "name"])  # type: ignore
        assert isinstance(title, str)

        if title:
            webpage_text += f"\n## {title}\n"

        stats = ""
        views = self._get(metadata, ["interactionCount"])  # type: ignore
        if views:
            stats += f"- **Views:** {views}\n"

        keywords = self._get(metadata, ["keywords"])  # type: ignore
        if keywords:
            stats += f"- **Keywords:** {keywords}\n"

        runtime = self._get(metadata, ["duration"])  # type: ignore
        if runtime:
            stats += f"- **Runtime:** {runtime}\n"

        if len(stats) > 0:
            webpage_text += f"\n### Video Metadata\n{stats}\n"

        description = self._get(metadata, ["description", "og:description"])  # type: ignore
        if description:
            webpage_text += f"\n### Description\n{description}\n"

        transcript_text = ""
        parsed_url = urlparse(url)  # type: ignore
        params = parse_qs(parsed_url.query)  # type: ignore
        if "v" in params:
            assert isinstance(params["v"][0], str)
            video_id = str(params["v"][0])
            try:
                # Must be a single transcript.
                transcript = YouTubeTranscriptApi.get_transcript(video_id)  # type: ignore
                # transcript_text = " ".join([part["text"] for part in transcript])  # type: ignore
                # Alternative formatting:
                transcript_text = SRTFormatter().format_transcript(transcript)
            except Exception:
                pass
        if transcript_text:
            webpage_text += f"\n### Transcript\n{transcript_text}\n"

        title = title if title else soup.title.string
        assert isinstance(title, str)

        return DocumentConverterResult(
            title=title,
            text_content=webpage_text,
        )

    def _get(self, metadata: Dict[str, str], keys: List[str], default: Union[str, None] = None) -> Union[str, None]:
        for k in keys:
            if k in metadata:
                return metadata[k]
        return default

    def _findKey(self, json: Any, key: str) -> Union[str, None]:  # TODO: Fix json type
        if isinstance(json, list):
            for elm in json:
                ret = self._findKey(elm, key)
                if ret is not None:
                    return ret
        elif isinstance(json, dict):
            for k in json:
                if k == key:
                    return json[k]
                else:
                    ret = self._findKey(json[k], key)
                    if ret is not None:
                        return ret
        return None


class PdfConverter(DocumentConverter):
    """
    Converts PDFs to Markdown. Most style information is ignored, so the results are essentially plain-text.
    优化版本：支持多线程处理、内容截断和页面限制，以提高大型PDF的处理速度。
    增加超时机制，避免处理过大或复杂PDF时卡死。
    """

    def convert(self, local_path, **kwargs) -> Union[None, DocumentConverterResult]:
        # Bail if not a PDF
        extension = kwargs.get("file_extension", "")
        if extension.lower() != ".pdf":
            return None
            
        # 获取限制参数
        max_pages = kwargs.get("max_pages", 6)  # 默认最多处理25页
        max_chars_per_page = kwargs.get("max_chars_per_page", 5000)  # 每页最多5000字符
        total_max_chars = kwargs.get("total_max_chars", 10000)  # 总共最多10万字符
        use_threading = kwargs.get("use_threading", True)  # 默认使用多线程
        timeout = kwargs.get("pdf_timeout", 10)  # 默认15秒超时
        
        # 文件大小检查
        try:
            file_size = os.path.getsize(local_path)
            if file_size > 50 * 1024 * 1024:  # 超过50MB
                return DocumentConverterResult(
                    title="PDF过大",
                    text_content="此PDF文件过大（超过50MB），为避免处理超时已跳过解析。请考虑使用更小的文件或只关注文件的特定部分。"
                )
        except Exception as e:
            print(f"检查文件大小时出错: {e}")
        
        # 使用concurrent.futures代替信号量来实现超时
        from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError
        
        try:
            # 简化处理逻辑，避免使用嵌套函数导致的pickle错误
            if use_threading:
                try:
                    # 使用多线程处理，但不使用ProcessPoolExecutor
                    import multiprocessing
                    
                    # 分析PDF结构以获取页数
                    import pdfminer.pdfparser
                    import pdfminer.pdfdocument
                    import pdfminer.pdfpage
                    from pdfminer.high_level import extract_text
                    
                    # 在主线程中获取总页数
                    with open(local_path, 'rb') as fp:
                        parser = pdfminer.pdfparser.PDFParser(fp)
                        document = pdfminer.pdfdocument.PDFDocument(parser)
                        total_pages = len(list(pdfminer.pdfpage.PDFPage.create_pages(document)))
                    
                    # 确定实际处理的页数
                    pages_to_process = min(total_pages, max_pages)
                    
                    # 创建页面范围列表
                    page_ranges = []
                    chunk_size = min(5, pages_to_process)  # 每个任务处理5页或更少
                    
                    for i in range(0, pages_to_process, chunk_size):
                        end_page = min(i + chunk_size, pages_to_process)
                        page_ranges.append((i, end_page))
                    
                    # 设置线程池并创建结果列表
                    results = []
                    with ThreadPoolExecutor(max_workers=4) as executor:
                        # 为每个页面范围创建任务
                        def extract_page_range(start, end):
                            try:
                                # 设置单个任务的超时，避免单个任务卡死
                                task_timeout = max(5, timeout // 2)  # 单任务超时时间
                                future = executor.submit(
                                    extract_text, 
                                    local_path, 
                                    page_numbers=list(range(start, end))
                                )
                                return future.result(timeout=task_timeout)
                            except Exception as e:
                                print(f"提取PDF页面 {start}-{end} 时出错: {e}")
                                return f"[页面 {start}-{end} 处理出错]"
                        
                        # 依次处理每个页面范围，使用超时控制
                        for start, end in page_ranges:
                            try:
                                result = extract_page_range(start, end)
                                results.append(result)
                            except Exception as e:
                                print(f"处理页面范围 {start}-{end} 失败: {e}")
                                results.append(f"[页面 {start}-{end} 处理失败]")
                        
                    # 合并结果并限制总字符数
                    text_content = "\n\n".join([r for r in results if r])
                        
                    # 对内容进行裁剪
                    if len(text_content) > total_max_chars:
                        text_content = text_content[:total_max_chars] + "...\n\n[内容已截断，原PDF过长]"
                    
                    return DocumentConverterResult(
                        title=None,
                        text_content=text_content,
                    )
                except FutureTimeoutError as e:
                    print(f"PDF处理超时: {e}")
                    return DocumentConverterResult(
                        title="PDF处理超时",
                        text_content="处理此PDF文件时超时。该文件可能太大或格式复杂，无法在规定时间内处理完成。"
                    )
                except Exception as e:
                    print(f"多线程PDF处理失败: {e}")
                    # 如果多线程处理失败，回退到单线程处理
                    use_threading = False
            
            # 单线程处理
            if not use_threading:
                # 设置超时控制的线程
                def extract_text_with_timeout():
                    from pdfminer.high_level import extract_text
                    try:
                        raw_text = extract_text(local_path)
                        # 裁剪过长内容
                        if len(raw_text) > total_max_chars:
                            raw_text = raw_text[:total_max_chars] + "...\n\n[内容已截断，原PDF过长]"
                        return raw_text
                    except Exception as e:
                        print(f"提取PDF文本时出错: {e}")
                        return f"[PDF提取失败: {str(e)}]"
                
                # 使用线程池和超时执行提取
                with ThreadPoolExecutor(max_workers=1) as executor:
                    future = executor.submit(extract_text_with_timeout)
                    try:
                        text_content = future.result(timeout=timeout)
                        return DocumentConverterResult(
                            title=None,
                            text_content=text_content,
                        )
                    except FutureTimeoutError:
                        return DocumentConverterResult(
                            title="PDF处理超时",
                            text_content="处理此PDF文件时超时。该文件可能太大或格式复杂，无法在规定时间内处理完成。"
                        )
        except Exception as e:
            print(f"PDF处理异常: {str(e)}")
            return DocumentConverterResult(
                title="PDF处理错误",
                text_content=f"处理此PDF文件时出错: {str(e)}"
            )


class DocxConverter(HtmlConverter):
    """
    Converts DOCX files to Markdown. Style information (e.g.m headings) and tables are preserved where possible.
    """

    def convert(self, local_path, **kwargs) -> Union[None, DocumentConverterResult]:
        # Bail if not a DOCX
        extension = kwargs.get("file_extension", "")
        if extension.lower() != ".docx":
            return None

        result = None
        with open(local_path, "rb") as docx_file:
            result = mammoth.convert_to_html(docx_file)
            html_content = result.value
            result = self._convert(html_content)

        return result


class XlsxConverter(HtmlConverter):
    """
    Converts XLSX files to Markdown, with each sheet presented as a separate Markdown table.
    """

    def convert(self, local_path, **kwargs) -> Union[None, DocumentConverterResult]:
        # Bail if not a XLSX
        extension = kwargs.get("file_extension", "")
        if extension.lower() not in [".xlsx", ".xls"]:
            return None

        sheets = pd.read_excel(local_path, sheet_name=None)
        md_content = ""
        for s in sheets:
            md_content += f"## {s}\n"
            html_content = sheets[s].to_html(index=False)
            md_content += self._convert(html_content).text_content.strip() + "\n\n"

        return DocumentConverterResult(
            title=None,
            text_content=md_content.strip(),
        )


class PptxConverter(HtmlConverter):
    """
    Converts PPTX files to Markdown. Supports heading, tables and images with alt text.
    """

    def convert(self, local_path, **kwargs) -> Union[None, DocumentConverterResult]:
        # Bail if not a PPTX
        extension = kwargs.get("file_extension", "")
        if extension.lower() != ".pptx":
            return None

        md_content = ""

        presentation = pptx.Presentation(local_path)
        slide_num = 0
        for slide in presentation.slides:
            slide_num += 1

            md_content += f"\n\n<!-- Slide number: {slide_num} -->\n"

            title = slide.shapes.title
            for shape in slide.shapes:
                # Pictures
                if self._is_picture(shape):
                    # https://github.com/scanny/python-pptx/pull/512#issuecomment-1713100069
                    alt_text = ""
                    try:
                        alt_text = shape._element._nvXxPr.cNvPr.attrib.get("descr", "")
                    except Exception:
                        pass

                    # A placeholder name
                    filename = re.sub(r"\W", "", shape.name) + ".jpg"
                    md_content += "\n![" + (alt_text if alt_text else shape.name) + "](" + filename + ")\n"

                # Tables
                if self._is_table(shape):
                    html_table = "<html><body><table>"
                    first_row = True
                    for row in shape.table.rows:
                        html_table += "<tr>"
                        for cell in row.cells:
                            if first_row:
                                html_table += "<th>" + html.escape(cell.text) + "</th>"
                            else:
                                html_table += "<td>" + html.escape(cell.text) + "</td>"
                        html_table += "</tr>"
                        first_row = False
                    html_table += "</table></body></html>"
                    md_content += "\n" + self._convert(html_table).text_content.strip() + "\n"

                # Text areas
                elif shape.has_text_frame:
                    if shape == title:
                        md_content += "# " + shape.text.lstrip() + "\n"
                    else:
                        md_content += shape.text + "\n"

            md_content = md_content.strip()

            if slide.has_notes_slide:
                md_content += "\n\n### Notes:\n"
                notes_frame = slide.notes_slide.notes_text_frame
                if notes_frame is not None:
                    md_content += notes_frame.text
                md_content = md_content.strip()

        return DocumentConverterResult(
            title=None,
            text_content=md_content.strip(),
        )

    def _is_picture(self, shape):
        if shape.shape_type == pptx.enum.shapes.MSO_SHAPE_TYPE.PICTURE:
            return True
        if shape.shape_type == pptx.enum.shapes.MSO_SHAPE_TYPE.PLACEHOLDER:
            if hasattr(shape, "image"):
                return True
        return False

    def _is_table(self, shape):
        if shape.shape_type == pptx.enum.shapes.MSO_SHAPE_TYPE.TABLE:
            return True
        return False


class MediaConverter(DocumentConverter):
    """
    Abstract class for multi-modal media (e.g., images and audio)
    """

    def _get_metadata(self, local_path):
        exiftool = shutil.which("exiftool")
        if not exiftool:
            return None
        else:
            try:
                result = subprocess.run([exiftool, "-json", local_path], capture_output=True, text=True).stdout
                return json.loads(result)[0]
            except Exception:
                return None


class WavConverter(MediaConverter):
    """
    Converts WAV files to markdown via extraction of metadata (if `exiftool` is installed), and speech transcription (if `speech_recognition` is installed).
    """

    def convert(self, local_path, **kwargs) -> Union[None, DocumentConverterResult]:
        # Bail if not a XLSX
        extension = kwargs.get("file_extension", "")
        if extension.lower() != ".wav":
            return None

        md_content = ""

        # Add metadata
        metadata = self._get_metadata(local_path)
        if metadata:
            for f in [
                "Title",
                "Artist",
                "Author",
                "Band",
                "Album",
                "Genre",
                "Track",
                "DateTimeOriginal",
                "CreateDate",
                "Duration",
            ]:
                if f in metadata:
                    md_content += f"{f}: {metadata[f]}\n"

        # Transcribe
        try:
            transcript = self._transcribe_audio(local_path)
            md_content += "\n\n### Audio Transcript:\n" + ("[No speech detected]" if transcript == "" else transcript)
        except Exception:
            md_content += "\n\n### Audio Transcript:\nError. Could not transcribe this audio."

        return DocumentConverterResult(
            title=None,
            text_content=md_content.strip(),
        )

    def _transcribe_audio(self, local_path) -> str:
        recognizer = sr.Recognizer()
        with sr.AudioFile(local_path) as source:
            audio = recognizer.record(source)
            return recognizer.recognize_google(audio).strip()


class Mp3Converter(WavConverter):
    """
    Converts MP3 files to markdown via extraction of metadata (if `exiftool` is installed), and speech transcription (if `speech_recognition` AND `pydub` are installed).
    """

    def convert(self, local_path, **kwargs) -> Union[None, DocumentConverterResult]:
        # Bail if not a MP3
        extension = kwargs.get("file_extension", "")
        if extension.lower() != ".mp3":
            return None

        md_content = ""

        # Add metadata
        metadata = self._get_metadata(local_path)
        if metadata:
            for f in [
                "Title",
                "Artist",
                "Author",
                "Band",
                "Album",
                "Genre",
                "Track",
                "DateTimeOriginal",
                "CreateDate",
                "Duration",
            ]:
                if f in metadata:
                    md_content += f"{f}: {metadata[f]}\n"

        # Transcribe
        handle, temp_path = tempfile.mkstemp(suffix=".wav")
        os.close(handle)
        try:
            sound = pydub.AudioSegment.from_mp3(local_path)
            sound.export(temp_path, format="wav")

            _args = dict()
            _args.update(kwargs)
            _args["file_extension"] = ".wav"

            try:
                transcript = super()._transcribe_audio(temp_path).strip()
                md_content += "\n\n### Audio Transcript:\n" + (
                    "[No speech detected]" if transcript == "" else transcript
                )
            except Exception:
                md_content += "\n\n### Audio Transcript:\nError. Could not transcribe this audio."

        finally:
            os.unlink(temp_path)

        # Return the result
        return DocumentConverterResult(
            title=None,
            text_content=md_content.strip(),
        )


class ZipConverter(DocumentConverter):
    """
    Extracts ZIP files to a permanent local directory and returns a listing of extracted files.
    """

    def __init__(self, extract_dir: str = "downloads"):
        """
        Initialize with path to extraction directory.

        Args:
            extract_dir: The directory where files will be extracted. Defaults to "downloads"
        """
        self.extract_dir = extract_dir
        # Create the extraction directory if it doesn't exist
        os.makedirs(self.extract_dir, exist_ok=True)

    def convert(self, local_path: str, **kwargs: Any) -> Union[None, DocumentConverterResult]:
        # Bail if not a ZIP file
        extension = kwargs.get("file_extension", "")
        if extension.lower() != ".zip":
            return None

        # Verify it's actually a ZIP file
        if not zipfile.is_zipfile(local_path):
            return None

        # Extract all files and build list
        extracted_files = []
        with zipfile.ZipFile(local_path, "r") as zip_ref:
            # Extract all files
            zip_ref.extractall(self.extract_dir)
            # Get list of all files
            for file_path in zip_ref.namelist():
                # Skip directories
                if not file_path.endswith("/"):
                    extracted_files.append(self.extract_dir + "/" + file_path)

        # Sort files for consistent output
        extracted_files.sort()

        # Build the markdown content
        md_content = "Downloaded the following files:\n"
        for file in extracted_files:
            md_content += f"* {file}\n"

        return DocumentConverterResult(title="Extracted Files", text_content=md_content.strip())


class ImageConverter(MediaConverter):
    """
    Converts images to markdown via extraction of metadata (if `exiftool` is installed), OCR (if `easyocr` is installed), and description via a multimodal LLM (if an mlm_client is configured).
    """

    def convert(self, local_path, **kwargs) -> Union[None, DocumentConverterResult]:
        # Bail if not a XLSX
        extension = kwargs.get("file_extension", "")
        if extension.lower() not in [".jpg", ".jpeg", ".png"]:
            return None

        md_content = ""

        # Add metadata
        metadata = self._get_metadata(local_path)
        if metadata:
            for f in [
                "ImageSize",
                "Title",
                "Caption",
                "Description",
                "Keywords",
                "Artist",
                "Author",
                "DateTimeOriginal",
                "CreateDate",
                "GPSPosition",
            ]:
                if f in metadata:
                    md_content += f"{f}: {metadata[f]}\n"

        # Try describing the image with GPTV
        mlm_client = kwargs.get("mlm_client")
        mlm_model = kwargs.get("mlm_model")
        if mlm_client is not None and mlm_model is not None:
            md_content += (
                "\n# Description:\n"
                + self._get_mlm_description(
                    local_path, extension, mlm_client, mlm_model, prompt=kwargs.get("mlm_prompt")
                ).strip()
                + "\n"
            )

        return DocumentConverterResult(
            title=None,
            text_content=md_content,
        )

    def _get_mlm_description(self, local_path, extension, client, model, prompt=None):
        if prompt is None or prompt.strip() == "":
            prompt = "Write a detailed caption for this image."

        sys.stderr.write(f"MLM Prompt:\n{prompt}\n")

        data_uri = ""
        with open(local_path, "rb") as image_file:
            content_type, encoding = mimetypes.guess_type("_dummy" + extension)
            if content_type is None:
                content_type = "image/jpeg"
            image_base64 = base64.b64encode(image_file.read()).decode("utf-8")
            data_uri = f"data:{content_type};base64,{image_base64}"

        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": data_uri,
                        },
                    },
                ],
            }
        ]

        response = client.chat.completions.create(model=model, messages=messages)
        return response.choices[0].message.content


class FileConversionException(Exception):
    pass


class UnsupportedFormatException(Exception):
    pass


class MarkdownConverter:
    """(In preview) An extremely simple text-based document reader, suitable for LLM use.
    This reader will convert common file-types or webpages to Markdown."""

    def __init__(
        self,
        requests_session: Optional[requests.Session] = None,
        mlm_client: Optional[Any] = None,
        mlm_model: Optional[Any] = None,
        pdf_max_pages: int = 25,
        pdf_total_max_chars: int = 100000,
        pdf_timeout: int = 30,  # 新增PDF处理超时时间（秒）
    ):
        if requests_session is None:
            self._requests_session = requests.Session()
        else:
            self._requests_session = requests_session

        self._mlm_client = mlm_client
        self._mlm_model = mlm_model
        self._pdf_max_pages = pdf_max_pages
        self._pdf_total_max_chars = pdf_total_max_chars
        self._pdf_timeout = pdf_timeout

        self._page_converters: List[DocumentConverter] = []

        # Register converters for successful browsing operations
        # Later registrations are tried first / take higher priority than earlier registrations
        # To this end, the most specific converters should appear below the most generic converters
        self.register_page_converter(PlainTextConverter())
        self.register_page_converter(HtmlConverter())
        self.register_page_converter(WikipediaConverter())
        self.register_page_converter(YouTubeConverter())
        self.register_page_converter(DocxConverter())
        self.register_page_converter(XlsxConverter())
        self.register_page_converter(PptxConverter())
        self.register_page_converter(WavConverter())
        self.register_page_converter(Mp3Converter())
        self.register_page_converter(ImageConverter())
        self.register_page_converter(ZipConverter())
        self.register_page_converter(PdfConverter())

    def convert(
        self, source: Union[str, requests.Response], **kwargs: Any
    ) -> DocumentConverterResult:  # TODO: deal with kwargs
        """
        Args:
            - source: can be a string representing a path or url, or a requests.response object
            - extension: specifies the file extension to use when interpreting the file. If None, infer from source (path, uri, content-type, etc.)
        """

        # Local path or url
        if isinstance(source, str):
            if source.startswith("http://") or source.startswith("https://") or source.startswith("file://"):
                return self.convert_url(source, **kwargs)
            else:
                return self.convert_local(source, **kwargs)
        # Request response
        elif isinstance(source, requests.Response):
            return self.convert_response(source, **kwargs)

    def convert_local(self, path: str, **kwargs: Any) -> DocumentConverterResult:  # TODO: deal with kwargs
        # Prepare a list of extensions to try (in order of priority)
        ext = kwargs.get("file_extension")
        extensions = [ext] if ext is not None else []

        # Get extension alternatives from the path and puremagic
        base, ext = os.path.splitext(path)
        self._append_ext(extensions, ext)
        self._append_ext(extensions, self._guess_ext_magic(path))

        # Convert
        return self._convert(path, extensions, **kwargs)

    # TODO what should stream's type be?
    def convert_stream(self, stream: Any, **kwargs: Any) -> DocumentConverterResult:  # TODO: deal with kwargs
        # Prepare a list of extensions to try (in order of priority)
        ext = kwargs.get("file_extension")
        extensions = [ext] if ext is not None else []

        # Save the file locally to a temporary file. It will be deleted before this method exits
        handle, temp_path = tempfile.mkstemp()
        fh = os.fdopen(handle, "wb")
        result = None
        try:
            # Write to the temporary file
            content = stream.read()
            if isinstance(content, str):
                fh.write(content.encode("utf-8"))
            else:
                fh.write(content)
            fh.close()

            # Use puremagic to check for more extension options
            self._append_ext(extensions, self._guess_ext_magic(temp_path))

            # Convert
            result = self._convert(temp_path, extensions, **kwargs)
        # Clean up
        finally:
            try:
                fh.close()
            except Exception:
                pass
            os.unlink(temp_path)

        return result

    def convert_url(self, url: str, **kwargs: Any) -> DocumentConverterResult:  # TODO: fix kwargs type
        # Send a HTTP request to the URL
        user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0"
        response = self._requests_session.get(url, stream=True, headers={"User-Agent": user_agent})
        response.raise_for_status()
        return self.convert_response(response, **kwargs)

    def convert_response(
        self, response: requests.Response, **kwargs: Any
    ) -> DocumentConverterResult:  # TODO fix kwargs type
        # Prepare a list of extensions to try (in order of priority)
        ext = kwargs.get("file_extension")
        extensions = [ext] if ext is not None else []

        # Guess from the mimetype
        content_type = response.headers.get("content-type", "").split(";")[0]
        self._append_ext(extensions, mimetypes.guess_extension(content_type))

        # Read the content disposition if there is one
        content_disposition = response.headers.get("content-disposition", "")
        m = re.search(r"filename=([^;]+)", content_disposition)
        if m:
            base, ext = os.path.splitext(m.group(1).strip("\"'"))
            self._append_ext(extensions, ext)

        # Read from the extension from the path
        base, ext = os.path.splitext(urlparse(response.url).path)
        self._append_ext(extensions, ext)

        # Save the file locally to a temporary file. It will be deleted before this method exits
        handle, temp_path = tempfile.mkstemp()
        fh = os.fdopen(handle, "wb")
        result = None
        try:
            # Download the file
            for chunk in response.iter_content(chunk_size=512):
                fh.write(chunk)
            fh.close()

            # Use puremagic to check for more extension options
            self._append_ext(extensions, self._guess_ext_magic(temp_path))

            # Convert
            result = self._convert(temp_path, extensions, url=response.url)
        except Exception as e:
            print(f"Error in converting: {e}")

        # Clean up
        finally:
            try:
                fh.close()
            except Exception:
                pass
            os.unlink(temp_path)

        return result

    def _convert(self, local_path: str, extensions: List[Union[str, None]], **kwargs) -> DocumentConverterResult:
        error_trace = ""
        for ext in extensions + [None]:  # Try last with no extension
            for converter in self._page_converters:
                _kwargs = copy.deepcopy(kwargs)

                # Overwrite file_extension appropriately
                if ext is None:
                    if "file_extension" in _kwargs:
                        del _kwargs["file_extension"]
                else:
                    _kwargs.update({"file_extension": ext})

                # Copy any additional global options
                if "mlm_client" not in _kwargs and self._mlm_client is not None:
                    _kwargs["mlm_client"] = self._mlm_client

                if "mlm_model" not in _kwargs and self._mlm_model is not None:
                    _kwargs["mlm_model"] = self._mlm_model
                    
                # 为PDF设置处理限制
                if isinstance(converter, PdfConverter) and ext and ext.lower() == ".pdf":
                    _kwargs.update({
                        "max_pages": self._pdf_max_pages,
                        "total_max_chars": self._pdf_total_max_chars,
                        "use_threading": True,
                        "pdf_timeout": self._pdf_timeout  # 添加超时参数
                    })

                # If we hit an error log it and keep trying
                res = None  # 初始化res变量，防止未定义错误
                try:
                    res = converter.convert(local_path, **_kwargs)
                except Exception as e:
                    error_trace = ("\n\n" + traceback.format_exc()).strip()
                    print(f"转换错误: {str(e)}")

                if res is not None:
                    # Normalize the content
                    res.text_content = "\n".join([line.rstrip() for line in re.split(r"\r?\n", res.text_content)])
                    res.text_content = re.sub(r"\n{3,}", "\n\n", res.text_content)

                    # Todo
                    return res

        # 如果所有转换器都失败，但有错误跟踪信息，则创建一个包含错误的DocumentConverterResult
        if len(error_trace) > 0:
            error_message = f"转换文件 '{local_path}' 时出错。识别的文件格式为 {extensions}。错误详情:\n\n{error_trace}"
            print(error_message)
            return DocumentConverterResult(
                title="文件转换错误",
                text_content=f"无法解析此文件。可能的原因：文件格式不支持、文件损坏或处理超时。\n\n技术详情：{error_message[:500]}..."
            )

        # 如果没有任何转换器能处理这个文件，返回一个友好的错误信息
        error_message = f"无法转换 '{local_path}' 文件。格式 {extensions} 不受支持。"
        print(error_message)
        return DocumentConverterResult(
            title="不支持的文件格式",
            text_content=f"无法解析此文件。格式 {extensions} 不受系统支持。请尝试使用其他格式的文件。"
        )

    def _append_ext(self, extensions, ext):
        """Append a unique non-None, non-empty extension to a list of extensions."""
        if ext is None:
            return
        ext = ext.strip()
        if ext == "":
            return
        # if ext not in extensions:
        if True:
            extensions.append(ext)

    def _guess_ext_magic(self, path):
        """Use puremagic (a Python implementation of libmagic) to guess a file's extension based on the first few bytes."""
        # Use puremagic to guess
        try:
            guesses = puremagic.magic_file(path)
            if len(guesses) > 0:
                ext = guesses[0].extension.strip()
                if len(ext) > 0:
                    return ext
        except FileNotFoundError:
            pass
        except IsADirectoryError:
            pass
        except PermissionError:
            pass
        return None

    def register_page_converter(self, converter: DocumentConverter) -> None:
        """Register a page text converter."""
        self._page_converters.insert(0, converter)


# 创建一个全局的HTML到Markdown转换函数，添加额外的错误处理
def safe_html_to_markdown(html_content: str) -> str:
    """安全地将HTML转换为Markdown，添加全面的错误处理"""
    try:
        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(html_content, "html.parser")
        
        # 使用自定义转换器转换为Markdown
        return _CustomMarkdownify().convert_soup(soup)
    except Exception as e:
        print(f"HTML到Markdown转换失败: {e}")
        # 如果转换失败，尝试直接提取文本
        try:
            soup = BeautifulSoup(html_content, "html.parser")
            return soup.get_text()
        except:
            # 如果仍然失败，返回原始HTML
            return html_content
