orchestrator:
  intent_model:
    name: qwen-plus
    temperature: 0.6
    max_tokens: 16
  clarification_model:
    name: deepseek-v3 # deepseek-r1
    temperature: 0.6
    max_tokens: 1000
  question_rewriter_model:
    name: deepseek-v3
    temperature: 0.6
    max_tokens: 1000

backup_model:
  name: deepseek-r1
  temperature: 0.6
  max_tokens: 2000

research_agent:
  action_selection_model:
    name: claude-3.7-sonnet-thinking
    temperature: 0.6
    max_tokens: 20000
  url_selection_model:
    name: gpt-4.1-mini
    temperature: 0.6
    max_tokens: 500
  max_search_times: 3
  report_editing_model:
    name: claude-3.7-sonnet
    temperature: 0.6
    max_tokens: 20000
  cognition_search_model:
    name: gpt-4.1-mini
    temperature: 0.6
    max_tokens: 2000
  preference_analysis_model:
    name: gpt-4.1-mini
    temperature: 0.6
    max_tokens: 2000

# Browse Agent配置:
# - browsing_model.name: 指定使用的模型名称，必须与service_config.yaml中的llms列表中的键名对应
# - browser: 浏览相关的配置参数
browse_agent:
  browsing_model:
    name: gpt-4.1-mini
    temperature: 0.6
    max_tokens: 4096
  url_filtering_model:
    name: doubao-lite
    temperature: 0.6
    max_tokens: 100
  browser:
    page_size: 4096
    firecrawl_api_url: "http://localhost:3002"
    max_page_number: 10

clarification_agent:
  model:
    name: qwen-plus
    temperature: 0.7
    max_tokens: 1000

# PPT Agent配置:
# - structure_model: 负责PPT结构设计的模型
# - content_model: 负责生成幻灯片内容的模型
# - design_model: 负责设计建议的模型
ppt_agent:
  structure_model:
    name: gpt-4.1-mini
    temperature: 0.7
    max_tokens: 3000
  content_model:
    name: gpt-4.1-mini
    temperature: 0.6
    max_tokens: 1500
  design_model:
    name: gpt-4.1-mini
    temperature: 0.5
    max_tokens: 1000
  templates:
    business_template: "business_template.pptx"
    academic_template: "academic_template.pptx"
    creative_template: "creative_template.pptx"
  output_settings:
    default_slide_count: 10
    max_slide_count: 50
    supported_languages: ["zh", "en"]

cognition_synthesis_model:
  name: gpt-4.1-mini
  temperature: 0.6
  max_tokens: 2000

image_api:
  model: "qwen-vl-plus"
  api_key: "sk-f129baeac55548679517444fdf82597b"
  base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
