import os
import sys
import asyncio
import json
import yaml
import signal  # 添加signal模块导入
from datetime import datetime
import random

from dc_agents.src.deep_cognition.research_agent import ResearchAgent
from dc_agents.src.deep_cognition.browse_agent import BrowseAgent
from dc_agents.src.services.llm_service import LLMService
from dc_agents.src.services.search_service import SearchService
from loguru import logger

# 配置日志
logger.add("research_demo.log", rotation="10 MB")

# 全局变量存储暂停状态和当前研究智能体
paused = False
current_research_agent = None

# 信号处理函数，用于捕获Ctrl+C
def signal_handler(sig, frame):
    """
    处理Ctrl+C信号，暂停研究流程而非终止程序
    """
    global paused
    if not paused:
        paused = True
        print("\n\n⚠️ 检测到中断请求，暂停研究流程...")
        print("请输入您的反馈，或直接按回车恢复研究流程：")

async def demo_research(question=None, standard_answer=None, language="zh", benchmark_id=None):
    """
    演示研究智能体的基本功能
    
    Args:
        question: 可选，预设的研究问题
        standard_answer: 可选，问题的标准答案
        language: 语言，默认为中文
        benchmark_id: benchmark测试的ID，用于保存轨迹
    """
    global paused, current_research_agent
    
    print("\n" + "="*80)
    print("【研究智能体演示】")
    print("="*80)
    
    # 加载配置
    agents_config = yaml.load(open("dc_agents/config/config.yaml"), Loader=yaml.FullLoader)
    service_config = yaml.load(open("dc_agents/config/service_config.yaml"), Loader=yaml.FullLoader)
    
    # 创建LLM服务
    llms = {}
    for model, _config in service_config["llms"].items():
        llms[model] = LLMService(_config)
    
    # 获取研究智能体配置
    research_config = agents_config.get("research_agent", {})
    research_model_config = research_config.get("model", {})
    research_model_name = research_model_config.get("name", "qwen-max")
    
    # 初始化浏览智能体（研究智能体需要）
    browse_config = agents_config.get("browse_agent", {})
    browse_agent = BrowseAgent(
        config=browse_config,
        llm_services=llms
    )
    
    # 初始化搜索服务（研究智能体需要）
    search_service = SearchService(service_config["search_service"])
    
    # 初始化研究智能体
    research_agent = ResearchAgent(
        config=research_config,
        llm_services=llms,
        search_service=search_service,
        browse_agent=browse_agent
    )
    
    # 存储当前研究智能体到全局变量，便于信号处理
    current_research_agent = research_agent
    
    # 获取用户研究问题
    if question is None:
        print("\n请输入您的研究问题:")
        question = input("> ")
        standard_answer = "未提供标准答案"
    else:
        print(f"\n使用预设研究问题: {question}")
    
    print("\n开始研究流程...\n")
    print("💡 提示: 您可以随时按下 Ctrl+C 暂停研究流程，输入反馈后继续进行\n")
    
    # 设置初始输入数据
    input_data = {
        "question": question,
    }
    
    # 如果有benchmark_id，添加到输入数据中
    if benchmark_id is not None:
        input_data["benchmark_id"] = benchmark_id
    
    # 将标准答案存储在研究智能体对象中
    # research_agent.question = question
    # research_agent.standard_answer = standard_answer
    
    current_stage = None  # 跟踪当前阶段，避免重复输出
    current_action = None  # 跟踪当前动作类型，避免重复前缀
    interaction_count = 0  # 交互轮次计数器
    
    # 注册信号处理程序
    original_handler = signal.getsignal(signal.SIGINT)
    signal.signal(signal.SIGINT, signal_handler)
    
    # 开始研究流程
    try:
        # 处理研究智能体的输出
        async for chunk in research_agent.run(input_data):
            # 检查是否已暂停
            if paused:
                # 等待用户输入反馈
                user_feedback = input("> ")
                
                if user_feedback.strip():  # 如果用户提供了反馈
                    # 添加用户反馈
                    research_agent.add_user_feedback(user_feedback)
                    print("\n✅ 已添加您的反馈，继续研究流程...\n")
                else:
                    print("\n✅ 继续研究流程，无新增反馈...\n")
                
                # 恢复研究流程
                await research_agent.resume()
                paused = False
            
            # 在每个新交互轮次开始时，打印当前的问题和报告草稿
            if "stage" in chunk and chunk["stage"] == "select_action" and current_stage != "select_action":
                interaction_count += 1
                print("\n" + "#"*80)
                print(f"【交互轮次 {interaction_count}】")
                print(f"当前问题: {question}")
                print(f"问题的正确答案: {standard_answer}")

            # 显示研究过程的输出
            if "stage" in chunk:
                stage = chunk["stage"]
                # 只在阶段变化时输出阶段标题
                if stage != current_stage:
                    current_stage = stage
                    current_action = None  # 每次进入新阶段时重置动作类型
                    print(f"\n----- 当前阶段: {stage} -----")
                    
                    # 为新阶段添加前缀标识
                    if stage == "select_action":
                        print("🤔 思考分析: ", end="", flush=True)
                    elif stage == "web_search":
                        print("🔍 搜索准备: ", end="", flush=True)
                    elif stage == "edit_report":
                        print("📝 报告编辑: ", end="", flush=True)
                    elif stage == "browse_webpages":
                        print("🌐 浏览网页: ", end="", flush=True)
                    elif stage == "clarification":
                        print("❓ 需要澄清: ", end="", flush=True)
            
            # 显示智能体思考过程或结果
            if "content_type" in chunk and ("content" in chunk or "reasoning_content" in chunk):
                content_type = chunk["content_type"]
                content = chunk["content"] if "content" in chunk else chunk["reasoning_content"]
                
                if content_type == "action":
                    action = chunk.get("action", "")
                    # 根据action类型显示内容
                    if action == "think":
                        # 如果前一个不是think，先输出一个前缀
                        if current_action != "think":
                            current_action = "think"
                            # 如果不是在select_action阶段，则也输出思考前缀
                            if current_stage != "select_action":
                                print("\n🤔 思考中: ", end="", flush=True)
                        # 思考内容使用同一行输出，不换行
                        print(content, end="", flush=True)
                    elif action == "answer":
                        # 如果前一个不是answer，先输出一个前缀
                        if current_action != "answer":
                            current_action = "answer"
                            print("\n💡 回答: ", end="", flush=True)
                        # 答案使用同一行输出，不换行
                        print(content, end="", flush=True)
                    elif action == "web_search":
                        print("\n🌐 搜索查询: " + str(content))
                        current_action = "web_search"
                    elif action == "clarify":
                        # 处理澄清请求
                        print("\n❓ 需要澄清问题: \n" + str(content))
                        current_action = "clarify"
                        
                        # 在处理用户输入前打印当前状态
                        print("\n" + "#"*80)
                        print(f"【澄清问题前状态】")
                        print(f"当前问题: {question}")
                        print(f"问题的正确答案: {standard_answer}")
                        print("-"*40 + " 当前报告草稿 " + "-"*40)
                        print(research_agent.report_draft if research_agent.report_draft else "尚未生成报告草稿")
                        print("#"*80 + "\n")
                        
                        # 标记正在进行澄清流程
                        in_clarification_flow = True
                        
                        # 循环处理澄清问题，直到所有问题都回答完毕
                        while in_clarification_flow:
                            # 获取用户对澄清问题的回答
                            print("\n请回答以上澄清问题:")
                            user_answer = input("> ")
                            
                            # 将用户回答添加到研究智能体，并获取返回值
                            response = research_agent.add_user_feedback(user_answer)
                            
                            # 检查是否需要继续澄清
                            if response and isinstance(response, dict):
                                if response.get("action") == "clarify" and response.get("continue_clarification", False):
                                    # 还有更多问题需要回答
                                    next_content = response.get("content", "")
                                    print("\n❓ 需要澄清问题: \n" + next_content)
                                    # 循环继续，获取下一个问题的回答
                                else:
                                    # 所有问题已回答完毕
                                    if response.get("action") == "clarification_complete":
                                        print("\n✅ " + response.get("content", "澄清问题已全部回答"))
                                    
                                    # 退出澄清循环
                                    in_clarification_flow = False
                            else:
                                # 如果没有特定的响应，假设澄清流程已结束
                                in_clarification_flow = False
                        
                        # 澄清完成后，再次打印当前状态
                        print("\n" + "#"*80)
                        print(f"【澄清问题后状态】")
                        print(f"当前问题: {question}")
                        print(f"问题的正确答案: {research_agent.standard_answer}")
                        print("-"*40 + " 当前报告草稿 " + "-"*40)
                        print(research_agent.report_draft if research_agent.report_draft else "尚未生成报告草稿")
                        print("#"*80 + "\n")
                        
                        # 澄清完成后恢复研究流程
                        await research_agent.resume()
                        
                        # 此处不中断循环，允许下一个 select_action 考虑用户的答案
                    elif action == "clarification_complete":
                        # 处理澄清完成的消息
                        print("\n✅ " + content)
                        current_action = "clarification_complete"
                        
                        # 恢复研究流程
                        await research_agent.resume()
                
                elif content_type == "observation":
                    # print("\n📋 搜索结果: " + json.dumps(content, ensure_ascii=False)[:150] + "...")
                    current_action = "observation"
                
            # 检查是否有特殊标记，例如需要返回
            if "return" in chunk and chunk["return"]:
                print("\n研究任务完成，返回最终结果")
        
        # 显示最终报告
        print("\n" + "="*80)
        print("【最终研究报告】")
        print("="*80)
        print(research_agent.report_draft)
        print("="*80)
        
        return research_agent.report_draft
        
    except Exception as e:
        error_msg = f"研究过程中发生错误: {str(e)}"
        print(error_msg)
        import traceback
        traceback.print_exc()
        return None
    finally:
        # 恢复原来的信号处理程序
        signal.signal(signal.SIGINT, original_handler)
        # 重置全局变量
        paused = False
        current_research_agent = None
    
    print("\n演示完成")

async def main():
    # 默认运行benchmark测试模式，除非指定其他模式
    await demo_research(language="zh")


if __name__ == "__main__":
    asyncio.run(main())
