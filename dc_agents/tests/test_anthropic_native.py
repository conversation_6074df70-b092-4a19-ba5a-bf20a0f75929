import asyncio
import json
from dc_agents.src.services.llm_service import LLMService
from loguru import logger

async def test_anthropic_native_adapter():
    """测试Anthropic原生API适配器 - 主要测试流式响应"""
    
    print("=== 测试Anthropic原生API适配器 - 流式响应 ===")
    
    # 测试配置列表
    test_configs = [
        {
            "name": "claude-sonnet-4-native",
            "description": "带thinking的推理模型"
        },
        {
            "name": "claude-sonnet-4-native-no-thinking", 
            "description": "不带thinking的普通模型"
        }
    ]
    
    # 测试消息
    test_messages = [
        {"role": "system", "content": "你是一个专业的研究员，擅长研究各种问题。"},
        {"role": "user", "content": "你好，请简单介绍一下你自己，并解释一下你的能力。"}
    ]
    
    for config in test_configs:
        print(f"\n{'='*60}")
        print(f"测试配置: {config['name']} - {config['description']}")
        print(f"{'='*60}")
        
        try:
            # 创建LLM服务实例
            service = LLMService({"name": config["name"]})
            
            print(f"模型名称: {service.model_name}")
            print(f"提供商: {service.provider}")
            print(f"API URL: {service.api_url}")
            print(f"是否为推理模型: {service.is_reasoning_model}")
            
            print(f"\n测试消息: {json.dumps(test_messages, ensure_ascii=False, indent=2)}")
            
            # 测试流式响应
            print(f"\n--- 测试流式响应 ({config['description']}) ---")
            try:
                stream = await service.get_completion(
                    test_messages,
                    temperature=0.7,  # 这个会被适配器强制改为1.0
                    max_tokens=20000
                )
                
                if stream:
                    thinking_content = ""
                    text_content = ""
                    chunk_count = 0
                    
                    print("开始接收流式数据...")
                    
                    async for chunk in stream:
                        chunk_count += 1
                        
                        # 检查是否是token信息
                        if isinstance(chunk, dict) and chunk.get("is_token_info"):
                            print(f"\n\n=== Token信息 ===")
                            print(f"Token信息: {chunk['token_info']}")
                            continue
                        
                        # 处理流式chunk
                        if hasattr(chunk, 'choices') and chunk.choices:
                            delta = chunk.choices[0].delta
                            finish_reason = chunk.choices[0].finish_reason
                            
                            # 处理thinking内容
                            if hasattr(delta, 'reasoning_content') and delta.reasoning_content:
                                thinking_content += delta.reasoning_content
                                print(f"[Thinking] {delta.reasoning_content}", end="", flush=True)
                            
                            # 处理普通文本内容
                            if hasattr(delta, 'content') and delta.content:
                                text_content += delta.content
                                print(f"[Content] {delta.content}", end="", flush=True)
                            
                            # 处理结束
                            if finish_reason:
                                print(f"\n[结束] finish_reason: {finish_reason}")
                    
                    print(f"\n\n=== 流式响应总结 ===")
                    print(f"总chunk数量: {chunk_count}")
                    print(f"Thinking内容长度: {len(thinking_content)}")
                    print(f"文本内容长度: {len(text_content)}")
                    
                    if thinking_content:
                        print(f"Thinking内容预览: {thinking_content[:200]}...")
                    else:
                        print("无Thinking内容")
                    
                    if text_content:
                        print(f"完整文本内容: {text_content}")
                    else:
                        print("无文本内容")
                        
                else:
                    print("流式请求失败 - 返回None")
                    
            except Exception as e:
                print(f"流式请求出错: {e}")
                import traceback
                traceback.print_exc()
            
        except Exception as e:
            print(f"初始化服务出错: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_anthropic_native_adapter()) 