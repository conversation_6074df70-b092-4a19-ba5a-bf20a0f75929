from dc_agents.src.services.llm_service import LLMService
import asyncio
import json

async def test_llm_token_billing():
    """测试LLM服务的token计算和计费功能"""
    
    print("=== LLM Token计费测试 ===")
    
    # 测试不同的模型配置
    test_configs = [
        {
            "name": "claude-3.7-sonnet-thinking",  # 假设这是一个可用的配置
            "test_messages": [
                {"role": "user", "content": "请详细介绍一下人工智能的发展历史、主要技术流派、关键突破，以及未来可能的趋势和挑战。"},
                {"role": "assistant", "content": "人工智能（AI）自20世纪50年代诞生以来，经历了多次浪潮。最早的AI研究以符号主义为主，强调逻辑推理和专家系统。80年代，连接主义（神经网络）兴起，但受限于算力和数据，发展缓慢。21世纪，深度学习（以多层神经网络为代表）在大数据和GPU推动下取得突破，推动了语音识别、图像识别、自然语言处理等领域的飞跃。近年来，生成式AI（如GPT、Diffusion模型）成为热点。未来，AI将面临可解释性、通用智能、伦理与安全等挑战，同时在医疗、教育、科学等领域展现巨大潜力。"},
                {"role": "user", "content": "你能否用更通俗的语言，举例说明深度学习和传统AI的区别？并预测一下AI未来十年的发展重点？"}
            ]
        }
    ]
    
    for config_test in test_configs:
        print(f"\n--- 测试配置: {config_test['name']} ---")
        
        try:
            # 创建LLM服务实例
            service = LLMService({"name": config_test["name"]})
            
            print(f"模型名称: {service.model_name}")
            print(f"提供商: {service.provider}")
            print(f"是否为Claude模型: {service.is_claude_model}")
            print(f"输入价格: {service.input_price} CNY/1K tokens")
            print(f"输出价格: {service.output_price} CNY/1K tokens")
            
            # 测试token计算
            messages = config_test["test_messages"]
            print(f"\n测试消息: {json.dumps(messages, ensure_ascii=False, indent=2)}")
            
            # 真实请求模型生成内容
            result = await service.get_completion_sync(
                messages,
                temperature=0.7,
                max_tokens=8000
            )
            if result and "token_info" in result:
                output_tokens = result["token_info"]["output_tokens"]
                input_tokens = result["token_info"]["input_tokens"]
                print(f"输入token数量: {input_tokens}")
                print(f"输出token数量: {output_tokens}")
                print(f"计费信息: {result['token_info']}")
                print(f"模型输出内容: {result['response'].choices[0].message.content}")
            else:
                print("模型请求失败或无token信息")
            
            # 如果是Claude模型，显示tokenizer信息
            if service.is_claude_model:
                if hasattr(service, 'claude_tokenizer') and service.claude_tokenizer is not None:
                    print(f"Claude tokenizer状态: 已加载")
                else:
                    print(f"Claude tokenizer状态: 未加载")
            
        except Exception as e:
            print(f"测试失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    print("开始LLM Token计费测试...")
    
    # 运行异步测试
    asyncio.run(test_llm_token_billing())
    
    print("\n测试完成！")