# Deep Cognition Agent

点击查看：[Deep Cognition Agent 架构图 (持续完善中...)](https://yaf2qum85lq.feishu.cn/docx/ELRVdakPDoLOUoxFccOcF85enhf?openbrd=1&doc_app_id=501&blockId=H4z2dVityo3C6mxlqCfc3CMAnRb&blockType=whiteboard&blockToken=Pfzkwum6HhqjGqbe4RLcNEhVnze#H4z2dVityo3C6mxlqCfc3CMAnRb)

### 代码结构

```
dc_agents/
├── config/                  # 配置文件夹
│   ├── config.yaml          # 主要配置文件
│   └── service_config.yaml  # 服务相关配置
├── src/                     # 源代码文件夹
│   ├── agents/              # Agent基类相关代码
│   │   ├── agent.py           # Agent基类
│   │   ├── memory.py          # Agent记忆相关功能
│   │   └── research_memory.py # Research Agent的记忆功能
│   ├── deep_cognition/      # Deep Cognition中的各个Agents
│   │   ├── browse_agent.py         # 浏览网页的智能体
│   │   ├── browse_agent_prompts.py # 浏览智能体的prompts
│   │   ├── orchestrator.py         # 任务编排器
│   │   ├── orchestrator_prompts.py # 编排器的prompts
│   │   ├── research_agent.py       # 研究型智能体
│   │   └── research_prompts.py     # 研究型智能体的prompts
│   ├── models/              # 数据模型 (Pydantic等)
│   │   ├── memory.py          # 记忆相关的数据模型
│   │   └── research_memory.py # 研究型智能体记忆的数据模型
│   ├── services/            # 应用服务
│   │   ├── llm_service.py     # 大语言模型服务
│   │   └── search_service.py  # 搜索服务
│   ├── tests/               # 测试代码
│   │   └── test_mongodb_memory.py # MongoDB记忆功能的测试
│   ├── tools/               # 工具代码
│   │   ├── browser.py         # 浏览器交互工具
│   │   ├── cookies.py         # Cookie管理工具
│   │   ├── mdconvert.py       # Markdown转换工具
│   │   ├── search_api.py      # 搜索API封装
│   │   ├── text_web_browser.py # 文本模式的网页浏览器
│   │   └── web_search_agent.py # 网页搜索智能体
│   └── utils.py             # 通用工具函数
├── trajectory/              # (可能用于存放智能体轨迹或日志，目前为空)
├── main.py                  # 命令行测试（之后会改掉）
├── models.json              # (模型定义)
└── README.md                # 项目说明文件
```

### 测试

- **端到端测试**：要端到端测试Agent，需要同时启动前后端，请参考[../README.md](../README.md)中的说明。

- **命令行测试**：
    - 在项目根目录(DAIR)使用`python -m dc_agents.main`进行命令行测试。
    - 使用Ctrl + C 暂停研究并提出反馈。
    - 使用Ctrl + Z 结束程序。
