#!/bin/bash

# SSL证书自动更新定时任务脚本
# 建议通过crontab每天运行一次：0 2 * * * /path/to/ssl-renew-cron.sh

set -e

DOMAIN="dev.q.opensii.ai"
CERT_DIR="./data/certbot/conf/live/$DOMAIN"
LOG_FILE="./data/certbot/logs/renewal.log"

# 确保日志目录存在
mkdir -p ./data/certbot/logs

# 记录日志
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') [SSL-RENEWAL] $1" | tee -a "$LOG_FILE"
}

log "开始SSL证书更新检查..."

# 检查证书是否在30天内过期
if [ -f "$CERT_DIR/fullchain.pem" ]; then
    if openssl x509 -checkend 2592000 -noout -in "$CERT_DIR/fullchain.pem"; then
        log "证书有效，无需更新"
        exit 0
    else
        log "证书即将过期，开始更新..."
    fi
else
    log "证书文件不存在，需要获取新证书"
fi

# 尝试更新证书
docker run --rm \
    --name certbot \
    -v "${PWD}/data/certbot/conf:/etc/letsencrypt" \
    -v "${PWD}/data/certbot/www:/var/www/certbot" \
    -v "${PWD}/data/certbot/logs:/var/log/letsencrypt" \
    certbot/certbot renew \
    --webroot \
    --webroot-path=/var/www/certbot \
    --quiet \
    --no-self-upgrade

if [ $? -eq 0 ]; then
    log "证书更新成功，重启nginx容器..."
    
    # 重启nginx容器以加载新证书
    docker restart dair-nginx 2>/dev/null || {
        log "nginx容器重启失败，尝试重新部署..."
        cd /path/to/your/project && bash deploy.sh
    }
    
    log "证书更新完成"
else
    log "证书更新失败"
    exit 1
fi 