{"name": "dair_web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@aarkue/tiptap-math-extension": "^1.3.6", "@arms/rum-browser": "^0.0.34", "@element-plus/icons-vue": "^2.3.1", "@mdi/font": "^7.4.47", "@sentry/browser": "^9.2.0", "@sentry/tracing": "^7.120.3", "@sentry/vue": "^9.1.0", "@tiptap/core": "^2.9.0", "@tiptap/extension-code-block-lowlight": "^2.11.7", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.11.7", "@tiptap/extension-placeholder": "^2.9.0", "@tiptap/extension-table": "^2.9.0", "@tiptap/extension-table-cell": "^2.9.0", "@tiptap/extension-table-header": "^2.9.0", "@tiptap/extension-table-row": "^2.9.0", "@tiptap/extension-underline": "^2.22.3", "@tiptap/pm": "^2.9.0", "@tiptap/starter-kit": "^2.9.0", "@tiptap/vue-3": "^2.9.0", "@types/lodash": "^4.17.16", "axios": "^1.7.9", "date-fns": "^4.1.0", "diff-match-patch": "^1.0.5", "dompurify": "^3.2.4", "echarts": "^5.6.0", "element-plus": "^2.9.10", "gsap": "^3.12.7", "highlight.js": "^11.11.1", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "katex": "^0.16.22", "lodash": "^4.17.21", "lowlight": "^3.3.0", "markdown-it": "^14.1.0", "markdown-it-texmath": "^1.0.0", "marked": "^15.0.7", "pdfmake": "^0.2.18", "pinia": "^2.3.1", "prosemirror-state": "^1.4.3", "prosemirror-tables": "^1.7.1", "prosemirror-view": "^1.38.1", "tiptap-markdown": "^0.8.10", "vite-plugin-vuetify": "^2.1.0", "vue": "^3.5.13", "vue-i18n": "^9.0.0", "vue-next-masonry": "^1.1.3", "vue-router": "^4.5.0", "vue-uuid": "^3.0.0", "vuetify": "^3.7.13"}, "devDependencies": {"@types/diff-match-patch": "^1.0.36", "@types/dompurify": "^3.0.5", "@types/html2canvas": "^0.5.35", "@types/node": "^22.13.5", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "prettier": "^2.0.0", "sass": "^1.85.0", "typescript": "~5.7.2", "vite": "^6.1.0", "vue-tsc": "^2.2.0"}}