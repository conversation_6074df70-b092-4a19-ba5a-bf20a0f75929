# DC FrontEnd 文档

基于 Vue + TypeScript + Vite 构建

⚠️ **请阅读本文档**

## 项目结构

### API 模块
> 封装来自后端的接口
- **authService** - 身份验证相关接口
- **chat** - 聊天功能接口
- **event** - 数据统计接口

### assets
> 项目使用的图片和静态资源
- **assets** - 存放项目图片资源

### component
> 项目的各个功能组件

- **canvas** - 页面左侧部分
  - **edit** - canvas 中的编辑框组件
- **hoverball** - 暂停/信箱悬浮球
- **interface** - 页面右侧部分
- **popout** - 展示 clarification 问题的弹出框
- **settings** - 设置弹出框
- **HistoryDialog** - 历史记录弹出框
- **MarkdownRendered** - Markdown 内容渲染容器
- **UserStatusButton** - 用户状态弹出框

### 组合式函数 composables
> 核心逻辑，跨组件使用的状态和函数
- **useCanvasEditor** - Canvas 相关状态/函数
- **useDeepConversation** - 对话相关状态/函数
- **usePreference** - 基本已弃用

### 页面 page
> 应用的主要页面
- **auth** - 登录/注册页面
- **draft** - 主页面
- **dashboard** - 统计页面（目前基本弃用）

### 其他模块
- **router** - Vue Router 路由配置
- **types** - 类型定义（基本已弃用，目前类型直接定义在 composables 中）

## 注意事项

- 在 `vite.config.ts` 中模拟 nginx 的 proxy 向后端转发，会导致一些请求路径的变化

## Draft 页面逻辑

currentConversationId (useDeepConversation当中) 代表着当前的conversation_id
当前的conversation_id也会反映在url当中，通过conversation_id="xxxxx"

### 渲染方式
- 通过 `useDeepConversation` 中的 `canvasItems` + `interfaceItems` 驱动页面渲染
  - 相当于 for 循环下，按顺序渲染所有内容为文档流
  - 加入的任何组件应能独一无二地区分，以便区分新内容是更新还是创建
  - 尽量不使用 props 进行渲染

### 事件处理方式
- 从下层组件触发，通过 emit 传值到 draft 页面，再由 composables 统一管理
- 少数不会对其他组件产生影响的函数（如分享），如果上层确实不必知道，可从下层直接调用 composables 中的函数处理 (通过provide/inject, 但并不太推荐)
- 通过 props 从上层向下更新状态变量（如 conversation_id）

### 流连接处理
- 通过同一个 WebSocket 连接处理所有接收到的事件
- 其余交互通过独立的 HTTP 接口进行通信
