{"compilerOptions": {"module": "ESNext", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "target": "ESNext", "skipLibCheck": true, "allowJs": true, "strict": false, "types": ["node"], "composite": true, "baseUrl": "./", "paths": {"@/*": ["src/*"]}, "outDir": "./dist", "declaration": true, "declarationDir": "./dist"}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", "src/**/*.vue", "src/types/**/*.d.ts"], "exclude": ["node_modules", "dist"]}