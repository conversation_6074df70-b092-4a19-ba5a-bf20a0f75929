import vue from "@vitejs/plugin-vue";
import { fileURLToPath } from "url";
import { defineConfig, loadEnv } from "vite";
import vuetify from "vite-plugin-vuetify";
import { wrapperEnv } from "./src/config/env";

// https://vite.dev/config/
export default defineConfig(({ command, mode }) => {
  // 根据当前工作目录中的 `mode` 加载 .env 文件
  const env = loadEnv(mode, process.cwd());
  const viteEnv = wrapperEnv(env);

  return {
    plugins: [vue(), vuetify({ autoImport: true })],
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url)),
      },
    },
    server: {
      host: true,
      port: viteEnv.VITE_PORT,
      proxy: {
        "/backend": {
          target: "http://localhost:8257",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/backend/, ""),
        },
        "/ws": {
          target: "ws://localhost:8257",
          changeOrigin: true,
          ws: true,  // 关键配置：启用 WebSocket 代理
        },
        "/conversation/ws": {
          target: "ws://localhost:8257",
          changeOrigin: true,
          ws: true,  // 关键配置：启用 WebSocket 代理
        },
      },
    },
    define: {
      // 定义全局常量替换方式
      __APP_ENV__: JSON.stringify(env.VITE_APP_ENV),
    },
    esbuild: {
      target: 'chrome89',
    },
  };
});
