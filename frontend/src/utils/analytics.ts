// 声明全局变量类型
declare global {
    interface Window {
      _czc: any[];
    }
  }
  
  export class AnalyticsService {
    /**
     * 跟踪页面浏览
     * @param path 页面路径
     */
    static trackPageView(path?: string) {
      if (window._czc) {
        window._czc.push(['_trackPageview', path || window.location.pathname]);
      }
    }
  
    /**
     * 跟踪事件
     * @param category 事件类别，必填项，表示事件发生在谁身上，如"视频"、"小说"、"轮显层"等
     * @param action 事件操作，必填项，表示访客跟元素交互的行为动作，如"播放"、"收藏"、"翻层"等
     * @param label 事件标签，选填项，用于更详细的描述事件，如具体是哪个视频，哪部小说，翻到了第几层等
     * @param value 事件值，选填项，整数型，用于填写打分型事件的分值，加载时间型事件的时长，订单型事件的价格等
     * @param nodeid div元素id，选填项，填写网页中的div元素id值，用于在"用户视点"功能上重绘元素的事件发生情况
     */
    static trackEvent(category: string, action: string, label: string = '', value: number = 0, nodeid?: string) {
      if (window._czc) {
        // 如果提供了nodeid，则添加到参数中，否则只使用前四个参数
        if (nodeid) {
          window._czc.push(['_trackEvent', category, action, label, value, nodeid]);
        } else {
          window._czc.push(['_trackEvent', category, action, label, value]);
        }
      }
    }
  
    /**
     * 设置自定义变量
     * @param name 变量名
     * @param value 变量值
     */
    static setCustomVar(name: string, value: string) {
      if (window._czc) {
        window._czc.push(['_setCustomVar', name, value]);
      }
    }
  }