import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import type {
  AxiosInstance,
  InternalAxiosRequestConfig,
} from "axios";
import { AnalyticsService } from '@/utils/analytics';

// 自定义错误类型
interface ApiError extends Error {
  response?: any;
}

// 创建默认axios实例
const apiClient = axios.create({
  baseURL: '/backend',
  timeout: 36000000,
  headers: {
    'Content-Type': 'application/json',
  },
  // 自定义参数序列化器，确保数组参数格式与FastAPI兼容
  paramsSerializer: {
    indexes: null // 这会让数组参数变成 topics=value1&topics=value2 格式而不是 topics[0]=value1&topics[1]=value2
  }
});

// 创建后端流式请求实例
const streamRequest = axios.create({
  baseURL: "/backend",
  timeout: 36000000,
  responseType: "text",
  headers: {
    "Content-Type": "application/json",
    Accept: "text/event-stream",
  },
});

// 错误处理
const errorHandler = (error: any) => {
  const { response } = error;
  if (response) {
    const { status } = response;
    // 定义常见的HTTP错误代码和对应的错误消息
    const errorMap: Record<number, string> = {
      400: "请求错误",
      401: "未授权",
      402: "Token预算不足",
      403: "禁止访问",
      404: "资源未找到",
      422: "请求数据验证失败",
      500: "服务器内部错误",
    };

    const errorMessage = errorMap[status] || `未知错误 (${status})`;
    console.error(`API错误: ${errorMessage}`, response.data);

    // 如果是401错误，清除token
    if (status === 401) {
      localStorage.removeItem('auth_token');
    }
  } else if (error.request) {
    // 请求已发送但没有收到响应
    console.error("服务器无响应", error.request);
  } else {
    // 发送请求时出错
    console.error("请求错误", error.message);
  }
  return Promise.reject(error);
};

// 为streamRequest添加请求拦截器
streamRequest.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 从localStorage获取token
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.set("Authorization", `Bearer ${token}`);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 为streamRequest添加响应拦截器
streamRequest.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response && error.response.status === 401) {
      console.error('401 Unauthorized error detected, clearing token');
      localStorage.removeItem('auth_token');
      // 如果当前不是登录页，才跳转
      if (!window.location.pathname.includes('/login')) {
        window.location.href = '/login';
      }
    }
    return errorHandler(error);
  }
);

// 为apiClient添加请求拦截器
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 从localStorage获取token
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.set("Authorization", `Bearer ${token}`);
    }
    
    // 如果是FormData，删除Content-Type让浏览器自动设置
    if (config.data instanceof FormData) {
      config.headers.delete("Content-Type");
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 为apiClient添加响应拦截器
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response && error.response.status === 401) {
      console.error('401 Unauthorized error detected, clearing token');
      localStorage.removeItem('auth_token');
      // 如果当前不是登录页，才跳转
      // if (!window.location.pathname.includes('/login')) {
      //   window.location.href = '/login';
      // }
    }
    return errorHandler(error);
  }
);

/**
 * 通用请求函数
 * @param url 请求URL
 * @param method 请求方法
 * @param data 请求数据
 * @param params 查询参数
 * @param config 额外配置
 * @returns 请求响应
 */
async function backendRequest<T = any>(
  url: string,
  method: string = 'GET',
  data?: any,
  params?: any,
  config: AxiosRequestConfig = {}
): Promise<T> {
  try {
    const response: AxiosResponse<T> = await apiClient({
      url,
      method,
      data,
      params,
      ...config,
    });
    return response.data;
  } catch (error: any) {
    // 处理Error类型
    if (error.response) {
      // 服务器返回了错误响应
      const status = error.response.status;
      let errorMessage = error.response.data?.detail || '服务器错误';

      // 处理常见错误类型
      if (status === 401) {
        errorMessage = '认证失败，请重新登录';
      } else if (status === 402) {
        errorMessage = error.response.data?.detail || 'Token使用额度已用尽，请联系管理员进行充值';
      } else if (status === 403) {
        errorMessage = '权限不足，无法访问';
      } else if (status === 422) {
        // 处理验证错误，可能有多个字段错误
        if (error.response.data?.detail && Array.isArray(error.response.data.detail)) {
          errorMessage = error.response.data.detail
            .map((err: any) => `${err.loc.join('.')}：${err.msg}`)
            .join('; ');
        }
      }

      const customError = new Error(errorMessage) as ApiError;
      customError.response = error.response;
      throw customError;
    } else if (error.request) {
      // 请求已发送但没有收到响应
      throw new Error('服务器无响应，请检查网络连接');
    } else {
      // 发送请求时出错
      throw new Error('请求错误: ' + error.message);
    }
  }
}

// 添加一个测试函数，验证 streamRequest 是否正确支持 AbortController
export const testAbortSupport = () => {
  console.log("[DEBUG] 测试 Axios 的 abort 支持");
  const controller = new AbortController();

  const request = streamRequest.get('/test-abort', {
    signal: controller.signal
  });

  // 立即取消请求
  setTimeout(() => {
    console.log("[DEBUG] 测试中断请求");
    controller.abort();
  }, 10);

  return request
    .then(() => {
      console.log("[DEBUG] 请求成功完成 (不应该看到这个)");
      return false;
    })
    .catch(err => {
      console.log(`[DEBUG] 请求被中断: ${err.name}, ${err.message}`);
      return err.name === 'CanceledError' || err.name === 'AbortError';
    });
};

// 确保 streamRequest 上有取消方法
console.log("[DEBUG] 检查 streamRequest 实例:",
  "axios.CancelToken 存在:", !!axios.CancelToken,
  "axios.isCancel 存在:", !!axios.isCancel);

export { streamRequest, backendRequest };
