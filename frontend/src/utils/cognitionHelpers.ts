/**
 * 认知数据处理工具函数
 */

// 获取当前语言设置（暂时默认中文，后续可以从store或i18n获取）
export function getCurrentLanguage(): 'zh' | 'en' {
  // TODO: 后续可以从 i18n 或用户设置中获取当前语言
  return 'zh';
}

// 获取对应语言的字段值
export function getCognitionField(cognition: any, fieldName: string, language?: 'zh' | 'en'): string {
  const lang = language || getCurrentLanguage();
  const fieldKey = `${fieldName}_${lang}`;
  
  // 优先返回指定语言的版本
  const primaryValue = cognition[fieldKey] || '';
  
  // 如果指定语言版本为空，尝试返回另一种语言的版本
  if (!primaryValue.trim()) {
    const fallbackLang = lang === 'zh' ? 'en' : 'zh';
    const fallbackKey = `${fieldName}_${fallbackLang}`;
    return cognition[fallbackKey] || '';
  }
  
  return primaryValue;
}

// 获取认知摘要（优先级：abstract > think > question）
export function getCognitionSummary(cognition: any, language?: 'zh' | 'en'): string {
  const abstract = getCognitionField(cognition, 'abstract', language);
  if (abstract.trim()) return abstract;
  
  const think = getCognitionField(cognition, 'think', language);
  if (think.trim()) return think;
  
  const question = getCognitionField(cognition, 'question', language);
  return question;
}

// 获取认知的完整显示内容
export function getCognitionContent(cognition: any, language?: 'zh' | 'en') {
  return {
    abstract: getCognitionField(cognition, 'abstract', language),
    think: getCognitionField(cognition, 'think', language),
    question: getCognitionField(cognition, 'question', language),
    answer: getCognitionField(cognition, 'answer', language),
    summary: getCognitionSummary(cognition, language)
  };
}

// 检查认知字段是否有内容
export function hasCognitionField(cognition: any, fieldName: string, language?: 'zh' | 'en'): boolean {
  const content = getCognitionField(cognition, fieldName, language);
  return content.trim().length > 0;
} 