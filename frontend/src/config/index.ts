import { getEnvConfig } from "./env";

// 全局配置
export const GlobalConfig = {
  ...getEnvConfig(import.meta.env),

  // 其他全局配置
  APP_DEFAULT_CONFIG: {
    theme: "light",
    language: "zh-CN",
  },

  // 存储配置
  STORAGE_CONFIG: {
    prefix: "my_app_",
    expire: 7 * 24 * 60 * 60 * 1000,
  },

  // 请求配置
  REQUEST_CONFIG: {
    timeout: 10 * 1000,
    withToken: true,
  },
};

// 导出类型
export type GlobalConfigType = typeof GlobalConfig;
