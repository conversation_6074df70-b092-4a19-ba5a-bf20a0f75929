import { Recordable } from "@/env";

// 直接使用全局类型
export function getEnvConfig(env: ImportMetaEnv) {
  const {
    VITE_APP_ENV,
    VITE_APP_TITLE,
    VITE_APP_API_BASE_URL,
    VITE_APP_UPLOAD_URL,
  } = env;

  return {
    // 环境
    ENV: VITE_APP_ENV,

    // 应用信息
    APP_TITLE: VITE_APP_TITLE,

    // API配置
    API_BASE_URL: VITE_APP_API_BASE_URL,
    UPLOAD_URL: VITE_APP_UPLOAD_URL,

    // 运行配置
    DEV: VITE_APP_ENV === "development",
    PROD: VITE_APP_ENV === "production",
    TEST: VITE_APP_ENV === "test",
  };
}

// 使用全局类型
export function wrapperEnv(envConf: Recordable): ImportMetaEnv {
  const ret: any = {};

  for (const envName of Object.keys(envConf)) {
    let realName = envConf[envName].replace(/\\n/g, "\n");

    // 转换布尔值
    realName =
      realName === "true" ? true : realName === "false" ? false : realName;

    // 端口可能是字符串，需要转换为数字
    if (envName === "VITE_PORT") {
      realName = Number(realName);
    }

    ret[envName] = realName;
  }

  return ret;
}
