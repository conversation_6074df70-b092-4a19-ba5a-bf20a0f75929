import type { App } from "vue";
import { createRouter, createWebHistory } from "vue-router";
// 使用路径别名可能导致类型错误，改为使用相对路径或添加类型声明
// import Home from "@/pages/Home.vue";
// import Login from "@/pages/auth/Login.vue";
// import Register from "@/pages/auth/Register.vue";
import { setupAuthGuard } from "@/router/guard/auth";
import type { RouteRecordRaw } from "vue-router";

// 安装路由
export function setupRouter(app: App) {
  // 注册路由守卫
  //   createAuthGuard(router);

  app.use(router);
  return router;
}

// export default router;

// 定义路由，使用动态导入组件解决路径问题
const routes: RouteRecordRaw[] = [
  {
    path: "/draft",
    name: "draft",
    // @ts-ignore
    component: () => import("@/pages/draft/index.vue"),
    meta: { title: "草稿页面" }
  },
  {
    path: "/note",
    name: "note",
    component: () => import("@/pages/note/index.vue"),
    meta: { title: "笔记页面" }
  },
  {
    path: "/share/:token",
    name: "shared-note",
    component: () => import("@/pages/share/token.vue"),
    meta: { title: "分享的笔记", requiresAuth: false }
  },
  {
    path: "/share/folder/:token",
    name: "shared-folder",
    component: () => import("@/pages/share/folder.vue"),
    meta: { title: "分享的文件夹", requiresAuth: false }
  },
  {
    path: "/cognition",
    name: "cognition",
    component: () => import("@/pages/cognition/index.vue"),
    meta: { title: "认知平台" }
  },
  {
    path: "/cognition/library",
    name: "cognition-library",
    component: () => import("@/pages/cognition/library.vue"),
    meta: { title: "我的认知库", requiresAuth: true }
  },
  {
    path: "/cognition/library/:id",
    name: "collection-detail",
    // @ts-ignore
    component: () => import("@/pages/cognition/collection-detail.vue"),
    meta: { title: "收藏夹详情", requiresAuth: true },
    props: true
  },
  {
    path: "/login",
    name: "login",
    // @ts-ignore
    component: () => import("@/pages/auth/Login.vue"),
    meta: { title: "登录" }
  },
  {
    path: "/register",
    name: "register",
    // @ts-ignore
    component: () => import("@/pages/auth/Register.vue"),
    meta: { title: "注册" }
  },
  {
    path: "/manage",
    name: "manage",
    // @ts-ignore
    component: () => import("@/pages/manage/index.vue"),
    meta: {
      title: "管理控制台",
      requiresAuth: true,
      requiresAdmin: true
    }
  },
  {
    path: "/admin/dashboard",
    name: "admin-dashboard",
    // @ts-ignore
    component: () => import("@/pages/dashboard.vue"),
    meta: { title: "数据统计看板" }
  },
  {
    path: "/ppt",
    name: "ppt-review",
    // @ts-ignore
    component: () => import("@/pages/ppt/index.vue"),
    meta: { title: "PPT评审" }
  },
  {
    path: "/test-hot-ranking",
    name: "test-hot-ranking",
    // @ts-ignore
    component: () => import("@/pages/test-hot-ranking.vue"),
    meta: { title: "热榜功能测试" }
  },
  {
    path: "/",
    redirect: "/draft"
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// 应用认证守卫
setupAuthGuard(router);

export default router;
