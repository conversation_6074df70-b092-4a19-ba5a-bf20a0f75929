import { Router } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'

// 不需要登录就可以访问的路由名称
const publicRoutes: string[] = [
  'login',
  'register',
  'admin-dashboard',
]

export function setupAuthGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    const authStore = useAuthStore()

    // 如果用户已登录但尚未加载用户信息，则获取用户信息
    if (authStore.isAuthenticated && !authStore.user.id) {
      try {
        // 验证token有效性并获取用户信息
        const isValid = await authStore.verifyTokenValidity()
        if (isValid) {
          await authStore.fetchUserInfo()
        } else {
          // 如果token无效，清除登录状态
          if (!isPublicRoute(to.name as string)) {
            return next({
              name: 'login',
              query: { redirect: to.fullPath }  // 保存原始目标路径
            })
          }
        }
      } catch (error) {
        console.error('Failed to validate auth state:', error)
        // 如果验证失败，清除登录状态
        authStore.clearToken()
        authStore.clearUser()

        // 如果当前页面需要认证，则重定向到登录页
        if (!isPublicRoute(to.name as string)) {
          return next({
            name: 'login',
            query: { redirect: to.fullPath }  // 保存原始目标路径
          })
        }
      }
    }

    // 检查路由是否需要认证
    if (!isPublicRoute(to.name as string)) {
      // 特殊情况：draft 页面带有 share=true 参数时允许不登录访问
      if (to.name === 'draft' && to.query.share === 'true') {
        return next()
      }

      // 需要认证的路由，检查登录状态
      if (!authStore.isAuthenticated) {
        return next({
          name: 'login',
          query: { redirect: to.fullPath }  // 保存原始目标路径
        })
      }

      // 如果已认证但用户状态不是激活状态，拒绝访问
      if (authStore.user.id && !authStore.user.is_active) {
        // 可以重定向到一个特定页面说明账户未激活
        return next({ name: 'draft' })
      }
    }

    // 如果用户已登录，尝试访问登录或注册页面，则重定向到主页
    if (authStore.isAuthenticated && (to.name === 'login' || to.name === 'register')) {
      return next({ name: 'draft' })
    }

    // 默认情况下允许导航
    next()
  })
}

// 判断路由是否公开（不需要登录）
function isPublicRoute(routeName: string): boolean {
  return publicRoutes.includes(routeName)
}
