import { cognitionAPI } from '@/api/cognition'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export const useHotRankingStore = defineStore('hotRanking', () => {
  // 状态
  const hotTopics = ref([])
  const hotCognitions = ref([])
  const loading = ref(false)
  const error = ref('')
  const lastUpdateTime = ref(null)
  const currentTimeFilter = ref('day')
  
  // 缓存相关
  const cacheTimeout = 30 * 60 * 1000 // 30分钟缓存
  const cache = ref(new Map()) // 缓存不同时间筛选的数据
  
  // 从localStorage恢复缓存
  const initCache = () => {
    try {
      const storedCache = localStorage.getItem('hotRankingCache')
      if (storedCache) {
        const parsedCache = JSON.parse(storedCache)
        // 验证缓存是否过期
        for (const [key, value] of Object.entries(parsedCache)) {
          if (Date.now() - value.timestamp < cacheTimeout) {
            cache.value.set(key, value)
          }
        }
        console.log('热榜缓存已从localStorage恢复:', cache.value.size, '条')
      }
    } catch (err) {
      console.warn('恢复热榜缓存失败:', err)
    }
  }
  
  // 保存缓存到localStorage
  const saveCache = () => {
    try {
      const cacheObj = Object.fromEntries(cache.value.entries())
      localStorage.setItem('hotRankingCache', JSON.stringify(cacheObj))
    } catch (err) {
      console.warn('保存热榜缓存失败:', err)
    }
  }
  
  // 计算属性
  const isDataStale = computed(() => {
    if (!lastUpdateTime.value) return true
    return Date.now() - lastUpdateTime.value > cacheTimeout
  })
  
  // 获取对比时间段的映射
  const getComparisonTimeFilter = (currentFilter) => {
    const comparisonMap = {
      'day': 'yesterday',      // 今日 vs 昨日
      'week': 'last_week',     // 本周 vs 上周
      'month': 'last_month'    // 本月 vs 上月
    }
    return comparisonMap[currentFilter] || null
  }

  // 获取热榜数据（增强版：支持获取对比数据）
  const fetchHotRanking = async (timeFilter = 'day', forceRefresh = false, includeComparison = true) => {
    const cacheKey = `hot-ranking-${timeFilter}`
    
    // 初始化时尝试恢复缓存
    if (cache.value.size === 0) {
      initCache()
    }
    
    // 检查缓存
    if (!forceRefresh && cache.value.has(cacheKey)) {
      const cachedData = cache.value.get(cacheKey)
      const isCacheValid = Date.now() - cachedData.timestamp < cacheTimeout
      
      if (isCacheValid) {
        hotTopics.value = cachedData.hotTopics
        hotCognitions.value = cachedData.hotCognitions
        lastUpdateTime.value = cachedData.timestamp
        currentTimeFilter.value = timeFilter
        
        // 异步预加载其他时间筛选的数据，提升用户体验
        setTimeout(() => {
          preloadOtherFilters(timeFilter)
        }, 100)
        
        return
      }
    }
    
    loading.value = true
    error.value = ''
    
    try {
      const params = {
        time_filter: timeFilter,
        search_scope: 'all',
        source_filter: 'all'
      }

      // 获取当前时间段数据
      const response = await cognitionAPI.getTrendStats(params)

      // 获取对比数据（如果需要）
      let comparisonResponse = null
      if (includeComparison) {
        const comparisonFilter = getComparisonTimeFilter(timeFilter)
        if (comparisonFilter) {
          try {
            // 尝试获取对比数据，但不影响主要功能
            const comparisonParams = { ...params, time_filter: comparisonFilter }
            comparisonResponse = await cognitionAPI.getTrendStats(comparisonParams)
          } catch (comparisonError) {
            console.warn(`获取对比数据失败 (${comparisonFilter}):`, comparisonError)
            // 继续处理主要数据，不中断流程
          }
        }
      }
      
      if (response && response.data) {
        // 处理对比数据
        let comparisonTopics = []
        if (comparisonResponse && comparisonResponse.data) {
          comparisonTopics = comparisonResponse.data
            .map(item => ({
              topic: item.topic || '未知话题',
              count: Number(item.count) || 0,
              percentage: Number(item.percentage) || 0,
              trend: item.trend || 'stable'
            }))
            .filter(item => item.count > 0)
        }

        // 处理热门topics (取前10个) - 增强版：包含排名变化信息
        const processedTopics = response.data
          .map((item, index) => {
            const currentRank = index + 1
            const topic = item.topic || '未知话题'
            const count = Number(item.count) || 0
            const percentage = Number(item.percentage) || 0

            // 查找在对比数据中的排名
            let previousRank = null
            let previousCount = 0
            let rankChange = 'stable'
            let isNew = false

            if (comparisonTopics.length > 0) {
              const previousItem = comparisonTopics.find(comp => comp.topic === topic)
              if (previousItem) {
                previousRank = comparisonTopics.indexOf(previousItem) + 1
                previousCount = previousItem.count

                if (currentRank < previousRank) {
                  rankChange = 'up'
                } else if (currentRank > previousRank) {
                  rankChange = 'down'
                } else {
                  rankChange = 'stable'
                }
              } else {
                isNew = true
                rankChange = 'new'
              }
            }

            // 计算热度变化百分比
            let heatChangePercent = 0
            if (previousCount > 0) {
              heatChangePercent = ((count - previousCount) / previousCount * 100)
            } else if (count > 0) {
              heatChangePercent = 100 // 新出现的话题视为100%增长
            }

            return {
              topic,
              count,
              percentage,
              trend: item.trend || 'stable',
              // 新增字段
              currentRank,
              previousRank,
              rankChange,
              isNew,
              heatChangePercent: Math.round(heatChangePercent * 100) / 100,
              previousCount
            }
          })
          .filter(item => item.count > 0)
          .slice(0, 10)
        
        // 处理热门认知 (从原始数据中取最新的前10条)
        let processedCognitions = []
        if (response.raw_cognitions && Array.isArray(response.raw_cognitions)) {
          processedCognitions = response.raw_cognitions
            .filter(cognition => cognition && cognition.id)
            .sort((a, b) => {
              // 按时间排序，最新的在前
              const timeA = a.raw_at ? new Date(a.raw_at) : new Date(0)
              const timeB = b.raw_at ? new Date(b.raw_at) : new Date(0)
              return timeB - timeA
            })
            .slice(0, 10)
        }
        
        // 更新状态
        hotTopics.value = processedTopics
        hotCognitions.value = processedCognitions
        lastUpdateTime.value = Date.now()
        currentTimeFilter.value = timeFilter
        
        // 缓存数据（包含对比数据）
        cache.value.set(cacheKey, {
          hotTopics: processedTopics,
          hotCognitions: processedCognitions,
          comparisonTopics: comparisonTopics, // 缓存对比数据
          timestamp: Date.now()
        })
        
        // 保存到localStorage
        saveCache()
        
        // 限制缓存大小，最多保留6个不同时间筛选的缓存
        if (cache.value.size > 6) {
          const oldestKey = Array.from(cache.value.keys())[0]
          cache.value.delete(oldestKey)
          saveCache()
        }
        
        // 异步预加载其他时间筛选的数据
        setTimeout(() => {
          preloadOtherFilters(timeFilter)
        }, 100)
        
        console.log(`热榜数据已更新 - ${timeFilter}:`, {
          topics: processedTopics.length,
          cognitions: processedCognitions.length
        })
      } else {
        throw new Error('API响应数据格式错误')
      }
    } catch (err) {
      console.error('获取热榜数据失败:', err)
      error.value = err.message || '获取热榜数据失败，请稍后重试'
      
      // 如果有缓存数据，继续使用（即使过期）
      if (cache.value.has(cacheKey)) {
        const cachedData = cache.value.get(cacheKey)
        hotTopics.value = cachedData.hotTopics
        hotCognitions.value = cachedData.hotCognitions
        lastUpdateTime.value = cachedData.timestamp
        currentTimeFilter.value = timeFilter
      } else {
        hotTopics.value = []
        hotCognitions.value = []
      }
    } finally {
      loading.value = false
    }
  }
  
  // 强制刷新数据
  const refreshData = async () => {
    await fetchHotRanking(currentTimeFilter.value, true)
  }

  // 获取带对比数据的热榜（新增方法）
  const fetchHotRankingWithComparison = async (timeFilter = 'day', forceRefresh = false) => {
    return await fetchHotRanking(timeFilter, forceRefresh, true)
  }
  
  // 清空缓存
  const clearCache = () => {
    cache.value.clear()
    try {
      localStorage.removeItem('hotRankingCache')
    } catch (err) {
      console.warn('清理localStorage缓存失败:', err)
    }
    console.log('热榜缓存已清空(包括localStorage)')
  }
  
  // 获取缓存状态信息
  const getCacheInfo = () => {
    return {
      cacheSize: cache.value.size,
      lastUpdate: lastUpdateTime.value,
      isStale: isDataStale.value,
      currentFilter: currentTimeFilter.value
    }
  }
  
  // 智能预加载其他时间筛选的数据
  const preloadOtherFilters = async (currentFilter) => {
    const filters = ['day', 'week', 'month'].filter(f => f !== currentFilter)
    
    // 并行预加载，提升效率
    const preloadPromises = filters.map(async (filter) => {
      const cacheKey = `hot-ranking-${filter}`
      
      // 检查是否已有有效缓存
      if (cache.value.has(cacheKey)) {
        const cachedData = cache.value.get(cacheKey)
        if (Date.now() - cachedData.timestamp < cacheTimeout) {
          return // 缓存仍然有效，跳过
        }
      }
      
      try {
        // 静默预加载，不显示loading状态
        const params = {
          time_filter: filter,
          search_scope: 'all',
          source_filter: 'all'
        }
        
        const response = await cognitionAPI.getTrendStats(params)

        // 获取预加载的对比数据
        let comparisonResponse = null
        const comparisonFilter = getComparisonTimeFilter(filter)
        if (comparisonFilter) {
          try {
            const comparisonParams = { ...params, time_filter: comparisonFilter }
            comparisonResponse = await cognitionAPI.getTrendStats(comparisonParams)
          } catch (comparisonError) {
            console.warn(`预加载对比数据失败 (${comparisonFilter}):`, comparisonError)
          }
        }

        if (response && response.data) {
          // 处理对比数据
          let comparisonTopics = []
          if (comparisonResponse && comparisonResponse.data) {
            comparisonTopics = comparisonResponse.data
              .map(item => ({
                topic: item.topic || '未知话题',
                count: Number(item.count) || 0,
                percentage: Number(item.percentage) || 0,
                trend: item.trend || 'stable'
              }))
              .filter(item => item.count > 0)
          }

          const processedTopics = response.data
            .map((item, index) => {
              const currentRank = index + 1
              const topic = item.topic || '未知话题'
              const count = Number(item.count) || 0
              const percentage = Number(item.percentage) || 0

              // 查找在对比数据中的排名
              let previousRank = null
              let previousCount = 0
              let rankChange = 'stable'
              let isNew = false

              if (comparisonTopics.length > 0) {
                const previousItem = comparisonTopics.find(comp => comp.topic === topic)
                if (previousItem) {
                  previousRank = comparisonTopics.indexOf(previousItem) + 1
                  previousCount = previousItem.count

                  if (currentRank < previousRank) {
                    rankChange = 'up'
                  } else if (currentRank > previousRank) {
                    rankChange = 'down'
                  } else {
                    rankChange = 'stable'
                  }
                } else {
                  isNew = true
                  rankChange = 'new'
                }
              }

              // 计算热度变化百分比
              let heatChangePercent = 0
              if (previousCount > 0) {
                heatChangePercent = ((count - previousCount) / previousCount * 100)
              } else if (count > 0) {
                heatChangePercent = 100
              }

              return {
                topic,
                count,
                percentage,
                trend: item.trend || 'stable',
                currentRank,
                previousRank,
                rankChange,
                isNew,
                heatChangePercent: Math.round(heatChangePercent * 100) / 100,
                previousCount
              }
            })
            .filter(item => item.count > 0)
            .slice(0, 10)
          
          let processedCognitions = []
          if (response.raw_cognitions && Array.isArray(response.raw_cognitions)) {
            processedCognitions = response.raw_cognitions
              .filter(cognition => cognition && cognition.id)
              .sort((a, b) => {
                const timeA = a.raw_at ? new Date(a.raw_at) : new Date(0)
                const timeB = b.raw_at ? new Date(b.raw_at) : new Date(0)
                return timeB - timeA
              })
              .slice(0, 10)
          }
          
          // 缓存预加载的数据（包含对比数据）
          cache.value.set(cacheKey, {
            hotTopics: processedTopics,
            hotCognitions: processedCognitions,
            comparisonTopics: comparisonTopics,
            timestamp: Date.now()
          })
          
          console.log(`预加载${filter}数据完成`)
        }
      } catch (err) {
        console.warn(`预加载${filter}数据失败:`, err.message)
      }
    })
    
    try {
      await Promise.all(preloadPromises)
      saveCache() // 保存所有预加载的数据
      console.log('智能预加载完成，总缓存大小:', cache.value.size)
    } catch (err) {
      console.warn('预加载过程中发生错误:', err)
    }
  }
  
  // 兼容旧的preloadData方法
  const preloadData = async () => {
    await preloadOtherFilters(currentTimeFilter.value)
  }
  
  // 重置状态
  const reset = () => {
    hotTopics.value = []
    hotCognitions.value = []
    loading.value = false
    error.value = ''
    lastUpdateTime.value = null
    currentTimeFilter.value = 'day'
    clearCache()
  }
  
      return {
    // 状态
    hotTopics,
    hotCognitions,
    loading,
    error,
    lastUpdateTime,
    currentTimeFilter,

    // 计算属性
    isDataStale,

    // 方法
    fetchHotRanking,
    fetchHotRankingWithComparison,
    refreshData,
    clearCache,
    getCacheInfo,
    preloadData,
    preloadOtherFilters,
    initCache,
    saveCache,
    reset,

    // 工具方法
    getComparisonTimeFilter
  }
}) 