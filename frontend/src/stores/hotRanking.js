import { cognitionAPI } from '@/api/cognition'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export const useHotRankingStore = defineStore('hotRanking', () => {
  // 状态
  const hotTopics = ref([])
  const hotCognitions = ref([])
  const loading = ref(false)
  const error = ref('')
  const lastUpdateTime = ref(null)
  const currentTimeFilter = ref('day')
  
  // 缓存相关
  const cacheTimeout = 30 * 60 * 1000 // 30分钟缓存
  const cache = ref(new Map()) // 缓存不同时间筛选的数据
  
  // 从localStorage恢复缓存
  const initCache = () => {
    try {
      const storedCache = localStorage.getItem('hotRankingCache')
      if (storedCache) {
        const parsedCache = JSON.parse(storedCache)
        // 验证缓存是否过期
        for (const [key, value] of Object.entries(parsedCache)) {
          if (Date.now() - value.timestamp < cacheTimeout) {
            cache.value.set(key, value)
          }
        }
        console.log('热榜缓存已从localStorage恢复:', cache.value.size, '条')
      }
    } catch (err) {
      console.warn('恢复热榜缓存失败:', err)
    }
  }
  
  // 保存缓存到localStorage
  const saveCache = () => {
    try {
      const cacheObj = Object.fromEntries(cache.value.entries())
      localStorage.setItem('hotRankingCache', JSON.stringify(cacheObj))
    } catch (err) {
      console.warn('保存热榜缓存失败:', err)
    }
  }
  
  // 计算属性
  const isDataStale = computed(() => {
    if (!lastUpdateTime.value) return true
    return Date.now() - lastUpdateTime.value > cacheTimeout
  })
  
  // 计算排名变化和热度指标的辅助函数
  const calculateRankingChanges = (currentData, previousData) => {
    if (!previousData || !Array.isArray(previousData)) return currentData

    return currentData.map((currentItem, currentIndex) => {
      const currentRank = currentIndex + 1
      const previousIndex = previousData.findIndex(prev =>
        prev.topic === currentItem.topic || prev.id === currentItem.id
      )

      let rankChange = 0
      let isNew = false

      if (previousIndex === -1) {
        // 新上榜
        isNew = true
      } else {
        const previousRank = previousIndex + 1
        rankChange = previousRank - currentRank // 正数表示上升，负数表示下降
      }

      // 计算热度变化百分比
      let heatChangePercent = 0
      if (!isNew && previousIndex !== -1) {
        const previousCount = previousData[previousIndex].count || 0
        const currentCount = currentItem.count || 0
        if (previousCount > 0) {
          heatChangePercent = ((currentCount - previousCount) / previousCount * 100)
        }
      }

      return {
        ...currentItem,
        rankChange,
        isNew,
        heatChangePercent: Math.round(heatChangePercent * 10) / 10, // 保留1位小数
        heatScore: calculateHeatScore(currentItem, currentIndex)
      }
    })
  }

  // 计算热度分数
  const calculateHeatScore = (item, rank) => {
    const baseScore = Math.max(0, 100 - rank * 5) // 基础分数，排名越高分数越高
    const countBonus = Math.min(50, (item.count || 0) / 10) // 数量加成，最多50分
    const percentageBonus = Math.min(30, (item.percentage || 0) * 2) // 占比加成，最多30分

    return Math.round(baseScore + countBonus + percentageBonus)
  }

  // 检测新兴话题
  const detectEmergingTopics = (currentTopics, previousTopics) => {
    if (!previousTopics || !Array.isArray(previousTopics)) return []

    const emergingTopics = []
    const previousTopicNames = new Set(previousTopics.map(t => t.topic))

    currentTopics.forEach((topic, index) => {
      if (!previousTopicNames.has(topic.topic) && index < 10) {
        emergingTopics.push({
          ...topic,
          rank: index + 1,
          isEmerging: true
        })
      }
    })

    return emergingTopics
  }

  // 获取热榜数据
  const fetchHotRanking = async (timeFilter = 'day', forceRefresh = false) => {
    const cacheKey = `hot-ranking-${timeFilter}`

    // 初始化时尝试恢复缓存
    if (cache.value.size === 0) {
      initCache()
    }

    // 检查缓存
    if (!forceRefresh && cache.value.has(cacheKey)) {
      const cachedData = cache.value.get(cacheKey)
      const isCacheValid = Date.now() - cachedData.timestamp < cacheTimeout

      if (isCacheValid) {
        hotTopics.value = cachedData.hotTopics
        hotCognitions.value = cachedData.hotCognitions
        lastUpdateTime.value = cachedData.timestamp
        currentTimeFilter.value = timeFilter

        // 异步预加载其他时间筛选的数据，提升用户体验
        setTimeout(() => {
          preloadOtherFilters(timeFilter)
        }, 100)

        return
      }
    }

    loading.value = true
    error.value = ''

    try {
      const params = {
        time_filter: timeFilter,
        search_scope: 'all',
        source_filter: 'all'
      }

      const response = await cognitionAPI.getTrendStats(params)

      if (response && response.data) {
        // 获取上一个时间段的数据用于对比
        const previousTimeFilter = getPreviousTimeFilter(timeFilter)
        let previousData = null

        if (cache.value.has(`hot-ranking-${previousTimeFilter}`)) {
          previousData = cache.value.get(`hot-ranking-${previousTimeFilter}`)
        } else {
          // 尝试获取上一个时间段的数据
          try {
            const prevParams = {
              time_filter: previousTimeFilter,
              search_scope: 'all',
              source_filter: 'all'
            }
            const prevResponse = await cognitionAPI.getTrendStats(prevParams)
            if (prevResponse && prevResponse.data) {
              previousData = {
                hotTopics: prevResponse.data.slice(0, 10),
                hotCognitions: prevResponse.raw_cognitions ? prevResponse.raw_cognitions.slice(0, 10) : []
              }
            }
          } catch (err) {
            console.warn('获取上一时间段数据失败:', err)
          }
        }

        // 处理热门topics (取前10个)
        let processedTopics = response.data
          .map(item => ({
            topic: item.topic || '未知话题',
            count: Number(item.count) || 0,
            percentage: Number(item.percentage) || 0,
            trend: item.trend || 'stable'
          }))
          .filter(item => item.count > 0)
          .slice(0, 10)

        // 计算排名变化和热度指标
        if (previousData && previousData.hotTopics) {
          processedTopics = calculateRankingChanges(processedTopics, previousData.hotTopics)
        } else {
          // 如果没有历史数据，添加默认值
          processedTopics = processedTopics.map((item, index) => ({
            ...item,
            rankChange: 0,
            isNew: false,
            heatChangePercent: 0,
            heatScore: calculateHeatScore(item, index)
          }))
        }

        // 处理热门认知 (从原始数据中取最新的前10条)
        let processedCognitions = []
        if (response.raw_cognitions && Array.isArray(response.raw_cognitions)) {
          processedCognitions = response.raw_cognitions
            .filter(cognition => cognition && cognition.id)
            .sort((a, b) => {
              // 按时间排序，最新的在前
              const timeA = a.raw_at ? new Date(a.raw_at) : new Date(0)
              const timeB = b.raw_at ? new Date(b.raw_at) : new Date(0)
              return timeB - timeA
            })
            .slice(0, 10)

          // 为认知添加排名变化信息
          if (previousData && previousData.hotCognitions) {
            processedCognitions = calculateRankingChanges(processedCognitions, previousData.hotCognitions)
          } else {
            processedCognitions = processedCognitions.map((item, index) => ({
              ...item,
              rankChange: 0,
              isNew: false,
              heatChangePercent: 0,
              heatScore: calculateHeatScore({ count: 1 }, index) // 认知的热度分数简化计算
            }))
          }
        }

        // 更新状态
        hotTopics.value = processedTopics
        hotCognitions.value = processedCognitions
        lastUpdateTime.value = Date.now()
        currentTimeFilter.value = timeFilter

        // 缓存数据
        cache.value.set(cacheKey, {
          hotTopics: processedTopics,
          hotCognitions: processedCognitions,
          timestamp: Date.now()
        })

        // 保存到localStorage
        saveCache()

        // 限制缓存大小，最多保留6个不同时间筛选的缓存
        if (cache.value.size > 6) {
          const oldestKey = Array.from(cache.value.keys())[0]
          cache.value.delete(oldestKey)
          saveCache()
        }

        // 异步预加载其他时间筛选的数据
        setTimeout(() => {
          preloadOtherFilters(timeFilter)
        }, 100)

        console.log(`热榜数据已更新 - ${timeFilter}:`, {
          topics: processedTopics.length,
          cognitions: processedCognitions.length,
          emergingTopics: processedTopics.filter(t => t.isNew).length
        })
      } else {
        throw new Error('API响应数据格式错误')
      }
    } catch (err) {
      console.error('获取热榜数据失败:', err)
      error.value = err.message || '获取热榜数据失败，请稍后重试'

      // 如果有缓存数据，继续使用（即使过期）
      if (cache.value.has(cacheKey)) {
        const cachedData = cache.value.get(cacheKey)
        hotTopics.value = cachedData.hotTopics
        hotCognitions.value = cachedData.hotCognitions
        lastUpdateTime.value = cachedData.timestamp
        currentTimeFilter.value = timeFilter
      } else {
        hotTopics.value = []
        hotCognitions.value = []
      }
    } finally {
      loading.value = false
    }
  }

  // 获取上一个时间段的过滤器
  const getPreviousTimeFilter = (currentFilter) => {
    const filterMap = {
      'day': 'week',    // 今日对比本周
      'week': 'month',  // 本周对比本月
      'month': 'week'   // 本月对比本周（作为参考）
    }
    return filterMap[currentFilter] || 'week'
  }
  
  // 强制刷新数据
  const refreshData = async () => {
    await fetchHotRanking(currentTimeFilter.value, true)
  }
  
  // 清空缓存
  const clearCache = () => {
    cache.value.clear()
    try {
      localStorage.removeItem('hotRankingCache')
    } catch (err) {
      console.warn('清理localStorage缓存失败:', err)
    }
    console.log('热榜缓存已清空(包括localStorage)')
  }
  
  // 获取缓存状态信息
  const getCacheInfo = () => {
    return {
      cacheSize: cache.value.size,
      lastUpdate: lastUpdateTime.value,
      isStale: isDataStale.value,
      currentFilter: currentTimeFilter.value
    }
  }
  
  // 智能预加载其他时间筛选的数据
  const preloadOtherFilters = async (currentFilter) => {
    const filters = ['day', 'week', 'month'].filter(f => f !== currentFilter)
    
    // 并行预加载，提升效率
    const preloadPromises = filters.map(async (filter) => {
      const cacheKey = `hot-ranking-${filter}`
      
      // 检查是否已有有效缓存
      if (cache.value.has(cacheKey)) {
        const cachedData = cache.value.get(cacheKey)
        if (Date.now() - cachedData.timestamp < cacheTimeout) {
          return // 缓存仍然有效，跳过
        }
      }
      
      try {
        // 静默预加载，不显示loading状态
        const params = {
          time_filter: filter,
          search_scope: 'all',
          source_filter: 'all'
        }
        
        const response = await cognitionAPI.getTrendStats(params)
        
        if (response && response.data) {
          const processedTopics = response.data
            .map(item => ({
              topic: item.topic || '未知话题',
              count: Number(item.count) || 0,
              percentage: Number(item.percentage) || 0,
              trend: item.trend || 'stable'
            }))
            .filter(item => item.count > 0)
            .slice(0, 10)
          
          let processedCognitions = []
          if (response.raw_cognitions && Array.isArray(response.raw_cognitions)) {
            processedCognitions = response.raw_cognitions
              .filter(cognition => cognition && cognition.id)
              .sort((a, b) => {
                const timeA = a.raw_at ? new Date(a.raw_at) : new Date(0)
                const timeB = b.raw_at ? new Date(b.raw_at) : new Date(0)
                return timeB - timeA
              })
              .slice(0, 10)
          }
          
          // 缓存预加载的数据
          cache.value.set(cacheKey, {
            hotTopics: processedTopics,
            hotCognitions: processedCognitions,
            timestamp: Date.now()
          })
          
          console.log(`预加载${filter}数据完成`)
        }
      } catch (err) {
        console.warn(`预加载${filter}数据失败:`, err.message)
      }
    })
    
    try {
      await Promise.all(preloadPromises)
      saveCache() // 保存所有预加载的数据
      console.log('智能预加载完成，总缓存大小:', cache.value.size)
    } catch (err) {
      console.warn('预加载过程中发生错误:', err)
    }
  }
  
  // 兼容旧的preloadData方法
  const preloadData = async () => {
    await preloadOtherFilters(currentTimeFilter.value)
  }
  
  // 重置状态
  const reset = () => {
    hotTopics.value = []
    hotCognitions.value = []
    loading.value = false
    error.value = ''
    lastUpdateTime.value = null
    currentTimeFilter.value = 'day'
    clearCache()
  }
  
      return {
    // 状态
    hotTopics,
    hotCognitions,
    loading,
    error,
    lastUpdateTime,
    currentTimeFilter,

    // 计算属性
    isDataStale,

    // 方法
    fetchHotRanking,
    refreshData,
    clearCache,
    getCacheInfo,
    preloadData,
    preloadOtherFilters,
    initCache,
    saveCache,
    reset,

    // 新增的辅助方法
    calculateRankingChanges,
    calculateHeatScore,
    detectEmergingTopics,
    getPreviousTimeFilter
  }
}) 