import { cognitionAPI } from '@/api/cognition'
import api from '@/api/request'
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useCognitionStore = defineStore('cognition', () => {
  // 状态
  const cognitions = ref([])
  const currentCognition = ref(null)
  const loading = ref(false)
  const error = ref(null)

  // 收藏状态缓存和事件监听
  const favoriteStatusCache = ref(new Map())
  const favoriteStatusListeners = ref(new Set())

  // 收藏状态管理辅助函数
  function updateFavoriteStatusCache(cognitionId, isFavorited) {
    favoriteStatusCache.value.set(cognitionId, isFavorited)
    // 通知所有监听器
    favoriteStatusListeners.value.forEach(listener => {
      listener(cognitionId, isFavorited)
    })
  }

  function addFavoriteStatusListener(listener) {
    favoriteStatusListeners.value.add(listener)
    return () => favoriteStatusListeners.value.delete(listener)
  }

  function getCachedFavoriteStatus(cognitionId) {
    return favoriteStatusCache.value.get(cognitionId)
  }

  // 获取认知列表
  async function getCognitions(params = {}) {
    try {
      loading.value = true
      error.value = null
      
      const response = await cognitionAPI.getCognitions(params)
      return response || []
    } catch (err) {
      error.value = err.message || '获取认知列表失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 获取单个认知
  async function getCognition(cognitionId) {
    try {
      loading.value = true
      error.value = null
      
      const response = await cognitionAPI.getCognition(cognitionId)
      currentCognition.value = response
      return response
    } catch (err) {
      error.value = err.message || '获取认知详情失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 创建认知
  async function createCognition(cognitionData) {
    try {
      loading.value = true
      error.value = null
      
      const response = await cognitionAPI.createCognition(cognitionData)
      return response
    } catch (err) {
      error.value = err.message || '创建认知失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 更新认知（仅管理员）
  async function updateCognition(cognitionId, cognitionData) {
    try {
      loading.value = true
      error.value = null
      
      const response = await cognitionAPI.updateCognition(cognitionId, cognitionData)
      return response
    } catch (err) {
      error.value = err.message || '更新认知失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 删除认知（仅管理员）
  async function deleteCognition(cognitionId) {
    try {
      error.value = null
      
      const response = await cognitionAPI.deleteCognition(cognitionId)
      return response
    } catch (err) {
      error.value = err.message || '删除认知失败'
      throw err
    }
  }

  // 投票
  async function voteCognition(cognitionId, voteType) {
    try {
      error.value = null
      
      const response = await cognitionAPI.voteCognition(cognitionId, voteType)
      return response
    } catch (err) {
      error.value = err.message || '投票失败'
      throw err
    }
  }

  // 添加评论
  async function addComment(cognitionId, content, parentId = null, replyToUsername = null) {
    try {
      error.value = null
      
      const response = await cognitionAPI.addComment(cognitionId, content, parentId, replyToUsername)
      return response
    } catch (err) {
      error.value = err.message || '添加评论失败'
      throw err
    }
  }

  // 获取用户投票状态
  async function getUserVote(cognitionId) {
    try {
      error.value = null
      
      const response = await cognitionAPI.getUserVote(cognitionId)
      return response.vote_type
    } catch (err) {
      error.value = err.message || '获取投票状态失败'
      throw err
    }
  }

  // 收藏相关方法
  async function getUserCollections() {
    try {
      error.value = null
      
      const response = await cognitionAPI.getUserCollections()
      return response || []
    } catch (err) {
      error.value = err.message || '获取收藏夹列表失败'
      throw err
    }
  }

  async function createCollection(collectionData) {
    try {
      error.value = null
      
      const response = await cognitionAPI.createCollection(collectionData)
      return response
    } catch (err) {
      error.value = err.message || '创建收藏夹失败'
      throw err
    }
  }

  async function deleteCollection(collectionId) {
    try {
      error.value = null
      
      const response = await cognitionAPI.deleteCollection(collectionId)
      return response
    } catch (err) {
      error.value = err.message || '删除收藏夹失败'
      throw err
    }
  }

  async function updateCollection(collectionId, collectionData) {
    try {
      error.value = null
      
      const response = await cognitionAPI.updateCollection(collectionId, collectionData)
      return response
    } catch (err) {
      error.value = err.message || '更新收藏夹失败'
      throw err
    }
  }

  async function addToFavorites(cognitionId, collectionId) {
    try {
      error.value = null

      const response = await cognitionAPI.addToFavorites(cognitionId, collectionId)

      // 更新缓存并通知监听器
      updateFavoriteStatusCache(cognitionId, true)

      return response
    } catch (err) {
      error.value = err.message || '收藏失败'
      throw err
    }
  }

  async function removeFromFavorites(cognitionId, collectionId = null) {
    try {
      error.value = null

      const response = await cognitionAPI.removeFromFavorites(cognitionId, collectionId)

      // 更新缓存并通知监听器
      updateFavoriteStatusCache(cognitionId, false)

      return response
    } catch (err) {
      error.value = err.message || '取消收藏失败'
      throw err
    }
  }

  async function getFavoriteStatus(cognitionId) {
    try {
      error.value = null

      // 先检查缓存
      const cachedStatus = getCachedFavoriteStatus(cognitionId)
      if (cachedStatus !== undefined) {
        return { is_favorited: cachedStatus }
      }

      const response = await cognitionAPI.getFavoriteStatus(cognitionId)

      // 更新缓存
      if (response && response.is_favorited !== undefined) {
        updateFavoriteStatusCache(cognitionId, response.is_favorited)
      }

      return response
    } catch (err) {
      error.value = err.message || '获取收藏状态失败'
      throw err
    }
  }

  async function getCollectionCognitions(collectionId, params = {}) {
    try {
      error.value = null
      
      const response = await cognitionAPI.getCollectionCognitions(collectionId, params)
      return response || { items: [], total: 0, page: 1, pages: 0, page_size: 20 }
    } catch (err) {
      error.value = err.message || '获取收藏夹认知失败'
      throw err
    }
  }

  // 获取可用的topics
  async function getAvailableTopics() {
    try {
      error.value = null
      
      const response = await cognitionAPI.getAvailableTopics()
      return response
    } catch (err) {
      error.value = err.message || '获取可用话题失败'
      throw err
    }
  }

  // 获取用户收藏的认知列表
  async function getFavoritedCognitions(params) {
    try {
      const response = await api.get('/cognitions/favorites', { params })
      return response.data
    } catch (err) {
      console.error('获取收藏认知失败:', err)
      throw err
    }
  }

  // 获取趋势统计
  async function getTrendStats(params) {
    try {
      const response = await cognitionAPI.getTrendStats(params)
      return response
    } catch (error) {
      console.error('获取趋势统计失败:', error)
      throw error
    }
  }

  // 合成认知
  async function synthesizeCognitions(cognitionIds) {
    try {
      const synthesizedCognition = await cognitionAPI.synthesizeCognitions(cognitionIds)
      // 可以在这里对返回的数据做一些处理，如果需要的话
      return synthesizedCognition
    } catch (error) {
      console.error('合成认知失败:', error)
      throw error
    }
  }

  // 清除错误
  function clearError() {
    error.value = null
  }

  // 重置状态
  function reset() {
    cognitions.value = []
    currentCognition.value = null
    loading.value = false
    error.value = null
  }

  return {
    // 状态
    cognitions,
    currentCognition,
    loading,
    error,
    
    // 方法
    getCognitions,
    getCognition,
    createCognition,
    updateCognition,
    deleteCognition,
    voteCognition,
    addComment,
    getUserVote,
    getUserCollections,
    createCollection,
    deleteCollection,
    updateCollection,
    addToFavorites,
    removeFromFavorites,
    getFavoriteStatus,
    getCollectionCognitions,
    getAvailableTopics,
    getFavoritedCognitions,
    getTrendStats,
    synthesizeCognitions,
    clearError,
    reset,

    // 收藏状态管理
    addFavoriteStatusListener,
    getCachedFavoriteStatus,
    updateFavoriteStatusCache
  }
}) 
