import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getEnvironmentConfig, updateEnvironmentConfig } from '@/api/admin'

export const useEnvironmentStore = defineStore('environment', () => {
  const environment = ref<'dev' | 'product'>('dev')
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 获取环境配置
  async function fetchEnvironment() {
    loading.value = true
    error.value = null
    try {
      const res = await getEnvironmentConfig()
      environment.value = res.environment
    } catch (err: any) {
      error.value = err.message || '获取环境配置失败'
      console.error('获取环境配置失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 更新环境配置
  async function updateEnvironment(newEnvironment: 'dev' | 'product') {
    loading.value = true
    error.value = null
    try {
      const res = await updateEnvironmentConfig(newEnvironment)
      environment.value = res.environment
    } catch (err: any) {
      error.value = err.message || '更新环境配置失败'
      console.error('更新环境配置失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    environment,
    loading,
    error,
    fetchEnvironment,
    updateEnvironment
  }
}) 