import { defineStore } from 'pinia'
import { authService, type UserResponse, type UserCreate, type UserUpdate, type TokenResponse } from '@/api/authService'

// 用户状态接口
export interface UserState {
  token: string | null
  isAuthenticated: boolean
  user: {
    id: number | null
    username: string | null
    name: string | null
    email: string | null
    created_at: string | null
    updated_at: string | null
    is_active: boolean
    is_admin: boolean
  }
  loading: boolean
  error: string | null
}

export const useAuthStore = defineStore('auth', {
  state: (): UserState => ({
    token: localStorage.getItem('auth_token'),
    isAuthenticated: !!localStorage.getItem('auth_token'),
    user: {
      id: null,
      username: null,
      name: null,
      email: null,
      created_at: null,
      updated_at: null,
      is_active: false,
      is_admin: false
    },
    loading: false,
    error: null,
  }),

  getters: {
    // 用户是否已认证
    userIsAuthenticated: (state) => state.isAuthenticated,
    
    // 获取当前用户
    currentUser: (state) => state.user,
    
    // 用户是否正在加载中
    userIsLoading: (state) => state.loading,
    
    // 获取错误信息
    authError: (state) => state.error,
    
    // 获取token
    getToken: (state) => state.token,
    
    // 检查是否为管理员
    isAdmin: (state) => state.isAuthenticated && state.user.is_admin
  },

  actions: {
    // 设置token
    setToken(token: string) {
      this.token = token
      this.isAuthenticated = true
      localStorage.setItem('auth_token', token)
    },

    // 清除token
    clearToken() {
      this.token = null
      this.isAuthenticated = false
      localStorage.removeItem('auth_token')
    },

    // 清除用户信息
    clearUser() {
      this.user = {
        id: null,
        username: null,
        name: null,
        email: null,
        created_at: null,
        updated_at: null,
        is_active: false,
        is_admin: false
      }
    },

    // 登录
    async login(username: string, password: string) {
      this.loading = true
      this.error = null
      try {
        console.log('Start login process...');
        const result = await authService.login(username, password)
        console.log('Login successful, token received');
        this.setToken(result.access_token)
        
        // 登录后立即获取用户信息
        try {
          await this.fetchUserInfo()
        } catch (userInfoError) {
          console.error('Failed to fetch user info after login:', userInfoError);
          // 不要在这里抛出错误，让登录过程继续
        }
        
        return result
      } catch (error: any) {
        console.error('Login error details:', error);
        this.error = error.message || '登录失败'
        throw error
      } finally {
        this.loading = false
      }
    },

    // 注册
    async register(name: string, username: string, email: string, password: string, invite_code: string) {
      this.loading = true
      this.error = null
      try {
        const userData: UserCreate = {
          username,
          email,
          password,
          invite_code
        }
        return await authService.register(userData)
      } catch (error: any) {
        this.error = error.message || '注册失败'
        throw error
      } finally {
        this.loading = false
      }
    },

    // 登出
    async logout() {
      this.loading = true
      try {
        authService.logout()
        this.clearToken()
        this.clearUser()
      } catch (error: any) {
        this.error = error.message || '登出失败'
      } finally {
        this.loading = false
      }
    },

    // 获取用户信息
    async fetchUserInfo() {
      this.loading = true
      try {
        if (!this.token) {
          console.error('fetchUserInfo called without token');
          throw new Error('未认证')
        }
        
        console.log('Fetching user info from authStore...');
        const userInfo = await authService.getUserInfo()
        console.log('User info fetched successfully:', userInfo);
        
        this.user = {
          id: userInfo._id,
          username: userInfo.username,
          name: userInfo.username,
          email: userInfo.email,
          created_at: userInfo.created_at,
          updated_at: userInfo.created_at,
          is_active: userInfo.is_active,
          is_admin: userInfo.is_admin
        }
        
        return userInfo
      } catch (error: any) {
        console.error('Error details in fetchUserInfo:', error);
        if (error.response && error.response.status === 401) {
          // Token 过期或无效
          console.log('Token is invalid or expired, clearing...');
          this.clearToken()
        }
        throw error;
      } finally {
        this.loading = false
      }
    },

    // 更新用户信息
    async updateUserInfo(userData: UserUpdate) {
      this.loading = true
      this.error = null
      try {
        if (!this.user.id) {
          throw new Error('未找到用户信息');
        }
        
        const updatedUser = await authService.updateUser(this.user.id, userData);
        
        // 更新本地状态
        this.user = {
          id: updatedUser._id,
          username: updatedUser.username,
          name: updatedUser.username,
          email: updatedUser.email,
          created_at: updatedUser.created_at,
          updated_at: updatedUser.created_at,
          is_active: updatedUser.is_active,
          is_admin: updatedUser.is_admin
        }
        
        return updatedUser;
      } catch (error: any) {
        this.error = error.message || '更新用户信息失败';
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // 验证令牌有效性
    async verifyTokenValidity() {
      try {
        const isValid = await authService.verifyToken();
        if (!isValid) {
          this.clearToken();
          this.clearUser();
        }
        return isValid;
      } catch (error) {
        this.clearToken();
        this.clearUser();
        return false;
      }
    },

    // 初始化方法 - 应用启动时调用
    async init() {
      // 如果有token，验证token并获取用户信息
      if (this.token) {
        try {
          await this.verifyTokenValidity();
          if (this.isAuthenticated) {
            await this.fetchUserInfo();
          }
        } catch (error) {
          console.error('Failed to initialize auth store:', error);
          this.clearToken();
          this.clearUser();
        }
      }
    }
  }
}) 