import { backendRequest } from "@/utils/requests";

/**
 * 令牌响应接口
 */
export interface TokenResponse {
  access_token: string;
  token_type: string;
}

/**
 * 用户创建接口
 */
export interface UserCreate {
  username: string;
  email: string;
  password: string;
  invite_code: string;
}

/**
 * 用户更新接口
 */
export interface UserUpdate {
  username?: string;
  email?: string;
  password?: string;
  is_active?: boolean;
}

/**
 * 用户响应接口
 */
export interface UserResponse {
  _id: string;
  username: string;
  email: string;
  is_active: boolean;
  is_admin: boolean;
  created_at: string;
}

/**
 * 邀请码接口
 */
export interface InviteCode {
  _id: string;
  code: string;
  is_used: boolean;
  used_by: string | null;
  used_at: string | null;
  created_by: string;
  created_at: string;
}

/**
 * 邀请码分页响应接口
 */
export interface InviteCodesResponse {
  items: InviteCode[];
  total: number;
}

/**
 * 创建邀请码请求
 */
export interface InviteCodeCreateRequest {
  count: number;
}

/**
 * 用户Token信息接口
 */
export interface UserTokenInfo {
  user_id: string;
  username: string;
  token_budget: number;
  current_token_usage: number;
  remaining_budget: number;
  usage_percentage: number;
}

/**
 * Token预算更新请求
 */
export interface TokenBudgetUpdateRequest {
  token_budget: number;
}

/**
 * Token使用量更新请求
 */
export interface TokenUsageUpdateRequest {
  current_token_usage: number;
}

/**
 * 批量Token预算更新请求
 */
export interface BatchTokenBudgetUpdateRequest {
  user_ids: string[];
  token_budget: number;
}

/**
 * 批量Token使用量更新请求
 */
export interface BatchTokenUsageUpdateRequest {
  user_ids: string[];
  current_token_usage: number;
}

let response_cache: UserResponse | null = null;
let response_timestamp: number = 0;

/**
 * 用户登录
 * @param username 用户名
 * @param password 密码
 * @returns 登录响应，包含访问令牌
 */
async function login(
  username: string,
  password: string
): Promise<TokenResponse> {
  try {
    // 创建FormData格式数据
    const formData = new URLSearchParams();
    formData.append("username", username);
    formData.append("password", password);

    // 打印调试信息
    console.log('Login request data:', { username, password: '******' });
    console.log('Form data:', formData.toString().replace(/password=[^&]+/, 'password=******'));

    // 发送登录请求
    const response = await backendRequest<TokenResponse>(
      "/auth/login",
      "POST",
      formData.toString(),
      undefined,
      {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      }
    );

    console.log('Login response:', {
      access_token: response.access_token ? `${response.access_token.substring(0, 10)}...` : 'no token',
      token_type: response.token_type
    });

    // 登录成功后立即保存token到localStorage
    if (response && response.access_token) {
      localStorage.setItem('auth_token', response.access_token);
    }

    return response;
  } catch (error) {
    console.error("登录失败:", error);
    throw error;
  }
}

/**
 * 用户注册
 * @param userData 用户数据
 * @returns 注册的用户信息
 */
async function register(userData: UserCreate): Promise<UserResponse> {
  try {
    // 发送注册请求
    const response = await backendRequest<UserResponse>(
      "/auth/register",
      "POST",
      userData
    );

    return response;
  } catch (error) {
    console.error("注册失败:", error);
    throw error;
  }
}

/**
 * 获取当前用户信息
 * @returns 用户信息
 */
async function getUserInfo(): Promise<UserResponse> {
  const now = Date.now();
  if (response_cache && (now - response_timestamp < 10000)) {
    console.log(`User info is cached because time now is ${now} and response_timestamp is ${response_timestamp}`)
    return response_cache;
  }
  console.log(`User info is cached failed because time now is ${now} and response_timestamp is ${response_timestamp}`)
  try {
    // 打印调试信息
    const token = localStorage.getItem('auth_token');
    console.log('Fetching user info with token:', token ? `${token.substring(0, 10)}...` : 'no token');

    // 获取用户信息
    const response = await backendRequest<UserResponse>(
      "/auth/me",
      "GET"
    );

    response_cache = response;
    response_timestamp = Date.now();

    console.log('User info response:', response);
    return response;
  } catch (error) {
    console.error("获取用户信息失败:", error);
    throw error;
  }
}

/**
 * 更新用户信息
 * @param userId 用户ID
 * @param userData 用户更新数据
 * @returns 更新后的用户信息
 */
async function updateUser(userId: string, userData: UserUpdate): Promise<UserResponse> {
  try {
    const response = await backendRequest<UserResponse>(
      `/users/${userId}`,
      "PATCH",
      userData
    );

    return response;
  } catch (error) {
    console.error("更新用户信息失败:", error);
    throw error;
  }
}

/**
 * 退出登录
 */
function logout(): void {
  // 客户端登出只需清除本地存储的token
  localStorage.removeItem('auth_token');
}

/**
 * 检查token是否有效
 * @returns 如果token有效，返回true
 */
async function verifyToken(): Promise<boolean> {
  try {
    const token = localStorage.getItem('auth_token');
    if (!token) {
      return false;
    }

    // 尝试获取用户信息来验证token
    await getUserInfo();
    return true;
  } catch (error) {
    console.error("Token验证失败:", error);
    // 如果是401错误，清除无效token
    if (error.message && error.message.includes('401')) {
      localStorage.removeItem('auth_token');
    }
    return false;
  }
}

/**
 * 管理员登录
 * @param username 管理员用户名
 * @param password 管理员密码
 * @returns 登录响应，包含访问令牌
 */
async function adminLogin(
  username: string,
  password: string
): Promise<TokenResponse> {
  try {
    // 创建FormData格式数据
    const formData = new URLSearchParams();
    formData.append("username", username);
    formData.append("password", password);

    // 发送管理员登录请求
    const response = await backendRequest<TokenResponse>(
      "/auth/admin/login",
      "POST",
      formData.toString(),
      undefined,
      {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      }
    );

    // 登录成功后保存token
    if (response && response.access_token) {
      localStorage.setItem('auth_token', response.access_token);
      localStorage.setItem('is_admin', 'true');
    }

    return response;
  } catch (error) {
    console.error("管理员登录失败:", error);
    throw error;
  }
}

/**
 * 生成邀请码
 * @param count 生成数量
 * @returns 生成的邀请码列表
 */
async function generateInviteCodes(count: number): Promise<InviteCode[]> {
  try {
    const data: InviteCodeCreateRequest = { count };
    const response = await backendRequest<InviteCode[]>(
      "/auth/admin/invite-codes/generate",
      "POST",
      data
    );

    return response;
  } catch (error) {
    console.error("生成邀请码失败:", error);
    throw error;
  }
}

/**
 * 获取邀请码列表
 * @param skip 跳过的数量
 * @param limit 获取的数量
 * @param status 状态 ('all', 'used', 'unused')
 * @returns 邀请码列表
 */
async function getInviteCodes(skip: number = 0, limit: number = 10, status: string = 'all'): Promise<InviteCodesResponse> {
  try {
    const response = await backendRequest<InviteCodesResponse>(
      `/auth/admin/invite-codes?skip=${skip}&limit=${limit}&status=${status}`,
      "GET"
    );

    return response;
  } catch (error) {
    console.error("获取邀请码失败:", error);
    throw error;
  }
}

/**
 * 获取用户token信息（仅管理员）
 * @param userId 用户ID
 * @returns 用户token信息
 */
async function getUserTokenInfo(userId: string): Promise<UserTokenInfo> {
  try {
    const response = await backendRequest<UserTokenInfo>(
      `/auth/admin/users/${userId}/token-info`,
      "GET"
    );

    return response;
  } catch (error) {
    console.error("获取用户token信息失败:", error);
    throw error;
  }
}

/**
 * 更新用户token预算（仅管理员）
 * @param userId 用户ID
 * @param tokenBudget 新的token预算
 * @returns 更新结果
 */
async function updateUserTokenBudget(userId: string, tokenBudget: number): Promise<any> {
  try {
    const data: TokenBudgetUpdateRequest = { token_budget: tokenBudget };
    const response = await backendRequest<any>(
      `/auth/admin/users/${userId}/token-budget`,
      "PUT",
      data
    );

    return response;
  } catch (error) {
    console.error("更新用户token预算失败:", error);
    throw error;
  }
}

/**
 * 更新用户token使用量（仅管理员）
 * @param userId 用户ID
 * @param currentUsage 当前使用量
 * @returns 更新结果
 */
async function updateUserTokenUsage(userId: string, currentUsage: number): Promise<any> {
  try {
    const data: TokenUsageUpdateRequest = { current_token_usage: currentUsage };
    const response = await backendRequest<any>(
      `/auth/admin/users/${userId}/token-usage`,
      "PUT",
      data
    );

    return response;
  } catch (error) {
    console.error("更新用户token使用量失败:", error);
    throw error;
  }
}

/**
 * 重置用户token使用量（仅管理员）
 * @param userId 用户ID
 * @returns 重置结果
 */
async function resetUserTokenUsage(userId: string): Promise<any> {
  try {
    const response = await backendRequest<any>(
      `/auth/admin/users/${userId}/reset-token-usage`,
      "POST"
    );

    return response;
  } catch (error) {
    console.error("重置用户token使用量失败:", error);
    throw error;
  }
}

/**
 * 获取所有用户token概览（仅管理员）
 * @returns 所有用户token使用概览
 */
async function getAllUsersTokenOverview(): Promise<any> {
  try {
    const response = await backendRequest<any>(
      "/auth/admin/users/token-overview",
      "GET"
    );

    return response;
  } catch (error) {
    console.error("获取用户token概览失败:", error);
    throw error;
  }
}

/**
 * 批量更新用户token预算（仅管理员）
 * @param userIds 用户ID列表
 * @param tokenBudget 新的token预算
 * @returns 批量更新结果
 */
async function batchUpdateUserTokenBudget(userIds: string[], tokenBudget: number): Promise<any> {
  try {
    const data: BatchTokenBudgetUpdateRequest = {
      user_ids: userIds,
      token_budget: tokenBudget
    };
    const response = await backendRequest<any>(
      "/auth/admin/users/batch-token-budget",
      "PUT",
      data
    );

    return response;
  } catch (error) {
    console.error("批量更新用户token预算失败:", error);
    throw error;
  }
}

/**
 * 批量更新用户token使用量（仅管理员）
 * @param userIds 用户ID列表
 * @param currentUsage 新的token使用量
 * @returns 批量更新结果
 */
async function batchUpdateUserTokenUsage(userIds: string[], currentUsage: number): Promise<any> {
  try {
    const data: BatchTokenUsageUpdateRequest = {
      user_ids: userIds,
      current_token_usage: currentUsage
    };
    const response = await backendRequest<any>(
      "/auth/admin/users/batch-token-usage",
      "PUT",
      data
    );

    return response;
  } catch (error) {
    console.error("批量更新用户token使用量失败:", error);
    throw error;
  }
}

/**
 * 从session记录同步用户token使用量到用户表（仅管理员）
 * @param userId 用户ID
 * @returns 同步结果
 */
async function syncUserTokenUsageFromSessions(userId: string): Promise<any> {
  try {
    const response = await backendRequest<any>(
      `/auth/admin/users/${userId}/sync-token-usage`,
      "POST"
    );

    return response;
  } catch (error) {
    console.error("同步用户token使用量失败:", error);
    throw error;
  }
}

/**
 * 批量同步所有用户的token使用量从session记录到用户表（仅管理员）
 * @returns 批量同步结果
 */
async function syncAllUsersTokenUsageFromSessions(): Promise<any> {
  try {
    const response = await backendRequest<any>(
      "/auth/admin/users/sync-all-token-usage",
      "POST"
    );

    return response;
  } catch (error) {
    console.error("批量同步用户token使用量失败:", error);
    throw error;
  }
}

// 导出所有服务函数
export const authService = {
  login,
  register,
  getUserInfo,
  updateUser,
  logout,
  verifyToken,
  adminLogin,
  generateInviteCodes,
  getInviteCodes,
  getUserTokenInfo,
  updateUserTokenBudget,
  updateUserTokenUsage,
  resetUserTokenUsage,
  getAllUsersTokenOverview,
  batchUpdateUserTokenBudget,
  batchUpdateUserTokenUsage,
  syncUserTokenUsageFromSessions,
  syncAllUsersTokenUsageFromSessions
};

export default authService;
