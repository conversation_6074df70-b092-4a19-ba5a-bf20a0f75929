import axios from 'axios'

// 创建axios实例
const apiRequest = axios.create({
  baseURL: '/backend',
  timeout: 36000000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器 - 添加认证token
apiRequest.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理错误
apiRequest.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response && error.response.status === 401) {
      console.error('401 Unauthorized error detected, clearing token')
      localStorage.removeItem('auth_token')
      // 如果当前不是登录页，才跳转
      if (!window.location.pathname.includes('/login')) {
        window.location.href = '/login'
      }
    }
    
    // 处理其他错误
    const { response } = error
    if (response) {
      const { status } = response
      const errorMap = {
        400: "请求错误",
        401: "未授权",
        403: "禁止访问",
        404: "资源未找到",
        422: "请求数据验证失败",
        500: "服务器内部错误",
      }
      
      const errorMessage = errorMap[status] || `未知错误 (${status})`
      console.error(`API错误: ${errorMessage}`, response.data)
    } else if (error.request) {
      console.error("服务器无响应", error.request)
    } else {
      console.error("请求错误", error.message)
    }
    
    return Promise.reject(error)
  }
)

export default apiRequest 