import { backendRequest } from '@/utils/requests'

export const cognitionAPI = {
  // 获取认知列表
  getCognitions(params = {}) {
    // 确保有默认语言参数
    const requestParams = {
      language: 'zh', // 默认显示中文
      ...params
    }
    
    // 如果有topics参数，确保它是数组格式
    if (requestParams.topics && Array.isArray(requestParams.topics)) {
      // FastAPI会自动处理数组参数
      // 确保topics参数正确传递
    }
    
    return backendRequest('/api/cognition/list', 'GET', undefined, requestParams)
  },

  // 获取认知推荐
  getRecommendations(cognitionId) {
    return backendRequest(`/api/cognition/${cognitionId}/recommendations`, 'GET')
  },

  // 批量获取认知
  getCognitionsByIds(ids) {
    return backendRequest('/api/cognition/batch', 'POST', { ids })
  },

  // 获取单个认知
  getCognition(cognitionId, language = 'zh') {
    return backendRequest(`/api/cognition/${cognitionId}`, 'GET', undefined, { language })
  },

  // 创建认知
  createCognition(data) {
    return backendRequest('/api/cognition/create', 'POST', data)
  },

  // 更新认知（仅管理员）
  updateCognition(cognitionId, data) {
    return backendRequest(`/api/cognition/${cognitionId}`, 'PUT', data)
  },

  // 删除认知（仅管理员）
  deleteCognition(cognitionId) {
    return backendRequest(`/api/cognition/${cognitionId}`, 'DELETE')
  },

  // 投票
  voteCognition(cognitionId, voteType) {
    return backendRequest(`/api/cognition/${cognitionId}/vote`, 'POST', { vote_type: voteType })
  },

  // 添加评论
  addComment(cognitionId, content, parentId = null, replyToUsername = null) {
    const data = { content }
    if (parentId) {
      data.parent_id = parentId
    }
    if (replyToUsername) {
      data.reply_to_username = replyToUsername
    }
    return backendRequest(`/api/cognition/${cognitionId}/comment`, 'POST', data)
  },

  // 获取用户投票状态
  getUserVote(cognitionId) {
    return backendRequest(`/api/cognition/${cognitionId}/vote`, 'GET')
  },

  // 收藏相关API
  // 获取用户收藏夹列表
  getUserCollections() {
    return backendRequest('/api/cognition/collections', 'GET')
  },

  // 创建收藏夹
  createCollection(data) {
    return backendRequest('/api/cognition/collections', 'POST', data)
  },

  // 删除收藏夹
  deleteCollection(collectionId) {
    return backendRequest(`/api/cognition/collections/${collectionId}`, 'DELETE')
  },

  // 更新收藏夹（重命名）
  updateCollection(collectionId, data) {
    return backendRequest(`/api/cognition/collections/${collectionId}`, 'PUT', data)
  },

  // 添加到收藏夹
  addToFavorites(cognitionId, collectionId) {
    return backendRequest(`/api/cognition/${cognitionId}/favorite`, 'POST', { collection_id: collectionId })
  },

  // 从收藏夹移除
  removeFromFavorites(cognitionId, collectionId = null) {
    const params = collectionId ? { collection_id: collectionId } : {}
    return backendRequest(`/api/cognition/${cognitionId}/favorite`, 'DELETE', undefined, params)
  },

  // 获取收藏状态
  getFavoriteStatus(cognitionId) {
    return backendRequest(`/api/cognition/${cognitionId}/favorite-status`, 'GET')
  },

  // 获取收藏夹中的认知
  getCollectionCognitions(collectionId, params = {}) {
    return backendRequest(`/api/cognition/collections/${collectionId}/cognitions`, 'GET', undefined, params)
  },

  // 获取可用的话题标签
  getAvailableTopics() {
    return backendRequest('/api/cognition/topics', 'GET')
  },

  // 获取趋势统计数据
  getTrendStats(params = {}) {
    return backendRequest('/api/cognition/trend-stats', 'GET', undefined, params)
  },

  // 合成认知
  synthesizeCognitions(cognitionIds) {
    const data = { cognition_ids: cognitionIds }
    return backendRequest('/api/cognition/synthesize', 'POST', data)
  },

  // 已读状态相关API
  // 设置认知已读状态
  setReadStatus(cognitionId, isRead) {
    return backendRequest(`/api/cognition/${cognitionId}/read-status`, 'POST', { is_read: isRead })
  },

  // 获取认知已读状态
  getReadStatus(cognitionId) {
    return backendRequest(`/api/cognition/${cognitionId}/read-status`, 'GET')
  }
} 