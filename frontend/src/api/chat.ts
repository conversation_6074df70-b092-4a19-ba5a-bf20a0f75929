// api/chat.ts
import axios, { AxiosResponse } from 'axios';

// 定义接口类型
export interface ChatContent {
  type: string;         // 内容类型，如"text"
  content: string;      // 内容文本
  content_id: number;   // 内容ID
}

export interface ChatRequest {
  conversation_id?: string;        // 可选，会话ID，不提供则创建新会话
  message_contents: ChatContent[]; // 消息内容数组
}

export interface ChatContentResponse {
  type: string;         // 内容类型
  content: string;      // 内容文本
  content_id: string;   // 内容ID
}

export interface ChatMessageResponse {
  id: string;           // 消息ID
  role: string;         // 角色类型："assistant"
  created_at: string;   // 创建时间
  turn_id: number;      // 对话轮次ID
  contents: ChatContentResponse[]; // 消息内容数组
}

export interface ChatResponse {
  message: ChatMessageResponse; // 系统响应消息
  conversation_id: string;      // 会话ID
}

// 添加Markdown转PDF的请求接口
export interface MarkdownToPdfRequest {
  markdown_content: string;     // Markdown内容
  filename: string;             // 文件名
}

export interface ResearchRequest {
  question: string;              // 研究问题
  language: string;              // 语言，默认中文
  benchmark_id?: number;         // 可选，基准测试ID
  enable_cognition_search?: boolean;
}

export interface ResearchResponse {
  session_id: string;            // 会话ID
  message: string;               // 响应消息
}

export interface ResearchUpdateMessage {
  type: string;                  // 消息类型："update", "clarification", "complete", "error", "status"
  content?: any;                 // 消息内容
  questions?: Array<{ id: number, content: string }>; // 澄清问题
  report?: string;               // 完成时的报告
  message?: string;              // 状态或错误消息
  status?: string;               // 状态信息
  messages?: ResearchUpdateMessage[]; // 批量更新消息
}

export interface WebSocketHandlers {
  onMessage: (message: ResearchUpdateMessage) => void;
  onError: (error: any) => void;
  onClose: () => void;
  onOpen?: (event: Event) => void;
}

// 对话历史记录接口
export interface ChatHistory {
  conversation_id: string;       // 对话ID
  prompt: string;                // 提示/问题
  created_at: string;            // 创建时间
  updated_at?: string;           // 更新时间
  user_id?: string;              // 用户ID
  report_draft?: string;         // 报告草稿
}

// 添加新的接口定义
export interface CreateSessionRequest {
  user_id: string;
  metadata?: Record<string, any>;
}

export interface UserPreferences {
  professional?: number;
  critical?: number;
  comparison?: number;
  organization?: number;
  cutting_edge?: number;
  coverage?: number;
  depth?: number;
}

export interface StartResearchRequest {
  session_id: string;
  question: string;
  benchmark_id?: number;
  enable_cognition_search?: boolean;
  enable_search?: boolean;
  user_preferences?: UserPreferences;
}

export interface UpdateResearchStatusRequest {
  session_id: string;
  enable_cognition_search: boolean;
}

export interface UpdateSearchStatusRequest {
  session_id: string;
  enable_search: boolean;
}

export interface CreateSessionResponse {
  session_id: string;
  message: string;
}

export interface StartResearchResponse {
  session_id: string;
  message: string;
}

// 创建 axios 实例 - 使用相对路径让Vite处理代理
const api = axios.create({
  baseURL: '/backend',
  headers: {
    'Content-Type': 'application/json',
  },
});



// 设置请求拦截器，添加 token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// 存储活跃的WebSocket连接
const activeWebSockets: Record<string, WebSocket> = {};

// 聊天 API 方法
export const chatApi = {
  // 普通聊天
  chat: async (data: ChatRequest): Promise<ChatResponse> => {
    const response = await api.post('/backend/api/conversations/chat', data);
    return response.data;
  },

  // Markdown转PDF
  markdownToPdf: async (markdownContent: string, filename: string = 'document.pdf'): Promise<Blob> => {
    const response = await api.post('/api/markdown-to-pdf', {
      markdown_content: markdownContent,
      filename: filename
    }, {
      responseType: 'blob'  // 指定响应类型为blob
    });
    return response.data;
  },

  // 提交编辑请求（chat to edit 功能）
  submitEditRequest: async (sessionId: string, editData: { edit_content: string, edit_type: string }): Promise<any> => {
    const response = await api.post(`/conversation/research/${sessionId}/edit_request`, editData);
    return response.data;
  },
};

// 研究API
export const researchApi = {
  // 获取当前用户的所有对话
  getMyChats: async (): Promise<any> => {
    const response = await api.get('/conversation/api/chats/my');
    return response.data || [];
  },

  // 启动研究任务
  startResearch: async (data: ResearchRequest): Promise<ResearchResponse> => {
    const response = await api.post('/conversation/research/start', data);
    return response.data;
  },

  // 获取研究状态
  getResearchStatus: async (sessionId: string): Promise<any> => {
    const response = await api.get(`/conversation/research/${sessionId}/status`);
    return response.data;
  },

  getAvailableModels: async (): Promise<any> => {
    const response = await api.get('/conversation/api/models/available');
    return response.data;
  },
  setModelsPreferences: async (preferences: any): Promise<any> => {
    const response = await api.post('/conversation/api/models/preferences', preferences);
    return response.data;
  },

  // 提交用户反馈
  submitFeedback: async (sessionId: string, feedback: string): Promise<any> => {
    const response = await api.post(`/conversation/research/${sessionId}/feedback`, {
      feedback: feedback
    });
    return response.data;
  },

  // 提交澄清问题回答
  submitClarificationAnswer: async (sessionId: string, answers: Array<{ id: number, answer: string }>): Promise<any> => {
    const response = await api.post(`/conversation/research/${sessionId}/clarification_answer`, {
      answers: answers // 直接传递完整的answers对象数组，包含id和answer
    });
    return response.data;
  },

  // 暂停研究任务
  pauseResearch: async (sessionId: string): Promise<any> => {
    const response = await api.post(`/conversation/research/${sessionId}/pause`);
    return response.data;
  },

  // 恢复研究任务
  resumeResearch: async (sessionId: string): Promise<any> => {
    const response = await api.post(`/conversation/research/${sessionId}/resume`);
    return response.data;
  },
  getPendingQuestions: async (sessionId: string): Promise<any> => {
    const response = await api.get(`/conversation/research/${sessionId}/pending_questions`);
    return response.data;
  },

  // 连接WebSocket获取实时研究进展
  connectWebSocket: (sessionId: string, handlers: WebSocketHandlers): WebSocket => {
    // 关闭已存在的连接
    if (activeWebSockets[sessionId]) {
      console.log(`关闭已存在的WebSocket连接: ${sessionId}`);
      activeWebSockets[sessionId].close();
    }

    // 创建WebSocket连接
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/conversation/ws/research/${sessionId}`;
    console.log(`正在连接WebSocket: ${wsUrl}`);

    const socket = new WebSocket(wsUrl);

    // 设置连接事件处理
    socket.onopen = (event) => {
      console.log(`WebSocket连接已建立: ${sessionId}`);
      handlers.onOpen && handlers.onOpen(event);
    };

    // 设置事件处理程序
    socket.onmessage = (event) => {
      // console.log(`WebSocket收到消息: ${sessionId}`, event.data.substring(0, 100) + (event.data.length > 100 ? '...' : ''));
      try {
        const data = JSON.parse(event.data);
        // console.log(`WebSocket消息解析成功: ${sessionId}, 类型=${data.type || 'unknown'}`);

        // 处理心跳消息，立即响应以保持连接活跃
        if (data.type === 'heartbeat') {
          // 立即响应心跳消息
          socket.send(JSON.stringify({
            type: 'heartbeat_response',
            id: data.id,  // 返回相同的ID便于服务器识别
            timestamp: new Date().getTime()
          }));
          // 不需要通知上层业务逻辑
          return;
        }

        // 处理ping消息
        if (data.type === 'ping') {
          // 立即响应ping消息
          socket.send(JSON.stringify({
            type: 'pong',
            timestamp: new Date().getTime()
          }));
          // 不需要通知上层业务逻辑
          return;
        }

        // 其他消息传递给处理器
        handlers.onMessage(data);
      } catch (error) {
        console.error('WebSocket消息解析错误:', error);
        handlers.onError(error);
      }
    };

    socket.onerror = (error) => {
      // 打印错误的详细信息
      console.error(`WebSocket错误: ${sessionId}`, {
        error: error,
        errorType: error instanceof Error ? error.name : typeof error,
        errorMessage: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : null,
        readyState: socket.readyState,
        url: socket.url,
        protocol: socket.protocol,
        extensions: socket.extensions,
        bufferedAmount: socket.bufferedAmount
      });

      // 分析可能的错误原因
      let errorAnalysis = '可能的错误原因:\n';
      if (socket.readyState === WebSocket.CLOSED) {
        errorAnalysis += '- WebSocket连接已关闭\n';
      }
      if (socket.readyState === WebSocket.CONNECTING) {
        errorAnalysis += '- WebSocket连接尚未建立，可能是网络问题\n';
      }
      if (!navigator.onLine) {
        errorAnalysis += '- 浏览器当前处于离线状态\n';
      }
      console.error(errorAnalysis);

      handlers.onError(error);
    };

    socket.onclose = (event) => {
      console.log(`WebSocket连接已关闭: ${sessionId}, 代码=${event.code}, 原因=${event.reason || '未提供'}`);
      delete activeWebSockets[sessionId];
      handlers.onClose();
    };

    // 保存连接
    activeWebSockets[sessionId] = socket;

    return socket;
  },

  // 断开WebSocket连接
  disconnectWebSocket: (sessionId: string): void => {
    if (activeWebSockets[sessionId]) {
      activeWebSockets[sessionId].close();
      delete activeWebSockets[sessionId];
    }
  },


  // 获取分享的研究内容
  getSharedResearch: async (conversationId: string): Promise<any> => {
    const response = await api.get(`/api/research/share/${conversationId}`);
    return response.data;
  },

  // 连接分享WebSocket获取重放的研究进展
  connectShareWebSocket: (conversationId: string, handlers: WebSocketHandlers): WebSocket => {
    // 关闭已存在的连接
    if (activeWebSockets[conversationId]) {
      console.log(`关闭已存在的分享WebSocket连接: ${conversationId}`);
      activeWebSockets[conversationId].close();
    }

    // 创建WebSocket连接
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws/research/share/${conversationId}`;
    console.log(`正在连接分享WebSocket: ${wsUrl}`);

    const socket = new WebSocket(wsUrl);

    // 设置连接事件处理
    socket.onopen = (event) => {
      console.log(`分享WebSocket连接已建立: ${conversationId}`);
      handlers.onOpen && handlers.onOpen(event);
    };

    // 设置事件处理程序
    socket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        // 处理心跳消息，立即响应以保持连接活跃
        if (data.type === 'heartbeat') {
          // 立即响应心跳消息
          socket.send(JSON.stringify({
            type: 'heartbeat_response',
            id: data.id,  // 返回相同的ID便于服务器识别
            timestamp: new Date().getTime()
          }));
          // 不需要通知上层业务逻辑
          return;
        }

        // 处理ping消息
        if (data.type === 'ping') {
          // 立即响应ping消息
          socket.send(JSON.stringify({
            type: 'pong',
            timestamp: new Date().getTime()
          }));
          // 不需要通知上层业务逻辑
          return;
        }

        // 其他消息传递给处理器
        handlers.onMessage(data);
      } catch (error) {
        console.error('分享WebSocket消息解析错误:', error);
        handlers.onError(error);
      }
    };

    socket.onerror = (error) => {
      console.error(`分享WebSocket错误: ${conversationId}`, error);
      handlers.onError(error);
    };

    socket.onclose = (event) => {
      console.log(`分享WebSocket连接已关闭: ${conversationId}, 代码=${event.code}, 原因=${event.reason || '未提供'}`);
      delete activeWebSockets[conversationId];
      handlers.onClose();
    };

    // 保存连接
    activeWebSockets[conversationId] = socket;

    return socket;
  },

  // 创建新的研究会话（V2版本）
  createSessionV2: async (data: CreateSessionRequest): Promise<CreateSessionResponse> => {
    const response = await api.post('/conversation/api/v2/research/create_session', data);
    return response.data;
  },

  // 启动新的研究任务（V2版本）
  startResearchV2: async (data: StartResearchRequest): Promise<StartResearchResponse> => {
    const response = await api.post('/conversation/api/v2/research/start', data);
    return response.data;
  },

  // 更新研究状态
  updateResearchStatus: async (data: UpdateResearchStatusRequest): Promise<any> => {
    const response = await api.post('/conversation/api/v2/research/update_status', data);
    return response.data;
  },

  // 更新搜索状态 - 预留接口位置
  updateSearchStatus: async (data: UpdateSearchStatusRequest): Promise<any> => {
    // 调用实际的搜索状态更新API
    const response = await api.post('/conversation/api/v2/research/update_search_status', data);
    return response.data;
  },

  // 上传文件到研究会话
  uploadFile: async (conversationId: string, file: File): Promise<any> => {
    const formData = new FormData();
    formData.append('conversation_id', conversationId);
    formData.append('file', file);
    formData.append('file_type', file.type);

    const response = await api.post('/conversation/v2/research/upload_file', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  },

  // 获取研究会话的所有上下文
  getContexts: async (conversationId: string): Promise<any> => {
    const response = await api.get(`/conversation/api/research/contexts/${conversationId}`);
    return response.data;
  },

  // 删除研究会话的指定上下文
  deleteContext: async (conversationId: string, contextId: string): Promise<any> => {
    const response = await api.post('/conversation/api/research/contexts/delete', {
      conversation_id: conversationId,
      context_id: contextId
    });
    return response.data;
  },
  // 提交报告反馈
  submitReportFeedback: async (feedback: { sessionId: string, rating: string, comment: string }): Promise<any> => {
    const response = await api.post(`/research/report_feedback`, feedback);
    return response.data;
  },

  // 请求上下文
  requestContext: async (contextId: string, conversationId: string): Promise<AxiosResponse<Blob>> => {
    const response = await api.get(`/conversation/api/research/request_context/${contextId}`, {
      params: {
        conversation_id: conversationId
      },
      responseType: 'blob'  // 指定响应类型为blob
    });
    return response;
  },

  // 添加note上下文
  addNoteContext: async (conversationId: string, noteId: string): Promise<any> => {
    const response = await api.post(`/conversation/api/research/select_note/${noteId}`, {
      conversation_id: conversationId,
    });
    return response.data;
  },

  // 获取所有用户的token使用统计（仅管理员）
  getAllUsersTokenUsage: async (): Promise<any> => {
    const response = await api.get('/api/admin/token-usage');
    return response.data;
  },

  // 获取所有会话的token使用统计（仅管理员）
  getAllSessionsTokenUsage: async (skip: number = 0, limit: number = 100): Promise<any> => {
    const response = await api.get('/api/admin/sessions-token-usage', {
      params: { skip, limit }
    });
    return response.data;
  },

  // 获取当前用户的token使用统计
  getUserTokenUsage: async (): Promise<any> => {
    const response = await api.get('/api/user/token-usage');
    return response.data;
  },

  // 提交内容偏好（点赞/取消点赞）
  submitContentPreference: async (data: {
    conversation_id: string;
    url: string;
    title: string;
    liked: boolean;
    timestamp?: string;
  }): Promise<any> => {
    const response = await api.post('/conversation/api/research/content_preference', data);
    return response.data;
  },
};

export default {
  chatApi,
  researchApi
};