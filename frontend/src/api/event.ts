// src/api/event.ts
import { backendRequest } from '../utils/requests';

// 定义统计数据类型
export interface EventStats {
  [eventType: string]: number;
}

// 定义时间段统计数据类型
export interface PeriodStats {
  [timePoint: string]: {
    [eventType: string]: number;
  };
}

// 添加事件类型常量，与后端保持一致
export const EVENT_TYPES = {
  LOGIN: "login",
  REGISTER: "register",
  CHAT_SUBMIT: "chat_submit",
  CHAT_CREATE: "chat_create",
  CHAT_SHARE: "chat_share",
  CHAT_VIEW_SHARED: "chat_view_shared",
  BROWSE_PUBLIC: "browse_public"
};

// 符合后端期望的事件追踪请求格式
interface TrackEventRequest {
  event_type: string;        
  data?: object;    
  anonymous?: boolean;    
}

export const eventApi = {
  // 添加事件追踪方法 - 修改为与后端匹配的请求格式
  async trackEvent(
    event_type: string, 
    data: Record<string, any> = {}, 
    anonymous: boolean = false
  ): Promise<{event_id: string, success: boolean}> {
    // 使用新的请求方式，直接通过URL参数传递event_type
    const url = `/api/events/track?event_type=${encodeURIComponent(event_type)}`;
    
    // data和anonymous作为请求体发送
    const requestBody = {
      data: data,
      anonymous: anonymous
    };
    
    return backendRequest(url, 'POST', requestBody);
  },

  // 获取每日统计
  async getDailyStats(date?: string): Promise<{ date: string; stats: EventStats }> {
    const params = date ? { date } : undefined;
    return backendRequest('/api/events/stats/daily', 'GET', undefined, params);
  },

  // 获取每小时统计
  async getHourlyStats(hour?: string): Promise<{ hour: string; stats: EventStats }> {
    const params = hour ? { hour } : undefined;
    return backendRequest('/api/events/stats/hourly', 'GET', undefined, params);
  },

  // 获取一段时间的统计
  /**
   * @param startDate 开始日期，格式为YYYYMMDD
   * @param endDate 结束日期，格式为YYYYMMDD
   * @param by 统计粒度，'day'或'hour'
   */
  async getPeriodStats(
    startDate: string,
    endDate: string,
    by: 'day' | 'hour' = 'day'
  ): Promise<{ start_date: string; end_date: string; by: string; stats: PeriodStats }> {
    return backendRequest('/api/events/stats/period', 'GET', undefined, {
      start_date: startDate,
      end_date: endDate,
      by
    });
  },

  // 获取所有事件类型的总计
  async getEventSummary(): Promise<{ summary: EventStats }> {
    return backendRequest('/api/events/stats/summary', 'GET');
  }
};