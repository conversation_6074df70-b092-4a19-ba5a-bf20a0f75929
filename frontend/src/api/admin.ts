import { backendRequest } from "@/utils/requests";

export async function getEnvironmentConfig() {
  return backendRequest<{ environment: 'dev' | 'product' }>(
    '/auth/admin/environment',
    'GET'
  );
}

export async function updateEnvironmentConfig(environment: 'dev' | 'product') {
  return backendRequest<{ environment: 'dev' | 'product' }>(
    '/auth/admin/environment',
    'POST',
    { environment }
  );
} 