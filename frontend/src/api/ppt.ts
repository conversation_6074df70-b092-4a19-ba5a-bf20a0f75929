import { backendRequest } from "@/utils/requests";

/**
 * PPT评审请求接口
 */
export interface PPTReviewRequest {
  language?: string;
  review_depth?: string;
  additional_context?: string;
}

/**
 * PPT评审响应接口
 */
export interface PPTReviewResponse {
  session_id: string;
  message: string;
  status: string;
}

/**
 * PPT状态响应接口
 */
export interface PPTStatusResponse {
  session_id: string;
  status: string;
  progress: number;
  current_stage: string;
  message: string;
  result?: any;
  error?: string;
}

/**
 * PPT问答请求接口
 */
export interface PPTQuestionRequest {
  question: string;
  context?: string;
}

/**
 * PPT问答响应接口
 */
export interface PPTQuestionResponse {
  answer: string;
  session_id: string;
  generated_at: string;
}

/**
 * PPT反馈请求接口
 */
export interface PPTFeedbackRequest {
  session_id: string;
  rating: number;
  feedback: string;
  improvement_suggestions?: string;
}

/**
 * PPT评审历史接口
 */
export interface PPTReviewHistory {
  session_id: string;
  file_name: string;
  review_date: string;
  status: string;
  summary: string;
}

/**
 * 上传PPT并启动评审
 * @param file PPT文件
 * @param options 评审选项
 * @returns 评审响应
 */
export async function startPPTReview(
  file: File,
  options: PPTReviewRequest = {}
): Promise<PPTReviewResponse> {
  try {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("language", options.language || "zh");
    formData.append("review_depth", options.review_depth || "standard");
    formData.append("additional_context", options.additional_context || "");

    console.log("Starting PPT review with file:", file.name);

    const response = await backendRequest<PPTReviewResponse>(
      "/api/ppt/review",
      "POST",
      formData
    );

    console.log("PPT review started:", response);
    return response;
  } catch (error) {
    console.error("PPT评审启动失败:", error);
    throw error;
  }
}

/**
 * 获取PPT评审状态
 * @param sessionId 会话ID
 * @returns 状态信息
 */
export async function getPPTReviewStatus(
  sessionId: string
): Promise<PPTStatusResponse> {
  try {
    const response = await backendRequest<PPTStatusResponse>(
      `/api/ppt/review/${sessionId}/status`,
      "GET"
    );

    return response;
  } catch (error) {
    console.error("获取PPT评审状态失败:", error);
    throw error;
  }
}

/**
 * 获取PPT评审报告
 * @param sessionId 会话ID
 * @returns 评审报告
 */
export async function getPPTReviewReport(sessionId: string): Promise<any> {
  try {
    const response = await backendRequest<any>(
      `/api/ppt/review/${sessionId}/report`,
      "GET"
    );

    return response;
  } catch (error) {
    console.error("获取PPT评审报告失败:", error);
    throw error;
  }
}

/**
 * 对评审结果提问
 * @param sessionId 会话ID
 * @param question 问题内容
 * @returns 问答响应
 */
export async function askQuestionAboutReview(
  sessionId: string,
  question: PPTQuestionRequest
): Promise<PPTQuestionResponse> {
  try {
    const response = await backendRequest<PPTQuestionResponse>(
      `/api/ppt/review/${sessionId}/question`,
      "POST",
      question
    );

    return response;
  } catch (error) {
    console.error("PPT问答失败:", error);
    throw error;
  }
}

/**
 * 获取PPT评审历史
 * @param skip 跳过数量
 * @param limit 限制数量
 * @returns 历史列表
 */
export async function getPPTReviewHistory(
  skip: number = 0,
  limit: number = 20
): Promise<{ reviews: PPTReviewHistory[], total: number }> {
  try {
    const response = await backendRequest<{ reviews: PPTReviewHistory[], total: number }>(
      `/api/ppt/reviews/history?skip=${skip}&limit=${limit}`,
      "GET"
    );

    return response;
  } catch (error) {
    console.error("获取PPT评审历史失败:", error);
    throw error;
  }
}

/**
 * 提交PPT评审反馈
 * @param feedback 反馈信息
 * @returns 提交结果
 */
export async function submitPPTFeedback(
  feedback: PPTFeedbackRequest
): Promise<any> {
  try {
    const response = await backendRequest<any>(
      `/api/ppt/feedback/${feedback.session_id}`,
      "POST",
      {
        rating: feedback.rating,
        feedback: feedback.feedback,
        improvement_suggestions: feedback.improvement_suggestions,
      }
    );

    return response;
  } catch (error) {
    console.error("提交PPT反馈失败:", error);
    throw error;
  }
}

/**
 * 获取PPT模板列表
 * @returns 模板列表
 */
export async function getPPTTemplates(): Promise<any> {
  try {
    const response = await backendRequest<any>("/api/ppt/templates", "GET");
    return response;
  } catch (error) {
    console.error("获取PPT模板失败:", error);
    throw error;
  }
} 