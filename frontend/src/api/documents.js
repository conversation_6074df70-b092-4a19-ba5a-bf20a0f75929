import apiRequest from './request'

export const documentsApi = {
  // 基础文档操作
  async createDocument(documentData) {
    try {
      const response = await apiRequest.post('/documents', documentData);
      return response.data;
    } catch (error) {
      console.error('创建文档失败:', error);
      throw error;
    }
  },

  async getDocument(documentId) {
    try {
      const response = await apiRequest.get(`/documents/${documentId}`);
      const document = response.data;
      
      // 处理文档内容中的TOS图片链接
      if (document.content) {
        document.content = processContentImages(document.content);
      }
      
      return document;
    } catch (error) {
      console.error('获取文档失败:', error);
      throw error;
    }
  },

  async updateDocument(documentId, documentData) {
    try {
      const response = await apiRequest.put(`/documents/${documentId}`, documentData);
      return response.data;
    } catch (error) {
      console.error('更新文档失败:', error);
      throw error;
    }
  },

  async deleteDocument(documentId, force = false) {
    try {
      const response = await apiRequest.delete(`/documents/${documentId}`, {
        params: { force }
      });
      return response.data;
    } catch (error) {
      console.error('删除文档失败:', error);
      throw error;
    }
  },

  async getDocumentTree() {
    try {
      const response = await apiRequest.get('/documents/tree');
      return response.data;
    } catch (error) {
      console.error('获取文档树失败:', error);
      throw error;
    }
  },

  async moveDocument(documentId, targetParentId) {
    try {
      const response = await apiRequest.put(`/documents/${documentId}/move`, {
        target_parent_id: targetParentId
      });
      return response.data;
    } catch (error) {
      console.error('移动文档失败:', error);
      throw error;
    }
  },

  async uploadImage(file) {
    try {
      const formData = new FormData();
      formData.append('file', file);
      const response = await apiRequest.post('/documents/upload_image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      // 检查返回的URL是否为TOS链接，如果是则转换为代理链接
      const result = response.data;
      if (result.url && isTOSImageUrl(result.url)) {
        result.url = convertTOSImageUrl(result.url);
      }
      
      return result;
    } catch (error) {
      console.error('上传图片失败:', error);
      throw error;
    }
  },

  // 分享和协作相关的API
  
  async shareDocument(documentId, shareOptions) {
    try {
      const response = await apiRequest.post(`/documents/${documentId}/share`, shareOptions);
      return response.data;
    } catch (error) {
      console.error('分享文档失败:', error);
      throw error;
    }
  },

  async revokeShare(documentId) {
    try {
      const response = await apiRequest.delete(`/documents/${documentId}/share`);
      return response.data;
    } catch (error) {
      console.error('撤销分享失败:', error);
      throw error;
    }
  },

  async getSharedDocument(shareToken) {
    try {
      const response = await apiRequest.get(`/documents/shared/${shareToken}`);
      const document = response.data;
      
      // 处理文档内容中的TOS图片链接
      if (document.content) {
        document.content = processContentImages(document.content);
      }
      
      return document;
    } catch (error) {
      console.error('获取分享文档失败:', error);
      throw error;
    }
  },

  async updateSharedDocument(shareToken, updateData) {
    try {
      const response = await apiRequest.put(`/documents/shared/${shareToken}`, updateData);
      return response.data;
    } catch (error) {
      console.error('更新分享文档失败:', error);
      throw error;
    }
  },

  async copySharedDocument(shareToken, targetParentId = null) {
    try {
      const response = await apiRequest.post(`/documents/shared/${shareToken}/copy`, {
        target_parent_id: targetParentId
      });
      return response.data;
    } catch (error) {
      console.error('复制分享文档失败:', error);
      throw error;
    }
  },

  async addSharedDocumentToNotes(shareToken) {
    try {
      const response = await apiRequest.post(`/documents/shared/${shareToken}/add_to_notes`);
      return response.data;
    } catch (error) {
      console.error('添加分享文档到笔记失败:', error);
      throw error;
    }
  },

  // 将分享文件夹添加到用户笔记（复制模式）
  async addSharedFolderToNotes(shareToken) {
    try {
      const response = await apiRequest.post(`/documents/shared/${shareToken}/add_folder_to_notes`);
      return response.data;
    } catch (error) {
      console.error('添加分享文件夹到笔记失败:', error);
      throw error;
    }
  },

  // 将分享文件夹添加到用户笔记（协作模式）
  async addSharedFolderToNotesCollaborative(shareToken) {
    try {
      const response = await apiRequest.post(`/documents/shared/${shareToken}/add_folder_to_notes_collaborative`);
      return response.data;
    } catch (error) {
      console.error('添加分享文件夹到笔记失败（协作模式）:', error);
      throw error;
    }
  },

  // 获取分享文件夹的文档树
  async getSharedFolderTree(shareToken) {
    try {
      const response = await apiRequest.get(`/documents/shared/${shareToken}/tree`);
      return response.data;
    } catch (error) {
      console.error('获取分享文件夹树失败:', error);
      throw error;
    }
  },

  // 获取分享文件夹中的特定文档
  async getDocumentInSharedFolder(shareToken, documentId) {
    try {
      const response = await apiRequest.get(`/documents/shared/${shareToken}/document/${documentId}`);
      const document = response.data;
      
      // 处理文档内容中的TOS图片链接
      if (document.content) {
        document.content = processContentImages(document.content);
      }
      
      return document;
    } catch (error) {
      console.error('获取分享文件夹中的文档失败:', error);
      throw error;
    }
  },

  async addCollaborator(documentId, collaboratorData) {
    try {
      const response = await apiRequest.post(`/documents/${documentId}/collaborators`, collaboratorData);
      return response.data;
    } catch (error) {
      console.error('添加协作者失败:', error);
      throw error;
    }
  },

  async removeCollaborator(documentId, collaboratorId) {
    try {
      const response = await apiRequest.delete(`/documents/${documentId}/collaborators/${collaboratorId}`);
      return response.data;
    } catch (error) {
      console.error('移除协作者失败:', error);
      throw error;
    }
  },

  async leaveCollaboration(documentId) {
    try {
      const response = await apiRequest.post(`/documents/${documentId}/leave`);
      return response.data;
    } catch (error) {
      console.error('退出协作失败:', error);
      throw error;
    }
  },

  async checkPermissions(documentId, permission = 'read') {
    try {
      const response = await apiRequest.get(`/documents/${documentId}/permissions`, {
        params: { permission }
      });
      return response.data;
    } catch (error) {
      console.error('检查权限失败:', error);
      throw error;
    }
  }
}

// TOS图片URL处理函数
export const isTOSImageUrl = (url) => {
  if (!url) return false
  return url.startsWith('https://tos-') && url.includes('volces.com')
}

export const convertTOSImageUrl = (originalUrl) => {
  if (!isTOSImageUrl(originalUrl)) {
    return originalUrl
  }
  
  // 将TOS直接访问链接转换为通过代理的链接
  return `/backend/documents/image_proxy?url=${encodeURIComponent(originalUrl)}`
}

/**
 * 处理内容中的TOS图片链接
 */
export const processContentImages = (content) => {
  if (!content) return content;
  
  // 匹配Markdown中的图片语法 ![alt](url)
  return content.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, (match, alt, url) => {
    if (isTOSImageUrl(url)) {
      const proxyUrl = convertTOSImageUrl(url);
      return `![${alt}](${proxyUrl})`;
    }
    return match;
  });
}

export default documentsApi 