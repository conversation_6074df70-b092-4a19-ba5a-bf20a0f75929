// api/documents.ts
import axios from 'axios';

// 定义文档接口类型
export interface Document {
  _id: string;
  title: string;
  content: string;
  parent_id?: string;
  creator_id: string;
  owner_id: string;
  collaborators: string[];
  permissions: {
    read: string[];
    write: string[];
    admin: string[];
  };
  created_at: string;
  updated_at: string;
  tags?: string[];
  metadata?: Record<string, any>;
}

// 创建文档请求接口
export interface DocumentCreate {
  title: string;
  content: string;
  parent_id?: string;
  tags?: string[];
  metadata?: Record<string, any>;
}

// 更新文档请求接口
export interface DocumentUpdate {
  title?: string;
  content?: string;
  parent_id?: string;
  tags?: string[];
  metadata?: Record<string, any>;
}

// 文档树节点接口
export interface DocumentTreeNode {
  id: string;
  title: string;
  parent_id?: string;
  children?: DocumentTreeNode[];
  created_at: string;
  updated_at: string;
  creator_id: string;
  owner_id: string;
}

// 创建 axios 实例 - 使用相对路径让Vite处理代理
const api = axios.create({
  baseURL: '/backend',
  headers: {
    'Content-Type': 'application/json',
  },
});

// 设置请求拦截器，添加 token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

/**
 * 检查是否为TOS存储链接
 */
export function isTOSImageUrl(url: string): boolean {
  return url.startsWith('https://tos-') && url.includes('volces.com');
}

/**
 * 将TOS链接转换为后端代理链接
 */
export function convertTOSImageUrl(url: string): string {
  if (!isTOSImageUrl(url)) {
    return url;
  }
  
  // 将TOS链接转换为后端代理链接
  const encodedUrl = encodeURIComponent(url);
  return `/backend/documents/image_proxy?url=${encodedUrl}`;
}

/**
 * 处理内容中的TOS图片链接
 */
export function processContentImages(content: string): string {
  if (!content) return content;
  
  // 匹配Markdown中的图片语法 ![alt](url)
  return content.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, (match, alt, url) => {
    if (isTOSImageUrl(url)) {
      const proxyUrl = convertTOSImageUrl(url);
      return `![${alt}](${proxyUrl})`;
    }
    return match;
  });
}

// 文档 API 方法
export const documentsApi = {
  // 获取文档树结构
  getDocumentTree: async (): Promise<DocumentTreeNode[]> => {
    const response = await api.get('/documents/tree');
    return response.data;
  },

  // 创建新文档
  createDocument: async (document: DocumentCreate): Promise<Document> => {
    const response = await api.post('/documents/', document);
    return response.data;
  },

  // 获取单个文档
  getDocument: async (documentId: string): Promise<Document> => {
    const response = await api.get(`/documents/${documentId}`);
    const document = response.data;
    
    // 处理文档内容中的TOS图片链接
    if (document.content) {
      document.content = processContentImages(document.content);
    }
    
    return document;
  },

  // 更新文档
  updateDocument: async (documentId: string, updateData: DocumentUpdate): Promise<Document> => {
    const response = await api.put(`/documents/${documentId}`, updateData);
    return response.data;
  },

  // 删除文档
  deleteDocument: async (documentId: string, force: boolean = false): Promise<boolean> => {
    const response = await api.delete(`/documents/${documentId}?force=${force}`);
    return response.status === 200;
  },

  // 获取用户的所有文档
  getUserDocuments: async (): Promise<Document[]> => {
    const response = await api.get('/documents/my');
    return response.data;
  },

  // 获取文档的协作者
  getDocumentCollaborators: async (documentId: string): Promise<string[]> => {
    const response = await api.get(`/documents/${documentId}/collaborators`);
    return response.data;
  },

  // 添加协作者
  addCollaborator: async (documentId: string, userId: string, permission: 'read' | 'write' | 'admin'): Promise<boolean> => {
    const response = await api.post(`/documents/${documentId}/collaborators`, {
      user_id: userId,
      permission: permission
    });
    return response.status === 200;
  },

  // 移除协作者
  removeCollaborator: async (documentId: string, userId: string): Promise<boolean> => {
    const response = await api.delete(`/documents/${documentId}/collaborators/${userId}`);
    return response.status === 200;
  },

  // 获取文档的子文档
  getChildDocuments: async (parentId: string): Promise<Document[]> => {
    const response = await api.get(`/documents/children/${parentId}`);
    return response.data;
  },

  // 移动文档（更改父文档）
  moveDocument: async (
    documentId: string, 
    newParentId: string | null, 
    position: string = "inside", 
    referenceNodeId?: string
  ): Promise<Document> => {
    const response = await api.put(`/documents/${documentId}/move`, {
      target_parent_id: newParentId,
      position: position,
      reference_node_id: referenceNodeId
    });
    return response.data;
  },

  // 导出文档为PDF
  exportDocumentToPdf: async (documentId: string): Promise<Blob> => {
    const response = await api.get(`/documents/${documentId}/export/pdf`, {
      responseType: 'blob'
    });
    return response.data;
  },

  // 导出文档为Markdown
  exportDocumentToMarkdown: async (documentId: string): Promise<Blob> => {
    const response = await api.get(`/documents/${documentId}/export/markdown`, {
      responseType: 'blob'
    });
    return response.data;
  },

  // 搜索文档
  searchDocuments: async (query: string): Promise<Document[]> => {
    const response = await api.get(`/documents/search?q=${encodeURIComponent(query)}`);
    return response.data;
  },

  // 上传图片
  uploadImage: async (file: File): Promise<{ url: string }> => {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await api.post('/documents/upload_image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    // 检查返回的URL是否为TOS链接，如果是则转换为代理链接
    const result = response.data;
    if (result.url && isTOSImageUrl(result.url)) {
      result.url = convertTOSImageUrl(result.url);
    }
    
    return result;
  },

  // 获取分享文档
  getSharedDocument: async (shareToken: string): Promise<Document> => {
    const response = await api.get(`/documents/shared/${shareToken}`);
    const document = response.data;
    
    // 处理文档内容中的TOS图片链接
    if (document.content) {
      document.content = processContentImages(document.content);
    }
    
    return document;
  },

  // 获取分享文件夹的文档树
  getSharedFolderTree: async (shareToken: string): Promise<DocumentTreeNode[]> => {
    const response = await api.get(`/documents/shared/${shareToken}/tree`);
    return response.data;
  },

  // 获取分享文件夹中的特定文档
  getDocumentInSharedFolder: async (shareToken: string, documentId: string): Promise<Document> => {
    const response = await api.get(`/documents/shared/${shareToken}/document/${documentId}`);
    const document = response.data;
    
    // 处理文档内容中的TOS图片链接
    if (document.content) {
      document.content = processContentImages(document.content);
    }
    
    return document;
  },

  // 更新分享文档
  updateSharedDocument: async (shareToken: string, updateData: DocumentUpdate): Promise<Document> => {
    const response = await api.put(`/documents/shared/${shareToken}`, updateData);
    return response.data;
  },

  // 分享文档
  shareDocument: async (documentId: string, options: { permissions: string[], expiry_days?: number }): Promise<any> => {
    const response = await api.post(`/documents/${documentId}/share`, options);
    return response.data;
  },

  // 撤销分享
  revokeShare: async (documentId: string): Promise<boolean> => {
    const response = await api.delete(`/documents/${documentId}/share`);
    return response.data.success;
  },

  // 将分享文档添加到用户笔记（协作模式）
  addSharedDocumentToNotes: async (shareToken: string): Promise<Document> => {
    const response = await api.post(`/documents/shared/${shareToken}/add_to_notes`);
    return response.data;
  },

  // 将分享文件夹添加到用户笔记（复制模式）
  addSharedFolderToNotes: async (shareToken: string): Promise<Document> => {
    const response = await api.post(`/documents/shared/${shareToken}/add_folder_to_notes`);
    return response.data;
  },

  // 将分享文件夹添加到用户笔记（协作模式）
  addSharedFolderToNotesCollaborative: async (shareToken: string): Promise<Document> => {
    const response = await api.post(`/documents/shared/${shareToken}/add_folder_to_notes_collaborative`);
    return response.data;
  },

  // 退出协作
  leaveCollaboration: async (documentId: string): Promise<boolean> => {
    const response = await api.post(`/documents/${documentId}/leave`);
    return response.data.success;
  }
};