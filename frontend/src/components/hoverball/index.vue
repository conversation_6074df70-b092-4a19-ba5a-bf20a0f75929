<template>
  <div class="hover-ball-container">
    <!-- 暂停/播放按钮 -->
    <div 
      class="hover-ball" 
      :style="ballStyle" 
      @mousedown="startDrag($event, 'main')"
      @touchstart="startDrag($event, 'main')"
      @click="handleClick"
      :class="{ 'is-processing': isProcessing }"
    >
      <v-icon :icon="isProcessing ? 'mdi-pause' : 'mdi-play'" color="white" />
    </div>
    
    <!-- 新增邮箱按钮 -->
    <div 
      class="hover-ball mail-ball" 
      :style="mailBallStyle"
      @mousedown="startDrag($event, 'mail')"
      @touchstart="startDrag($event, 'mail')"
      @click="togglePopout"
    >
      <v-icon icon="mdi-email" color="white" />
      <!-- 未解决问题的角标 -->
      <div v-if="unsolvedCount > 0" class="badge" :class="{'badge-animate': badgeAnimate}">{{unsolvedCount}}</div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, onUnmounted, watch } from 'vue';

export default defineComponent({
  name: 'HoverBall',
  props: {
    initialX: {
      type: Number,
      default: 20
    },
    initialY: {
      type: Number,
      default: 100
    },
    size: {
      type: Number,
      default: 48
    },
    margin: {
      type: Number,
      default: 20
    },
    isProcessing: {
      type: Boolean,
      default: false
    },
    unsolvedCount: {
      type: Number,
      default: 0
    }
  },
  emits: ['pause', 'resume', 'toggle-popout'],
  setup(props, { emit }) {
    // 位置状态
    const posX = ref(props.initialX);
    const posY = ref(props.initialY);
    
    // 拖动状态
    const isDragging = ref(false);
    const hasMoved = ref(false);
    const startX = ref(0);
    const startY = ref(0);
    const offsetX = ref(0);
    const offsetY = ref(0);
    const dragStartTime = ref(0);
    const activeBall = ref('main'); // 当前拖动的球
    
    // 角标动画状态
    const badgeAnimate = ref(false);
    
    // 边缘状态
    const isAtEdgeVertical = ref(false);
    
    // 监听unsolvedCount变化，触发角标动画
    watch(() => props.unsolvedCount, (newVal, oldVal) => {
      if (newVal !== oldVal) {
        badgeAnimate.value = true;
        setTimeout(() => {
          badgeAnimate.value = false;
        }, 600); // 动画持续时间600ms
      }
    });
    
    // 计算主球的样式
    const ballStyle = computed(() => {
      // 如果在上边缘或下边缘，并且两个球需要横向排列
      if (isAtEdgeVertical.value) {
        return {
          left: `${posX.value}px`,
          top: `${posY.value}px`,
          width: `${props.size}px`,
          height: `${props.size}px`
        };
      } else {
        return {
          left: `${posX.value}px`,
          top: `${posY.value}px`,
          width: `${props.size}px`,
          height: `${props.size}px`
        };
      }
    });
    
    // 计算邮箱球的样式
    const mailBallStyle = computed(() => {
      // 如果在上边缘或下边缘，两个球左右排列
      if (isAtEdgeVertical.value) {
        return {
          left: `${posX.value + props.size + 10}px`,
          top: `${posY.value}px`,
          width: `${props.size}px`,
          height: `${props.size}px`
        };
      } else {
        // 默认上下排列
        return {
          left: `${posX.value}px`,
          top: `${posY.value + props.size + 10}px`,
          width: `${props.size}px`,
          height: `${props.size}px`
        };
      }
    });
    
    // 开始拖动
    const startDrag = (event: MouseEvent | TouchEvent, ball: string) => {
      event.preventDefault();
      isDragging.value = true;
      hasMoved.value = false;
      dragStartTime.value = Date.now();
      activeBall.value = ball;
      
      // 获取鼠标/触摸起始位置
      if ('touches' in event) {
        startX.value = event.touches[0].clientX;
        startY.value = event.touches[0].clientY;
      } else {
        startX.value = event.clientX;
        startY.value = event.clientY;
      }
      
      // 计算偏移量
      offsetX.value = posX.value - startX.value;
      offsetY.value = posY.value - startY.value;
      
      // 添加移动和停止事件监听
      document.addEventListener('mousemove', onDrag);
      document.addEventListener('touchmove', onDrag);
      document.addEventListener('mouseup', stopDrag);
      document.addEventListener('touchend', stopDrag);
    };
    
    // 拖动中
    const onDrag = (event: MouseEvent | TouchEvent) => {
      if (!isDragging.value) return;
      
      let clientX, clientY;
      
      // 获取当前鼠标/触摸位置
      if ('touches' in event) {
        clientX = event.touches[0].clientX;
        clientY = event.touches[0].clientY;
      } else {
        clientX = event.clientX;
        clientY = event.clientY;
      }
      
      // 检查是否移动了足够距离来标记为拖动
      const moveDistanceX = Math.abs(clientX - startX.value);
      const moveDistanceY = Math.abs(clientY - startY.value);
      
      // 如果移动距离超过5像素，标记为已移动
      if (moveDistanceX > 5 || moveDistanceY > 5) {
        hasMoved.value = true;
      }
      
      // 计算新位置
      posX.value = clientX + offsetX.value;
      posY.value = clientY + offsetY.value;
      
      // 防止拖出屏幕
      const maxX = window.innerWidth - props.size;
      const maxY = window.innerHeight - props.size;
      
      posX.value = Math.max(0, Math.min(posX.value, maxX));
      posY.value = Math.max(0, Math.min(posY.value, maxY));
    };
    
    // 停止拖动，吸附到最近的屏幕边缘
    const stopDrag = () => {
      if (!isDragging.value) return;
      
      isDragging.value = false;
      
      // 移除事件监听
      document.removeEventListener('mousemove', onDrag);
      document.removeEventListener('touchmove', onDrag);
      document.removeEventListener('mouseup', stopDrag);
      document.removeEventListener('touchend', stopDrag);
      
      if (hasMoved.value) {
        // 计算到各边缘的距离
        const distToLeft = posX.value;
        const distToRight = window.innerWidth - (posX.value + props.size);
        const distToTop = posY.value;
        const distToBottom = window.innerHeight - (posY.value + props.size);
        
        // 找到最近的边缘
        const minDist = Math.min(distToLeft, distToRight, distToTop, distToBottom);
        
        // 吸附到最近的边缘，保留固定的边距
        if (minDist === distToLeft) {
          // 吸附到左边缘
          posX.value = props.margin;
          isAtEdgeVertical.value = false;
        } else if (minDist === distToRight) {
          // 吸附到右边缘
          posX.value = window.innerWidth - props.size - props.margin;
          isAtEdgeVertical.value = false;
        } else if (minDist === distToTop) {
          // 吸附到上边缘
          posY.value = props.margin;
          isAtEdgeVertical.value = true;
        } else {
          // 吸附到下边缘
          posY.value = window.innerHeight - props.size - props.margin;
          isAtEdgeVertical.value = true;
        }
        
        // 存储吸附后的位置到本地存储
        localStorage.setItem('hoverball_pos_x', posX.value.toString());
        localStorage.setItem('hoverball_pos_y', posY.value.toString());
        localStorage.setItem('hoverball_vertical_edge', isAtEdgeVertical.value.toString());
      }
    };
    
    // 处理点击事件，避免拖动触发
    const handleClick = (event: MouseEvent) => {
      // 只有在没有拖动并且点击时间小于300ms时才触发
      if (!hasMoved.value && (Date.now() - dragStartTime.value < 300)) {
        toggleProcessing();
      }
    };
    
    // 切换暂停/继续状态
    const toggleProcessing = () => {
      if (props.isProcessing) {
        emit('pause');
      } else {
        emit('resume');
      }
    };
    
    // 添加邮箱点击事件
    const togglePopout = () => {
      if (!hasMoved.value && (Date.now() - dragStartTime.value < 300)) {
        emit('toggle-popout');
      }
    };
    
    // 窗口大小变化时调整位置，避免超出边界
    const handleResize = () => {
      const maxX = window.innerWidth - props.size;
      const maxY = window.innerHeight - props.size;
      
      // 如果当前位置超出新的边界，则调整
      if (posX.value > maxX) {
        posX.value = maxX - props.margin;
      }
      
      if (posY.value > maxY) {
        posY.value = maxY - props.margin;
      }
      
      // 重新应用吸附逻辑
      const distToLeft = posX.value;
      const distToRight = window.innerWidth - (posX.value + props.size);
      const distToTop = posY.value;
      const distToBottom = window.innerHeight - (posY.value + props.size);
      
      // 找到最近的边缘并吸附
      const minDist = Math.min(distToLeft, distToRight, distToTop, distToBottom);
      if (minDist === distToLeft && distToLeft < props.margin * 2) {
        posX.value = props.margin;
        isAtEdgeVertical.value = false;
      } else if (minDist === distToRight && distToRight < props.margin * 2) {
        posX.value = window.innerWidth - props.size - props.margin;
        isAtEdgeVertical.value = false;
      } else if (minDist === distToTop && distToTop < props.margin * 2) {
        posY.value = props.margin;
        isAtEdgeVertical.value = true;
      } else if (minDist === distToBottom && distToBottom < props.margin * 2) {
        posY.value = window.innerHeight - props.size - props.margin;
        isAtEdgeVertical.value = true;
      }
      
      // 存储调整后的位置
      localStorage.setItem('hoverball_pos_x', posX.value.toString());
      localStorage.setItem('hoverball_pos_y', posY.value.toString());
      localStorage.setItem('hoverball_vertical_edge', isAtEdgeVertical.value.toString());
    };
    
    // 组件挂载时，从本地存储恢复位置并应用吸附逻辑
    onMounted(() => {
      // 恢复保存的位置
      const savedX = localStorage.getItem('hoverball_pos_x');
      const savedY = localStorage.getItem('hoverball_pos_y');
      const savedVerticalEdge = localStorage.getItem('hoverball_vertical_edge');
      
      if (savedX) {
        posX.value = parseFloat(savedX);
      }
      
      if (savedY) {
        posY.value = parseFloat(savedY);
      }
      
      if (savedVerticalEdge) {
        isAtEdgeVertical.value = savedVerticalEdge === 'true';
      }
      
      // 初始加载时应用边缘吸附
      setTimeout(() => {
        const distToLeft = posX.value;
        const distToRight = window.innerWidth - (posX.value + props.size);
        const distToTop = posY.value;
        const distToBottom = window.innerHeight - (posY.value + props.size);
        
        const minDist = Math.min(distToLeft, distToRight, distToTop, distToBottom);
        
        if (minDist === distToLeft) {
          posX.value = props.margin;
          isAtEdgeVertical.value = false;
        } else if (minDist === distToRight) {
          posX.value = window.innerWidth - props.size - props.margin;
          isAtEdgeVertical.value = false;
        } else if (minDist === distToTop) {
          posY.value = props.margin;
          isAtEdgeVertical.value = true;
        } else {
          posY.value = window.innerHeight - props.size - props.margin;
          isAtEdgeVertical.value = true;
        }
        
        localStorage.setItem('hoverball_pos_x', posX.value.toString());
        localStorage.setItem('hoverball_pos_y', posY.value.toString());
        localStorage.setItem('hoverball_vertical_edge', isAtEdgeVertical.value.toString());
      }, 100);
      
      // 添加窗口大小变化事件
      window.addEventListener('resize', handleResize);
    });
    
    // 组件卸载时，移除所有事件监听
    onUnmounted(() => {
      document.removeEventListener('mousemove', onDrag);
      document.removeEventListener('touchmove', onDrag);
      document.removeEventListener('mouseup', stopDrag);
      document.removeEventListener('touchend', stopDrag);
      window.removeEventListener('resize', handleResize);
    });
    
    return {
      posX,
      posY,
      ballStyle,
      mailBallStyle,
      isDragging,
      hasMoved,
      startDrag,
      onDrag,
      stopDrag,
      handleClick,
      toggleProcessing,
      togglePopout,
      badgeAnimate,
      isAtEdgeVertical
    };
  }
});
</script>

<style scoped>
.hover-ball-container {
  position: fixed;
  top: -120px;
  z-index: 1000;
}

.hover-ball {
  position: fixed;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #654C8C;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: background-color 0.3s;
}

.hover-ball:hover {
  background-color: #7B5EB5;
}

.hover-ball.is-processing {
  background-color: #E35B5B;
}

.hover-ball.is-processing:hover {
  background-color: #FF6B6B;
}

.mail-ball {
  background-color: #3C83F6;
  position: relative;
}

.mail-ball:hover {
  background-color: #4B93FF;
}

.badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #FF4444;
  color: white;
  border-radius: 50%;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease-in-out;
}

.badge-animate {
  animation: badge-pulse 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes badge-pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.5); }
  100% { transform: scale(1); }
}
</style>