<template>
  <div class="user-status-button">
    <v-menu
      v-model="menuOpen"
      :close-on-content-click="false"
      location="bottom end"
      transition="scale-transition"
    >
      <template v-slot:activator="{ props }">
        <v-btn
          v-bind="props"
          variant="text"
          :icon="user.isAuthenticated ? 'mdi-account-circle' : 'mdi-account-outline'"
          :color="user.isAuthenticated ? 'primary' : 'grey'"
          :loading="user.loading"
          class="tool-btn"
          :title="user.isAuthenticated ? '用户信息' : '登录'"
        >
          <v-icon :icon="user.isAuthenticated ? 'mdi-account-circle' : 'mdi-account-outline'" />
        </v-btn>
      </template>

      <v-card min-width="200px" class="user-menu-card">
        <v-card-text class="pa-3">
          <template v-if="user.isAuthenticated">
            <div class="text-center mb-3">
              <v-avatar color="primary" size="64">
                <span class="text-h5 font-weight-bold text-white">{{ userInitials }}</span>
              </v-avatar>
            </div>
            <div class="user-info text-center mb-3">
              <div class="text-subtitle-1 font-weight-bold">{{ user.user.name }}</div>
              <div class="text-caption text-medium-emphasis">{{ user.user.email }}</div>
              <div class="text-caption text-medium-emphasis">{{ user.user.username }}</div>
            </div>
            
            <!-- Token使用统计 -->
            <v-divider class="my-2"></v-divider>
            <div class="token-usage-info mt-2">
              <div class="text-subtitle-2 font-weight-bold text-center mb-1">Token使用统计</div>
              <div v-if="tokenUsageLoading" class="text-center">
                <v-progress-circular indeterminate size="20" width="2" color="primary"></v-progress-circular>
              </div>
              <template v-else-if="tokenUsage">
                <div class="d-flex justify-space-between">
                  <span class="text-caption text-medium-emphasis">输入Token:</span>
                  <span class="text-caption font-weight-medium">{{ formatNumber(tokenUsage.total_input_tokens) }}</span>
                </div>
                <div class="d-flex justify-space-between">
                  <span class="text-caption text-medium-emphasis">输出Token:</span>
                  <span class="text-caption font-weight-medium">{{ formatNumber(tokenUsage.total_output_tokens) }}</span>
                </div>
                <div class="d-flex justify-space-between">
                  <span class="text-caption text-medium-emphasis">总计成本:</span>
                  <span class="text-caption font-weight-medium">${{ formatNumber(tokenUsage.total_cost, 4) }}</span>
                </div>
                <div class="d-flex justify-space-between">
                  <span class="text-caption text-medium-emphasis">会话数量:</span>
                  <span class="text-caption font-weight-medium">{{ tokenUsage.sessions_count }}</span>
                </div>
              </template>
              <div v-else class="text-center text-caption text-medium-emphasis">
                暂无使用数据
              </div>
            </div>
          </template>
          <template v-else>
            <div class="text-center mb-3">
              <v-avatar color="grey-lighten-2" size="64">
                <v-icon icon="mdi-account" size="32" color="grey"></v-icon>
              </v-avatar>
            </div>
            <div class="user-info text-center mb-3">
              <div class="text-subtitle-1 font-weight-bold">未登录</div>
              <div class="text-caption text-medium-emphasis">请登录以使用全部功能</div>
            </div>
          </template>
        </v-card-text>
        
        <v-divider></v-divider>
        
        <v-card-actions class="pa-3">
          <template v-if="user.isAuthenticated">
            <v-btn
              block
              color="error"
              variant="tonal"
              prepend-icon="mdi-logout"
              @click="handleLogout"
              :loading="loggingOut"
            >
              退出登录
            </v-btn>
          </template>
          <template v-else>
            <v-btn
              block
              color="primary"
              variant="tonal"
              prepend-icon="mdi-login"
              @click="redirectToLogin"
            >
              登录
            </v-btn>
          </template>
        </v-card-actions>
      </v-card>
    </v-menu>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, watch } from 'vue';
import { useAuthStore } from '@/stores/authStore';
import { useRouter } from 'vue-router';
import { researchApi } from '@/api/chat';

export default defineComponent({
  name: 'UserStatusButton',
  setup() {
    const authStore = useAuthStore();
    const router = useRouter();
    const menuOpen = ref(false);
    const loggingOut = ref(false);
    const tokenUsage = ref<any>(null);
    const tokenUsageLoading = ref(false);

    // 计算用户首字母头像
    const userInitials = computed(() => {
      if (!authStore.user.name) return '?';
      
      const nameParts = authStore.user.name.split(' ');
      if (nameParts.length > 1) {
        return (nameParts[0][0] + nameParts[1][0]).toUpperCase();
      }
      return nameParts[0][0].toUpperCase();
    });

    // 处理登出
    const handleLogout = async () => {
      try {
        loggingOut.value = true;
        await authStore.logout();
        menuOpen.value = false;
        // 可选：登出后重定向到登录页
        // router.push('/login');
      } catch (error) {
        console.error('登出失败:', error);
      } finally {
        loggingOut.value = false;
      }
    };

    // 重定向到登录页
    const redirectToLogin = () => {
      menuOpen.value = false;
      router.push('/login');
    };

    // 获取用户token使用统计
    const loadTokenUsage = async () => {
      if (!authStore.isAuthenticated) return;
      
      try {
        tokenUsageLoading.value = true;
        const response = await researchApi.getUserTokenUsage();
        if (response.status === 'success') {
          tokenUsage.value = response.data;
        }
      } catch (error) {
        console.error('获取Token使用统计失败:', error);
      } finally {
        tokenUsageLoading.value = false;
      }
    };

    // 格式化数字，添加千位分隔符
    const formatNumber = (num: number, decimals: number = 0) => {
      if (num === undefined || num === null) return '0';
      return num.toLocaleString('en-US', { 
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
      });
    };

    // 在组件挂载时加载token使用数据
    onMounted(() => {
      if (authStore.isAuthenticated) {
        loadTokenUsage();
      }
    });

    // 监听菜单打开事件，刷新token使用数据
    watch(menuOpen, (newValue) => {
      if (newValue && authStore.isAuthenticated) {
        loadTokenUsage();
      }
    });

    // 监听认证状态变化
    watch(() => authStore.isAuthenticated, (isAuthenticated) => {
      if (isAuthenticated) {
        loadTokenUsage();
      } else {
        tokenUsage.value = null;
      }
    });

    return {
      user: authStore,
      menuOpen,
      userInitials,
      handleLogout,
      redirectToLogin,
      loggingOut,
      tokenUsage,
      tokenUsageLoading,
      formatNumber
    };
  }
});
</script>

<style scoped>
.user-status-button {
  margin-right: 8px;
}

.user-menu-card {
  overflow: visible;
}

.token-usage-info {
  font-size: 0.85rem;
}
</style> 