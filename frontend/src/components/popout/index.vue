<template>
  <div class="popout-container" :class="{ 'hidden': isHidden, 'visible': !isHidden }">
    <!-- 弹出框主体内容 -->
    <div class="popout-content" @click.stop>
      <!-- 主体内容，使用自适应高度 -->
      <div class="popout-main-content">
        <!-- 如果没有问题，显示提示 -->
        <div v-if="!hasQuestions" class="no-questions">
          <p>目前没有需要澄清的问题</p>
        </div>
        
        <!-- 有问题时显示标签页 -->
        <div v-if="hasQuestions" class="questions-tabs">
          <!-- 标签页导航 -->
          <div class="tabs-header">
            <div 
              v-for="(q, index) in questions" 
              :key="q.id"
              class="tab-button"
              :class="{ 'active': activeTabIndex === index }"
              @click="activeTabIndex = index"
            >
              问题 {{ index + 1 }}
            </div>
          </div>
          
          <!-- 标签页内容 -->
          <div class="tab-content">
            <template v-for="(q, index) in questions" :key="q.id">
              <div v-if="activeTabIndex === index" class="question-panel">
                <!-- 问题显示区域 -->
                <div class="question-container">
                  <p class="question-text">{{ q.content }}</p>
                  <div class="question-progress">
                    {{ q.activeQuestionIndex + 1 }}/{{ q.totalQuestions }}
                  </div>
                </div>
                
                <!-- 选项区域 - 多选框 -->
                <div class="options-container" v-if="q.options && q.options.length > 0">
                  <v-checkbox
                    v-for="(option, optIndex) in q.options"
                    :key="optIndex"
                    v-model="selectedOptions[q.id]"
                    :label="option"
                    :value="option"
                    density="compact"
                    color="primary"
                    hide-details
                    class="option-checkbox"
                  ></v-checkbox>
                </div>
                
                <!-- 输入框区域 -->
                <div class="input-container">
                  <v-text-field
                    v-model="userInputs[q.id]"
                    :label="inputLabel"
                    :placeholder="inputPlaceholder"
                    variant="outlined"
                    density="compact"
                    hide-details
                    @keyup.enter="() => submitInput(q.id)"
                  ></v-text-field>
                </div>
                
                <!-- 提交按钮 -->
                <div class="submit-container">
                  <v-btn 
                    color="primary" 
                    variant="tonal" 
                    @click="() => submitInput(q.id)" 
                    :disabled="isSubmitDisabled(q.id)"
                    :loading="isSubmitting"
                  >
                    {{ submitText }}
                  </v-btn>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 添加遮罩层用于捕获点击事件 -->
  <div v-if="!isHidden" class="popout-overlay" @click="closePopout"></div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, reactive, watch, onMounted, onUnmounted } from 'vue';

export interface Question {
  id: string;
  content: string;
  options: string[];
  activeQuestionIndex: number;
  totalQuestions: number;
}

export default defineComponent({
  name: 'PopoutComponent',
  
  props: {
    questions: {
      type: Array as () => Question[],
      default: () => []
    },
    inputLabel: {
      type: String,
      default: '您的回答'
    },
    inputPlaceholder: {
      type: String,
      default: '请输入您的回答...'
    },
    submitText: {
      type: String,
      default: '提交'
    },
    visible: {
      type: Boolean,
      default: false
    },
    minHeight: {
      type: String,
      default: 'auto'
    },
    maxHeight: {
      type: String,
      default: 'auto'
    },
    width: {
      type: String,
      default: '100%'
    },
    maxWidth: {
      type: String,
      default: '800px'
    },
    requireSelection: {
      type: Boolean,
      default: false
    }
  },
  
  emits: ['submit', 'close', 'update:visible'],
  
  setup(props, { emit }) {
    // 控制弹出框状态
    const isHidden = computed(() => !props.visible);
    const userInputs = reactive<Record<string, string>>({});
    const selectedOptions = reactive<Record<string, string[]>>({});
    const isSubmitting = ref(false);
    const activeTabIndex = ref(0);
    
    // 判断是否有问题要显示
    const hasQuestions = computed(() => {
      return props.questions && props.questions.length > 0;
    });
    
    // 当新问题来临时确保每个问题都有对应的输入和选项数组
    watch(() => props.questions.length, (newLength, oldLength) => {      
      // 保证每个问题都有对应的输入和选项数组
      props.questions.forEach(q => {
        if (!userInputs[q.id]) userInputs[q.id] = '';
        if (!selectedOptions[q.id]) selectedOptions[q.id] = [];
      });
      
      // 如果有新问题但没有选中标签，选中第一个
      if (newLength > 0 && activeTabIndex.value >= newLength) {
        activeTabIndex.value = 0;
      }

      // 当问题数量变化且不为0时，自动显示弹窗
      if (newLength !== oldLength && newLength > 0) {
        // 不需要修改props.visible，因为它是由父组件控制的
        // 这里通过其他方式通知父组件可以显示弹窗
        emit('update:visible', true);
      }
    }, { immediate: true });
    
    // 计算提交按钮是否禁用
    const isSubmitDisabled = (questionId: string) => {
      // 获取当前问题
      const q = props.questions.find(q => q.id === questionId);
      if (!q) return true;
      
      // 如果要求选择选项，但没有选择任何选项，则禁用提交按钮
      if (q.options && q.options.length > 0 && props.requireSelection) {
        const selected = selectedOptions[questionId] || [];
        return selected.length === 0 && !(userInputs[questionId] && userInputs[questionId].trim());
      } else {
        // 如果不要求选择选项，但输入框也是空的，则禁用提交按钮
        const selected = selectedOptions[questionId] || [];
        return selected.length === 0 && !(userInputs[questionId] && userInputs[questionId].trim());
      }
    };
    
    // 提交输入
    const submitInput = (questionId: string) => {
      if (isSubmitDisabled(questionId) || isSubmitting.value) return;
      
      isSubmitting.value = true;
      
      const submitData = {
        input: userInputs[questionId] || '',
        selectedOptions: selectedOptions[questionId] || []
      };
      console.log('submitData', submitData);
      console.log('questionId', questionId);
      emit('submit', submitData, questionId);

      setTimeout(() => {
        isSubmitting.value = false;
      }, 1000);
    };
    
    // 关闭弹窗
    const closePopout = () => {
      emit('close');
      emit('update:visible', false);
    };
    
    // 添加ESC键关闭功能
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && !isHidden.value) {
        closePopout();
      }
    };
    
    onMounted(() => {
      window.addEventListener('keydown', handleKeyDown);
    });
    
    onUnmounted(() => {
      window.removeEventListener('keydown', handleKeyDown);
    });
    
    return {
      isHidden,
      userInputs,
      selectedOptions,
      isSubmitting,
      activeTabIndex,
      hasQuestions,
      isSubmitDisabled,
      submitInput,
      clearInputsAndSelections: () => {
        Object.keys(userInputs).forEach(key => {
          userInputs[key] = '';
        });
        Object.keys(selectedOptions).forEach(key => {
          selectedOptions[key] = [];
        });
        activeTabIndex.value = 0;
      },
      closePopout
    };
  }
});
</script>

<style scoped>
.popout-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.2);
  max-width: v-bind(maxWidth);
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  z-index: 999;
  display: flex;
  color: #000000;
  flex-direction: column;
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.popout-container.hidden {
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.95);
  pointer-events: none;
}

.popout-container.visible {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.popout-content {
  overflow-y: auto;
  flex: 1;
  padding: 20px;
}

.popout-main-content {
  min-height: v-bind(minHeight);
  max-height: v-bind(maxHeight);
}

.tabs-header {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 15px;
}

.tab-button {
  padding: 8px 16px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.tab-button.active {
  border-bottom-color: #654C8C;
  color: #654C8C;
  font-weight: bold;
}

.question-container {
  margin-bottom: 20px;
}

.question-text {
  font-size: 1.1rem;
  margin-bottom: 10px;
  color: #000000;
}

.question-progress {
  font-size: 0.85rem;
  color: #000000;
}

.options-container {
  margin-bottom: 20px;
}

.input-container {
  margin-bottom: 20px;
}

.submit-container {
  display: flex;
  justify-content: flex-end;
}

.no-questions {
  text-align: center;
  padding: 40px 0;
  color: #000000;
}

.popout-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 998;
}

@media (max-width: 600px) {
  .popout-container {
    width: 95%;
    max-width: none;
  }
}
</style>