<template>
  <v-dialog 
    v-model="localValue" 
    max-width="520px" 
    @click:outside="closeDialog"
    :persistent="false"
  >
    <v-card class="share-dialog-card">
      <!-- 标题栏 -->
      <v-card-title class="share-dialog-header">
        <div class="d-flex align-center">
          <div class="share-icon-wrapper">
            <v-icon color="primary" size="24">mdi-share-variant</v-icon>
          </div>
          <span class="share-title">分享与协作</span>
        </div>
        <v-btn
          icon
          variant="text"
          size="small"
          @click="closeDialog"
          class="close-btn"
        >
          <v-icon size="20">mdi-close</v-icon>
        </v-btn>
      </v-card-title>

      <v-divider />

      <v-card-text class="share-dialog-content">
        <!-- 分享状态切换 -->
        <div class="share-toggle-section">
          <div class="d-flex align-center justify-space-between">
            <div class="share-toggle-info">
              <h3 class="share-toggle-title">公开分享</h3>
              <p class="share-toggle-desc">
                启用后，任何人都可以通过链接访问此笔记
              </p>
            </div>
            <v-switch
              v-model="shareEnabled"
              color="primary"
              @change="handleShareToggle"
              :loading="isToggling"
              hide-details
              class="share-switch"
            />
          </div>
        </div>

        <!-- 分享设置 -->
        <div v-if="shareEnabled" class="share-settings">
          <v-divider class="my-6" />
          
          <!-- 权限设置 -->
          <div class="permission-section">
            <h4 class="section-title">访问权限</h4>
            <v-radio-group 
              v-model="sharePermissions" 
              @update:model-value="updateShareSettings"
              class="permission-radio-group"
            >
              <v-radio value="read" color="primary" class="permission-radio">
                <template #label>
                  <div class="permission-label">
                    <div class="permission-name">只读</div>
                    <div class="permission-desc">访问者只能查看内容，无法编辑</div>
                  </div>
                </template>
              </v-radio>
              <v-radio value="write" color="primary" class="permission-radio">
                <template #label>
                  <div class="permission-label">
                    <div class="permission-name">编辑</div>
                    <div class="permission-desc">访问者可以查看和编辑内容</div>
                  </div>
                </template>
              </v-radio>
            </v-radio-group>
          </div>

          <!-- 过期时间设置 -->
          <div class="expiry-section">
            <h4 class="section-title">过期时间</h4>
            <v-select
              v-model="expiryDays"
              :items="expiryOptions"
              variant="outlined"
              density="comfortable"
              @update:model-value="updateShareSettings"
              class="expiry-select"
              hide-details
            />
          </div>

          <v-divider class="my-6" />

          <!-- 分享链接 -->
          <div class="share-link-section">
            <h4 class="section-title">分享链接</h4>
            <div class="share-link-container">
              <v-text-field
                :model-value="shareUrl"
                variant="outlined"
                density="comfortable"
                readonly
                hide-details
                class="share-link-input"
              />
              <v-btn
                variant="elevated"
                @click="copyShareLink"
                :color="linkCopied ? 'success' : 'primary'"
                class="copy-btn"
                :disabled="!shareUrl"
              >
                <v-icon size="18">{{ linkCopied ? 'mdi-check' : 'mdi-content-copy' }}</v-icon>
                {{ linkCopied ? '已复制' : '复制' }}
              </v-btn>
            </div>
            
            <!-- 分享信息卡片 -->
            <div class="share-info-card">
              <div class="info-row">
                <v-icon size="16" color="primary" class="info-icon">mdi-shield-account</v-icon>
                <span class="info-text">权限：{{ getPermissionText(sharePermissions) }}</span>
              </div>
              <div class="info-row">
                <v-icon size="16" color="primary" class="info-icon">mdi-clock-outline</v-icon>
                <span class="info-text">
                  {{ expiryDays ? `${expiryDays} 天后过期` : '永不过期' }}
                </span>
              </div>
              <!-- 文件夹分享特殊标识 -->
              <div v-if="isFolder" class="info-row folder-share-info">
                <v-icon size="16" color="success" class="info-icon">mdi-folder-multiple</v-icon>
                <span class="info-text">文件夹分享 - 包含所有子文档</span>
              </div>
            </div>
            
            <!-- 文件夹分享说明 -->
            <div v-if="isFolder" class="folder-share-notice">
              <v-alert
                type="info"
                variant="tonal"
                class="folder-notice-alert"
                density="compact"
              >
                <template #prepend>
                  <v-icon>mdi-information</v-icon>
                </template>
                <div class="folder-notice-content">
                  <h4 class="folder-notice-title">文件夹分享说明</h4>
                  <ul class="folder-notice-list">
                    <li>访问者可以浏览整个文件夹结构</li>
                    <li>所有子文档都会包含在分享中</li>
                    <li>文件夹结构将以树形方式展示</li>
                  </ul>
                </div>
              </v-alert>
            </div>
          </div>
        </div>

        <!-- 分享功能说明 -->
        <div v-else class="share-intro">
          <div class="intro-content">
            <div class="intro-icon">
              <v-icon size="48" color="grey-lighten-1">mdi-share-variant-outline</v-icon>
            </div>
            <h3 class="intro-title">分享笔记</h3>
            <p class="intro-desc">启用分享后，您可以：</p>
            
            <div class="feature-list">
              <div class="feature-item">
                <v-icon color="success" size="18" class="feature-icon">mdi-check-circle</v-icon>
                <span class="feature-text">生成公开访问链接</span>
              </div>
              <div class="feature-item">
                <v-icon color="success" size="18" class="feature-icon">mdi-check-circle</v-icon>
                <span class="feature-text">设置访问权限和过期时间</span>
              </div>
              <div class="feature-item">
                <v-icon color="success" size="18" class="feature-icon">mdi-check-circle</v-icon>
                <span class="feature-text">随时撤销分享链接</span>
              </div>
            </div>
          </div>
        </div>
      </v-card-text>

      <!-- 底部操作栏 -->
      <v-divider />
      <v-card-actions class="share-dialog-actions">
        <v-btn
          v-if="shareEnabled"
          color="error"
          variant="text"
          @click="revokeShare"
          :loading="isRevoking"
          class="revoke-btn"
        >
          撤销分享
        </v-btn>
        <v-spacer />
        <v-btn
          color="grey-darken-1"
          variant="text"
          @click="closeDialog"
          class="close-text-btn"
        >
          关闭
        </v-btn>
      </v-card-actions>
    </v-card>

    <!-- 成功提示 -->
    <v-snackbar v-model="showSuccess" color="success" timeout="3000">
      {{ successMessage }}
    </v-snackbar>

    <!-- 错误提示 -->
    <v-snackbar v-model="showError" color="error" timeout="5000">
      {{ errorMessage }}
    </v-snackbar>
  </v-dialog>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue'
import { documentsApi } from '@/api/documents'

export default {
  name: 'ShareDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    documentId: {
      type: String,
      default: ''
    },
    documentTitle: {
      type: String,
      default: '未命名文档'
    },
    ownerId: {
      type: String,
      default: ''
    },
    currentUserId: {
      type: String,
      default: ''
    },
    isFolder: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'share-updated'],
  setup(props, { emit }) {
    const localValue = computed({
      get: () => props.modelValue,
      set: (val) => emit('update:modelValue', val)
    })

    // 分享相关状态
    const shareEnabled = ref(false)
    const sharePermissions = ref('read')
    const expiryDays = ref(null)
    const shareUrl = ref('')
    const linkCopied = ref(false)
    const isFolder = ref(props.isFolder) // 本地状态
    
    // 加载状态
    const isToggling = ref(false)
    const isRevoking = ref(false)
    
    // 消息状态
    const showSuccess = ref(false)
    const showError = ref(false)
    const successMessage = ref('')
    const errorMessage = ref('')

    // 配置选项
    const expiryOptions = [
      { title: '永不过期', value: null },
      { title: '1 天', value: 1 },
      { title: '7 天', value: 7 },
      { title: '30 天', value: 30 },
      { title: '90 天', value: 90 }
    ]

    // 方法
    const getPermissionText = (permission) => {
      const map = {
        'read': '只读',
        'write': '编辑'
      }
      return map[permission] || permission
    }

    const handleShareToggle = async () => {
      isToggling.value = true
      try {
        if (!props.documentId) {
          showMessage('请先选择一个文档', 'error')
          shareEnabled.value = false // 确保状态正确
          return
        }
        
        console.log('开始切换分享状态:', {
          documentId: props.documentId,
          shareEnabled: shareEnabled.value,
          sharePermissions: sharePermissions.value,
          expiryDays: expiryDays.value
        })
        
        if (shareEnabled.value) {
          // 启用分享
          const shareOptions = {
            permissions: [sharePermissions.value],
            expiry_days: expiryDays.value
          }
          console.log('发送分享请求:', shareOptions)
          
          const result = await documentsApi.shareDocument(props.documentId, shareOptions)
          console.log('分享成功，结果:', result)
          
          // 根据是否为文件夹分享生成不同的URL
          if (result.is_folder) {
            shareUrl.value = `${window.location.origin}/share/folder/${result.share_token}`
          } else {
            shareUrl.value = `${window.location.origin}/share/${result.share_token}`
          }
          
          // 设置文件夹标识
          isFolder.value = result.is_folder || false
          
          showMessage(result.is_folder ? '文件夹分享已启用' : '分享已启用', 'success')
          emit('share-updated', result)
        } else {
          // 禁用分享
          console.log('撤销分享')
          await documentsApi.revokeShare(props.documentId)
          shareUrl.value = ''
          isFolder.value = false
          showMessage('分享已撤销', 'success')
          emit('share-updated', null)
        }
      } catch (error) {
        console.error('分享操作失败:', error)
        shareEnabled.value = !shareEnabled.value // 恢复状态
        showMessage('操作失败，请重试', 'error')
      } finally {
        isToggling.value = false
      }
    }

    const updateShareSettings = async () => {
      if (!shareEnabled.value) return
      
      try {
        console.log('更新分享设置:', {
          documentId: props.documentId,
          sharePermissions: sharePermissions.value,
          expiryDays: expiryDays.value
        })
        
        const result = await documentsApi.shareDocument(props.documentId, {
          permissions: [sharePermissions.value],
          expiry_days: expiryDays.value
        })
        
        console.log('更新分享设置成功:', result)
        // 根据是否为文件夹分享生成不同的URL
        if (result.is_folder) {
          shareUrl.value = `${window.location.origin}/share/folder/${result.share_token}`
        } else {
          shareUrl.value = `${window.location.origin}/share/${result.share_token}`
        }
        isFolder.value = result.is_folder || false // 更新文件夹标识
        showMessage('分享设置已更新', 'success')
        emit('share-updated', result)
      } catch (error) {
        console.error('更新分享设置失败:', error)
        console.error('错误详情:', {
          message: error.message,
          response: error.response?.data,
          status: error.response?.status
        })
        
        // 显示具体的错误信息
        let errorMessage = '更新失败，请重试'
        if (error.response?.data?.detail) {
          errorMessage = error.response.data.detail
        } else if (error.response?.status === 401) {
          errorMessage = '请先登录'
        } else if (error.response?.status === 403) {
          errorMessage = '没有权限执行此操作'
        } else if (error.message) {
          errorMessage = error.message
        }
        
        showMessage(errorMessage, 'error')
      }
    }

    const copyShareLink = async () => {
      try {
        await navigator.clipboard.writeText(shareUrl.value)
        linkCopied.value = true
        showMessage('链接已复制到剪贴板', 'success')
        setTimeout(() => {
          linkCopied.value = false
        }, 2000)
      } catch (error) {
        console.error('复制失败:', error)
        showMessage('复制失败，请手动复制', 'error')
      }
    }

    const revokeShare = async () => {
      isRevoking.value = true
      try {
        await documentsApi.revokeShare(props.documentId)
        shareEnabled.value = false
        shareUrl.value = ''
        showMessage('分享已撤销', 'success')
        emit('share-updated', null)
      } catch (error) {
        console.error('撤销分享失败:', error)
        showMessage('撤销失败，请重试', 'error')
      } finally {
        isRevoking.value = false
      }
    }

    const showMessage = (message, type) => {
      if (type === 'success') {
        successMessage.value = message
        showSuccess.value = true
      } else {
        errorMessage.value = message
        showError.value = true
      }
    }

    const closeDialog = () => {
      localValue.value = false
    }

    return {
      localValue,
      shareEnabled,
      sharePermissions,
      expiryDays,
      shareUrl,
      linkCopied,
      isFolder,
      isToggling,
      isRevoking,
      showSuccess,
      showError,
      successMessage,
      errorMessage,
      expiryOptions,
      getPermissionText,
      handleShareToggle,
      updateShareSettings,
      copyShareLink,
      revokeShare,
      closeDialog
    }
  }
}
</script>

<style scoped>
.share-dialog-card {
  border-radius: 16px;
  overflow: hidden;
}

.share-dialog-header {
  padding: 20px 24px 16px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
}

.share-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: rgba(103, 126, 234, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.share-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
}

.close-btn {
  color: #6c757d;
  transition: all 0.2s ease;
}

.close-btn:hover {
  color: #495057;
  background-color: rgba(0, 0, 0, 0.05);
}

.share-dialog-content {
  padding: 24px;
}

.share-toggle-section {
  margin-bottom: 8px;
}

.share-toggle-info {
  flex: 1;
}

.share-toggle-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.share-toggle-desc {
  font-size: 0.875rem;
  color: #6c757d;
  margin: 0;
  line-height: 1.4;
}

.share-switch {
  margin-left: 16px;
}

.share-settings {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16px;
}

.permission-section {
  margin-bottom: 32px;
}

.permission-radio-group {
  margin-top: 8px;
}

.permission-radio {
  margin-bottom: 12px;
}

.permission-label {
  margin-left: 8px;
}

.permission-name {
  font-size: 0.95rem;
  font-weight: 500;
  color: #2c3e50;
  line-height: 1.2;
}

.permission-desc {
  font-size: 0.8rem;
  color: #6c757d;
  margin-top: 2px;
  line-height: 1.3;
}

.expiry-section {
  margin-bottom: 32px;
}

.expiry-select {
  margin-top: 8px;
}

.share-link-section {
  margin-bottom: 16px;
}

.share-link-container {
  display: flex;
  gap: 12px;
  align-items: center;
  margin: 16px 0;
}

.share-link-input {
  flex: 1;
}

.copy-btn {
  min-width: 90px;
  height: 48px;
  border-radius: 8px;
  font-weight: 500;
  text-transform: none;
  box-shadow: 0 2px 8px rgba(103, 126, 234, 0.2);
}

.share-info-card {
  background: rgba(103, 126, 234, 0.05);
  border: 1px solid rgba(103, 126, 234, 0.1);
  border-radius: 12px;
  padding: 16px;
  margin-top: 16px;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-icon {
  margin-right: 8px;
}

.info-text {
  font-size: 0.875rem;
  color: #495057;
  font-weight: 500;
}

.share-intro {
  text-align: center;
  padding: 32px 16px;
}

.intro-content {
  max-width: 320px;
  margin: 0 auto;
}

.intro-icon {
  margin-bottom: 20px;
}

.intro-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.intro-desc {
  font-size: 0.9rem;
  color: #6c757d;
  margin-bottom: 24px;
}

.feature-list {
  text-align: left;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-icon {
  margin-right: 12px;
  flex-shrink: 0;
}

.feature-text {
  font-size: 0.875rem;
  color: #495057;
  line-height: 1.4;
}

.share-dialog-actions {
  padding: 16px 24px;
  background-color: #fafbfc;
}

.revoke-btn {
  font-weight: 500;
  text-transform: none;
}

.close-text-btn {
  font-weight: 500;
  text-transform: none;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .share-dialog-header {
    padding: 16px 20px 12px 20px;
  }
  
  .share-dialog-content {
    padding: 20px;
  }
  
  .share-link-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .copy-btn {
    width: 100%;
  }
}

/* 文件夹分享相关样式 */
.folder-share-info {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0fff0 100%);
  border-radius: 6px;
  padding: 8px 12px;
  margin-top: 8px;
}

.folder-share-info .info-icon {
  color: #4caf50 !important;
}

.folder-share-info .info-text {
  color: #2e7d32;
  font-weight: 500;
}

.folder-share-notice {
  margin-top: 16px;
}

.folder-notice-alert {
  border-radius: 8px;
  border: 1px solid rgba(33, 150, 243, 0.2);
}

.folder-notice-content {
  padding: 4px 0;
}

.folder-notice-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1976d2;
  margin-bottom: 8px;
}

.folder-notice-list {
  margin: 0;
  padding-left: 16px;
  color: #424242;
}

.folder-notice-list li {
  font-size: 0.8rem;
  line-height: 1.4;
  margin-bottom: 4px;
}

.folder-notice-list li:last-child {
  margin-bottom: 0;
}
</style> 