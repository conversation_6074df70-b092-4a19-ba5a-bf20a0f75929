<template>
  <div class="input-card-wrapper">
    <!-- 添加上下文按钮 -->
    <div class="context-button-wrapper">
      <div class="context-header" ref="contextHeaderRef">
        <v-menu
          v-model="showContextMenu"
          :close-on-content-click="false"
          location="top"
          :position="'top'"
          :offset="10"
          :max-width="300"
          :attach="true"
        >
          <template v-slot:activator="{ props }">
            <v-btn
              class="context-button"
              :class="{ 'compact-mode': isCompactMode }"
              color="#654c8c"
              variant="outlined"
              v-bind="props"
            >
              <span class="context-at-symbol" v-if="isCompactMode">@</span>
              <span class="context-text-desktop" v-else>Add Context</span>
            </v-btn>
            <v-switch
              :model-value="isCognitionEnabled"
              color="#654c8c"
              density="compact"
              hide-details
              class="cognition-switch"
              :class="{ 'compact-mode': isCompactMode }"
              @update:model-value="handleCognitionSwitch"
            >
              <template v-slot:label>
                <v-icon class="switch-icon-mobile" v-if="isCompactMode">mdi-brain</v-icon>
                <span class="switch-label-desktop cognition-label" v-else>Cognition</span>
              </template>
            </v-switch>
            <v-switch
              :model-value="isSearchEnabled"
              color="#654c8c"
              density="compact"
              hide-details
              class="search-switch"
              :class="{ 'compact-mode': isCompactMode }"
              @update:model-value="handleSearchSwitch"
            >
              <template v-slot:label>
                <v-icon class="switch-icon-mobile" v-if="isCompactMode">mdi-magnify</v-icon>
                <span class="switch-label-desktop search-label" v-else>Search</span>
              </template>
            </v-switch>
          </template>
          <v-card class="context-menu">
            <v-tabs v-model="activeTab" color="#654c8c" align-tabs="center" style="min-width: 360px;">
              <v-tab value="file" style="min-width: 90px; font-size: 14px;">
                <v-icon start>mdi-file-upload</v-icon>
                文件
              </v-tab>
              <v-tab value="web" style="min-width: 90px; font-size: 14px;">
                <v-icon start>mdi-web</v-icon>
                网页
              </v-tab>
              <v-tab value="chat" style="min-width: 90px; font-size: 14px;">
                <v-icon start>mdi-chat</v-icon>
                对话
              </v-tab>
              <v-tab value="note" style="min-width: 90px; font-size: 14px;">
                <v-icon start>mdi-notebook</v-icon>
                笔记
              </v-tab>
            </v-tabs>

            <v-window v-model="activeTab">
              <!-- 文件上传标签页 -->
              <v-window-item value="file">
                <v-list>
                  <v-list-item>
                    <v-file-input
                      v-model="selectedFile"
                      accept=".pdf,.ppt,.doc,.pptx,.docx,.png,.jpg,.jpeg,.md,.txt"
                      label="上传文件"
                      prepend-icon="mdi-upload"
                      @update:model-value="handleFileSelect"
                      hide-details
                      density="compact"
                      :max-size="80 * 1024 * 1024"
                    ></v-file-input>
                  </v-list-item>
                </v-list>
              </v-window-item>

              <!-- 网页链接标签页 -->
              <v-window-item value="web">
                <v-list>
                  <v-list-item>
                    <v-text-field
                      v-model="webUrl"
                      label="输入网页链接"
                      prepend-icon="mdi-link"
                      hide-details
                      density="compact"
                      placeholder="https://..."
                      disabled
                    ></v-text-field>
                  </v-list-item>
                  <v-list-item class="text-center">
                    <span class="text-caption text-grey">功能开发中...</span>
                  </v-list-item>
                </v-list>
              </v-window-item>

              <!-- 对话选择标签页 -->
              <v-window-item value="chat">
                <v-list>
                  <v-list-item>
                    <v-select
                      v-model="selectedChat"
                      :items="chatHistory"
                      item-title="prompt"
                      item-value="id"
                      label="选择历史对话"
                      prepend-icon="mdi-chat-history"
                      hide-details
                      density="compact"
                      placeholder="暂无历史对话"
                      :loading="isLoadingChats"
                      disabled
                    ></v-select>
                  </v-list-item>
                  <v-list-item class="text-center">
                    <span class="text-caption text-grey">功能开发中...</span>
                  </v-list-item>
                </v-list>
              </v-window-item>

              <!-- 笔记标签页 -->
              <v-window-item value="note">
                <div style="max-height: 350px; min-width: 320px; overflow-y: auto;">
                  <NoteSelect
                    :nodes="noteTree"
                    :selected-id="selectedNote?.id"
                    @node-selected="handleNoteSelect"
                  />
                </div>
              </v-window-item>
            </v-window>

            <v-divider></v-divider>

            <v-list-item class="d-flex justify-end">
              <v-btn
                color="primary"
                variant="text"
                size="small"
                @click="confirmContext"
                :disabled="!canConfirm"
              >
                确认
              </v-btn>
              <v-btn
                color="grey"
                variant="text"
                size="small"
                @click="closeMenu"
              >
                取消
              </v-btn>
            </v-list-item>
          </v-card>
        </v-menu>

        <v-btn
          v-if="uploadedFiles.length > 0"
          class="collapse-btn"
          icon
          size="x-small"
          variant="text"
          @click="toggleFilesDisplay"
        >
          <v-icon>{{ isFilesExpanded ? 'mdi-chevron-down' : 'mdi-chevron-up' }}</v-icon>
        </v-btn>
      </div>

      <!-- 文件展示条 -->
      <div class="files-display" v-if="uploadedFiles.length > 0">
        <div class="files-scroll" v-show="isFilesExpanded">
          <div 
            v-for="(file, index) in uploadedFiles" 
            :key="index" 
            class="file-item"
            :class="{ 
              'file-item-loading': loadingStates[index] === true,
              'file-item-expanded': index === uploadedFiles.length - 1 && !isHovering
            }"
            @mouseenter="handleFileHover(index)"
            @mouseleave="handleFileLeave"
            @click="handleFileClick(file)"
          >
            <div class="file-content">
              <div class="file-icon">
                <v-icon :icon="getFileIcon(file)" size="small"></v-icon>
              </div>
              <div class="file-info">
                <div class="file-name">{{ getFileName(file) }}</div>
                <div class="file-details">
                  <div class="file-type">{{ getFileType(file) }}</div>
                  <div class="file-size">{{ formatFileSize(file) }}</div>
                </div>
              </div>
            </div>
            <v-btn
              class="delete-btn"
              icon
              size="x-small"
              variant="text"
              @click.stop="removeFile(index)"
              :disabled="loadingStates[index]"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
            <div v-if="loadingStates[index]" class="loading-overlay">
              <v-progress-circular
                indeterminate
                size="20"
                width="2"
                color="#654c8c"
              ></v-progress-circular>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="input-card">
      <v-textarea
        v-model="inputText"
        placeholder="请输入内容..."
        variant="plain"
        density="compact"
        hide-details
        class="input-textfield"
        @keydown.enter.exact.prevent="handleSend"
        @keydown.shift.enter.stop="handleNewLine"
        :disabled="disabled"
        bg-color="white"
        auto-grow
        rows="2"
        row-height="22"
      ></v-textarea>
      <v-btn
        class="send-button"
        color="#654c8c"
        size="small"
        @click="handleSend"
        :disabled="!canSend || disabled"
        :loading="disabled"
      >
        <img src="@/assets/send.png" alt="发送" class="send-icon" />
      </v-btn>
    </div>
    <div v-if="error" class="error-message">{{ error }}</div>
  </div>
</template>

<script lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { PropType } from 'vue';
import { researchApi } from '@/api/chat';
import { documentsApi } from '@/api/documents';
import TreeView from '@/components/TreeView.vue';
import { ExtendedFile } from '@/composables/useDeepConversation'
import NoteSelect from '@/components/NoteSelect.vue';
export default {
  name: 'InputComponent',
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    error: {
      type: String,
      default: ''
    },
    uploadedFiles: {
      type: Array as PropType<ExtendedFile[]>,
      required: true
    },
    loadingStates: {
      type: Array as PropType<boolean[]>,
      default: () => []
    },
    isCognitionEnabled: {
      type: Boolean,
      default: false
    },
    isSearchEnabled: {
      type: Boolean,
      default: false
    }
  },
  emits: ['send', 'uploadFile', 'removeFile', 'update:isCognitionEnabled', 'update:isSearchEnabled', 'previewFile'],
  setup(props, { emit }) {
    const inputText = ref('');
    const showContextMenu = ref(false);
    const selectedFile = ref<File | null>(null);
    const tempFile = ref<File | null>(null);
    const activeTab = ref<'file' | 'web' | 'chat' | 'note'>('file');
    const webUrl = ref('');
    const selectedChat = ref<string | null>(null);
    const chatHistory = ref<Array<{id: string, prompt: string}>>([]);
    const isLoadingChats = ref(false);
    const isFilesExpanded = ref(true);
    const isHovering = ref(false);
    const hoveredIndex = ref<number | null>(null);
    const noteTree = ref([]);
    const selectedNote = ref<any>(null);
    const isCompactMode = ref(false);
    const contextHeaderRef = ref<HTMLElement | null>(null);
    
    // 监听组件宽度变化
    const checkCompactMode = () => {
      if (contextHeaderRef.value) {
        const width = contextHeaderRef.value.offsetWidth;
        // 当组件宽度小于400px时启用紧凑模式
        isCompactMode.value = width < 400;
      }
    };

    // 设置 ResizeObserver
    onMounted(() => {
      fetchNoteTree();
      fetchChatHistory();
      
      // 初始检查
      checkCompactMode();
      
      // 设置 ResizeObserver 监听宽度变化
      if (contextHeaderRef.value && window.ResizeObserver) {
        const resizeObserver = new ResizeObserver(() => {
          checkCompactMode();
        });
        resizeObserver.observe(contextHeaderRef.value);
        
        // 在组件卸载时清理观察者
        onUnmounted(() => {
          resizeObserver.disconnect();
        });
      }
    });

    // 获取对话历史
    const fetchChatHistory = async () => {
      try {
        isLoadingChats.value = true;
        const chats = await researchApi.getMyChats();
        chatHistory.value = chats.chats || [];
      } catch (error) {
        console.error('获取对话历史失败:', error);
      } finally {
        isLoadingChats.value = false;
      }
    };

    // 获取笔记树
    const fetchNoteTree = async () => {
      const tree = await documentsApi.getDocumentTree();
      noteTree.value = tree.map((node: any) => ({
        id: node.id,
        name: node.name || node.title,
        parentId: node.parent_id || null,
        expanded: false
      }));
    };

    // 判断是否可以发送
    const canSend = computed(() => {
      return inputText.value.trim().length > 0;
    });
    
    // 判断是否可以确认
    const canConfirm = computed(() => {
      switch (activeTab.value) {
        case 'file':
          return !!tempFile.value;
        case 'web':
          return !!webUrl.value;
        case 'chat':
          return !!selectedChat.value;
        case 'note':
          return true;
        default:
          return false;
      }
    });
    
    // 处理发送事件
    const handleSend = () => {
      if (!canSend.value || props.disabled) return;
      
      // 发送当前输入内容
      emit('send', inputText.value);
      
      // 清空输入框
      inputText.value = '';
    };
    
    // 处理Shift+Enter换行
    const handleNewLine = (event: KeyboardEvent) => {
      // 允许Shift+Enter执行默认的换行操作
      event.stopPropagation();
    };

    // 处理文件选择
    const handleFileSelect = (files: File | File[]) => {
      const file = Array.isArray(files) ? files[0] : files;
      if (file) {
        console.log('选择的文件:', {
          name: file.name,
          type: file.type,
          size: file.size,
          lastModified: file.lastModified,
          isFile: file instanceof File,
          isBlob: file instanceof Blob
        });
        tempFile.value = file;
      }
    };

    // 构建id到节点的映射
    const noteMap = computed(() => {
      const map = {};
      noteTree.value.forEach(n => { map[n.id] = n; });
      return map;
    });
    // 递归获取所有叶子节点
    const getAllLeafNotes = (node) => {
      const children = noteTree.value.filter(n => n.parentId === node.id);
      if (children.length === 0) {
        return [node];
      }
      let leafs = [];
      for (const child of children) {
        leafs = leafs.concat(getAllLeafNotes(child));
      }
      return leafs;
    };

    // 确认添加上下文
    const confirmContext = async () => {
      switch (activeTab.value) {
        case 'file':
          if (tempFile.value) {
            emit('uploadFile', tempFile.value);
            tempFile.value = null;
          }
          break;
        case 'web':
          if (webUrl.value) {
            // TODO: 处理网页链接
            console.log('添加网页链接:', webUrl.value);
            webUrl.value = '';
          }
          break;
        case 'chat':
          if (selectedChat.value) {
            // TODO: 处理历史对话
            console.log('选择历史对话:', selectedChat.value);
            selectedChat.value = null;
          }
          break;
        case 'note':
          if (selectedNote.value) {
            const node = noteMap.value[selectedNote.value.id];
            // 先把自己加进去
            let notesToProcess = [node];
            // 再加所有叶子节点（如果有子节点）
            const children = noteTree.value.filter(n => n.parentId === node.id);
            if (children.length > 0) {
              notesToProcess = notesToProcess.concat(getAllLeafNotes(node));
            }
            // 去重（防止自己是叶子节点时重复）
            const uniqueNotes = Array.from(new Set(notesToProcess.map(n => n.id))).map(id => noteMap.value[id]);
            for (const note of uniqueNotes) {
              const doc = await documentsApi.getDocument(note.id);
              const blob = new Blob([doc.content || ''], { type: 'text/markdown' });
              const fileName = `${doc.title || '未命名笔记'}.md`;
              const file = new File([blob], fileName, { type: 'text/markdown' });
              emit('uploadFile', file);
            }
            selectedNote.value = null;
          }
          break;
        default:
          break;
      }
      closeMenu();
    };

    // 关闭菜单
    const closeMenu = () => {
      showContextMenu.value = false;
      selectedFile.value = null;
      tempFile.value = null;
      webUrl.value = '';
      selectedChat.value = null;
      activeTab.value = 'file';
    };

    // 切换文件展示状态
    const toggleFilesDisplay = () => {
      isFilesExpanded.value = !isFilesExpanded.value;
    };

    // 获取文件图标
    const getFileIcon = (file: ExtendedFile): string => {
      const type = (file instanceof File ? file.type : file.type || '').toLowerCase();
      if (type.includes('pdf')) return 'mdi-file-pdf-box';
      if (type.includes('docs') || type.includes('doc')) return 'mdi-file-word';
      if (type.includes('xlsx') || type.includes('xls')) return 'mdi-file-excel';
      if (type.includes('pptx') || type.includes('ppt')) return 'mdi-file-powerpoint';
      if (type.includes('png') || type.includes('jpg') || type.includes('jpeg')) return 'mdi-file-image';
      if (type.includes('txt') || type.includes('md')) return 'mdi-file-document';
      return 'mdi-file';
    };

    // 获取文件名
    const getFileName = (file: ExtendedFile): string => {
      return file.name;
    };

    // 获取文件类型
    const getFileType = (file: ExtendedFile): string => {
      const type = (file instanceof File ? file.type : file.type || '').toLowerCase();
      if (type.includes('pdf')) return 'PDF文档';
      if (type.includes('docx') || type.includes('doc')) return 'Word文档';
      if (type.includes('pptx') || type.includes('ppt')) return 'PPT文档';
      if (type.includes('xlsx') || type.includes('xls')) return 'Excel表格';
      if (type.includes('png') || type.includes('jpg') || type.includes('jpeg')) return '图片文件';
      if (type.includes('txt') || type.includes('md')) return '文本文件';
      return '未知类型';
    };
    
    // 格式化文件大小
    const formatFileSize = (file: ExtendedFile): string => {
      const bytes = file instanceof File ? file.size : (file.size || 0);
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };
    
    // 删除文件
    const removeFile = (index: number) => {
      emit('removeFile', index);
    };

    // 处理文件hover
    const handleFileHover = (index: number) => {
      isHovering.value = true;
      hoveredIndex.value = index;
    };

    // 处理文件离开
    const handleFileLeave = () => {
      isHovering.value = false;
      hoveredIndex.value = null;
    };

    // 处理笔记选择
    const handleNoteSelect = (node: any) => {
      selectedNote.value = node;
    };

    // 处理认知搜索开关
    const handleCognitionSwitch = (value: boolean) => {
      console.log('[Deep Conversation] 认知搜索开关:', value);
      emit('update:isCognitionEnabled', value);
    };

    // 处理搜索开关
    const handleSearchSwitch = (value: boolean) => {
      console.log('[Deep Conversation] 搜索开关:', value);
      emit('update:isSearchEnabled', value);
    };

    // 处理文件点击
    const handleFileClick = (file: ExtendedFile) => {
      emit('previewFile', file);
    };

    return {
      inputText,
      canSend,
      handleSend,
      handleNewLine,
      showContextMenu,
      selectedFile,
      tempFile,
      handleFileSelect,
      confirmContext,
      closeMenu,
      formatFileSize,
      getFileIcon,
      getFileName,
      getFileType,
      removeFile,
      activeTab,
      webUrl,
      selectedChat,
      chatHistory,
      canConfirm,
      isLoadingChats,
      isFilesExpanded,
      toggleFilesDisplay,
      isHovering,
      hoveredIndex,
      handleFileHover,
      handleFileLeave,
      noteTree,
      handleNoteSelect,
      selectedNote,
      handleCognitionSwitch,
      handleSearchSwitch,
      handleFileClick,
      isCompactMode,
      contextHeaderRef,
    };
  },
  components: {
    TreeView,
    NoteSelect
  }
}
</script>

<style scoped>
.input-card-wrapper {
  padding: 8px 16px;
  background-color: transparent;
  width: 90%;
}

.context-button-wrapper {
  width: 90%;
  margin: 0 auto 4px auto;
  position: relative;
  overflow: hidden;
}

.context-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 4px;
  overflow: hidden;
  flex-wrap: nowrap;
}

.context-button {
  width: 30% !important;
  text-transform: none !important;
  font-size: 15px !important;
  font-weight: 500 !important;
  letter-spacing: 0.3px !important;
  border-radius: 8px !important;
  height: 30px !important;
  min-width: 80px !important;
  flex-shrink: 0;
  overflow: hidden;
}

.context-menu {
  min-width: 420px;
  max-width: 420px;
  padding: 0;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.context-menu :deep(.v-tabs) {
  background-color: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.context-menu :deep(.v-tab) {
  text-transform: none;
  font-size: 14px;
  font-weight: 500;
  min-width: 90px;
  letter-spacing: 0.3px;
}

.context-menu :deep(.v-window) {
  background-color: white;
}

.context-menu :deep(.v-list) {
  padding: 12px;
}

.context-menu :deep(.v-list-item) {
  min-height: 52px;
  padding: 0;
}

.context-menu :deep(.v-divider) {
  margin: 0;
}

.context-menu :deep(.v-btn) {
  text-transform: none;
  letter-spacing: 0.3px;
  font-weight: 500;
}

.files-display {
  width: 100%;
  overflow: hidden;
  background-color: #f8f9fa;
  border-radius: 12px;
  padding: 2px;
  margin-top: 4px;
}

.files-scroll {
  display: flex;
  overflow-x: auto;
  gap: 12px;
  padding: 2px 0;
  scrollbar-width: thin;
  min-height: 48px;
  transition: all 0.3s ease;
}

.files-scroll::-webkit-scrollbar {
  height: 6px;
}

.files-scroll::-webkit-scrollbar-thumb {
  background-color: #654c8c;
  border-radius: 6px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 10px;
  min-width: 48px;
  width: 48px;
  height: 48px;
  padding: 8px;
  background-color: white;
  border-radius: 10px;
  border: 1px solid #eef0f2;
  position: relative;
  transition: all 0.3s ease;
  overflow: hidden;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.file-item:hover,
.file-item-expanded {
  min-width: 240px;
  width: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.file-item:hover .file-info,
.file-item-expanded .file-info {
  opacity: 1;
  transform: translateX(0);
}

.file-item:hover .delete-btn,
.file-item-expanded .delete-btn {
  opacity: 0.7;
}

.file-item-loading {
  opacity: 0.7;
  pointer-events: none;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  z-index: 1;
  backdrop-filter: blur(2px);
}

.file-content {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  min-width: 0;
}

.file-icon {
  color: #654c8c;
  flex-shrink: 0;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-info {
  min-width: 0;
  flex: 1;
  opacity: 0;
  transform: translateX(-10px);
  transition: all 0.3s ease;
  white-space: nowrap;
}

.file-name {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
  width: 80%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-details {
  display: flex;
  gap: 10px;
  margin-top: 4px;
}

.file-type {
  font-size: 12px;
  color: #64748b;
  text-transform: uppercase;
  font-weight: 500;
}

.file-size {
  font-size: 12px;
  color: #64748b;
}

.delete-btn {
  color: #64748b !important;
  opacity: 0;
  transition: all 0.3s ease;
  position: absolute;
  right: 6px;
  top: 50%;
  transform: translateY(-50%);
}

.delete-btn:hover {
  opacity: 1 !important;
  color: #ef4444 !important;
}

.input-card {
  position: relative;
  width: 90%;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 16px;
  padding: 12px 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  min-height: 80px;
}

.input-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.input-textfield {
  padding-right: 48px;
  font-size: 15px;
  margin-top: 0;
}

.input-textfield :deep(.v-field__field) {
  padding-top: 0;
}

.input-textfield :deep(.v-field__input) {
  min-height: 48px !important;
  padding: 4px;
  font-size: 15px;
  line-height: 1.5;
}

.input-textfield :deep(textarea) {
  min-height: 48px;
  line-height: 24px;
}

.send-button {
  position: absolute;
  right: 12px;
  bottom: 12px;
  margin: 0;
  width: 44px !important;
  height: 44px !important;
  min-width: 44px !important;
  min-height: 44px !important;
  border-radius: 50%;
  transition: all 0.3s ease;
  background-color: #654C8C !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0 !important;
  box-shadow: 0 2px 8px rgba(101, 76, 140, 0.2);
}

.send-button:hover:not(:disabled) {
  transform: scale(1.05);
  background-color: #5a3f7a !important;
  box-shadow: 0 4px 12px rgba(101, 76, 140, 0.3);
}

.send-button:active {
  background-color: #4f3769 !important;
  transform: scale(0.98);
}

.send-button.v-btn--loading {
  background-color: #654C8C !important;
}

.send-icon {
  width: 20px;
  height: 20px;
  filter: brightness(0) invert(1);
}

.error-message {
  color: #ef4444;
  font-size: 13px;
  margin-top: 6px;
  text-align: center;
  width: 90%;
  margin-left: auto;
  margin-right: auto;
  font-weight: 500;
}

.collapse-btn {
  color: #64748b !important;
  opacity: 0.7;
  transition: all 0.3s ease;
  margin-left: auto;
}

.collapse-btn:hover {
  opacity: 1;
  color: #654c8c !important;
  transform: scale(1.1);
}

.cognition-switch {
  margin-left: 8px;
  flex-shrink: 1;
  min-width: 0;
}

.cognition-label {
  font-size: 12px;
  font-weight: 500;
  color: #654c8c;
  text-transform: uppercase;
  white-space: nowrap;
}

.search-switch {
  margin-left: 8px;
  flex-shrink: 1;
  min-width: 0;
}

.search-label {
  font-size: 12px;
  font-weight: 500;
  color: #654c8c;
  text-transform: uppercase;
  white-space: nowrap;
}

/* @ 符号样式 */
.context-at-symbol {
  font-size: 16px;
  font-weight: 600;
  color: #654c8c;
}

/* 紧凑模式样式 */
.context-button.compact-mode {
  width: auto !important;
  min-width: 36px !important;
  padding: 0 8px !important;
}

.cognition-switch.compact-mode,
.search-switch.compact-mode {
  margin-left: 4px;
  flex-shrink: 0;
}

.cognition-switch.compact-mode :deep(.v-selection-control),
.search-switch.compact-mode :deep(.v-selection-control) {
  min-height: 24px;
}

.cognition-switch.compact-mode :deep(.v-label),
.search-switch.compact-mode :deep(.v-label) {
  font-size: 10px;
  opacity: 1;
}

.switch-icon-mobile {
  font-size: 14px;
  margin-right: 2px;
}

/* 保留媒体查询作为后备方案 */
@media (max-width: 768px) {
  .context-header {
    gap: 8px;
  }
  
  .context-button {
    font-size: 13px !important;
  }
  
  .cognition-switch,
  .search-switch {
    margin-left: 4px;
  }
  
  .cognition-label,
  .search-label {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .context-button-wrapper {
    width: 95%;
  }
  
  .context-header {
    gap: 4px;
  }
}
</style>