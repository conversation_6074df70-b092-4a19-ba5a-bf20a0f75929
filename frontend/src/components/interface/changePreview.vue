<template>
  <v-dialog 
    v-model="dialog" 
    max-width="45vw" 
    max-height="90vh"
    persistent
    scrollable
  >
    <v-card class="change-preview-dialog">
      <v-card-title class="dialog-header">
        <div class="header-content">
          <v-icon icon="mdi-compare" class="header-icon" />
          <span class="header-title">编辑对比</span>
        </div>
        <div class="change-stats">
          <span v-if="changeStats.added > 0" class="stat-badge stat-added">
            +{{ changeStats.added }}
          </span>
          <span v-if="changeStats.deleted > 0" class="stat-badge stat-deleted">
            -{{ changeStats.deleted }}
          </span>
          <span class="stat-badge stat-neutral">
            {{ beforeContent.length }} → {{ afterContent.length }} 字符
          </span>
        </div>
        <v-btn
          icon
          variant="text"
          size="small"
          @click="closeDialog"
        >
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      
      <v-card-text class="dialog-content">
        <div class="unified-diff-container">
          <div class="diff-content">
            <div class="content-text" v-html="unifiedDiffContent"></div>
          </div>
        </div>
      </v-card-text>
      
      <v-card-actions class="dialog-actions">
        <v-spacer />
        <v-btn
          variant="outlined"
          @click="closeDialog"
        >
          关闭
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import { ref, computed } from 'vue';
import * as DiffMatchPatch from 'diff-match-patch';

// 专业diff算法接口
interface UnifiedDiffLine {
  type: 'context' | 'insert' | 'delete';
  text: string;
  lineNumber?: number;
}

// HTML转义函数
function escapeHtml(text: string): string {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}

// 使用diff-match-patch生成统一的diff
function generateUnifiedDiff(oldText: string, newText: string): UnifiedDiffLine[] {
  const dmp = new DiffMatchPatch.diff_match_patch();
  
  // 生成diff
  const diffs = dmp.diff_main(oldText, newText);
  
  // 优化diff结果
  dmp.diff_cleanupSemantic(diffs);
  
  const result: UnifiedDiffLine[] = [];
  
  diffs.forEach(([operation, text]) => {
      switch (operation) {
        case DiffMatchPatch.DIFF_DELETE:
          result.push({
            type: 'delete',
          text: text
          });
          break;
        case DiffMatchPatch.DIFF_INSERT:
          result.push({
            type: 'insert',
          text: text
          });
          break;
        case DiffMatchPatch.DIFF_EQUAL:
          result.push({
            type: 'context',
          text: text
          });
          break;
      }
  });
  
  return result;
}

// 渲染统一diff内容
function renderUnifiedDiff(diffLines: UnifiedDiffLine[]): string {
  let html = '';
  
  diffLines.forEach((diffBlock) => {
    const { type, text } = diffBlock;
    
    if (text === '') {
      return;
    }
    
    // 处理包含换行符的文本块
    const lines = text.split('\n');
    
    lines.forEach((line, lineIndex) => {
      const escapedLine = escapeHtml(line);
      const isLastLine = lineIndex === lines.length - 1;
      
      // 处理空行
      if (line.length === 0) {
        // 对于空行，只有在非最后一行或者原文本以换行结尾时才添加br
        if (!isLastLine || (isLastLine && text.endsWith('\n'))) {
          html += '<br>';
        }
        return;
      }
      
      // 根据类型添加相应的样式
      switch (type) {
        case 'delete':
          html += `<span class="diff-inline diff-inline-deleted">-${escapedLine}</span>`;
          break;
        case 'insert':
          html += `<span class="diff-inline diff-inline-inserted">+${escapedLine}</span>`;
          break;
        case 'context':
          html += `<span class="diff-inline diff-inline-context">${escapedLine}</span>`;
        break;
    }
      
      // 添加换行符，除非是最后一行且原文本不以换行结尾
      if (!isLastLine || (isLastLine && text.endsWith('\n'))) {
        html += '<br>';
      }
    });
  });
  
  return html;
}

export default {
  name: 'ChangePreview',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    beforeContent: {
      type: String,
      default: ''
    },
    afterContent: {
      type: String,
      default: ''
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const dialog = computed({
      get: () => props.modelValue,
      set: (value: boolean) => emit('update:modelValue', value)
    });

    const closeDialog = () => {
      dialog.value = false;
    };

    // 生成统一diff结果
    const unifiedDiffLines = computed(() => {
      if (!props.beforeContent && !props.afterContent) {
        return [];
      }
      return generateUnifiedDiff(props.beforeContent || '', props.afterContent || '');
    });

    // 渲染统一diff内容
    const unifiedDiffContent = computed(() => {
      return renderUnifiedDiff(unifiedDiffLines.value);
    });

    // 计算变化统计
    const changeStats = computed(() => {
      const lines = unifiedDiffLines.value;
      const added = lines.filter(line => line.type === 'insert').length;
      const deleted = lines.filter(line => line.type === 'delete').length;
      
      return { 
        added, 
        deleted,
        beforeChars: props.beforeContent.length,
        afterChars: props.afterContent.length
      };
    });

    return {
      dialog,
      closeDialog,
      unifiedDiffContent,
      changeStats
    };
  }
};
</script>

<style scoped>
.change-preview-dialog {
  height: 90vh;
  display: flex;
  flex-direction: column;
}

.dialog-header {
  background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
  color: white;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 24px;
}

.header-title {
  font-size: 1.2rem;
  font-weight: 600;
}

.change-stats {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.stat-added {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.stat-deleted {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.stat-neutral {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.dialog-content {
  flex: 1;
  padding: 0;
  overflow: hidden;
}

.unified-diff-container {
  height: 100%;
  background: #f8f9fa;
}

.diff-content {
  height: 100%;
  overflow-y: auto;
  background: white;
}

.content-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
  color: #333 !important;
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  padding: 20px !important;
}

/* 行内diff样式 */
:deep(.diff-inline) {
  display: inline !important;
  padding: 2px 4px !important;
  margin: 0 !important;
  border-radius: 3px !important;
  font-weight: 500 !important;
  white-space: pre-wrap !important;
  word-break: break-word !important;
}

/* 删除内容样式 */
:deep(.diff-inline-deleted) {
  background-color: #ffeef0 !important;
  color: #cb2431 !important;
  border: 1px solid #f4c6d0 !important;
  text-decoration: line-through !important;
}

/* 删除内容前缀样式 */
:deep(.diff-inline-deleted::before) {
  content: '';
  margin-right: 2px;
}

/* 插入内容样式 */
:deep(.diff-inline-inserted) {
  background-color: #e6ffec !important;
  color: #22863a !important;
  border: 1px solid #9dd4a8 !important;
}

/* 插入内容前缀样式 */
:deep(.diff-inline-inserted::before) {
  content: '';
  margin-right: 2px;
}

/* 上下文内容样式 */
:deep(.diff-inline-context) {
  background-color: transparent !important;
  color: #333 !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
  text-decoration: none !important;
}

/* 空行和换行处理 */
:deep(.content-text br) {
  line-height: 1.6 !important;
  display: block !important;
  margin: 0 !important;
  content: '' !important;
}

.dialog-actions {
  flex-shrink: 0;
  padding: 16px 24px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

/* 滚动条样式 */
.diff-content::-webkit-scrollbar {
  width: 12px;
}

.diff-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.diff-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 6px;
}

.diff-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .change-preview-dialog {
    max-width: 90vw !important; /* 在小屏幕上仍使用较大宽度 */
  }
  
  .change-stats {
    display: none;
  }
  
  .header-title {
    font-size: 1rem;
  }
  
  .content-text {
    font-size: 13px !important;
    padding: 16px !important;
  }
  
  :deep(.diff-inline) {
    padding: 1px 3px !important;
    margin: 0 !important;
    font-size: 12px !important;
  }
}
</style>