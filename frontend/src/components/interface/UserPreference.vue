<template>
  <div class="user-preference-container">
    <div class="preference-card">
      <div class="preference-header">
        <div class="icon-container">
          <div class="icon-wrapper">
            <v-icon icon="mdi-tune" class="preference-icon" />
          </div>
          <span class="preference-label">用户偏好设置</span>
        </div>
        <div class="chevron-icon" @click="toggleExpanded">
          <div class="chevron-down" :class="{ 'rotated': isExpanded }"></div>
        </div>
      </div>
      
      <div v-if="isExpanded" class="preference-content">
        <div class="unified-preference-container">
          <div 
            v-for="(preference, index) in preferences" 
            :key="`preference-${index}`"
            class="preference-item"
          >
            <div class="preference-item-header">
              <span class="preference-item-label">{{ preference.label }}</span>
              <div class="preference-value-badge">
                {{ getPreferenceValue(preference.key) }}
              </div>
            </div>
            <div class="preference-item-description">
              {{ preference.description }}
            </div>
            <div class="preference-item-control">
              <v-slider
                :model-value="getPreferenceValue(preference.key)"
                :color="preference.color || '#654C8C'"
                :min="1"
                :max="5"
                :step="1"
                thumb-label="always"
                track-color="#E8E6F6"
                hide-details
                @update:model-value="updatePreference(preference.key, $event)"
              >
                <template v-slot:thumb-label="{ modelValue }">
                  {{ modelValue }}
                </template>
              </v-slider>
              <div class="preference-range-labels">
                <span class="range-label-start">低</span>
                <span class="range-label-end">高</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { ref, defineProps, defineEmits, reactive, watch, toRef } from 'vue';

interface PreferenceItem {
  key: string;
  label: string;
  description: string;
  value: number;
  color?: string;
}

export type { PreferenceItem };
export default {
  name: 'UserPreference',
  props: {
    preferences: {
      type: Array as () => PreferenceItem[],
      default: () => [
        {
          key: 'professional',
          label: '专业性',
          description: '增强回答的专业深度和技术准确性',
          value: 3,
          color: '#654C8C'
        },
        {
          key: 'critical',
          label: '批判性',
          description: '提供更多质疑和多角度分析',
          value: 3,
          color: '#7B68EE'
        },
        {
          key: 'comparison',
          label: '表格对比',
          description: '优先使用表格形式进行对比分析',
          value: 3,
          color: '#9370DB'
        },
        {
            key: 'organization',
            label: '组织性',
            description: '生成文稿的组织结构',
            value: 3,
            color: '#9370DB'
        },
        {
            key: 'cutting-edge',
            label: '前沿性',
            description: '优先使用前沿技术进行分析',
            value: 3,
            color: '#9370DB'
        },
        {
            key: 'coverage',
            label: '覆盖面',
            description: '优先使用覆盖面广的分析',
            value: 3,
            color: '#9370DB'
        },
        {
            key: 'depth',
            label: '深度',
            description: '优先使用深度分析',
            value: 3,
            color: '#9370DB'
        }
      ]
    },
    initialExpanded: {
      type: Boolean,
      default: false
    }
  },
  emits: ['preferenceChange'],
  setup(props, { emit }) {
    const isExpanded = ref(props.initialExpanded);
    
    // 创建本地状态来跟踪偏好设置值，确保滑块能立即响应
    const localPreferences = reactive<{[key: string]: number}>({});
    
    // 创建preferences的响应式引用
    const preferencesRef = toRef(props, 'preferences');
    
    // 初始化本地状态
    const initializeLocalPreferences = () => {
      preferencesRef.value.forEach(pref => {
        localPreferences[pref.key] = pref.value;
      });
    };
    
    // 监听props变化，同步到本地状态
    watch(preferencesRef, (newPrefs) => {
      console.log('[UserPreference] preferences变化检测到:', newPrefs);
      newPrefs.forEach(pref => {
        if (localPreferences[pref.key] !== pref.value) {
          console.log(`[UserPreference] 更新本地偏好: ${pref.key} = ${pref.value}`);
          localPreferences[pref.key] = pref.value;
        }
      });
    }, { deep: true, immediate: true });
    
    const toggleExpanded = () => {
      isExpanded.value = !isExpanded.value;
    };
    
    const updatePreference = (key: string, value: number) => {
      console.log(`[UserPreference] 滑块值变化: ${key} = ${value}`);
      
      // 立即更新本地状态，确保UI响应
      localPreferences[key] = value;
      
      // 发出事件给父组件
      emit('preferenceChange', { key, value });
      console.log(`[UserPreference] 已发出事件 preferenceChange: { key: ${key}, value: ${value} }`);
    };
    
    const getPreferenceValue = (key: string) => {
      return localPreferences[key] ?? 3;
    };
    
    return {
      isExpanded,
      toggleExpanded,
      updatePreference,
      getPreferenceValue,
    };
  }
}
</script>

<style scoped>
.user-preference-container {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #f7f6fb;
  border-bottom: 1px solid rgba(217, 214, 239, 0.5);
  padding: 8px 16px 8px 8px;
}

.preference-card {
  background-color: #E8E6F6;
  border: 0.5px solid #C2BFD5;
  border-radius: 16px;
  box-shadow: none;
  padding: 16px 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  position: relative;
}

.preference-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.icon-container {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.icon-wrapper {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  background-color: #ffffff;
  justify-content: center;
  border-radius: 50%;
}

.preference-icon {
  font-size: 24px;
  color: #654C8C;
}

.preference-label {
  font-size: clamp(0.9rem, 2vw, 1.1rem);
  font-weight: 500;
  color: #333;
}

.chevron-icon {
  width: 36px;
  height: 36px;
  min-width: 36px;
  min-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background-color: #654C8C;
  border-radius: 50%;
  position: relative;
  flex-shrink: 0;
  margin-right: -8px;
}

.chevron-down {
  width: 8px;
  height: 8px;
  border-right: 2px solid white;
  border-bottom: 2px solid white;
  transform: rotate(45deg);
  margin-top: -4px;
  transition: transform 0.3s ease;
}

.chevron-down.rotated {
  transform: rotate(-135deg);
  margin-top: 4px;
}

.preference-content {
  padding: 8px 0;
  margin-top: 8px;
  max-height: 400px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(101, 76, 140, 0.3) transparent;
}

.preference-content::-webkit-scrollbar {
  width: 4px;
}

.preference-content::-webkit-scrollbar-track {
  background: transparent;
  margin: 4px 0;
}

.preference-content::-webkit-scrollbar-thumb {
  background-color: rgba(101, 76, 140, 0.3);
  border-radius: 4px;
}

.preference-content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(101, 76, 140, 0.5);
}

.unified-preference-container {
  background-color: #F0EBFF;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.preference-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.preference-item:not(:last-child) {
  border-bottom: 1px solid rgba(101, 76, 140, 0.15);
  padding-bottom: 16px;
}

.preference-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preference-item-label {
  font-size: 1rem;
  font-weight: 600;
  color: #654C8C;
}

.preference-value-badge {
  background-color: #654C8C;
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 12px;
  min-width: 28px;
  text-align: center;
}

.preference-item-description {
  font-size: 0.8rem;
  color: #666;
  line-height: 1.3;
  margin-bottom: 4px;
}

.preference-item-control {
  position: relative;
  margin-top: 2px;
}

.preference-range-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 4px;
  padding: 0 2px;
}

.range-label-start,
.range-label-end {
  font-size: 0.75rem;
  color: #9E9BB3;
  font-weight: 500;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .preference-card {
    padding: 12px 16px;
  }
  
  .icon-wrapper {
    width: 32px;
    height: 32px;
  }
  
  .preference-icon {
    font-size: 20px;
  }
  
  .chevron-icon {
    width: 32px;
    height: 32px;
  }
  
  .preference-content {
    max-height: 350px;
  }
  
  .unified-preference-container {
    padding: 14px;
    gap: 14px;
  }
  
  .preference-item:not(:last-child) {
    padding-bottom: 14px;
  }
  
  .preference-item {
    gap: 5px;
  }
}

@media screen and (max-width: 480px) {
  .user-preference-container {
    padding: 4px 12px 4px 4px;
  }
  
  .preference-card {
    padding: 10px 12px;
  }
  
  .icon-wrapper {
    width: 28px;
    height: 28px;
  }
  
  .preference-icon {
    font-size: 18px;
  }
  
  .chevron-icon {
    width: 28px;
    height: 28px;
  }
  
  .preference-content {
    max-height: 300px;
  }
  
  .unified-preference-container {
    padding: 12px;
    gap: 12px;
  }
  
  .preference-item:not(:last-child) {
    padding-bottom: 12px;
  }
  
  .preference-item {
    gap: 4px;
  }
  
  .preference-item-description {
    font-size: 0.75rem;
  }
}

/* 自定义v-slider样式 */
:deep(.v-slider) {
  margin: 4px 0;
  height: 20px;
}

:deep(.v-slider-track-container) {
  height: 4px;
}

:deep(.v-slider-thumb) {
  transition: transform 0.2s ease;
  width: 16px;
  height: 16px;
}

:deep(.v-slider-thumb:hover) {
  transform: scale(1.1);
}

:deep(.v-slider__thumb-label) {
  background-color: #654C8C !important;
  font-weight: 600;
  font-size: 0.75rem;
}
</style> 