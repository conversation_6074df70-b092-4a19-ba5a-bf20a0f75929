<template>
  <div class="interface-container">
    <v-card class="interface-card">
      <!-- 用户偏好设置模块 - 固定在顶部 -->
      <UserPreference 
        v-if="!isShareMode"
        :preferences="preferences"
        :initialExpanded="false"
        @preferenceChange="handlePreferenceChange"
      />
      
      <!-- 空状态展示 -->
      <div v-if="interfaceItems.length === 0" class="empty-state">
        <img src="@/assets/volcan.png" alt="Volcan" class="empty-state-icon" />
        <div class="empty-state-text">请输入您的问题开始对话</div>
      </div>
      
      <!-- 思考、行动、预览区域 -->
      <div v-else class="sections-container" ref="sectionsContainerRef">
        <!-- 使用v-for循环渲染各个区域 -->
        <div 
          v-for="section in interfaceItems" 
          :key="section.id" 
          class="section" 
          :class="[
            `${section.type}-section`, 
            { 'expanded': section.type === 'thought' && section.isExpanded }
          ]"
        >
          <div class="section-header">
            <div class="icon-container">
              <div class="icon-wrapper">
                <img :src="section.icon" :alt="section.label" class="section-icon" />
              </div>
              <span class="section-label">{{ section.label }}</span>
              <!-- 添加轮次指示器 -->
              <div v-if="section.round !== undefined" class="round-badge" :title="`轮次 ${section.round}`">
                {{ section.round }}
              </div>
              <!-- <span class="completion-info">
                {{ section.completionInfo }}
              </span> -->
            </div>
            <div 
              v-if="section.type === 'thought'" 
              class="chevron-icon" 
              @click="(event) => { event.preventDefault(); toggleThought(section.id); }"
            >
              <div class="chevron-down" :class="{ 'rotated': section.isExpanded }"></div>
            </div>
          </div>
          <div 
            class="section-content" 
            :ref="el => { if (section.type === 'thought') setThoughtRef(el, section.id); }" 
            @scroll="section.type === 'thought' ? handleThoughtScroll($event, section.id) : null"
          >
            <!-- 思考区域内容 -->
            <template v-if="section.type === 'thought'">
              <div v-if="section.isExpanded" class="expanded-content">
                <MarkdownRenderer
                  v-for="(paragraph, index) in section.content"
                  :key="`thought-p-${index}`"
                  :content="paragraph"
                  :isComplete="true"
                />
                
                <!-- 添加 Acting 区域 -->
                <div v-if="section.acting" class="acting-container">
                  <div class="acting-header" @click="toggleActing(section.id)">
                    <span class="acting-title">执行内容</span>
                    <div class="acting-chevron" :class="{ 'rotated': actingExpandStatus.get(section.id) }"></div>
                  </div>
                  <div v-if="actingExpandStatus.get(section.id)" class="acting-content">
                    <p class="preserved-format">{{ section.acting }}</p>
                  </div>
                </div>
              </div>
            </template>
            
            <!-- 观察区域内容 -->
            <template v-else-if="section.type === 'observation'">
              <p class="preserved-format">{{ section.content.searchQuery }}</p>
            </template>
            
            <!-- 行动区域内容 -->
            <template v-else-if="section.type === 'action'">
              <p class="preserved-format">{{ section.content.browsing }}</p>
              
              <!-- 预览卡片列表 - 两栏式布局 -->
              <div class="preview-grid">
                <div 
                  v-for="(item, index) in section.content.previews" 
                  :key="`preview-${index}`" 
                  class="preview-item-compact" 
                  :class="{'has-useful-info': item.useful_information && item.useful_information !== '无摘要信息'}"
                >
                  <div class="preview-icon">
                    <img 
                      v-if="!faviconErrors[item.url]"
                      :src="getFaviconUrl(item.url)" 
                      @error="onFaviconError($event, item.url)" 
                      class="favicon-img" 
                      :alt="getDomainFromUrl(item.url)"
                    />
                    <v-icon v-if="faviconErrors[item.url]" icon="mdi-earth" />
                  </div>
                  <div class="preview-content" @click="openUrl(item.url)">
                    <div class="preview-title-text">{{ truncateTitle(item.title) }}</div>
                    <div class="preview-domain-text">
                      {{ getDomainFromUrl(item.url) }}
                      <span 
                        v-if="item.useful_information && item.useful_information !== '无摘要信息'" 
                        class="info-button"
                        @mouseenter="setActiveTooltip(item, $event)"
                        @mouseleave="clearActiveTooltip"
                      >
                        <v-icon icon="mdi-information" size="x-small" color="#654C8C" />
                      </span>
                    </div>
                  </div>
                  <!-- 点赞按钮（钥匙形状） -->
                  <div class="like-button-container">
                    <button 
                      class="like-button-key"
                      :class="{ 'liked': item.liked, 'loading': likeLoadingStates[item.url] }"
                      @click.stop="handleLikeToggle(item.url, section.round)"
                      :disabled="likeLoadingStates[item.url]"
                      :title="item.liked ? '取消点赞' : '点赞此内容'"
                    >
                      <svg class="key-icon" :class="{ 'filled': item.liked }" width="16" height="16" viewBox="0 0 24 24">
                        <path d="M12.65 10C11.83 7.67 9.61 6 7 6c-2.76 0-5 2.24-5 5s2.24 5 5 5c2.61 0 4.83-1.67 5.65-4H17v4h4v-4h2v-2H12.65zM7 14c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3z"/>
                        <circle cx="7" cy="11" r="1"/>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </template>
            
            <!-- 系统消息区域内容 -->
            <template v-else-if="section.type === 'system'">
              <p class="preserved-format">{{ section.content }}</p>
            </template>

            <!-- 用户查询区域内容 -->
            <template v-else-if="section.type === 'userquery'">
              <div class="user-query-content">
                <template v-if="parsedFeedback(section.content).isFullPageEdit">
                  <div class="full-page-edit-request">
                    <div class="full-page-edit-icon">
                      <v-icon icon="mdi-file-document-edit" color="#654C8C" size="large" />
                      <span class="full-page-edit-label">整页编辑</span>
                    </div>
                    <p class="preserved-format full-page-edit-content">{{ parsedFeedback(section.content).opinion }}</p>
                  </div>
                </template>
                <template v-else-if="parsedFeedback(section.content).selectedParagraph">
                  <div class="feedback-selection-quote">
                    <p class="preserved-format">{{ parsedFeedback(section.content).selectedParagraph }}</p>
                  </div>
                  <p class="preserved-format feedback-opinion">{{ parsedFeedback(section.content).opinion }}</p>
                </template>
                <template v-else>
                  <p class="preserved-format">{{ section.content }}</p>
                </template>
              </div>
            </template>

            <!-- 模型动作区域内容 -->
            <template v-else-if="section.type === 'modelaction'">
              <div class="model-action-content">
                <!-- 第一行：动作类型标记和完成状态 -->
                <div class="action-info-row">
                  <div class="action-type-badge" :class="getActionTypeClass(section.content.actionType)">
                    <v-icon :icon="getActionTypeIcon(section.content.actionType)" size="small" />
                    <span>{{ getActionTypeLabel(section.content.actionType) }}</span>
                  </div>
                  
                  <div class="action-status" :class="{ 'completed': section.isCompleted }">
                    <v-icon 
                      :icon="section.isCompleted ? 'mdi-check-circle' : 'mdi-clock-outline'" 
                      :color="section.isCompleted ? '#4CAF50' : '#FF9800'"
                      size="small"
                    />
                    <span>{{ section.isCompleted ? '已完成' : '进行中' }}</span>
                  </div>
                </div>

                <!-- 第二行：内容变化统计（如果有且已完成） -->
                <div v-if="section.content.changes && section.isCompleted" class="content-changes-compact">
                  <div class="changes-stats">
                    <span v-if="section.content.changes.added > 0" class="stat-item added">
                      <v-icon icon="mdi-plus" size="x-small" />
                      +{{ section.content.changes.added }}字符
                    </span>
                    <span v-if="section.content.changes.deleted > 0" class="stat-item deleted">
                      <v-icon icon="mdi-minus" size="x-small" />
                      -{{ section.content.changes.deleted }}字符
                    </span>
                    <span v-if="section.content.changes.modified > 0" class="stat-item modified">
                      <v-icon icon="mdi-pencil" size="x-small" />
                      ~{{ section.content.changes.modified }}段落
                    </span>
                  </div>
                </div>

                <!-- 显示编辑前后内容对比（如果有且已完成） -->
                <div v-if="section.content.beforeContent && section.content.afterContent && section.isCompleted" class="content-comparison">
                  <div class="comparison-toggle" @click="toggleComparison(section.id)">
                    <span>查看编辑对比</span>
                    <v-icon 
                      :icon="comparisonExpandStatus.get(section.id) ? 'mdi-chevron-up' : 'mdi-chevron-down'" 
                      size="small"
                    />
                  </div>
                  <div v-if="comparisonExpandStatus.get(section.id)" class="comparison-content">

                    <div class="comparison-panels">
                      <div class="before-content">
                        <div class="comparison-label">编辑前：</div>
                        <div class="comparison-text">{{ truncateContent(section.content.beforeContent, 200) }}</div>
                      </div>
                      <div class="after-content">
                        <div class="comparison-label">编辑后：</div>
                        <div class="comparison-text">{{ truncateContent(section.content.afterContent, 200) }}</div>
                      </div>
                    </div>
                    <div class="comparison-expand-button">
                      <v-btn 
                        variant="outlined" 
                        size="small" 
                        @click="openChangePreview(section.content.beforeContent, section.content.afterContent)"
                        prepend-icon="mdi-magnify-expand"
                      >
                        查看详细对比
                      </v-btn>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
      
      <!-- 输入区域 -->
      <div class="input-wrapper" v-if="!isShareMode">
        <InputComponent
          :disabled="isProcessing"
          :error="chatError"
          :uploadedFiles="uploadedFiles"
          :loadingStates="loadingStates"
          :isCognitionEnabled="isCognitionEnabled"
          :isSearchEnabled="isSearchEnabled"
          @send="handleUserInput"
          @uploadFile="handleFileUpload"
          @removeFile="handleFileRemove"
          @previewFile="handleFilePreview"
          @update:isCognitionEnabled="handleCognitionSwitch"
          @update:isSearchEnabled="handleSearchSwitch"
        />
      </div>
      
      <!-- 在分享模式下显示提示信息 -->
      <div class="share-mode-message" v-if="isShareMode">
        <v-alert
          type="info"
          variant="tonal"
          class="share-alert"
        >
          这是一个分享的对话，您只能查看内容，不能进行交互。
        </v-alert>
      </div>
    </v-card>

    <!-- 将悬停提示框移到DOM树更高层级 -->
    <teleport to="body">
      <div 
        v-if="activeTooltip" 
        class="preview-tooltip"
        :style="tooltipStyle"
        @mouseenter="keepTooltipActive = true" 
        @mouseleave="clearActiveTooltip"
      >
        <div class="tooltip-title">{{ activeTooltip.title }}</div>
        <div class="tooltip-section" v-if="activeTooltip.snippet">
          <div class="tooltip-section-title">Snippet:</div>
          <div class="tooltip-section-content">{{ activeTooltip.snippet }}</div>
        </div>
        <div class="tooltip-section" v-if="activeTooltip.useful_information">
          <div class="tooltip-section-title">
            有用信息
            <span v-if="activeTooltip.useful_information && activeTooltip.useful_information !== '无摘要信息'" class="useful-info-badge">重要</span>
          </div>
          <div 
            class="tooltip-section-content"
            :class="{'highlighted-info': activeTooltip.useful_information && activeTooltip.useful_information !== '无摘要信息'}"
          >{{ activeTooltip.useful_information }}</div>
        </div>
      </div>
    </teleport>
  </div>

  <!-- 编辑对比弹出框 -->
  <ChangePreview
    v-model="showChangePreview"
    :beforeContent="changePreviewData.beforeContent"
    :afterContent="changePreviewData.afterContent"
  />
</template>

<script lang="ts">
import { ref, inject, computed, onMounted, onUnmounted, watch, nextTick, toRef, defineAsyncComponent } from 'vue';
import InputComponent from './input.vue';
import MarkdownRenderer from '@/components/MarkdownRenderer.vue';
import type { InterfaceItem, FileDescription, PreviewItem, ExtendedFile } from '@/composables/useDeepConversation';
import type { PropType } from 'vue';
import type { PreferenceItem } from './UserPreference.vue';

export default {
  name: 'InterfaceComponent',
  components: {
    InputComponent,
    MarkdownRenderer,
    UserPreference: defineAsyncComponent(() => import('./UserPreference.vue')),
    ChangePreview: defineAsyncComponent(() => import('./changePreview.vue'))
  },
  props: {
    interfaceItems: {
      type: Array as PropType<InterfaceItem[]>,
      required: true
    },
    isProcessing: {
      type: Boolean,
      default: false
    },
    chatError: {
      type: String,
      default: ''
    },
    isShareMode: {
      type: Boolean,
      default: false
    },
    uploadedFiles: {
      type: Array as PropType<ExtendedFile[]>,
      required: true
    },
    loadingStates: {
      type: Array as PropType<boolean[]>,
      required: true
    },
    isCognitionEnabled: {
      type: Boolean,
      default: false
    },
    isSearchEnabled: {
      type: Boolean,
      default: false
    },
    preferences: {
      type: Array as PropType<PreferenceItem[]>,
      default: () => [
        {
          key: 'professional',
          label: '专业性',
          description: '增强回答的专业深度和技术准确性',
          value: 3,
          color: '#654C8C'
        },
        {
          key: 'critical',
          label: '批判性',
          description: '提供更多质疑和多角度分析',
          value: 3,
          color: '#7B68EE'
        },
        {
          key: 'comparison',
          label: '表格对比',
          description: '优先使用表格形式进行对比分析',
          value: 3,
          color: '#9370DB'
        },
        {
          key: 'organization',
          label: '组织性',
          description: '生成文稿的组织结构',
          value: 3,
          color: '#9370DB'
        },
        {
          key: 'cutting-edge',
          label: '前沿性',
          description: '优先使用前沿技术进行分析',
          value: 3,
          color: '#9370DB'
        },
        {
          key: 'coverage',
          label: '覆盖面',
          description: '优先使用覆盖面广的分析',
          value: 3,
          color: '#9370DB'
        },
        {
          key: 'depth',
          label: '深度',
          description: '优先使用深度分析',
          value: 3,
          color: '#9370DB'
        }
      ]
    }
  },
  emits: ['userInput', 'toggleThought', 'uploadFile', 'removeFile', 'previewFile', 'update:isCognitionEnabled', 'preferenceChange', 'update:isSearchEnabled'],
  setup(props, { emit }) {
    // 使用通信接口
    const canvasInterface = inject('canvasInterface', null);
    
    // 注入useDeepConversation的toggleContentLike函数
    const toggleContentLike = inject('toggleContentLike', null);
    
    // 定义引用和状态
    const sectionsContainerRef = ref(null);
    const isAutoScrollingMain = ref(true); // 默认启用自动滚动
    const lastUserInteractionTime = ref(Date.now());
    
    // 思考区域引用和状态管理 - 使用Map代替普通对象，更可靠地跟踪引用
    const thoughtRefs = ref(new Map());
    const thoughtAutoScrollStatus = ref(new Map());
    
    // 添加执行内容的展开状态管理
    const actingExpandStatus = ref(new Map());
    
    // 添加内容对比的展开状态管理
    const comparisonExpandStatus = ref(new Map());
    
    // 添加编辑对比弹出框状态管理
    const showChangePreview = ref(false);
    const changePreviewData = ref({
      beforeContent: '',
      afterContent: ''
    });
    
    // 是否正在更新内容，避免用户交互与自动滚动冲突
    const isUpdatingContent = ref(false);
    
    // 用户主动滚动标记，避免自动滚动与用户滚动冲突
    const userHasScrolled = ref(false);
    
    // 跟踪哪些favicon加载失败
    const faviconErrors = ref({});
    
    // 跟踪当前激活的提示框
    const activeTooltip = ref<PreviewItem | null>(null);
    
    // 跟踪当前选中预览项的位置信息
    const activePreviewPosition = ref<{ x: number, y: number, width: number, height: number } | null>(null);
    
    // 标记是否需要保持tooltip激活（鼠标悬停在tooltip上）
    const keepTooltipActive = ref(false);
    
    // 点赞加载状态管理
    const likeLoadingStates = ref<Record<string, boolean>>({});
    
    // 清除活动的tooltip
    const clearActiveTooltip = () => {
      // 如果鼠标不在tooltip上，则清除
      if (!keepTooltipActive.value) {
        activeTooltip.value = null;
        activePreviewPosition.value = null;
      } else {
        // 如果鼠标在tooltip上，延迟200ms后重新检查
        setTimeout(() => {
          if (!keepTooltipActive.value) {
            activeTooltip.value = null;
            activePreviewPosition.value = null;
          }
        }, 200);
      }
      keepTooltipActive.value = false;
    };
    
    // 设置激活的tooltip和位置
    const setActiveTooltip = (item: PreviewItem, event: MouseEvent) => {
      // 获取预览卡片元素
      const previewElement = event.currentTarget as HTMLElement;
      if (previewElement) {
        const rect = previewElement.getBoundingClientRect();
        activePreviewPosition.value = {
          x: rect.left + window.scrollX,
          y: rect.top + window.scrollY + 30, // 增加一些偏移量，使得更容易把鼠标放上去
          width: rect.width,
          height: rect.height
        };
        activeTooltip.value = item;
      }
    };
    
    // 计算tooltip的样式
    const tooltipStyle = computed(() => {
      if (!activePreviewPosition.value) return {};
      
      const pos = activePreviewPosition.value;
      // 定位在元素上方中央位置
      return {
        position: 'fixed' as const,
        left: `${pos.x + pos.width / 2}px`,
        top: `${pos.y - 10}px`,
        transform: 'translate(-50%, -100%)',
        zIndex: '9999'  // 确保在最上层
      };
    });
    
    // 裁剪标题函数
    const truncateTitle = (title: string): string => {
      if (!title) return '';
      const maxLength = 30;
      return title.length > maxLength 
        ? title.substring(0, maxLength) + '...' 
        : title;
    };
    
    // 解析用户反馈内容，提取选中段落和修改意见
    const parsedFeedback = (content) => {
      const result = {
        selectedParagraph: null,
        opinion: content,
        isFullPageEdit: false
      };
      
      // 检查是否包含整页编辑请求
      if (content.includes('[整页编辑请求]')) {
        result.isFullPageEdit = true;
        // 移除标记，保留实际的编辑请求内容
        result.opinion = content.replace('[整页编辑请求]', '').trim();
        return result;
      }
      
      // 检查是否包含段落选择和修改意见格式
      if (content.includes('用户选中的修改段落:') && content.includes('用户的修改意见:')) {
        const paragraphMatch = content.match(/用户选中的修改段落:(.*?)(?=用户的修改意见:)/s);
        const opinionMatch = content.match(/用户的修改意见:(.*)/s);
        
        if (paragraphMatch && paragraphMatch[1] && opinionMatch && opinionMatch[1]) {
          let selectedText = paragraphMatch[1].trim();
          
          // 如果选中段落过长（超过200字符），则进行裁剪
          const maxLength = 50;
          if (selectedText.length > maxLength) {
            selectedText = selectedText.substring(0, maxLength) + '...';
          }
          
          result.selectedParagraph = selectedText;
          result.opinion = opinionMatch[1].trim();
        }
      }
      
      return result;
    };
    
    // 直接从网站获取favicon
    const getFaviconUrl = (url) => {
      try {
        const domain = getDomainFromUrl(url);
        return `https://${domain}/favicon.ico`;
      } catch (e) {
        // 立即标记为错误
        faviconErrors.value[url] = true;
        return '';
      }
    };
    
    // 处理favicon加载失败
    const onFaviconError = (event, url) => {
      // 标记该URL的favicon加载失败
      faviconErrors.value[url] = true;
    };
    
    // 提取URL中的域名
    const getDomainFromUrl = (url) => {
      try {
        const urlObj = new URL(url);
        return urlObj.hostname;
      } catch (e) {
        // 如果URL解析失败，尝试简单的字符串处理
        return url.replace(/^https?:\/\//, '').split('/')[0];
      }
    };
    
    // 检查元素是否滚动到底部
    const isScrolledToBottom = (element) => {
      if (!element) return false;
      return Math.abs(element.scrollHeight - element.scrollTop - element.clientHeight) < 10;
    };
    
    // 安全地执行滚动到底部操作，避免DOM未就绪错误
    const scrollToBottom = (element) => {
      if (!element || typeof element.scrollHeight !== 'number') return false;
      
      try {
        element.scrollTop = element.scrollHeight;
        return true;
      } catch (error) {
        console.error('滚动到底部时出错:', error);
        return false;
      }
    };
    
    // 设置思考区域引用 - 改进版本，更可靠的引用处理
    const setThoughtRef = (el, id) => {
      if (el && id) {
        // 存储引用
        thoughtRefs.value.set(id, el);
        
        // 默认启用自动滚动
        thoughtAutoScrollStatus.value.set(id, true);
        
        // 初始化时滚动到底部，但仅在没有用户交互时
        if (!isUserInteracting()) {
          nextTick(() => scrollToBottom(el));
        }
      }
    };
    
    // 检查用户是否正在交互（3秒内有操作）
    const isUserInteracting = () => {
      return Date.now() - lastUserInteractionTime.value < 3000;
    };
    
    // 切换思考项展开状态
    const toggleThought = (id: string) => {
      const thought = props.interfaceItems.find((item: any) => item.id === id && item.type === 'thought') as any;
      if (thought) {
        thought.isExpanded = !thought.isExpanded;
        // 展开后，需要重新设置滚动状态
        if (thought.isExpanded) {
          nextTick(() => {
            const el = thoughtRefs.value.get(id);
            if (el) scrollToBottom(el);
          });
        }
      }
    };
    
    // 切换执行内容展开状态
    const toggleActing = (id: string) => {
      // 获取当前状态
      const currentState = actingExpandStatus.value.get(id);
      // 设置为相反状态
      actingExpandStatus.value.set(id, !currentState);
    };
    
    // 打开URL
    const openUrl = (url: string) => {
      window.open(url, '_blank');
    };
    
    // 执行自动滚动
    const performAutoScroll = () => {
      // 如果用户正在交互或已经主动滚动，不执行自动滚动
      if (isUserInteracting() || userHasScrolled.value) return;
      
      // 主容器自动滚动
      if (isAutoScrollingMain.value && sectionsContainerRef.value) {
        scrollToBottom(sectionsContainerRef.value);
      }
      
      // 思考区域自动滚动
      thoughtRefs.value.forEach((el, id) => {
        if (thoughtAutoScrollStatus.value.get(id)) {
          scrollToBottom(el);
        }
      });
    };
    
    // 主容器滚动事件处理 - 更可靠的滚动检测
    const handleMainScroll = () => {
      if (!sectionsContainerRef.value) return;
      
      // console.log('[界面滚动] 检测到主容器滚动事件，容器引用存在:', !!sectionsContainerRef.value);
      
      // 更新最近交互时间
      lastUserInteractionTime.value = Date.now();
      
      // 内容正在更新时不改变自动滚动状态
      if (isUpdatingContent.value) {
        // console.log('[界面滚动] 内容正在更新，忽略滚动事件');
        return;
      }
      
      // 标记用户已主动滚动
      userHasScrolled.value = true;
      
      // 检查是否滚动到底部
      const isAtBottom = isScrolledToBottom(sectionsContainerRef.value);
      
      // 更新自动滚动状态
      isAutoScrollingMain.value = isAtBottom;
      
      // 如果用户滚动到底部，取消用户滚动标记
      if (isAtBottom) {
        userHasScrolled.value = false;
      }
    };
    
    // 思考区域滚动事件处理 - 委托方式处理
    const handleThoughtScroll = (event, id) => {
      if (!event.target) return;
      
      // 更新最近交互时间
      lastUserInteractionTime.value = Date.now();
      
      // 内容正在更新时不改变自动滚动状态
      if (isUpdatingContent.value) return;
      
      // 标记用户已主动滚动
      userHasScrolled.value = true;
      
      // 检查是否滚动到底部
      const isAtBottom = isScrolledToBottom(event.target);
      
      // 更新自动滚动状态
      thoughtAutoScrollStatus.value.set(id, isAtBottom);
      
      // 如果用户滚动到底部，取消用户滚动标记
      if (isAtBottom) {
        userHasScrolled.value = false;
      }
    };
    
    // 使用ResizeObserver监听元素尺寸变化，确保滚动正确
    let resizeObserver = null;
    
    // 初始化ResizeObserver
    const initResizeObserver = () => {
      if (typeof ResizeObserver === 'undefined') return;
      
      resizeObserver = new ResizeObserver(entries => {
        // 当容器大小变化时检查是否需要滚动
        if (!isUserInteracting()) {
          performAutoScroll();
        }
      });
      
      // 观察主容器
      if (sectionsContainerRef.value) {
        resizeObserver.observe(sectionsContainerRef.value);
      }
    };
    
    // 使用MutationObserver监听DOM结构变化
    let mutationObserver = null;
    
    // 初始化MutationObserver
    const initMutationObserver = () => {
      mutationObserver = new MutationObserver(mutations => {
        // 标记内容正在更新
        isUpdatingContent.value = true;
        
        // 延迟执行滚动操作，确保DOM已完全更新
        setTimeout(() => {
          performAutoScroll();
          isUpdatingContent.value = false;
        }, 100);
      });
      
      // 观察主容器内容变化
      if (sectionsContainerRef.value) {
        mutationObserver.observe(sectionsContainerRef.value, {
          childList: true,
          subtree: true,
          characterData: true
        });
      }
    };
    
    // 在组件挂载后设置相关监听和初始化
    onMounted(async () => {
      // 从localStorage恢复用户偏好设置
      const savedPreferences = localStorage.getItem('userPreferences');
      if (savedPreferences) {
        try {
          const parsed = JSON.parse(savedPreferences);
          // 这里需要根据实际的偏好设置结构来更新
          // 例如：preferences.value = parsed;
        } catch (error) {
          console.warn('无法解析保存的用户偏好设置:', error);
        }
      }

      // 等待DOM渲染完成
      await nextTick();
      
      // 确保各种引用和状态正确初始化
      // console.log("[挂载] 初始化自动滚动状态", {
      //   hasContainer: !!sectionsContainerRef.value,
      //   autoScrollMain: isAutoScrollingMain.value
      // });
      
      // 定义一个初始化所有监听器的函数
      const initAllListeners = () => {
        // console.log('[初始化] 开始初始化所有滚动监听器和观察器');
        // 初始化ResizeObserver
        initResizeObserver();
        
        // 初始化MutationObserver
        initMutationObserver();
        
        // 初始设置主容器滚动事件
        if (sectionsContainerRef.value) {
          // 先移除可能存在的监听器，避免重复
          sectionsContainerRef.value.removeEventListener('scroll', handleMainScroll);
          // 添加新的监听器
          sectionsContainerRef.value.addEventListener('scroll', handleMainScroll);
          // console.log('[初始化] 主容器滚动监听器已绑定');
        } else {
          // console.warn('[初始化] 主容器不存在，无法绑定滚动监听器');
        }
      };
      
      // 监听界面项变化，但仅处理思考区域的状态维护
      watch(() => props.interfaceItems, (newItems, oldItems) => {
        // 有新增项时才标记更新
        if (newItems.length > (oldItems?.length || 0)) {
          isUpdatingContent.value = true;
          
          // 延迟执行自动滚动，确保DOM已更新
          setTimeout(() => {
            performAutoScroll();
            isUpdatingContent.value = false;
          }, 200);
        }
        
        // 处理思考区域的自动滚动
        newItems.forEach(item => {
          if (item.type === 'thought') {
            // 为尚未初始化的思考区域设置默认自动滚动状态
            if (!thoughtAutoScrollStatus.value.has(item.id)) {
              thoughtAutoScrollStatus.value.set(item.id, true);
            }
            
            // 为尚未初始化的执行内容区域设置默认展开状态（默认收起）
            if (!actingExpandStatus.value.has(item.id)) {
              actingExpandStatus.value.set(item.id, false);
            }
          }
        });
        
        // console.log('[监听] interfaceItems变化，当前项数：', newItems.length);
        // 检查容器是否就绪
        nextTick(() => {
          if (sectionsContainerRef.value) {
            // 有内容且容器已就绪，确保监听器已绑定
            initAllListeners();
          } else {
            // console.warn('[监听] 容器未就绪，无法绑定监听器');
          }
        });
      }, { deep: true });
      
      // 立即执行一次初始化
      initAllListeners();
      
      // 延迟200ms后再次执行，确保DOM已完全就绪
      setTimeout(initAllListeners, 200);
      
      // 初始化周期性执行自动滚动
      const autoScrollInterval = setInterval(() => {
        // console.log("[自动滚动检查]", {
        //   userInteracting: isUserInteracting(),
        //   userScrolled: userHasScrolled.value,
        //   autoScrollMain: isAutoScrollingMain.value
        // });
        performAutoScroll();
      }, 500);
      
      // 创建一个具名函数用于历史对话恢复事件处理
      const handleHistoryRestored = () => {
        // console.log('[界面] 检测到历史对话已恢复，重新初始化监听器');
        // 立即执行一次初始化
        nextTick(initAllListeners);
        // 延迟执行一次，确保DOM完全更新
        setTimeout(initAllListeners, 500);
        
        // 重置滚动状态
        isAutoScrollingMain.value = true;
        userHasScrolled.value = false;
          
        // 重新执行自动滚动
        setTimeout(performAutoScroll, 500);
      };
      
      // 处理接口重新初始化事件
      const handleInterfaceReinitialize = () => {
        // console.log('[界面] 接收到接口重新初始化请求');
        // 确保DOM已更新，使用nextTick
        nextTick(() => {
          // console.log('[界面] 重新初始化所有接口监听器');
          // 重置用户交互状态
          lastUserInteractionTime.value = 0;
          userHasScrolled.value = false;
          isAutoScrollingMain.value = true;
          
          // 重新初始化思考区域引用和状态
          thoughtRefs.value.clear();
          thoughtAutoScrollStatus.value.clear();
          
          // 使用通用初始化函数
          initAllListeners();
          
          // 执行一次自动滚动
          performAutoScroll();
          
          // 再次延迟执行自动滚动和初始化，确保完全加载
          setTimeout(() => {
            performAutoScroll();
            initAllListeners();
          }, 500);
        });
      };
      
      // 监听历史对话恢复事件
      window.addEventListener('interface-history-restored', handleHistoryRestored);
      
      // 监听接口重新初始化事件
      window.addEventListener('interface-reinitialize', handleInterfaceReinitialize);
      
      // 另外监听DOM变化，如果interfaceItems变化但sectionsContainerRef不存在，等待它存在后初始化
      const monitorDOM = setInterval(() => {
        if (props.interfaceItems.length > 0 && !sectionsContainerRef.value) {
          // console.log('[监控] 发现interfaceItems已加载但sectionsContainerRef未就绪，等待DOM更新');
          nextTick(initAllListeners);
        } else if (sectionsContainerRef.value && props.interfaceItems.length > 0) {
          // 如果已经存在，检查是否已绑定事件（通过设置标记）
          initAllListeners();
        }
      }, 500);
      
      // 组件卸载时清理资源
      onUnmounted(() => {
        clearInterval(autoScrollInterval);
        clearInterval(monitorDOM);
        
        if (mutationObserver) {
          mutationObserver.disconnect();
        }
        
        if (resizeObserver) {
          resizeObserver.disconnect();
        }
        
        if (sectionsContainerRef.value) {
          sectionsContainerRef.value.removeEventListener('scroll', handleMainScroll);
        }
        
        // 移除历史对话恢复事件监听，使用相同的函数引用
        window.removeEventListener('interface-history-restored', handleHistoryRestored);
        window.removeEventListener('interface-reinitialize', handleInterfaceReinitialize);
      });
    });
    
    // 处理用户输入
    const handleUserInput = (input: string) => {
      emit('userInput', input);
    };

    // 处理文件上传
    const handleFileUpload = (file: File) => {
      emit('uploadFile', file);
    };

    // 处理文件预览
    const handleFilePreview = (file: ExtendedFile) => {
      emit('previewFile', file);
    };

    // 处理文件删除
    const handleFileRemove = (index: number) => {
      emit('removeFile', index);
    };

    // 处理认知搜索开关
    const handleCognitionSwitch = (value: boolean) => {
      emit('update:isCognitionEnabled', value);
    };

    // 处理偏好设置变化
    const handlePreferenceChange = ({ key, value }) => {
      console.log(`[Interface] 接收到偏好设置变化: ${key} = ${value}`);
      
      // 发送事件给父组件，让父组件处理状态更新
      emit('preferenceChange', { key, value });
      
      console.log(`[Interface] 已向父组件发送偏好设置变化事件: ${key} = ${value}`);
    };

    // 处理搜索开关
    const handleSearchSwitch = (value: boolean) => {
      emit('update:isSearchEnabled', value);
    };

    // 获取动作类型的样式类
    const getActionTypeClass = (actionType: string): string => {
      switch (actionType) {
        case 'create_draft':
          return 'action-create';
        case 'edit_draft':
          return 'action-edit';
        case 'revise_draft':
          return 'action-revise';
        default:
          return 'action-default';
      }
    };

    // 获取动作类型的图标
    const getActionTypeIcon = (actionType: string): string => {
      switch (actionType) {
        case 'create_draft':
          return 'mdi-file-plus';
        case 'edit_draft':
          return 'mdi-file-edit';
        case 'revise_draft':
          return 'mdi-file-refresh';
        default:
          return 'mdi-cog';
      }
    };

    // 获取动作类型的标签
    const getActionTypeLabel = (actionType: string): string => {
      switch (actionType) {
        case 'create_draft':
          return '创建草稿';
        case 'edit_draft':
          return '编辑草稿';
        case 'revise_draft':
          return '修订草稿';
        default:
          return '未知动作';
      }
    };

    // 切换内容对比展开状态
    const toggleComparison = (id: string) => {
      const currentState = comparisonExpandStatus.value.get(id);
      comparisonExpandStatus.value.set(id, !currentState);
    };

    // 截断内容函数
    const truncateContent = (content: string, maxLength: number): string => {
      if (!content) return '';
      if (content.length <= maxLength) return content;
      return content.substring(0, maxLength) + '...';
    };

    // 打开编辑对比弹出框
    const openChangePreview = (beforeContent: string, afterContent: string) => {
      showChangePreview.value = true;
      changePreviewData.value = { beforeContent, afterContent };
    };

    // 处理点赞
    const handleLikeToggle = async (url: string, round: number) => {
      // 检查是否已经在加载中
      if (likeLoadingStates.value[url]) return;
      
      // 检查toggleContentLike函数是否可用
      if (!toggleContentLike) {
        console.error('toggleContentLike 函数不可用');
        return;
      }
      
      // 设置加载状态
      likeLoadingStates.value[url] = true;
      
      try {
        // 调用toggleContentLike函数
        const success = await toggleContentLike(url, round);
        if (!success) {
          console.error('点赞操作失败');
        }
      } catch (error) {
        console.error('点赞操作出错:', error);
      } finally {
        // 短暂延迟后重置加载状态，提供用户反馈
        setTimeout(() => {
          likeLoadingStates.value[url] = false;
        }, 300);
      }
    };

    return {
      sectionsContainerRef,
      thoughtRefs,
      actingExpandStatus,
      parsedFeedback,
      clearActiveTooltip,
      setActiveTooltip,
      handleUserInput,
      openUrl,
      getFaviconUrl,
      onFaviconError,
      getDomainFromUrl,
      setThoughtRef,
      toggleThought,
      handlePreferenceChange,
      handleThoughtScroll,
      toggleActing,
      truncateTitle,
      tooltipStyle,
      activeTooltip,
      keepTooltipActive,
      handleFileUpload,
      faviconErrors,
      handleFileRemove,
      handleFilePreview,
      handleCognitionSwitch,
      handleLikeToggle,
      likeLoadingStates,
      handleSearchSwitch,
      getActionTypeClass,
      getActionTypeIcon,
      getActionTypeLabel,
      toggleComparison,
      truncateContent,
      comparisonExpandStatus,
      showChangePreview,
      changePreviewData,
      openChangePreview
    };
  }
}
</script>

<style scoped>
.interface-container {
  height: 100%;
  border-left: 1px solid #d9d6ef;
  display: flex;
  flex-direction: column;
}

.interface-card {
  height: 100%;
  border-radius: 0;
  background: #f7f6fb;
  display: flex;
  flex-direction: column;
}

/* 添加空状态样式 */
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 24px;
  padding: 20px;
}

.empty-state-icon {
  width: 160px;
  height: auto;
  opacity: 0.8;
  animation: float 3s ease-in-out infinite;
}

.empty-state-text {
  font-size: 1.1rem;
  font-weight: 500;
  text-align: center;
  opacity: 0.8;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.sections-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 8px 16px 8px 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  scrollbar-width: thin;
  scrollbar-gutter: stable both-edges;
  padding-right: 24px;
  scroll-behavior: smooth;
  max-height: calc(100vh - 200px);
}

.section {
  background-color: #E8E6F6;
  border: 0.5px solid #C2BFD5;
  border-radius: 16px;
  box-shadow: none;
  padding: 16px 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  position: relative;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.icon-container {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.icon-wrapper {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  background-color: #ffffff;
  justify-content: center;
  border-radius: 50%;
}

.section-icon {
  width: 32px;
  height: 32px;
  color: #ffffff;
  /* background-color: #ffffff; */
  object-fit: contain;
}

.section-label {
  font-size: clamp(0.9rem, 2vw, 1.1rem);
  font-weight: 500;
  color: #333;
}

.section-content {
  padding: 8px 0;
  font-size: clamp(0.8rem, 1.5vw, 0.95rem);
  line-height: 1.5;
  color: #333;
  max-height: 400px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(101, 76, 140, 0.2) transparent;
}

.section-content::-webkit-scrollbar {
  width: 4px;
}

.section-content::-webkit-scrollbar-track {
  background: transparent;
  margin: 4px 0;
}

.section-content::-webkit-scrollbar-thumb {
  background-color: rgba(101, 76, 140, 0.2);
  border-radius: 4px;
}

.section-content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(101, 76, 140, 0.4);
}

.preview-item {
  margin-top: 16px;
  margin-bottom: 16px;
  margin-right: 8px;
  background-color: #F7F6FF;
  border-radius: 20px;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  box-shadow: none;
  height: 180px; /* 减小卡片高度 */
  cursor: pointer;
  transition: box-shadow 0.2s ease, transform 0.2s ease;
}

.preview-item:hover {
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.preview-title {
  font-weight: 300;
  font-style: italic;
  color: #FFFFFF;
  background-color: #654C8C;
  padding: 10px 16px;
  font-size: clamp(11px, 1.2vw, 13px);
  line-height: 18px;
  border-radius: 20px 20px 0 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.preview-url-container {
  padding: 6px 16px;
  background-color: #F7F6FF;
}

.preview-url {
  background-color: #D9D6EF;
  padding: 6px 12px;
  font-size: clamp(9px, 1vw, 11px);
  line-height: 16px;
  font-weight: 300;
  font-style: italic;
  color: #654C8C;
  border-radius: 20px;
}

.preview-summary {
  padding: 8px 16px;
  margin-top: -6px;
  font-size: clamp(11px, 1.2vw, 13px);
  line-height: 18px;
  color: #000000;
  font-weight: 300;
  font-style: italic;
  background-color: #F7F6FF;
  border-radius: 0 0 20px 20px;
}

.input-wrapper {
  margin-top: auto;
  background-color: #f7f6fb;
  border-top: 1px solid rgba(217, 214, 239, 0.5);
  width: 100%;
}

/* 自定义滚动条样式 */
.sections-container::-webkit-scrollbar {
  width: 12px;
}

.sections-container::-webkit-scrollbar-track {
  background: rgba(217, 214, 239, 0.3);
  border-radius: 6px;
  margin: 4px 0;
  border: 1px solid rgba(217, 214, 239, 0.4);
}

.sections-container::-webkit-scrollbar-thumb {
  background-color: rgba(101, 76, 140, 0.6);
  border-radius: 6px;
  transition: background-color 0.2s ease;
  border: 2px solid rgba(217, 214, 239, 0.1);
}

.sections-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(101, 76, 140, 0.8);
}

.sections-container::-webkit-scrollbar-thumb:active {
  background-color: rgba(101, 76, 140, 1);
}

.sections-container {
  scrollbar-width: auto;
  scrollbar-color: rgba(101, 76, 140, 0.6) rgba(217, 214, 239, 0.3);
}

.chat-history {
  display: none; /* Hide the chat history section as it's being replaced */
}

.chevron-icon {
  width: 36px; /* 增加尺寸 */
  height: 36px; /* 增加尺寸 */
  min-width: 36px;
  min-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background-color: #654C8C;
  border-radius: 50%;
  position: relative;
  flex-shrink: 0;
  margin-right: -8px; /* 向右移动一点 */
}

.chevron-down {
  width: 8px; /* 减小箭头大小 */
  height: 8px; /* 减小箭头大小 */
  border-right: 2px solid white;
  border-bottom: 2px solid white;
  transform: rotate(45deg);
  margin-top: -4px;
  transition: transform 0.3s ease;
}

.floating-label {
  display: none;
}

.expanded-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 8px;
}

.expanded-content p {
  margin: 0;
  line-height: 1.5;
  font-size: clamp(0.8rem, 1.5vw, 0.95rem);
}

.thought-section {
  transition: all 0.3s ease;
}

.thought-section.expanded {
  margin-bottom: 8px;
}

.chevron-down.rotated {
  transform: rotate(-135deg);
  margin-top: 4px;
  margin-top: 4px;
}

.completion-info {
  font-size: clamp(0.7rem, 1.2vw, 0.8rem);
  color: #9e9bb3;
  font-weight: 300;
  margin-left: 12px;
  white-space: nowrap;
}

.thought-section:not(.expanded) {
  padding: 8px 20px;
}

.thought-section:not(.expanded) .section-content {
  display: none;
}

.thought-section:not(.expanded) .icon-container {
  margin-bottom: 0;
}

.thought-section:not(.expanded) .chevron-icon {
  margin-top: 0;
  margin-bottom: 0;
}

/* 用户查询卡片样式 */
.userquery-section {
  background-color: #F0EBFF;
  border-color: #C2BFD5;
}

.userquery-section .icon-wrapper {
  background-color: #ffffff;
}

.userquery-section .section-label {
  color: #654C8C;
  font-weight: 600;
}

.user-query-content {
  padding: 8px 12px;
  background-color: #FFFFFF;
  border-radius: 8px;
  margin-top: 8px;
}

.user-query-content p {
  margin: 0;
  line-height: 1.5;
  font-size: 0.9rem;
  font-style: italic;
  color: #333;
}

/* 添加响应式设计的媒体查询 */
@media screen and (max-width: 768px) {
  .section {
    padding: 12px 16px;
  }
  
  .icon-wrapper {
    width: 32px;
    height: 32px;
  }
  
  .section-icon {
    width: 28px;
    height: 28px;
  }
  
  .preview-item {
    height: 160px;
  }
}

@media screen and (max-width: 480px) {
  .section {
    padding: 10px 12px;
  }
  
  .icon-wrapper {
    width: 28px;
    height: 28px;
  }
  
  .section-icon {
    width: 24px;
    height: 24px;
  }
  
  .preview-item {
    height: 140px;
  }
  
  .sections-container {
    padding: 4px 12px 4px 4px;
  }
}

/* 保留文本格式化样式 */
.preserved-format {
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-word;
}

/* 新的紧凑型预览卡片样式 */
.preview-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin: 8px 0;
}

/* 预览项布局调整，为点赞按钮留出空间 */
.preview-item-compact {
  display: flex;
  align-items: center;
  gap: 0; /* 移除gap，使用margin精确控制间距 */
}

.preview-content {
  flex: 1;
  min-width: 0;
  margin-left: 12px; /* 与图标的间距 */
  margin-right: 8px; /* 与点赞按钮的间距 */
}

.preview-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-right: 12px;
  color: #654C8C;
}

.preview-icon .v-icon {
  font-size: 18px;
}

.favicon-img {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

/* 标题文本样式 */
.preview-title-text {
  font-size: 14px;
  font-weight: 500;
  color: #654C8C;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

/* 域名文本样式 */
.preview-domain-text {
  font-size: 12px;
  color: #9E9BB3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 移除旧的域名样式 */
.preview-domain {
  display: none;
}

/* 悬停提示框样式 */
.preview-tooltip {
  position: fixed;
  min-width: 400px;
  max-width: 600px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
  padding: 16px;
  transition: opacity 0.2s ease;
  border: 1px solid #E0E0E0;
  z-index: 9999;
  pointer-events: auto; /* 确保可以接收鼠标事件 */
  max-height: calc(90vh - 20px); /* 防止在小屏幕上超出视口 */
  overflow-y: auto; /* 内容过多时可滚动 */
}

/* 在小屏幕设备上调整悬停提示框样式 */
@media screen and (max-width: 768px) {
  .preview-tooltip {
    min-width: 320px;
    max-width: 480px;
    max-height: 60vh;
    font-size: 13px;
  }
  
  .tooltip-title {
    font-size: 14px !important;
  }
  
  .tooltip-section-title {
    font-size: 12px !important;
  }
  
  .tooltip-section-content {
    font-size: 12px !important;
    max-height: 150px !important;
  }
}

/* 在更小的屏幕上进一步调整 */
@media screen and (max-width: 480px) {
  .preview-tooltip {
    min-width: 200px;
    max-width: 90vw;
    padding: 8px;
    left: 50% !important; /* 强制居中显示 */
    transform: translateX(-50%) translateY(-100%) !important;
  }
}

/* 提示框小三角形 */
.preview-tooltip::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
  width: 12px;
  height: 12px;
  background: white;
  border-right: 1px solid #E0E0E0;
  border-bottom: 1px solid #E0E0E0;
  z-index: -1; /* 确保在内容下方 */
}

/* 提示框标题 */
.tooltip-title {
  font-size: 16px;
  font-weight: 600;
  color: #654C8C;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #F0EBFF;
}

/* 提示框分节 */
.tooltip-section {
  margin-top: 12px;
}

.tooltip-section-title {
  font-size: 14px;
  font-weight: 600;
  color: #654C8C;
  margin-bottom: 6px;
}

.tooltip-section-content {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  max-height: 200px;
  overflow-y: auto;
  word-break: break-word;
  white-space: normal;
  padding: 4px 0;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .preview-grid {
    grid-template-columns: 1fr;
  }
  
  .comparison-content {
    grid-template-columns: 1fr;
  }
  
  .comparison-panels {
    flex-direction: column;
    gap: 8px;
  }
  
  .changes-stats {
    gap: 8px;
  }
  
  .stat-item {
    font-size: 0.7rem;
  }

  /* 模型动作区域的响应式调整 */
  .action-info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .action-type-badge {
    font-size: 0.75rem;
  }

  .action-status {
    font-size: 0.8rem;
    padding: 4px 8px;
  }
}

/* 用户反馈引用块样式 */
.feedback-selection-quote {
  background-color: #F0EBFF;
  border-left: 4px solid #654C8C;
  padding: 10px 12px;
  margin-bottom: 12px;
  border-radius: 0 8px 8px 0;
}

.feedback-selection-quote p {
  font-style: normal;
  color: #654C8C;
  font-weight: 500;
}

.feedback-opinion {
  padding: 0 8px;
}

/* 整页编辑请求样式 */
.full-page-edit-request {
  background-color: #E8F5E8;
  border: 2px solid #4CAF50;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
}

.full-page-edit-icon {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(76, 175, 80, 0.3);
}

.full-page-edit-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #4CAF50;
}

.full-page-edit-content {
  color: #2E7D32;
  font-weight: 500;
  margin: 0;
  padding: 4px 0;
}

/* 添加轮次指示器样式 */
.round-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 22px;
  height: 22px;
  background-color: #654C8C;
  color: #FFFFFF;
  border-radius: 11px;
  font-size: 0.7rem;
  font-weight: 500;
  padding: 0 6px;
  margin-left: 8px;
  cursor: default;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.round-badge:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(101, 76, 140, 0.3);
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .round-badge {
    min-width: 20px;
    height: 20px;
    font-size: 0.65rem;
    padding: 0 5px;
    margin-left: 6px;
  }
}

@media screen and (max-width: 480px) {
  .round-badge {
    min-width: 18px;
    height: 18px;
    font-size: 0.6rem;
    padding: 0 4px;
    margin-left: 4px;
  }
}

/* 执行内容区域样式 */
.acting-container {
  margin-top: 12px;
  border: 1px dashed #C2BFD5;
  border-radius: 8px;
  overflow: hidden;
}

.acting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #F0EBFF;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.acting-header:hover {
  background-color: #E5E0FA;
}

.acting-title {
  font-size: 0.9rem;
  font-weight: 500;
  color: #654C8C;
}

.acting-chevron {
  width: 8px;
  height: 8px;
  border-right: 2px solid #654C8C;
  border-bottom: 2px solid #654C8C;
  transform: rotate(45deg);
  transition: transform 0.3s ease;
}

.acting-chevron.rotated {
  transform: rotate(-135deg);
}

.acting-content {
  padding: 12px;
  background-color: #FFFFFF;
  font-size: 0.85rem;
  line-height: 1.5;
  color: #333;
}

/* 添加有用信息的预览卡片特殊样式 */
.preview-item-compact.has-useful-info {
  background-color: #ECE5FF;
  border-left: 3px solid #654C8C;
}

.preview-item-compact.has-useful-info:hover {
  background-color: #E5DCFF;
  box-shadow: 0 2px 6px rgba(101, 76, 140, 0.2);
}

/* 高亮显示有用信息 */
.tooltip-section-content.highlighted-info {
  background-color: #F0EBFF;
  border-left: 3px solid #654C8C;
  padding: 8px 10px;
  margin-left: -5px;
  border-radius: 0 4px 4px 0;
}

/* 有用信息指示器 */
.useful-info-indicator {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 5px;
  vertical-align: middle;
}

/* 有用信息指示器动画效果 */
@keyframes pulse-light {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

.has-useful-info .useful-info-indicator {
  animation: pulse-light 2s infinite ease-in-out;
}

/* 有用信息标记 */
.useful-info-badge {
  display: inline-block;
  background-color: #654C8C;
  color: white;
  font-size: 10px;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 6px;
  vertical-align: middle;
}

.share-mode-message {
  padding: 16px;
  margin-top: auto;
}

.share-alert {
  margin: 0;
}

.info-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: rgba(101, 76, 140, 0.1);
  cursor: pointer;
  margin-left: 4px;
  transition: background-color 0.2s ease;
}

.info-button:hover {
  background-color: rgba(101, 76, 140, 0.2);
}

.info-button .v-icon {
  font-size: 14px;
}

/* 模型动作区域样式 */
.modelaction-section {
  background-color: #F0EBFF;
  border-color: #C2BFD5;
}

.modelaction-section .icon-wrapper {
  background-color: #ffffff;
}

.modelaction-section .section-label {
  color: #654C8C;
  font-weight: 600;
}

.model-action-content {
  padding: 8px 12px;
  background-color: #FFFFFF;
  border-radius: 8px;
  margin-top: 8px;
}

/* 动作类型标记样式 */
.action-type-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 10px;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 500;
  margin-bottom: 8px;
}

.action-type-badge.action-create {
  background-color: #E8F5E8;
  color: #4CAF50;
  border: 1px solid #C8E6C9;
}

.action-type-badge.action-edit {
  background-color: #FFF3E0;
  color: #FF9800;
  border: 1px solid #FFE0B2;
}

.action-type-badge.action-revise {
  background-color: #F3E5F5;
  color: #9C27B0;
  border: 1px solid #E1BEE7;
}

.action-type-badge.action-default {
  background-color: #F5F5F5;
  color: #757575;
  border: 1px solid #E0E0E0;
}

/* 动作状态样式 */
.action-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 0.85rem;
  font-weight: 500;
  margin-bottom: 12px;
  background-color: #FFF8E1;
  color: #F57C00;
  border: 1px solid #FFCC02;
}

.action-status.completed {
  background-color: #E8F5E8;
  color: #2E7D32;
  border: 1px solid #81C784;
}

/* 内容变化统计样式 */
.content-changes {
  margin-bottom: 12px;
  padding: 8px 12px;
  background-color: #F8F9FA;
  border-radius: 8px;
  border-left: 4px solid #654C8C;
}

/* 紧凑版内容变化统计样式 */
.content-changes-compact {
  margin-bottom: 8px;
  padding: 6px 0;
}

.content-changes-compact .changes-stats {
  justify-content: flex-start;
  gap: 8px;
}

.changes-title {
  font-size: 0.8rem;
  font-weight: 600;
  color: #654C8C;
  margin-bottom: 6px;
}

.changes-stats {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 12px;
}

.stat-item.added {
  background-color: #E8F5E8;
  color: #2E7D32;
}

.stat-item.deleted {
  background-color: #FFEBEE;
  color: #C62828;
}

.stat-item.modified {
  background-color: #F0EBFF;
  color: #654C8C;
}

/* 内容对比样式 */
.content-comparison {
  margin-top: 12px;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  overflow: hidden;
}

.comparison-toggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #F5F5F5;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 0.85rem;
  font-weight: 500;
  color: #654C8C;
}

.comparison-toggle:hover {
  background-color: #EEEEEE;
}

.comparison-content {
  padding: 12px;
  background-color: #FFFFFF;
  border-top: 1px solid #E0E0E0;
}

.comparison-expand-button {
  margin-top: 12px;
  text-align: center;
}

.comparison-expand-button .v-btn {
  font-size: 0.8rem;
  text-transform: none;
}

.comparison-panels {
  display: flex;
  gap: 12px;
  margin-top: 12px;
}

.before-content,
.after-content {
  flex: 1;
  background-color: #FFFFFF;
  border-radius: 6px;
  border: 1px solid #E0E0E0;
  padding: 8px 12px;
}

.before-content {
  border-left: 3px solid #FF5722;
}

.after-content {
  border-left: 3px solid #4CAF50;
}

.comparison-label {
  font-size: 0.75rem;
  font-weight: 600;
  margin-bottom: 6px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.before-content .comparison-label {
  color: #C62828;
}

.after-content .comparison-label {
  color: #2E7D32;
}

.comparison-text {
  font-size: 0.8rem;
  line-height: 1.4;
  color: #666;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .comparison-content {
    grid-template-columns: 1fr;
  }
  
  .comparison-panels {
    flex-direction: column;
    gap: 8px;
  }
  
  .changes-stats {
    gap: 8px;
  }
  
  .stat-item {
    font-size: 0.7rem;
  }
}

/* 第一行：动作类型和状态的左右布局 */
.action-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  gap: 12px;
}

/* 调整动作类型标记的下边距 */
.action-info-row .action-type-badge {
  margin-bottom: 0;
  flex-shrink: 0;
}

/* 调整动作状态的样式 */
.action-info-row .action-status {
  margin-bottom: 0;
  flex-shrink: 0;
}

/* 钥匙形状点赞按钮样式 */
.like-button-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

.like-button-key {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: #fff;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
}

.like-button-key:hover {
  border-color: #654C8C;
  color: #654C8C;
  background: #F0EBFF;
}

.like-button-key.liked {
  border-color: #654C8C;
  background: #654C8C;
  color: #fff;
}

.like-button-key:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.like-button-key.loading {
  opacity: 0.7;
}

.key-icon {
  fill: currentColor;
  transition: all 0.2s ease;
}

.key-icon.filled {
  fill: currentColor;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .like-button-key {
    width: 28px;
    height: 28px;
  }
  
  .key-icon {
    width: 14px;
    height: 14px;
  }
}

@media screen and (max-width: 480px) {
  .like-button-key {
    width: 24px;
    height: 24px;
  }
  
  .key-icon {
    width: 12px;
    height: 12px;
  }
  
  .like-button-container {
    margin-left: 4px;
  }
  
  .preview-content {
    margin-right: 4px;
  }
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .sections-container {
    max-height: calc(100vh - 180px);
    padding-right: 20px;
  }
  
  .sections-container::-webkit-scrollbar {
    width: 10px;
  }
}

@media screen and (max-width: 480px) {
  .sections-container {
    padding: 4px 12px 4px 4px;
    max-height: calc(100vh - 160px);
    padding-right: 16px;
  }
  
  .sections-container::-webkit-scrollbar {
    width: 8px;
  }
}
</style>