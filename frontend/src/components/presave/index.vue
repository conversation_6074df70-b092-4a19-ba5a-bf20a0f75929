<template>
  <v-dialog v-model="visible" max-width="1600" persistent>
    <v-card>
      <v-card-title class="d-flex align-center justify-space-between">
        <span>保存至笔记</span>
        <v-btn icon variant="text" @click="handleClose">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      
      <v-card-text class="pa-6">
        <!-- 标题输入 -->
        <v-text-field
          v-model="documentTitle"
          label="笔记标题"
          variant="outlined"
          density="comfortable"
          class="mb-4"
          :rules="titleRules"
        />
        
        <!-- 编辑和预览区域 - 左右并排 -->
        <div class="content-section">
          <v-row no-gutters>
                        <!-- 左侧编辑区域 -->
            <v-col cols="6" class="pr-3">
              <div class="section-label mb-2">笔记内容（Markdown格式）</div>
              <v-textarea
                v-model="documentContent"
                variant="outlined"
                rows="15"
                :rules="contentRules"
                class="markdown-editor"
                style="font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 14px;"
              />
            </v-col>
            
            <!-- 右侧预览区域 -->
            <v-col cols="6" class="pl-3">
              <div class="section-label mb-2">内容预览</div>
              <div class="preview-container">
                <div v-html="renderedContent" class="rendered-content"></div>
              </div>
            </v-col>
          </v-row>
        </div>
      </v-card-text>
      
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn
          color="grey"
          variant="text"
          @click="handleClose"
        >
          取消
        </v-btn>
        <v-btn
          color="primary"
          variant="flat"
          @click="handleSave"
          :loading="saving"
          :disabled="!isValid"
        >
          保存笔记
        </v-btn>
      </v-card-actions>
    </v-card>
    
    <!-- 保存成功/失败提示 -->
    <v-snackbar
      v-model="showSnackbar"
      :timeout="3000"
      :color="snackbarColor"
      location="top"
    >
      {{ snackbarMessage }}
      <template v-slot:actions>
        <v-btn
          color="white"
          variant="text"
          @click="showSnackbar = false"
        >
          关闭
        </v-btn>
      </template>
    </v-snackbar>
  </v-dialog>
</template>

<script lang="ts">
import { ref, computed, watch, defineComponent } from 'vue';
import { marked } from 'marked';
import { documentsApi } from '@/api/documents';

export default defineComponent({
  name: 'PreSave',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    content: {
      type: String,
      default: ''
    }
  },
  emits: ['update:visible', 'saved'],
  setup(props, { emit }) {
    const documentTitle = ref('');
    const documentContent = ref('');
    const saving = ref(false);
    
    // Snackbar 状态
    const showSnackbar = ref(false);
    const snackbarMessage = ref('');
    const snackbarColor = ref('success');
    
    // 验证规则
    const titleRules = [
      (v: string) => !!v || '标题不能为空',
      (v: string) => v.length <= 100 || '标题不能超过100个字符'
    ];
    
    const contentRules = [
      (v: string) => !!v || '内容不能为空',
      (v: string) => v.length <= 50000 || '内容不能超过50000个字符'
    ];
    
    // 验证表单是否有效
    const isValid = computed(() => {
      return documentTitle.value.trim() !== '' && 
             documentContent.value.trim() !== '' &&
             documentTitle.value.length <= 100 &&
             documentContent.value.length <= 50000;
    });
    
    // 渲染的Markdown内容
    const renderedContent = computed(() => {
      if (!documentContent.value) return '';
      try {
        return marked(documentContent.value);
      } catch (error) {
        console.error('Markdown渲染失败:', error);
        return '<p>内容预览失败</p>';
      }
    });
    
    // 从内容中提取首行作为标题
    const extractTitleFromContent = (content: string): string => {
      if (!content) return '';
      
      const lines = content.split('\n');
      for (const line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine) {
          // 移除Markdown标题标记
          const title = trimmedLine.replace(/^#+\s*/, '').trim();
          // 限制标题长度
          return title.length > 100 ? title.substring(0, 100) : title;
        }
      }
      return '未命名笔记';
    };
    
    // 监听props.content变化，初始化内容
    watch(() => props.content, (newContent) => {
      if (newContent && props.visible) {
        documentContent.value = newContent;
        documentTitle.value = extractTitleFromContent(newContent);
      }
    }, { immediate: true });
    
    // 监听visible变化，重置内容
    watch(() => props.visible, (newVisible) => {
      if (newVisible && props.content) {
        documentContent.value = props.content;
        documentTitle.value = extractTitleFromContent(props.content);
      }
    });
    
    // 处理关闭
    const handleClose = () => {
      emit('update:visible', false);
    };
    
    // 显示提示消息
    const showMessage = (message: string, color: string = 'success') => {
      snackbarMessage.value = message;
      snackbarColor.value = color;
      showSnackbar.value = true;
    };
    
    // 处理保存
    const handleSave = async () => {
      if (!isValid.value) {
        showMessage('请检查标题和内容是否符合要求', 'error');
        return;
      }
      
      saving.value = true;
      
      try {
        const document = await documentsApi.createDocument({
          title: documentTitle.value.trim(),
          content: documentContent.value.trim(),
          // 不设置parent_id，保存在最外层
          tags: ['canvas-export'], // 添加标签标识来源
          metadata: {
            source: 'canvas',
            created_from: 'draft_canvas',
            export_timestamp: new Date().toISOString()
          }
        });
        
        console.log('笔记保存成功:', document);
        showMessage('笔记保存成功！');
        
        // 通知父组件保存成功
        emit('saved', document);
        
        // 1秒后自动关闭对话框
        setTimeout(() => {
          handleClose();
        }, 1000);
        
      } catch (error) {
        console.error('保存笔记失败:', error);
        showMessage('保存失败，请重试', 'error');
      } finally {
        saving.value = false;
      }
    };
    
    return {
      documentTitle,
      documentContent,
      saving,
      titleRules,
      contentRules,
      isValid,
      renderedContent,
      handleClose,
      handleSave,
      showSnackbar,
      snackbarMessage,
      snackbarColor
    };
  }
});
</script>

<style scoped>
.content-section {
  margin-bottom: 16px;
}

.section-label {
  font-size: 14px;
  font-weight: 500;
  color: #424242;
}

.markdown-editor {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 14px !important;
}

.markdown-editor :deep(textarea) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  resize: none !important;
}



.rendered-content {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
}

.rendered-content :deep(h1),
.rendered-content :deep(h2),
.rendered-content :deep(h3),
.rendered-content :deep(h4),
.rendered-content :deep(h5),
.rendered-content :deep(h6) {
  margin-top: 16px;
  margin-bottom: 8px;
  font-weight: 600;
}

.rendered-content :deep(p) {
  margin-bottom: 8px;
}

.rendered-content :deep(ul),
.rendered-content :deep(ol) {
  margin-bottom: 8px;
  padding-left: 20px;
}

.rendered-content :deep(code) {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

.rendered-content :deep(pre) {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin-bottom: 12px;
}

.rendered-content :deep(blockquote) {
  border-left: 4px solid #e0e0e0;
  padding-left: 12px;
  margin: 12px 0;
  color: #666;
}

.rendered-content :deep(table) {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 12px;
}

.rendered-content :deep(th),
.rendered-content :deep(td) {
  border: 1px solid #e0e0e0;
  padding: 8px 12px;
  text-align: left;
}

.rendered-content :deep(th) {
  background-color: #f5f5f5;
  font-weight: 600;
}

/* 左右分栏样式 */
.pr-3 {
  padding-right: 12px;
}

.pl-3 {
  padding-left: 12px;
}

/* 确保编辑器和预览区域高度一致 */
.markdown-editor :deep(.v-field__field) {
  height: 600px;
}

.markdown-editor :deep(.v-field__input) {
  height: 600px;
  align-items: flex-start;
}

/* 预览区域样式优化 */
.preview-container {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  height: 600px;
  overflow-y: auto;
  background-color: #fafafa;
  font-size: 14px;
  line-height: 1.6;
}

/* 滚动条样式 */
.preview-container::-webkit-scrollbar,
.markdown-editor :deep(textarea)::-webkit-scrollbar {
  width: 8px;
}

.preview-container::-webkit-scrollbar-track,
.markdown-editor :deep(textarea)::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.preview-container::-webkit-scrollbar-thumb,
.markdown-editor :deep(textarea)::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.preview-container::-webkit-scrollbar-thumb:hover,
.markdown-editor :deep(textarea)::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style> 