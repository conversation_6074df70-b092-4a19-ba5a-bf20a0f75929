<template>
  <div class="message-wrapper" :class="{ 'user-wrapper': isUser, 'assistant-wrapper': !isUser }">
    <!-- 消息内容 -->
    <div class="message-container">
      <!-- 用户头像放在消息卡片的右侧 -->
      <div v-if="isUser" class="message-avatar user-avatar">
        <v-avatar :color="userAvatarColor" size="32">
          <span class="text-white">{{ userInitials }}</span>
        </v-avatar>
      </div>
      
      <div :class="['chat-message', { 'user-message': isUser, 'assistant-message': !isUser }]">
        <div class="message-content">
          <!-- 显示助手标识和时间戳 -->
          <div class="message-header">
            <span v-if="!isUser" class="assistant-badge">Deep Cognition</span>
            <span v-if="showTimestamp" class="message-time">{{ formatTime(timestamp) }}</span>
          </div>
          <div class="message-body" v-html="formattedContent"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';
import { marked } from 'marked';
import { useAuthStore } from '@/stores/authStore';

export default defineComponent({
  name: 'ChatMessage',
  props: {
    content: {
      type: String,
      required: true
    },
    isUser: {
      type: Boolean,
      default: false
    },
    timestamp: {
      type: [Date, Number, String],
      default: () => new Date()
    },
    showTimestamp: {
      type: Boolean,
      default: true
    }
  },
  setup(props) {
    const authStore = useAuthStore();

    // 获取用户名称的首字母作为头像显示
    const userInitials = computed(() => {
      const name = authStore.user.name || authStore.user.username || 'User';
      
      // 处理中文名称 - 取第一个字符
      if (/[\u4e00-\u9fa5]/.test(name)) {
        return name.charAt(0);
      }
      
      // 处理英文名称 - 取首字母或首尾字母
      const parts = name.split(/\s+/);
      if (parts.length === 1) {
        return name.charAt(0).toUpperCase();
      } else {
        return (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase();
      }
    });
    
    // 根据用户名计算一个稳定的头像颜色
    const userAvatarColor = computed(() => {
      const name = authStore.user.name || authStore.user.username || 'User';
      const colors = [
        '#7c4dff', // 紫色
        '#6200ea', // 深紫色
        '#7B1FA2', // 紫色
        '#9C27B0', // 紫色
        '#C2185B', // 粉红色
        '#D81B60', // 粉红色
        '#E91E63', // 粉红色
        '#5E35B1', // 深紫色
        '#673AB7', // 紫色
        '#8E24AA'  // 紫色
      ];
      
      // 使用名称计算一个索引
      let hashCode = 0;
      for (let i = 0; i < name.length; i++) {
        hashCode = name.charCodeAt(i) + ((hashCode << 5) - hashCode);
      }
      
      // 使用哈希值选择颜色
      const index = Math.abs(hashCode) % colors.length;
      return colors[index];
    });

    // 将消息内容转换为HTML（支持Markdown）
    const formattedContent = computed(() => {
      try {
        // 处理空消息
        if (!props.content.trim()) {
          return '<p class="typing-indicator"><span></span><span></span><span></span></p>';
        }
        return marked(props.content);
      } catch (error) {
        console.error('Markdown解析错误:', error);
        return props.content;
      }
    });

    // 格式化时间
    const formatTime = (time: Date | number | string) => {
      const date = new Date(time);
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      });
    };

    return {
      formattedContent,
      formatTime,
      userInitials,
      userAvatarColor
    };
  }
});
</script>

<style scoped>
.message-wrapper {
  display: flex;
  width: 100%;
  margin-bottom: 14px;
  align-items: flex-start;
}

.user-wrapper {
  justify-content: flex-end;
}

.assistant-wrapper {
  justify-content: flex-start;
}

.message-container {
  display: flex;
  align-items: flex-start;
  flex-direction: row-reverse; /* 用户消息时头像在右侧 */
  max-width: 85%;
}

.user-wrapper .message-container {
  flex-direction: row-reverse; /* 用户消息时头像在右侧 */
}

.assistant-wrapper .message-container {
  flex-direction: row; /* 助手消息时正常顺序 */
}

.chat-message {
  padding: 10px 14px;
  margin-top: 4px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.06);
  display: flex;
  align-items: flex-start;
  transition: all 0.2s ease;
}

.user-message {
  background-color: #f0eafa;
  border: 1px solid #e8e3f7;
  border-top-right-radius: 4px;
  margin-right: 10px; /* 为用户头像腾出空间 */
}

.assistant-message {
  background-color: #ffffff;
  border: 1px solid #e9e9e9;
  border-top-left-radius: 4px;
}

.message-avatar {
  display: flex;
  align-items: flex-start;
  margin-top: 4px; /* 对齐消息框顶部 */
}

.user-avatar {
  margin-left: 4px;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.assistant-badge {
  font-size: 0.7rem;
  color: #654C8C;
  font-weight: 500;
  background-color: rgba(101, 76, 140, 0.08);
  padding: 2px 6px;
  border-radius: 4px;
  letter-spacing: 0.2px;
}

.message-time {
  text-align: right;
  color: #9e9e9e;
  font-size: 0.65rem;
}

.message-body {
  font-size: 0.9rem;
  line-height: 1.5;
  word-break: break-word;
  color: #333333;
}

/* 打字指示器 */
.typing-indicator {
  display: flex;
  align-items: center;
  padding: 4px 8px;
}

.typing-indicator span {
  height: 8px;
  width: 8px;
  float: left;
  margin: 0 1px;
  background-color: #9e9e9e;
  display: block;
  border-radius: 50%;
  opacity: 0.4;
}

.typing-indicator span:nth-of-type(1) {
  animation: 1s blink infinite 0.3333s;
}

.typing-indicator span:nth-of-type(2) {
  animation: 1s blink infinite 0.6666s;
}

.typing-indicator span:nth-of-type(3) {
  animation: 1s blink infinite 0.9999s;
}

@keyframes blink {
  50% {
    opacity: 1;
  }
}

/* Markdown样式 */
.message-body :deep(p) {
  margin: 0.4rem 0;
  color: #333333;
}

.message-body :deep(code) {
  background-color: rgba(0, 0, 0, 0.04);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
  font-size: 0.85em;
  color: #333333;
}

.message-body :deep(pre) {
  background-color: rgba(0, 0, 0, 0.04);
  padding: 10px 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 0.6rem 0;
  color: #333333;
}

.message-body :deep(pre code) {
  background-color: transparent;
  padding: 0;
  font-size: 0.85em;
}

.message-body :deep(h1),
.message-body :deep(h2),
.message-body :deep(h3),
.message-body :deep(h4),
.message-body :deep(h5),
.message-body :deep(h6) {
  color: #333333;
  margin: 0.6rem 0 0.4rem 0;
}

.message-body :deep(h1) { font-size: 1.3em; }
.message-body :deep(h2) { font-size: 1.2em; }
.message-body :deep(h3) { font-size: 1.1em; }
.message-body :deep(h4) { font-size: 1em; }
.message-body :deep(h5) { font-size: 0.95em; }
.message-body :deep(h6) { font-size: 0.9em; }

.message-body :deep(ul), 
.message-body :deep(ol) {
  padding-left: 1.4em;
  margin: 0.4rem 0;
}

.message-body :deep(li) {
  color: #333333;
  margin: 0.3rem 0;
  line-height: 1.4;
}

.message-body :deep(blockquote) {
  color: #555555;
  margin: 0.6rem 0;
  padding: 0.2rem 0 0.2rem 12px;
  border-left: 3px solid #e0e0e0;
  background-color: rgba(0, 0, 0, 0.02);
}

.message-body :deep(img) {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  margin: 0.4rem 0;
}

.message-body :deep(a) {
  color: #7c4dff;
  text-decoration: none;
  border-bottom: 1px dotted #7c4dff;
  transition: all 0.2s ease;
}

.message-body :deep(a:hover) {
  text-decoration: none;
  border-bottom: 1px solid #7c4dff;
}

.message-body :deep(table) {
  border-collapse: collapse;
  width: 100%;
  margin: 0.6rem 0;
  font-size: 0.85em;
}

.message-body :deep(th),
.message-body :deep(td) {
  border: 1px solid #e0e0e0;
  padding: 6px 8px;
  text-align: left;
}

.message-body :deep(hr) {
  border: none;
  border-top: 1px solid #e0e0e0;
  margin: 0.8rem 0;
}
</style> 