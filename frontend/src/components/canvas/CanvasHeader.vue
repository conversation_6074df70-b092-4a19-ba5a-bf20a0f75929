<template>
  <div class="header-wrapper">
    <!-- 折角效果 -->
    <div class="folded-corner"></div>
    
    <v-container fluid class="pa-0">
      <v-row no-gutters align="center" class="header-container">
        <v-col cols="auto" class="mr-3">
          <img 
            src="@/assets/co.png" 
            alt="草稿图标" 
            class="header-icon"
          />
        </v-col>
        
        <v-col cols="auto" class="d-flex align-center">
          <h2 class="font-weight-medium mb-0" style="letter-spacing: 0.1em; font-size: 0.95rem;">
            使用<span style="font-family: 'Courier New', Courier, monospace;">[草稿纸]</span>与AI合作
          </h2>
        </v-col>
        
        <!-- 版本切换器 -->
        <v-col v-if="versionInfo && versionInfo.totalVersions > 1" cols="auto" class="ml-3">
          <div class="version-switcher">
            <v-btn
              variant="text"
              icon
              size="small"
              @click="$emit('previousVersion')"
              :disabled="versionInfo.currentVersion <= 0"
              class="version-btn"
            >
              <v-icon>mdi-chevron-left</v-icon>
            </v-btn>
            
            <span class="version-info">
              {{ versionInfo.currentVersion + 1 }} / {{ versionInfo.totalVersions }}
            </span>
            
            <v-btn
              variant="text"
              icon
              size="small"
              @click="$emit('nextVersion')"
              :disabled="versionInfo.currentVersion >= versionInfo.totalVersions - 1"
              class="version-btn"
            >
              <v-icon>mdi-chevron-right</v-icon>
            </v-btn>
          </div>
        </v-col>
        
        <!-- <v-col cols="auto" class="ml-3">
          <v-btn
            color="white"
            class="custom-btn"
            size="small"
            prepend-icon="mdi-pause"
            @click="handlePause"
          >
            暂停
          </v-btn>
        </v-col> -->
        
        <v-spacer></v-spacer>
        
        <!-- 添加复制、分享和导出PDF按钮 -->
        <v-col cols="auto" class="d-flex align-center mr-4">
          <!-- 添加保存至笔记按钮 -->
          <v-btn
            variant="text"
            density="comfortable"
            size="small"
            class="action-btn"
            @click="handleSaveToNotes"
          >
            <v-icon size="small" class="mr-1">mdi-content-save-outline</v-icon>
            <span>保存至笔记</span>
          </v-btn>
          
          <!-- 添加 Chat to Edit 按钮 -->
          <v-btn
            variant="text"
            density="comfortable"
            size="small"
            class="action-btn ml-2"
            @click="handleChatToEdit"
          >
            <v-icon size="small" class="mr-1">mdi-chat-processing</v-icon>
            <span>聊天编辑</span>
          </v-btn>
          
          <!-- 添加编辑文本按钮 -->
          <!-- <v-btn
            variant="text"
            density="comfortable"
            size="small"
            class="action-btn ml-2"
            @click="handleEditText"
          >
            <v-icon size="small" class="mr-1">mdi-pencil</v-icon>
            <span>编辑文本</span>
          </v-btn> -->
          
          <!-- <v-btn
            variant="text"
            density="comfortable"
            size="small"
            class="action-btn ml-2"
            @click="handleShare"
          >
            <v-icon size="small" class="mr-1">mdi-share-variant</v-icon>
            <span>分享</span>
          </v-btn> -->
          
          <!-- <v-btn
            variant="text"
            density="comfortable"
            size="small"
            class="action-btn ml-2"
            @click="handleExportPdf"
          >
            <v-icon size="small" class="mr-1">mdi-file-pdf-box</v-icon>
            <span>导出PDF</span>
          </v-btn> -->
        </v-col>
      </v-row>
    </v-container>
    <div class="dashed-divider"></div>
    
    <!-- 添加暂停操作的提示框 -->
    <v-snackbar
      v-model="showPauseSnackbar"
      timeout="3000"
      color="info"
      location="top"
    >
      正在暂停当前操作，请稍候...
      
      <template v-slot:actions>
        <v-btn
          variant="text"
          @click="showPauseSnackbar = false"
        >
          关闭
        </v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { PropType } from 'vue';

export default defineComponent({
  name: 'CanvasHeader',
  props: {
    versionInfo: {
      type: Object as PropType<{
        totalVersions: number;
        currentVersion: number;
        versions: Array<{
          version: number;
          content: string;
          timestamp: number;
          round?: number;
          draft_id?: string;
        }>;
        isLatestVersion: boolean;
      } | null>,
      default: null
    }
  },
  emits: ['pause', 'copy', 'share', 'exportPdf', 'editText', 'previousVersion', 'nextVersion', 'chatToEdit', 'saveToNotes'],
  setup(props, { emit }) {
    const showPauseSnackbar = ref(false);
    const isEditing = ref(false);

    const handlePause = () => {
      showPauseSnackbar.value = true;
      emit('pause');
    };
    
    const handleCopy = () => {
      emit('copy');
    };
    
    const handleShare = () => {
      emit('share');
    };
    
    const handleExportPdf = () => {
      console.log('导出PDF');
      emit('exportPdf');
    };
    
    const handleEditText = () => {
      emit('editText');
    };
    
    const handleChatToEdit = () => {
      emit('chatToEdit');
    };
    
    const handleSaveToNotes = () => {
      emit('saveToNotes');
    };
    
    return {
      showPauseSnackbar,
      handlePause,
      handleCopy,
      handleShare,
      handleExportPdf,
      handleEditText,
      handleChatToEdit,
      handleSaveToNotes
    };
  }
});
</script>

<style scoped>
.header-wrapper {
  background-color: white;
  width: 100%;
  border-bottom: none;
  position: relative;
  overflow: hidden;
}

.header-container {
  min-height: 56px;
  padding: 0 16px;
}

.header-icon {
  width: 36px;
  height: 36px;
  object-fit: contain;
}

.dashed-divider {
  height: 1px;
  width: 100%;
  background: repeating-linear-gradient(to right, #e0e0e0, #e0e0e0 5px, transparent 5px, transparent 10px);
}

/* 折角效果 */
.folded-corner {
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 24px 24px 0;
  border-color: transparent #e8e6f6 transparent transparent;
  border-radius: 0px 0px 0px 0px;
  z-index: 1;
  box-shadow: -2px 2px 3px rgba(0, 0, 0, 0.165);
}

.custom-btn {
  background-color: #654c8c !important;
  color: white !important;
  font-size: 0.85rem;
}

/* 确保图标也是白色 */
.custom-btn :deep(.v-icon) {
  color: white;
}

/* 添加按钮样式 */
.action-btn {
  color: #654c8c !important;
  border-radius: 20px;
  font-size: 0.85rem;
}

.action-btn:hover {
  background-color: rgba(101, 76, 140, 0.1);
}

/* 版本切换器样式 */
.version-switcher {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  background-color: rgba(101, 76, 140, 0.05);
  border-radius: 20px;
  border: 1px solid rgba(101, 76, 140, 0.2);
}

.version-btn {
  color: #654c8c !important;
  width: 28px;
  height: 28px;
}

.version-btn:hover {
  background-color: rgba(101, 76, 140, 0.1);
}

.version-btn:disabled {
  opacity: 0.3;
}

.version-info {
  font-size: 0.85rem;
  font-weight: 500;
  color: #654c8c;
  min-width: 40px;
  text-align: center;
}
</style>