<template>
  <div class="canvas-editor">
    <!-- Header 固定在顶部 -->
    <div class="canvas-header">
      <CanvasHeader 
        :version-info="versionInfo"
        @pause="handlePause" 
        @share="handleShare"
        @exportPdf="handleExportPdf"
        @editText="handleEditTextToggle"
        @chatToEdit="handleChatToEdit"
        @previousVersion="handlePreviousVersion"
        @nextVersion="handleNextVersion"
        @saveToNotes="handleSaveToNotes"
      />
    </div>
      
    <div class="canvas-content">
      
      <div class="editor-container">
        <MarkdownEditor
          :initial-content="markdownContent"
          @update:content="updateContent"
          @edit-command="handleEditCommand"
          class="markdown-content pointer-events-auto"
          ref="markdownEditorRef"
        />
      </div>
    </div>
    
    <!-- Chat to Edit 对话框 -->
    <CommandInput
      v-model="showChatToEdit"
      :position="chatToEditPosition"
      title="对话编辑 - 整页"
      placeholder="请输入您想要对整个研究报告进行的修改指令..."
      @command="handleChatToEditCommand"
      @close="handleChatToEditClose"
    />
    
    <!-- 添加局部 Snackbar -->
    <v-snackbar
      v-model="showSnackbar"
      :timeout="snackbarTimeout"
      :color="snackbarColor"
      location="top"
    >
      {{ snackbarMessage }}
      <template v-slot:actions>
        <v-btn
          color="white"
          variant="text"
          @click="showSnackbar = false"
        >
          关闭
        </v-btn>
      </template>
    </v-snackbar>
    
    <!-- 添加全文编辑对话框 -->
    <div v-if="showEditDialog" class="edit-dialog-overlay" @click.self="cancelEdit">
      <div class="edit-dialog">
        <div class="edit-dialog-header">
          <h3>编辑研究报告</h3>
          <div class="edit-dialog-actions">
            <button class="edit-btn save-btn" @click="submitEdit">保存修改</button>
            <button class="edit-btn cancel-btn" @click="cancelEdit">取消</button>
          </div>
        </div>
        <div class="edit-dialog-content">
          <textarea 
            v-model="editableContent" 
            class="edit-textarea"
            placeholder="编辑研究报告内容..."
          ></textarea>
        </div>
      </div>
    </div>

    <!-- 保存至笔记对话框 -->
    <PreSave
      v-model:visible="showPreSaveDialog"
      :content="currentMarkdownContent"
      @saved="handleNoteSaved"
    />
  </div>
</template>

<script lang="ts">
import { ref, onMounted, inject, nextTick, watchEffect, reactive, watch, computed } from 'vue';
import MarkdownEditor from '@/components/edit/index.vue';
import CanvasHeader from './CanvasHeader.vue';
import CommandInput from '@/components/edit/command.vue';
import { useCanvasEditor } from '@/composables/useCanvasEditor';
import { chatApi } from '@/api/chat';
import { PropType } from 'vue';
import PreSave from '@/components/presave/index.vue';

export default {
  name: 'CanvasEditor',
  components: {
    MarkdownEditor,
    CanvasHeader,
    CommandInput,
    PreSave
  },
  props: {
    initialContent: {
      type: String,
      default: ''
    },
    editorId: {
      type: String,
      required: true
    },
    versionInfo: {
      type: Object as PropType<{
        totalVersions: number;
        currentVersion: number;
        versions: Array<{
          version: number;
          content: string;
          timestamp: number;
          round?: number;
          draft_id?: string;
        }>;
        isLatestVersion: boolean;
      } | null>,
      default: null
    }
  },
  emits: ['update:content', 'pause', 'edit-command', 'export-pdf', 'edit-report', 'switchVersion'],
  setup(props, { emit }) {
    const markdownEditorRef = ref(null);
    
    const markdownContent = ref(props.initialContent || [
      '# 报告生成中',
      '',
      '我们正在为您生成一份详细的报告，请稍候片刻。',
      '',
      '请耐心等待......',
    ].join('\n'));

    // 使用Canvas编辑器组合式函数
    const canvasEditor = useCanvasEditor();
    
    // 获取通信接口
    const canvasInterface = inject('canvasInterface', null);
    
    // 差异显示控制
    const showDiffView = ref(false);
    
    // 切换差异显示
    const toggleDiffView = () => {
      showDiffView.value = !showDiffView.value;
    };
    
    // 编辑相关状态
    const isEditable = ref(false);
    const showEditDialog = ref(false);
    const editableContent = ref('');
    const originalContent = ref('');
    
    // Chat to Edit 相关状态
    const showChatToEdit = ref(false);
    const chatToEditPosition = ref({ top: 100, left: 100 });
    
    // PreSave 相关状态
    const showPreSaveDialog = ref(false);
    const currentMarkdownContent = computed(() => {
      return markdownContent.value;
    });
    
    // 添加对editableContent的监听，实现实时预览
    watch(editableContent, (newContent) => {
      if (showEditDialog.value) {
        // 只更新编辑器显示内容，但不发送到后端
        updateContent(newContent);
      }
    });
    
    // 动画状态
    const animationState = reactive({
      inProgress: false,
      progress: 0
    });

    // Snackbar 相关状态
    const showSnackbar = ref(false);
    const snackbarMessage = ref('');
    const snackbarColor = ref('success');
    const snackbarTimeout = ref(2000);
    
    // 显示 Snackbar 的方法
    const showMessage = (message: string, color: string = 'success', timeout: number = 2000) => {
      snackbarMessage.value = message;
      snackbarColor.value = color;
      snackbarTimeout.value = timeout;
      showSnackbar.value = true;
    };

    // 当编辑器内容更新时
    const updateContent = (newContent: string) => {
      // 更新组合式函数中的内容
      canvasEditor.updateContent(newContent);
      // 更新组件内的内容状态
      markdownContent.value = newContent;
      
      // 更新共享状态
      if (canvasInterface?.state) {
        canvasInterface.state.content = newContent;
      }
      
      // 触发事件传递给父组件
      emit('update:content', newContent);
    };

    // 处理编辑文本的切换
    const handleEditTextToggle = () => {
      // 进入编辑模式
      startEdit();
      showMessage('您现在可以编辑整个研究报告', 'info', 3000);
    };

    // 处理Chat to Edit对话
    const handleChatToEdit = () => {
      // 计算对话框位置（页面中央偏上）
      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight;
      chatToEditPosition.value = {
        top: Math.max(100, windowHeight * 0.3),
        left: Math.max(100, (windowWidth - 400) / 2) // 400是对话框宽度
      };
      
      showChatToEdit.value = true;
      showMessage('请输入您想要对整个研究报告进行的修改指令', 'info', 3000);
    };

    // 处理Chat to Edit命令
    const handleChatToEditCommand = (command: string) => {
      console.log('收到整页编辑命令:', command);
      // TODO: 这里应该调用专门的整页编辑接口，而不是标准的feedback接口
      // 暂时留空，后端会实现特殊的处理逻辑
      
      // 构造特殊的命令格式，标识这是整页编辑请求
      const formattedMessage = `[整页编辑请求] ${command}`;
      
      // 向上传递事件，但标识这是chat-to-edit类型的请求
      emit('edit-command', formattedMessage);
      
      showMessage('已发送整页编辑请求，AI正在处理...', 'success', 2000);
    };

    // 关闭Chat to Edit对话框
    const handleChatToEditClose = () => {
      showChatToEdit.value = false;
    };

    // 处理保存至笔记
    const handleSaveToNotes = () => {
      console.log('打开保存至笔记对话框');
      showPreSaveDialog.value = true;
      showMessage('准备保存当前内容至笔记', 'info', 2000);
    };

    // 处理笔记保存成功
    const handleNoteSaved = (document: any) => {
      console.log('笔记保存成功:', document);
      showMessage(`笔记 "${document.title}" 保存成功！`, 'success', 3000);
    };

    // 将编辑记录保存到trajectory
    const saveEditToTrajectory = async (originalContent: string, editedContent: string) => {
      try {
        // 获取当前会话ID
        const sessionId = canvasInterface?.state?.sessionId;
        if (!sessionId) {
          console.warn('无法获取会话ID，编辑记录未保存到trajectory');
          return;
        }
        
        // 构造编辑记录
        const editRecord = {
          type: 'user_edit',
          timestamp: new Date().toISOString(),
          original_content: originalContent,
          edited_content: editedContent
        };
        
        // 发送编辑记录到后端
        const response = await fetch(`/api/research/${sessionId}/edit_record`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(editRecord)
        });
        
        if (!response.ok) {
          throw new Error('保存编辑记录失败: ' + response.statusText);
        }
        
        console.log('编辑记录已保存到trajectory');
      } catch (error) {
        console.error('保存编辑记录失败:', error);
      }
    };

    // 保存编辑内容
    const submitEdit = () => {
      if (editableContent.value !== originalContent.value) {
        // 构造formattedMessage
        const formattedMessage = `以下是我修改后的研究报告，请根据我的修改进行回应：\n\n${editableContent.value}`;
        
        // 发送编辑命令
        emit('edit-command', formattedMessage);
        
        // 触发edit-report事件
        emit('edit-report', editableContent.value);
        
        // 同时将编辑版本保存到trajectory中
        saveEditToTrajectory(originalContent.value, editableContent.value);
        
        showMessage('修改已保存', 'success', 2000);
      } else {
        showMessage('内容未变化', 'info', 2000);
      }
      
      // 关闭编辑对话框
      showEditDialog.value = false;
    };
    
    // 取消编辑
    const cancelEdit = () => {
      // 恢复原始内容
      updateContent(originalContent.value);
      showEditDialog.value = false;
      showMessage('已取消编辑', 'info', 2000);
    };

    // 暂停处理
    const handlePause = () => {
      console.log('CanvasEditor: 暂停按钮被点击');
      emit('pause');
    };

    // 处理标签替换（普通版本）
    const handleTagReplacement = (tagString: string) => {
      // 使用更新后的标签替换方法
      const result = canvasEditor.parseAndReplaceTags(tagString);
      
      // 如果替换成功，我们需要确保编辑器的视图也更新了
      if (result && markdownEditorRef.value && (markdownEditorRef.value as any).editor) {
        // 获取最新的Markdown内容
        const updatedContent = canvasEditor.getMarkdownContent();
        
        // 如果内容有变更，需要更新本地状态
        if (updatedContent !== markdownContent.value) {
          markdownContent.value = updatedContent;
          
          // 确保编辑器内容与最新的Markdown保持同步
          if (typeof (markdownEditorRef.value as any).editor.commands.setMarkdown === 'function') {
            // @ts-ignore
            (markdownEditorRef.value as any).editor.commands.setMarkdown(updatedContent);
          }
        }
      }
      
      // 更新操作结果
      if (canvasInterface?.state) {
        canvasInterface.state.lastActionResult = result;
      }
      
      return result;
    };

    // 处理复制事件
    const handleCopy = async () => {
      // 获取当前编辑器内容
      const content = markdownContent.value;
      
      try {
        // 尝试将内容复制到剪贴板
        await navigator.clipboard.writeText(content);
        console.log('内容已复制到剪贴板');
        
        // 可以添加一个提示或通知
        if (canvasInterface?.methods?.showToast) {
          canvasInterface.methods.showToast('内容已复制到剪贴板');
        }
      } catch (error) {
        console.error('复制失败:', error);
        
        // 复制失败的提示
        if (canvasInterface?.methods?.showToast) {
          canvasInterface.methods.showToast('复制失败，请重试', 'error');
        }
      }
    };

    // 处理分享事件
    const handleShare = () => {
      // 获取当前内容
      const content = markdownContent.value;
      
      // 共享逻辑
      console.log('分享内容:', content);
      
      // 如果有Web Share API支持，使用它
      if (navigator.share) {
        navigator.share({
          title: '草稿纸内容',
          text: content,
        }).then(() => {
          console.log('成功分享');
        }).catch((error) => {
          console.error('分享失败:', error);
        });
      } else {
        // 无Web Share API支持，可以触发其他分享方式
        console.log('当前环境不支持分享功能');
        
        // 可以通过界面提示
        if (canvasInterface?.methods?.showToast) {
          canvasInterface.methods.showToast('当前环境不支持分享功能，请手动复制后分享');
        }
      }
    };
    
    // 处理编辑命令
    const handleEditCommand = (formattedMessage: string) => {
      console.log('收到编辑命令:', formattedMessage);
      // 不再直接调用 handleUserInput，只向上传递事件
      // handleUserInput(formattedMessage);
      // 向上传递事件
      emit('edit-command', formattedMessage);
    };
    
    // 处理导出PDF
    const handleExportPdf = async () => {
      console.log('CanvasEditor: 导出PDF按钮被点击');
      
      try {
        // 显示加载提示
        showMessage('正在生成PDF，请稍候...', 'info');

        // 获取原始Markdown内容并处理AI光标标记
        let markdownText = markdownContent.value;
        
        // 清理所有AI相关标记
        markdownText = markdownText
          .replace(/\|AI\|/g, '') // 移除AI光标标记
          .replace(/✎/g, '') // 移除编辑标记
          .replace(/\{\{.*?\}\}/g, '') // 移除模板标记
          .replace(/\[\[.*?\]\]/g, '') // 移除特殊标记
          .replace(/\(\(.*?\)\)/g, ''); // 移除注释标记
        
        // 获取当前内容的标题
        let title = '报告';
        try {
          // 尝试从内容中提取第一个标题作为PDF标题
          const contentLines = markdownText.split('\n');
          for (const line of contentLines) {
            if (line.startsWith('# ')) {
              title = line.substring(2).trim();
              break;
            }
          }
        } catch (e) {
          console.warn('无法提取标题:', e);
        }
        
        // 获取当前日期时间作为文件名的一部分
        const now = new Date();
        const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
        const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');
        const fileName = `${title}_${dateStr}_${timeStr}.pdf`;
        
        // 调用API生成PDF
        const pdfBlob = await chatApi.markdownToPdf(markdownText, fileName);
        
        // 创建下载链接
        const url = window.URL.createObjectURL(pdfBlob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        
        // 清理
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        // 导出成功提示
        showMessage('PDF导出成功', 'success');
        
        // 触发导出事件
        emit('export-pdf', fileName);
        
      } catch (error) {
        console.error('PDF导出失败:', error);
        showMessage('PDF导出失败，请重试', 'error', 3000);
      }
    };
    
    // 设置编辑器可编辑状态
    const setEditable = (editable: boolean) => {
      isEditable.value = editable;
      
      if (markdownEditorRef.value && typeof markdownEditorRef.value.setEditable === 'function') {
        markdownEditorRef.value.setEditable(editable);
      }
    };

    // 监听props变化
    watchEffect(() => {
      if (props.initialContent && props.initialContent !== markdownContent.value) {
        // console.log('[DEBUG] CanvasEditor: 接收到新的初始内容');
        // console.log('[DEBUG] 新内容前50个字符:', props.initialContent.substring(0, 50) + "...");
        // console.log('[DEBUG] 当前内容前50个字符:', markdownContent.value.substring(0, 50) + "...");
        
        // 检查是否是版本切换导致的内容变化（不包含AI光标标记）
        const isVersionSwitch = !props.initialContent.includes('|AI|');
        
        // 更新本地内容状态
        markdownContent.value = props.initialContent;
        
        // 更新共享状态
        if (canvasInterface?.state) {
          canvasInterface.state.content = props.initialContent;
        }
      }
    });

    onMounted(async () => {
      // 等待下一个DOM更新周期
      await nextTick();
      
      // 在组件挂载后，如果编辑器实例可用，就设置到canvasEditor中
      if (markdownEditorRef.value && (markdownEditorRef.value as any).editor) {
        canvasEditor.setEditor((markdownEditorRef.value as any).editor);
        
        // 通知编辑器已就绪
        if (canvasInterface?.state) {
          canvasInterface.state.ready = true;
          canvasInterface.state.content = markdownContent.value;
        }
        
        // 通知父组件编辑器已就绪
        window.dispatchEvent(new CustomEvent('canvas-ready', {
          detail: { ready: true }
        }));
      }
    });

    // 监听编辑器内容变化，确保我们的状态总是最新的
    watchEffect(() => {
      if (canvasEditor.currentContent.value && canvasEditor.currentContent.value !== markdownContent.value) {
        markdownContent.value = canvasEditor.currentContent.value;
      }
      
      // 同步动画状态
      if (canvasInterface?.state) {
        canvasInterface.state.animationInProgress = canvasEditor.isAnimating.value;
      }
    });

    // 启动编辑对话框
    const startEdit = () => {
      editableContent.value = markdownContent.value;
      originalContent.value = markdownContent.value; // 保存原始内容
      showEditDialog.value = true;
    };

    // 处理版本切换
    const handlePreviousVersion = () => {
      const info = props.versionInfo;
      if (info && info.currentVersion > 0) {
        emit('switchVersion', props.editorId, info.currentVersion - 1);
        showMessage(`正在切换到Version ${info.currentVersion}`, 'info', 2000);
      }
    };

    const handleNextVersion = () => {
      const info = props.versionInfo;
      if (info && info.currentVersion < info.totalVersions - 1) {
        emit('switchVersion', props.editorId, info.currentVersion + 1);
        showMessage(`正在切换到Version ${info.currentVersion + 2}`, 'info', 2000);
      }
    };

    return {
      markdownContent,
      markdownEditorRef,
      showDiffView,
      toggleDiffView,
      isEditable,
      showEditDialog,
      editableContent,
      handleEditTextToggle,
      submitEdit,
      cancelEdit,
      updateContent,
      handlePause,
      handleTagReplacement,
      handleAnimatedTagReplacement: (tagString: string, options: any = {}) => {
        console.log('暂不支持');
        return handleTagReplacement(tagString);
      },
      handleCopy,
      handleShare,
      handleEditCommand,
      handleExportPdf,
      showMessage,
      showSnackbar,
      snackbarMessage,
      snackbarColor,
      snackbarTimeout,
      animationState,
      // 暴露canvasEditor，以便外部组件可以调用
      canvasEditor,
      setEditable,
      startEdit,
      handlePreviousVersion,
      handleNextVersion,
      showChatToEdit,
      chatToEditPosition,
      handleChatToEdit,
      handleChatToEditCommand,
      handleChatToEditClose,
      // PreSave 相关
      showPreSaveDialog,
      currentMarkdownContent,
      handleSaveToNotes,
      handleNoteSaved
    };
  }
}
</script>

<style scoped>
.canvas-editor {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: visible; /* 确保内容不溢出 */
  border-radius: 8px;
  background-color: white;
  /* box-shadow: 0 1px 4px rgba(0,0,0,0.1); */
}

.canvas-header {
  flex-shrink: 0; /* 防止头部被压缩 */
  position: sticky; /* 使头部固定 */
  top: 0;
  z-index: 10; 
  background: white; /* 确保背景色 */
}

.canvas-content {
  flex: 1;
  overflow-y: hidden; /* 内部内容不可滚动 */
  padding: 0 0 32px 0; /* 底部留出空间 */
  display: flex;
  flex-direction: column;
  justify-content: center; /* 水平居中 */
  box-sizing: border-box;
  width: 100%;
  background-color: white;
}

.editor-container {
  width: 95%; /* 编辑器容器宽度 */
  box-sizing: border-box;
  max-width: 1200px;
  margin: 0 auto;
}

.editor-container.editable-mode {
  background-color: rgba(124, 77, 255, 0.05);
  border: 1px dashed rgba(124, 77, 255, 0.3);
  padding: 8px;
  box-shadow: 0 0 0 2px rgba(124, 77, 255, 0.1);
  pointer-events: auto !important;
}

.editor-container.editable-mode .ProseMirror {
  cursor: text !important;
  background-color: rgba(255, 255, 255, 0.7);
  pointer-events: auto !important;
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}

.editor-container.editable-mode::before {
  content: "可编辑模式";
  position: absolute;
  top: -20px;
  right: 10px;
  background-color: rgba(124, 77, 255, 0.8);
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  z-index: 10;
}

.markdown-content {
  width: 100%;
  display: flex;
}

/* AI光标标记样式增强，确保与AICursorMark扩展的效果一致 */
:deep(.ai-cursor-mark), :deep(.cursor-marker) {
  display: none; /* 不显示蓝色光标，只使用AICursorMark扩展提供的紫色光标 */
}

/* 删除自定义光标样式，使用AICursorMark扩展处理|AI|标记 */
.ai-cursor-mark, .cursor-marker {
  /* 空样式，删除自定义光标 */
  display: none;
}

/* 差异显示样式 */
.diff-container {
  width: 95%;
  max-width: 1200px;
  margin: 16px auto;
  border: 1px solid #eaeaea;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  animation: slide-down 0.3s ease;
}

@keyframes slide-down {
  from { 
    opacity: 0;
    transform: translateY(-20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

.diff-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eaeaea;
}

.diff-title {
  font-weight: 600;
  color: #333;
}

.diff-stats {
  display: flex;
  gap: 8px;
  font-size: 0.85rem;
}

.diff-insert-count {
  color: #27ae60;
  font-weight: bold;
}

.diff-delete-count {
  color: #e74c3c;
  font-weight: bold;
}

.diff-close-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
}

.diff-close-btn:hover {
  background-color: #eee;
}

.diff-view {
  padding: 16px;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
  font-family: 'SF Mono', Monaco, Menlo, Consolas, 'Liberation Mono', monospace;
}

.diff-toggle {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 8px;
}

.diff-toggle-btn {
  position: relative;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  padding: 4px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s ease;
}

.diff-toggle-btn:hover {
  background-color: #e8e8e8;
}

.diff-toggle-btn.active {
  background-color: #e8f4fd;
  border-color: #b3d7ff;
  color: #0d6efd;
}

.diff-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #e74c3c;
  color: white;
  font-size: 0.7rem;
  min-width: 16px;
  height: 16px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

/* 差异样式 */
:deep(.diff-equal) {
  color: #333;
}

:deep(.diff-delete) {
  color: #e74c3c;
  text-decoration: line-through;
  background-color: rgba(231, 76, 60, 0.1);
  transition: background-color 0.3s ease;
}

:deep(.diff-delete:hover) {
  background-color: rgba(231, 76, 60, 0.2);
}

:deep(.diff-insert) {
  color: #27ae60;
  background-color: rgba(39, 174, 96, 0.1);
  font-weight: bold;
  transition: background-color 0.3s ease;
}

:deep(.diff-insert:hover) {
  background-color: rgba(39, 174, 96, 0.2);
}

/* 动画高亮效果 */
:deep(.diff-delete-highlight) {
  color: #e74c3c;
  text-decoration: line-through;
  background-color: rgba(231, 76, 60, 0.4);
  animation: highlight-delete 1s ease-in-out;
  padding: 0 2px;
  border-radius: 2px;
  font-weight: bold;
}

:deep(.diff-insert-highlight) {
  color: #27ae60;
  background-color: rgba(39, 174, 96, 0.4);
  animation: highlight-insert 1s ease-in-out;
  padding: 0 2px;
  border-radius: 2px;
  font-weight: bold;
}

@keyframes highlight-delete {
  0%, 100% { background-color: rgba(231, 76, 60, 0.4); }
  50% { background-color: rgba(231, 76, 60, 0.7); }
}

@keyframes highlight-insert {
  0%, 100% { background-color: rgba(39, 174, 96, 0.4); }
  50% { background-color: rgba(39, 174, 96, 0.7); }
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

:deep(.status-pending) {
  opacity: 0.5;
}

.markdown-content :deep(h1) {
  margin-bottom: 1rem;
  color: #333333;
}

.markdown-content :deep(h2) {
  margin-top: 2rem;
  margin-bottom: 1rem;
  color: #333333;
}

.markdown-content :deep(h3),
.markdown-content :deep(h4),
.markdown-content :deep(h5),
.markdown-content :deep(h6) {
  color: #333333;
}

.markdown-content :deep(p) {
  margin-bottom: 1rem;
  color: #333333;
}

.markdown-content :deep(li) {
  color: #333333;
}

.markdown-content :deep(blockquote) {
  color: #555555;
}

.markdown-content :deep(code) {
  color: #333333;
}

.markdown-content :deep(table) {
  color: #333333;
}

.no-pointer-events {
  pointer-events: none !important; 
}

.pointer-events-auto {
  pointer-events: auto !important;
}

/* 编辑对话框样式 */
.edit-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.edit-dialog {
  width: 80%;
  max-width: 900px;
  height: 80%;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.edit-dialog-header {
  padding: 12px 16px;
  background-color: #7c4dff;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.edit-dialog-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.edit-dialog-actions {
  display: flex;
  gap: 8px;
}

.edit-btn {
  padding: 4px 12px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.save-btn {
  background-color: white;
  color: #7c4dff;
}

.save-btn:hover {
  background-color: rgba(255, 255, 255, 0.9);
}

.cancel-btn {
  background-color: transparent;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.cancel-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.edit-dialog-content {
  flex: 1;
  padding: 16px;
  overflow: hidden;
}

.edit-textarea {
  width: 100%;
  height: 100%;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-family: "Courier New", monospace;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  overflow-y: auto;
}

.edit-textarea:focus {
  outline: none;
  border-color: #7c4dff;
  box-shadow: 0 0 0 2px rgba(124, 77, 255, 0.1);
}
</style> 