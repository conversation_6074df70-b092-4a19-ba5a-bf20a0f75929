<template>
    <div class="sidebar" :class="{ 'collapsed': !isOpen }">
      <div class="sidebar-back-btn-wrapper">
        <v-btn class="back-home-btn" color="#654C8C" variant="text" @click="$router.push('/')">
          <v-icon left>mdi-arrow-left</v-icon>
          返回主页
        </v-btn>
      </div>
      <button @click="toggleSidebar" class="sidebar-toggle-btn">
        <v-icon>{{ isOpen ? 'mdi-chevron-left' : 'mdi-chevron-right' }}</v-icon>
      </button>
      <div class="sidebar-actual-content" v-show="isOpen">
        <h3 class="sidebar-title">导航树</h3>
        
        <!-- 美化的提示信息 -->
        <div class="sidebar-tip">
          <v-icon size="18" class="tip-icon">mdi-information-outline</v-icon>
          <span class="tip-text">右键节点查看更多操作</span>
        </div>
        
        <!-- 使用TreeView组件 -->
        <TreeView 
          :nodes="nodes" 
          :selected-node-id="selectedNodeId"
          :dragging-node="draggingNode"
          @node-selected="handleNodeSelected"
          @update:nodes="updateNodes"
          @add-child="handleAddChild"
          @delete-node="handleDeleteNode"
          @rename-node="handleRenameNode"
          @move-node="handleMoveNode"
          @drag-start="handleDragStart"
          @drag-end="handleDragEnd"
        />
      </div>
    </div>
  </template>
  
  <script>
  import { ref, defineEmits } from 'vue';
  import TreeView from '../TreeView.vue';
  
  export default {
    name: 'CanvasSidebar',
    components: {
      TreeView
    },
    props: {
      nodes: {
        type: Array,
        required: true
      },
      selectedNodeId: {
        type: [Number, String],
        default: null
      }
    },
    emits: ['node-selected', 'update:nodes', 'toggle', 'add-child', 'delete-node', 'rename-node', 'move-node'],
    setup(props, { emit }) {
      const isOpen = ref(true);
      const draggingNode = ref(null);
      
      const toggleSidebar = () => {
        isOpen.value = !isOpen.value;
        emit('toggle', isOpen.value);
      };
      
      const handleNodeSelected = (node) => {
        emit('node-selected', node);
      };
      
      const updateNodes = (updatedNodes) => {
        emit('update:nodes', updatedNodes);
      };

      const handleAddChild = (node) => {
        emit('add-child', node);
      };

      const handleDeleteNode = (node) => {
        emit('delete-node', node);
      };

      const handleRenameNode = (data) => {
        emit('rename-node', data);
      };

      const handleMoveNode = (data) => {
        emit('move-node', data);
      };
      
      const handleDragStart = (node) => {
        draggingNode.value = node;
      };
      
      const handleDragEnd = () => {
        draggingNode.value = null;
      };
      
      return {
        isOpen,
        draggingNode,
        toggleSidebar,
        handleNodeSelected,
        updateNodes,
        handleAddChild,
        handleDeleteNode,
        handleRenameNode,
        handleMoveNode,
        handleDragStart,
        handleDragEnd
      };
    }
  }
  </script>
  
  <style scoped>
  .sidebar {
    width: 280px; /* 侧边栏展开宽度 */
    height: 100%;
    background-color: #f7f6fb; /* 与 interface-card 背景色协调 */
    border-right: 1px solid #d9d6ef; /* 与 interface-card 边框色协调 */
    transition: width 0.3s ease;
    display: flex;
    flex-direction: column;
    position: relative; /* 用于绝对定位切换按钮 */
    flex-shrink: 0; /* 防止侧边栏在空间不足时被压缩 */
  }
  
  .sidebar.collapsed {
    width: 60px; /* 侧边栏收起宽度 */
  }
  
  .sidebar-toggle-btn {
    position: absolute;
    top: 15px;
    right: -18px; /* 使按钮部分突出在主内容区边缘 */
    z-index: 100; /* 确保按钮在最上层 */
    background-color: #654C8C; /* Vuetify 主题色 */
    color: white;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    transition: right 0.3s ease, transform 0.3s ease;
  }
  
  .sidebar.collapsed .sidebar-toggle-btn {
    right: 50%;
    transform: translateX(50%);
  }
  
  .sidebar-actual-content {
    padding: 20px 15px;
    overflow-y: auto; /* 如果树内容过长，允许滚动 */
    flex-grow: 1;
    margin-top: 50px; /* 为切换按钮留出空间，如果按钮在内容区上方 */
  }
  
  .sidebar.collapsed .sidebar-actual-content {
    display: none; /* 或者使用 v-if/v-show 控制 */
  }
  
  .sidebar.collapsed .sidebar-back-btn-wrapper {
    display: none; /* 折叠时隐藏返回主页按钮 */
  }
  
  .sidebar-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #654C8C;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(101, 76, 140, 0.2);
  }
  
  .sidebar-tip {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    margin-bottom: 16px;
    background: linear-gradient(135deg, #f3f1ff 0%, #e8e5ff 100%);
    border: 1px solid rgba(101, 76, 140, 0.15);
    border-radius: 8px;
    font-size: 0.85rem;
    color: #5a4a77;
    transition: all 0.2s ease;
  }
  
  .sidebar-tip:hover {
    background: linear-gradient(135deg, #ebe7ff 0%, #ddd8ff 100%);
    border-color: rgba(101, 76, 140, 0.25);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(101, 76, 140, 0.1);
  }
  
  .tip-icon {
    color: #654C8C;
    opacity: 0.8;
  }
  
  .tip-text {
    font-weight: 500;
    line-height: 1.3;
  }
  
  .sidebar-back-btn-wrapper {
    padding: 18px 18px 0 18px;
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
  .back-home-btn {
    font-size: 15px;
    font-weight: 500;
    color: #654C8C;
    background: transparent;
    border-radius: 20px;
    box-shadow: none;
    padding: 6px 18px;
    margin-bottom: 8px;
    transition: color 0.2s;
  }
  .back-home-btn:hover {
    background: transparent;
    color: #4a3670;
  }
  </style>