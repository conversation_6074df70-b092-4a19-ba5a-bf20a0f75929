<template>
  <div class="canvas-container-wrapper">
    <!-- <CanvasSidebar 
      :nodes="treeNodes" 
      @node-selected="handleNodeSelected"
      @update:nodes="updateTreeNodes"
      @toggle="handleSidebarToggle"
    /> -->
    <!-- 原有的 canvas-layout 作为主内容区 -->
    <div class="canvas-layout">
      <!-- 文档流内容 -->
      <div class="document-flow">
        <!-- 空状态显示 -->
        <div v-if="canvasItems.length === 0" class="empty-state">
          <div class="greeting-container">
            <h2 class="greeting">{{ greeting }}<span v-if="username">, {{ username }}</span></h2>
            <p class="greeting-subtitle">有什么可以帮助您的吗？</p>
            <div class="buttons-wrapper" v-if="environmentStore.environment === 'dev'">
              <v-btn color="#654C8C" class="action-btn" large rounded @click="$router.push('/note')">
                <span class="btn-inner">
                  <v-icon left style="margin-right:8px;">mdi-notebook</v-icon>
                  <span>我的笔记</span>
                </span>
              </v-btn>
              <!-- <v-btn color="#8B5A96" class="action-btn" large rounded @click="$router.push('/cognition')">
                <span class="btn-inner">
                  <v-icon left style="margin-right:8px;">mdi-brain</v-icon>
                  <span>认知平台</span>
                </span>
              </v-btn> -->
            </div>
          </div>
          
          <div class="recent-container" v-if="recentVisits.length > 0">
            <h3 class="recent-title">最近访问</h3>
            <div class="recent-list">
              <div 
                v-for="(item, index) in recentVisits" 
                :key="index" 
                class="recent-item"
                @click="navigateToConversation(item)"
              >
                <div class="recent-icon">
                  <img src="@/assets/deep.png" alt="会话" />
                </div>
                <div class="recent-content">
                  <div class="recent-prompt">{{ item.prompt }}</div>
                  <div class="recent-time">{{ formatTime(item.created_at) }}</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 没有历史记录时的空状态 -->
          <div v-else class="empty-history-container">
            <div class="empty-history-card">
              <v-icon icon="mdi-history" size="36" color="deep-purple" class="mb-4"></v-icon>
              <h3 class="empty-history-title">没有最近对话</h3>
              <p class="empty-history-text">您可以开始一个新的对话，或者通过右上角的历史按钮查看更多历史记录</p>
            </div>
          </div>
        </div>
        
        <!-- 内容区域：使用v-for渲染canvasItems -->
        <div v-else class="canvas-content">
          <!-- 聊天消息 -->
          <template v-for="item in canvasItems" :key="item.id">
            <!-- 用户消息或AI消息 -->
            <div v-if="item.type === 'chat'" class="message-item">
              <ChatMessage 
                :content="item.content" 
                :isUser="item.isUser"
                :showTimestamp="false"
              />
            </div>
            
            <!-- 编辑器内容 -->
            <div v-else-if="item.type === 'editor'" class="editor-container">
              <CanvasEditor 
                :initial-content="item.content"
                :editor-id="item.id"
                :version-info="editorVersionsInfo[item.id]"
                @update:content="handleEditorUpdate"
                @pause="handlePause"
                @edit-command="handleEditCommand"
                @edit-report="handleEditReport"
                @switchVersion="handleSwitchVersion"
              ref="canvasEditorRef"
              />
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { ref, onMounted, inject, nextTick, reactive, computed, watch, defineAsyncComponent } from 'vue';
import ChatMessage from './ChatMessage.vue';
import { type CanvasItem } from '@/composables/useDeepConversation';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/authStore';
import { researchApi } from '@/api/chat';
import CanvasSidebar from './CanvasSidebar.vue';
import { useEnvironmentStore } from '@/stores/environmentStore';


export default {
  name: 'CanvasComponent',
  components: {
    CanvasEditor: defineAsyncComponent(() => import('./CanvasEditor.vue')),
    ChatMessage,
    CanvasSidebar
  },
  props: {
    canvasItems: {
      type: Array<CanvasItem>,
      required: true
    },
    // 添加版本信息的prop
    editorVersionsInfo: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['updateEditorContent', 'pause', 'edit-command', 'edit-report', 'export-pdf', 'select-history', 'switchVersion'],
  setup(props, { emit }) {
    const canvasEditorRef = ref(null);
    const router = useRouter();
    const authStore = useAuthStore();
    const environmentStore = useEnvironmentStore();
    
    // 获取通信接口
    const canvasInterface = inject('canvasInterface', null);
    
    // 用户信息
    const username = computed(() => {
      return authStore.user?.name || authStore.user?.username;
    });
    
    // 根据时间生成问候语
    const greeting = computed(() => {
      const hour = new Date().getHours();
      if (hour < 12) {
        return '早上好';
      } else if (hour < 18) {
        return '下午好';
      } else {
        return '晚上好';
      }
    });
    
    // 最近访问的对话记录
    const recentVisits = ref([]);
    
    // 加载最近访问的对话
    const loadRecentVisits = async () => {
      try {
        if (!authStore.isAuthenticated) return;
        
        const response = await researchApi.getMyChats();
        // 检查API返回的数据结构
        const chats = Array.isArray(response.chats) ? response.chats : ([]);
        
        // 先按创建时间降序排序
        const sortedChats = [...chats].sort((a, b) => {
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        });
        
        // 取最新的5条记录
        recentVisits.value = sortedChats.slice(0, 5).map(item => ({
          ...item,
          prompt: item.prompt && item.prompt.length > 40 
            ? item.prompt.substring(0, 40) + '...' 
            : (item.prompt || '新对话'),
        }));
        
        console.log('加载最近对话成功:', recentVisits.value);
      } catch (error) {
        console.error('加载最近对话失败:', error);
        recentVisits.value = [];
      }
    };
    
    // 格式化时间
    const formatTime = (timeString) => {
      if (!timeString) return '';
      
      // 将日期字符串转换为东八区时间
      const date = new Date(timeString);
      const cnDate = new Date(date.getTime() + (8 * 60 * 60 * 1000));
      
      const now = new Date();
      const cnNow = new Date(now.getTime() + (8 * 60 * 60 * 1000));
      
      // 如果是今天
      if (cnDate.toDateString() === cnNow.toDateString()) {
        return `今天 ${cnDate.getHours().toString().padStart(2, '0')}:${cnDate.getMinutes().toString().padStart(2, '0')}`;
      }
      
      // 如果是昨天
      const yesterday = new Date(cnNow);
      yesterday.setDate(cnNow.getDate() - 1);
      if (cnDate.toDateString() === yesterday.toDateString()) {
        return `昨天 ${cnDate.getHours().toString().padStart(2,'0')}:${cnDate.getMinutes().toString().padStart(2,'0')}`;
      }

      // 其他日期
      return `${cnDate.getMonth() + 1}月${cnDate.getDate()}日 ${cnDate.getHours().toString().padStart(2, '0')}:${cnDate.getMinutes().toString().padStart(2, '0')}`;
    };
    
    // 导航到特定对话
    const navigateToConversation = (history) => {
      console.log('[CanvasComponent] 导航到特定对话:', history.id);
      localStorage.setItem('current_conversation_id', history.id);
      router.push(`/draft?conversation_id=${history.id}`);
      emit('select-history', history);
    };
    
    // 当编辑器内容更新时
    const handleEditorUpdate = (newContent) => {
      // 发送事件到父组件
      emit('updateEditorContent', newContent);
      
      // 更新共享状态
      if (canvasInterface?.state) {
        canvasInterface.state.content = newContent;
      }
    };
    
    // 监听canvasItems变化，确保编辑器内容得到更新
    watch(() => props.canvasItems, (newItems) => {
      // 查找最新的编辑器项（从后往前查找）
      let editorItem = null;
      for (let i = newItems.length - 1; i >= 0; i--) {
        if (newItems[i].type === 'editor') {
          editorItem = newItems[i];
          break;
        }
      }
      
      if (editorItem && canvasEditorRef.value) {
        // 检查编辑器组件中是否有更新方法
        if (canvasEditorRef.value.updateContent) {
          canvasEditorRef.value.updateContent(editorItem.content);
        }
      }
    }, { deep: true });
    
    // 处理暂停事件
    const handlePause = () => {
      // 发送暂停事件到父组件
      emit('pause');
      
      // 如果存在canvasInterface方法，也调用它
      if (canvasInterface?.methods?.handlePause) {
        canvasInterface.methods.handlePause();
      }
    };
    
    // 处理编辑命令
    const handleEditCommand = (formattedMessage) => {
      // 向上发送编辑命令
      emit('edit-command', formattedMessage);
    };

    // 树形数据
    const treeNodes = ref([
      {
        id: 1,
        name: '我的文档',
        parentId: null,
        expanded: true
      },
      {
        id: 2,
        name: '项目计划',
        parentId: 1,
        expanded: false
      },
      {
        id: 3,
        name: '研究笔记',
        parentId: 1,
        expanded: true
      },
      {
        id: 4,
        name: '会议记录',
        parentId: 3,
        expanded: false
      },
      {
        id: 5,
        name: '想法收集',
        parentId: 1,
        expanded: false
      }
    ]);
    
    // 处理节点选择
    const handleNodeSelected = (node) => {
      console.log('选中节点:', node);
      // 这里可以根据节点类型执行不同操作
      // 例如加载文档内容等
    };

    // 更新树节点数据
    const updateTreeNodes = (newNodes) => {
      treeNodes.value = newNodes;
    };
    
    // 处理侧边栏切换
    const handleSidebarToggle = (isOpen) => {
      console.log('侧边栏状态:', isOpen ? '打开' : '关闭');
    };
    
    // 处理编辑完整报告
    const handleEditReport = (reportContent: string) => {
      console.log('Canvas组件: 接收到编辑完整报告，转发到上层组件');
      // 向上发送编辑完整报告事件
      emit('edit-report', reportContent);
    };
    
    // 处理版本切换
    const handleSwitchVersion = (editorId: string, versionIndex: number) => {
      console.log('Canvas组件: 版本切换到', editorId, versionIndex);
      // 向上emit版本切换事件，让draft/index.vue处理
      emit('switchVersion', editorId, versionIndex);
    };
    
    onMounted(() => {
      environmentStore.fetchEnvironment();
      // 通知父组件编辑器已就绪
      window.dispatchEvent(new CustomEvent('canvas-ready', {
        detail: { ready: true }
      }));
      
      // 加载最近访问的对话
      loadRecentVisits();
    });

    return {
      canvasEditorRef,
      handleEditorUpdate,
      handlePause,
      handleEditCommand,
      handleEditReport,
      handleSwitchVersion,
      // 对外暴露的方法
      handleTagReplacement: (...args) => canvasEditorRef.value?.handleTagReplacement?.(...args),
      handleAnimatedTagReplacement: (...args) => canvasEditorRef.value?.handleAnimatedTagReplacement?.(...args),
      // 获取编辑器实例
      get canvasEditor() {
        return canvasEditorRef.value?.canvasEditor || null;
      },
      // 空状态相关
      greeting,
      username,
      recentVisits,
      formatTime,
      navigateToConversation,
      // 侧边栏相关
      treeNodes,
      handleNodeSelected,
      updateTreeNodes,
      handleSidebarToggle,
      environmentStore
    };
  }
}
</script>

<style scoped>
.canvas-container-wrapper {
  display: flex;
  height: 100%;
  width: 100%;
  overflow: hidden; /* 防止过渡期间出现不必要的滚动条 */
}

.canvas-layout {
  display: flex;
  flex-direction: column;
  width: 100%; /* 将会由 flex-grow 调整 */
  height: 100%;
  overflow-y: auto;
  flex-grow: 1; /* 主内容区占据剩余空间 */
  transition: margin-left 0.3s ease; /* 如果侧边栏不是flex item，则需要这个 */
}

.document-flow {
  width: 90%;
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 40px 20px;
  gap: 40px;
}

.greeting-container {
  text-align: center;
  margin-top: 40px;
}

.greeting {
  font-size: 2.2rem;
  font-weight: 600;
  color: #654C8C;
  margin-bottom: 8px;
}

.greeting-subtitle {
  font-size: 1.2rem;
  color: #8A7DA5;
  margin-top: 8px;
}

.recent-container {
  margin-top: 40px;
  width: 100%;
}

.recent-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: #654C8C;
  margin-bottom: 16px;
  border-bottom: 1px solid rgba(101, 76, 140, 0.2);
  padding-bottom: 8px;
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 空历史状态样式 */
.empty-history-container {
  margin-top: 20px;
  width: 100%;
  display: flex;
  justify-content: center;
}

.empty-history-card {
  background-color: rgba(240, 235, 255, 0.7);
  border-radius: 16px;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 500px;
  box-shadow: 0 2px 8px rgba(101, 76, 140, 0.05);
  border: 1px dashed rgba(101, 76, 140, 0.2);
}

.empty-history-title {
  font-size: 1.4rem;
  font-weight: 500;
  color: #654C8C;
  margin-bottom: 8px;
}

.empty-history-text {
  font-size: 1rem;
  color: #8A7DA5;
  line-height: 1.5;
}

.recent-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #F0EBFF;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.recent-item:hover {
  background-color: #E8E6F6;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(101, 76, 140, 0.1);
}

.recent-icon {
  width: 40px;
  height: 40px;
  min-width: 40px;
  background-color: #654C8C;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.recent-icon img {
  width: 24px;
  height: 24px;
  object-fit: contain;
  filter: brightness(0) invert(1);
}

.recent-content {
  flex: 1;
  overflow: hidden;
}

.recent-prompt {
  font-size: 1rem;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;
}

.recent-time {
  font-size: 0.85rem;
  color: #8A7DA5;
}

.canvas-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding-bottom: 32px;
}

.message-item {
  width: 100%;
}

.editor-container {
  width: 100%;
  margin-top: 12px;
  margin-bottom: 16px;
  border-top: 1px solid #eaeaea;
}

.buttons-wrapper {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin: 24px 0 0 0;
  flex-wrap: wrap;
}

.action-btn {
  font-size: 18px;
  font-weight: 500;
  padding: 0 36px;
  height: 48px;
  color: #fff;
  border-radius: 24px;
  box-shadow: 0 2px 8px rgba(101, 76, 140, 0.08);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  min-width: 140px;
}

.btn-inner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn .v-icon {
  font-size: 22px;
  margin-right: 8px;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(101, 76, 140, 0.15);
}

/* 我的笔记按钮 */
.action-btn:first-child {
  background: #654C8C;
}

.action-btn:first-child:hover {
  background: #4a3670;
}

/* 认知平台按钮 */
.action-btn:last-child {
  background: #8B5A96;
}

.action-btn:last-child:hover {
  background: #6d4575;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .buttons-wrapper {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }
  
  .action-btn {
    width: 200px;
  }
}
</style>