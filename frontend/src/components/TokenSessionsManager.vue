<template>
  <div class="token-sessions-manager">
    <div class="header-section">
      <h2>会话 Token 用量详情</h2>
      <div class="actions">
        <button class="btn-refresh" @click="loadSessionsTokenUsage" :disabled="isLoading">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': isLoading }"></i>
          刷新数据
        </button>
      </div>
    </div>

    <div class="summary-cards">
      <div class="card">
        <div class="card-title">显示会话数</div>
        <div class="card-value">{{ summary.total_sessions || 0 }}</div>
      </div>
      <div class="card">
        <div class="card-title">总输入Token</div>
        <div class="card-value">{{ formatNumber(summary.total_input_tokens || 0) }}</div>
      </div>
      <div class="card">
        <div class="card-title">总输出Token</div>
        <div class="card-value">{{ formatNumber(summary.total_output_tokens || 0) }}</div>
      </div>
      <div class="card highlight">
        <div class="card-title">总成本 (¥)</div>
        <div class="card-value">{{ formatCurrency(summary.total_cost || 0) }}</div>
      </div>
    </div>

    <div class="sessions-table">
      <table>
        <thead>
          <tr>
            <th>会话ID</th>
            <th>用户ID</th>
            <th>问题</th>
            <th>状态</th>
            <th>输入Token</th>
            <th>输出Token</th>
            <th>成本 ($)</th>
            <th>开始时间</th>
            <th>最近活动</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="isLoading">
            <td colspan="9" class="loading">
              <i class="fas fa-spinner fa-spin"></i> 加载中...
            </td>
          </tr>
          <tr v-else-if="sessionsData.length === 0">
            <td colspan="9" class="no-data">暂无会话Token使用数据</td>
          </tr>
          <tr v-for="session in sessionsData" :key="session.session_id">
            <td class="session-id">{{ session.session_id }}</td>
            <td>{{ session.username }}</td>
            <td class="question">{{ truncateText(session.question, 50) }}</td>
            <td>
              <span :class="'status ' + session.status">{{ session.status }}</span>
            </td>
            <td>{{ formatNumber(session.total_input_tokens) }}</td>
            <td>{{ formatNumber(session.total_output_tokens) }}</td>
            <td class="cost">{{ formatCurrency(session.total_cost) }}</td>
            <td>{{ formatDate(session.started_at) }}</td>
            <td>{{ formatDate(session.last_activity) }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="pagination">
      <button @click="prevPage" :disabled="currentPage === 1 || isLoading">
        <i class="fas fa-chevron-left"></i> 上一页
      </button>
      <span>第 {{ currentPage }} 页</span>
      <button @click="nextPage" :disabled="isLoading">
        <i class="fas fa-chevron-right"></i> 下一页
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { researchApi } from '@/api/chat';

interface SessionTokenUsage {
  session_id: string;
  user_id: string;
  username: string;
  question: string;
  status: string;
  total_input_tokens: number;
  total_output_tokens: number;
  total_cost: number;
  started_at: string;
  last_activity: string;
  models_usage?: Record<string, any>;
}

interface SessionsSummary {
  total_sessions: number;
  total_input_tokens: number;
  total_output_tokens: number;
  total_cost: number;
}

// 状态
const sessionsData = ref<SessionTokenUsage[]>([]);
const isLoading = ref(false);
const currentPage = ref(1);
const pageSize = 10;
const summary = ref<SessionsSummary>({
  total_sessions: 0,
  total_input_tokens: 0,
  total_output_tokens: 0,
  total_cost: 0
});

// 加载会话Token使用统计数据
const loadSessionsTokenUsage = async () => {
  isLoading.value = true;
  try {
    const skip = (currentPage.value - 1) * pageSize;
    const response = await researchApi.getAllSessionsTokenUsage(skip, pageSize);
    sessionsData.value = response.data || [];
    summary.value = response.summary || {
      total_sessions: 0,
      total_input_tokens: 0,
      total_output_tokens: 0,
      total_cost: 0
    };
  } catch (error) {
    console.error('获取会话Token使用统计失败:', error);
    // 可以添加提示信息
  } finally {
    isLoading.value = false;
  }
};

// 翻页操作
const nextPage = () => {
  currentPage.value++;
  loadSessionsTokenUsage();
};

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
    loadSessionsTokenUsage();
  }
};

// 格式化数字，添加千位分隔符
const formatNumber = (num: number): string => {
  return num.toLocaleString('zh-CN');
};

// 格式化货币，保留2位小数
const formatCurrency = (num: number): string => {
  return num.toFixed(2);
};

// 格式化日期
const formatDate = (dateString: string): string => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN');
};

// 截断文本
const truncateText = (text: string, maxLength: number): string => {
  if (!text) return '-';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
};

// 组件挂载后加载数据
onMounted(() => {
  loadSessionsTokenUsage();
});
</script>

<style scoped>
.token-sessions-manager {
  width: 100%;
  padding: 20px;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-section h2 {
  margin: 0;
  font-size: 24px;
}

.btn-refresh {
  background-color: #3699ff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-refresh:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.summary-cards {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.card {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-width: 160px;
  flex: 1;
}

.card.highlight {
  background-color: #3699ff;
  color: white;
}

.card-title {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 8px;
}

.card.highlight .card-title {
  color: rgba(255, 255, 255, 0.8);
}

.card-value {
  font-size: 24px;
  font-weight: 600;
}

.sessions-table {
  width: 100%;
  overflow-x: auto;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

th {
  background-color: #f8f9fa;
  font-weight: 600;
  position: sticky;
  top: 0;
}

.session-id {
  font-family: monospace;
  font-size: 12px;
}

.question {
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status.running {
  background-color: #d1ecf1;
  color: #0c5460;
}

.status.paused {
  background-color: #fff3cd;
  color: #856404;
}

.status.completed {
  background-color: #d4edda;
  color: #155724;
}

.status.error {
  background-color: #f8d7da;
  color: #721c24;
}

.cost {
  font-weight: 600;
  color: #e74c3c;
}

.loading, .no-data {
  text-align: center;
  padding: 20px;
  color: #6c757d;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
}

.pagination button {
  background-color: #fff;
  border: 1px solid #ddd;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
}

.pagination button:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

.pagination span {
  font-weight: 500;
}
</style> 