<template>
  <div class="invite-code-manager">
    <div class="header-section">
      <h2>邀请码管理</h2>
      <div class="actions">
        <div class="generate-form">
          <input 
            type="number" 
            v-model="generateCount" 
            min="1" 
            max="100" 
            placeholder="生成数量"
          />
          <button 
            class="btn-generate" 
            @click="generateCodes" 
            :disabled="isLoading"
          >
            <i class="fas fa-plus" v-if="!isLoading"></i>
            <i class="fas fa-spinner fa-spin" v-else></i>
            生成邀请码
          </button>
        </div>
        <div class="filter-form">
          <select v-model="filter">
            <option value="all">全部</option>
            <option value="used">已使用</option>
            <option value="unused">未使用</option>
          </select>
        </div>
      </div>
    </div>

    <div class="codes-table">
      <table>
        <thead>
          <tr>
            <th>邀请码</th>
            <th>状态</th>
            <th>使用者</th>
            <th>使用时间</th>
            <th>创建时间</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="isLoading">
            <td colspan="5" class="loading">
              <i class="fas fa-spinner fa-spin"></i> 加载中...
            </td>
          </tr>
          <template v-else>
            <tr v-if="inviteCodes.length === 0">
              <td colspan="5" class="no-data">暂无邀请码数据</td>
            </tr>
            <tr v-for="code in inviteCodes" v-else :key="code.code" :class="{'used': code.is_used}">
              <td>
                <div class="code-value">
                  {{ code.code }}
                  <button class="copy-btn" @click="copyToClipboard(code.code)">
                    <i class="fas fa-copy"></i>
                  </button>
                </div>
              </td>
              <td>
                <span :class="code.is_used ? 'status used' : 'status unused'">
                  {{ code.is_used ? '已使用' : '未使用' }}
                </span>
              </td>
              <td>{{ code.used_by || '-' }}</td>
              <td>{{ code.used_at ? formatDate(code.used_at) : '-' }}</td>
              <td>{{ formatDate(code.created_at) }}</td>
            </tr>
          </template>
        </tbody>
      </table>
    </div>

    <div class="pagination" v-if="totalPages > 1">
      <button 
        @click="changePage(currentPage - 1)" 
        :disabled="currentPage === 1"
      >
        <i class="fas fa-chevron-left"></i>
      </button>
      <span>{{ currentPage }} / {{ totalPages }}</span>
      <button 
        @click="changePage(currentPage + 1)" 
        :disabled="currentPage === totalPages"
      >
        <i class="fas fa-chevron-right"></i>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { authService, type InviteCode } from '@/api/authService';

// 状态
const inviteCodes = ref<InviteCode[]>([]);
const isLoading = ref(false);
const generateCount = ref(5);
const filter = ref('all');
const currentPage = ref(1);
const itemsPerPage = 25;
const totalCodes = ref(0);

// 计算属性
const totalPages = computed(() => {
  if (totalCodes.value === 0) return 1;
  return Math.ceil(totalCodes.value / itemsPerPage);
});

// 加载邀请码列表
const loadInviteCodes = async () => {
  isLoading.value = true;
  try {
    const skip = (currentPage.value - 1) * itemsPerPage;
    const response = await authService.getInviteCodes(skip, itemsPerPage, filter.value);

    inviteCodes.value = response.items || [];
    totalCodes.value = response.total || 0;

  } catch (error) {
    console.error('获取邀请码失败:', error);
    inviteCodes.value = [];
    totalCodes.value = 0;
  } finally {
    isLoading.value = false;
  }
};

// 生成新的邀请码
const generateCodes = async () => {
  if (generateCount.value < 1 || generateCount.value > 100) {
    alert('生成数量必须在1-100之间');
    return;
  }
  
  isLoading.value = true;
  try {
    await authService.generateInviteCodes(generateCount.value);
    // 回到第一页并重新加载数据
    if (currentPage.value !== 1) {
      currentPage.value = 1;
    } else {
      await loadInviteCodes();
    }
  } catch (error) {
    console.error('生成邀请码失败:', error);
  } finally {
    isLoading.value = false;
  }
};

// 复制邀请码到剪贴板
const copyToClipboard = (code: string) => {
  navigator.clipboard.writeText(code).then(() => {
    alert('邀请码已复制到剪贴板');
  }).catch(err => {
    console.error('复制失败:', err);
  });
};

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString();
};

// 翻页
const changePage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
  }
};

// 监听筛选条件和页码变化
watch(filter, () => {
  if (currentPage.value !== 1) {
    currentPage.value = 1;
  } else {
    loadInviteCodes();
  }
});

watch(currentPage, loadInviteCodes, { immediate: false });

// 组件挂载后加载数据
onMounted(() => {
  loadInviteCodes();
});
</script>

<style scoped>
.invite-code-manager {
  width: 100%;
  padding: 20px;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-section h2 {
  margin: 0;
  font-size: 24px;
}

.actions {
  display: flex;
  gap: 15px;
}

.generate-form {
  display: flex;
  gap: 10px;
}

.generate-form input {
  width: 80px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.btn-generate {
  background-color: #3699ff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-generate:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.filter-form select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.codes-table {
  width: 100%;
  overflow-x: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.code-value {
  display: flex;
  align-items: center;
  gap: 8px;
}

.copy-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #6c757d;
}

.copy-btn:hover {
  color: #3699ff;
}

.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status.used {
  background-color: #f8d7da;
  color: #721c24;
}

.status.unused {
  background-color: #d4edda;
  color: #155724;
}

tr.used {
  background-color: #f8f9fa;
}

.loading, .no-data {
  text-align: center;
  padding: 20px;
  color: #6c757d;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  gap: 10px;
}

.pagination button {
  background: none;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
}

.pagination button:disabled {
  color: #ccc;
  cursor: not-allowed;
}
</style> 