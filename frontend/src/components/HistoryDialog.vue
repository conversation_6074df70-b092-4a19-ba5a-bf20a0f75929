<template>
  <div>
    <v-dialog v-model="dialogVisible" max-width="700">
      <template v-slot:activator="{ props }">
        <v-btn
          v-bind="props"
          variant="text"
          icon="mdi-history"
          color="secondary"
          :disabled="loading"
          class="tool-btn"
          title="对话历史"
        >
          <v-icon icon="mdi-history" />
        </v-btn>
      </template>
      
      <v-card class="history-dialog" color="grey-lighten-5">
        <v-container fluid class="pa-0">
          <!-- 加载状态 -->
          <div v-if="loading" class="d-flex flex-column align-center justify-center py-8">
            <v-progress-circular indeterminate color="primary"></v-progress-circular>
            <div class="mt-4 text-body-2">加载您的对话历史...</div>
          </div>
          
          <!-- 错误状态 -->
          <div v-else-if="error" class="d-flex flex-column align-center justify-center py-8">
            <v-icon color="error" size="48" class="mb-4">mdi-alert-circle</v-icon>
            <div class="text-body-1 font-weight-medium mb-2">加载失败</div>
            <div class="text-body-2 text-center px-4">{{ error }}</div>
            <v-btn color="primary" variant="tonal" class="mt-4" @click="fetchHistories">
              重试
            </v-btn>
          </div>
          
          <!-- 空状态 -->
          <div v-else-if="histories.length === 0" class="d-flex flex-column align-center justify-center py-8">
            <v-icon color="grey" size="48" class="mb-4">mdi-chat-question</v-icon>
            <div class="text-body-1 font-weight-medium mb-2">暂无对话历史</div>
            <div class="text-body-2 text-center px-4">开始一个新的对话，探索AI的能力</div>
          </div>
          
          <!-- 历史列表 -->
          <v-list v-else class="history-list py-0">
            <div class="dialog-title pa-4 mb-2">
              <div class="d-flex align-center">
                <v-icon icon="mdi-history" class="mr-2" color="deep-purple"></v-icon>
                <span class="text-h6">对话历史</span>
                <v-spacer></v-spacer>
                <v-btn icon="mdi-close" variant="text" @click="dialogVisible = false" color="deep-purple">
                  <v-icon>mdi-close</v-icon>
                </v-btn>
              </div>
            </div>
            
            <v-list-item
              v-for="history in histories"
              :key="history.id"
              @click="selectHistory(history)"
              :class="{ 'active-item': history.conversation_id === currentConversationId }"
              class="history-item my-1"
            >
              <template v-slot:prepend>
                <v-avatar color="deep-purple" class="history-avatar">
                  <v-icon icon="mdi-chart-bubble" color="white"></v-icon>
                </v-avatar>
              </template>
              
              <v-list-item-title>
                <span class="text-truncate">{{ formatPrompt(history.prompt) }}</span>
              </v-list-item-title>
              
              <v-list-item-subtitle>
                <span class="text-caption text-grey">
                  {{ formatDate(history.created_at) }}
                </span>
              </v-list-item-subtitle>
            </v-list-item>
            
            <div class="d-flex justify-end pa-3">
              <v-btn color="deep-purple" variant="text" @click="fetchHistories" :loading="loading">
                刷新
              </v-btn>
              <v-btn color="grey" variant="text" @click="dialogVisible = false" class="ml-2">
                关闭
              </v-btn>
            </div>
          </v-list>
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed } from 'vue';
import { researchApi } from '@/api/chat';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/authStore';
import { useDeepConversation } from '@/composables/useDeepConversation';

export interface ChatHistory {
  id: string;
  conversation_id: string;
  draft_id: string;
  status: string;
  prompt: string;
  created_at: string;
  updated_at: string;
  completed_at: string | null;
  message_count: number;
  total_tokens: number;
  metadata: any;
}

export default defineComponent({
  name: 'HistoryDialog',
  props: {
    currentConversationId: {
      type: String,
      default: ''
    }
  },
  emits: ['select-history'],
  setup(props, { emit }) {
    const authStore = useAuthStore();
    const router = useRouter();
    const dialogVisible = ref(false);
    const histories = ref<ChatHistory[]>([]);
    const loading = ref(false);
    const error = ref('');
    
    // 获取useDeepConversation组合式API中的恢复历史方法
    const { restoreConversationHistory } = useDeepConversation();

    // 格式化提示语
    const formatPrompt = (prompt: string) => {
      if (!prompt) return '无标题对话';
      return prompt.length > 30 ? prompt.substring(0, 30) + '...' : prompt;
    };

    // 格式化日期
    const formatDate = (dateString: string) => {
      if (!dateString) return '';
      
      // 将日期字符串转换为东八区时间
      const date = new Date(dateString);
      const cnDate = new Date(date.getTime() + (8 * 60 * 60 * 1000));
      
      const now = new Date();
      const cnNow = new Date(now.getTime() + (8 * 60 * 60 * 1000));
      const diff = Math.floor((cnNow.getTime() - cnDate.getTime()) / 1000);
      
      // 今天的日期
      if (diff < 86400 && 
          cnDate.getDate() === cnNow.getDate() && 
          cnDate.getMonth() === cnNow.getMonth() && 
          cnDate.getFullYear() === cnNow.getFullYear()) {
        return `今天 ${cnDate.getHours().toString().padStart(2, '0')}:${cnDate.getMinutes().toString().padStart(2, '0')}`;
      }
      
      // 昨天的日期
      const yesterday = new Date(cnNow);
      yesterday.setDate(yesterday.getDate() - 1);
      if (cnDate.getDate() === yesterday.getDate() && 
          cnDate.getMonth() === yesterday.getMonth() && 
          cnDate.getFullYear() === yesterday.getFullYear()) {
        return `昨天 ${cnDate.getHours().toString().padStart(2, '0')}:${cnDate.getMinutes().toString().padStart(2, '0')}`;
      }
      
      // 一周内的日期
      if (diff < 604800) {
        const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        return `${days[cnDate.getDay()]} ${cnDate.getHours().toString().padStart(2, '0')}:${cnDate.getMinutes().toString().padStart(2, '0')}`;
      }
      
      // 其他日期
      return `${cnDate.getFullYear()}-${(cnDate.getMonth() + 1).toString().padStart(2, '0')}-${cnDate.getDate().toString().padStart(2, '0')}`;
    };

    // 获取历史记录
    const fetchHistories = async () => {
      if (!authStore.isAuthenticated) {
        error.value = '请先登录后查看您的对话历史';
        return;
      }
      
      loading.value = true;
      error.value = '';
      
      try {
        const result = await researchApi.getMyChats();
        // 检查API返回的数据结构
        const chats = Array.isArray(result.chats) ? result.chats : (result.chats || []);
        console.log('[HistoryDialog] 获取对话历史:', chats);
        histories.value = chats.sort((a: ChatHistory, b: ChatHistory) => {
          // 按照创建时间降序排序
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        });
      } catch (err: any) {
        console.error('获取对话历史失败:', err);
        error.value = err.message || '获取对话历史失败，请稍后再试';
      } finally {
        loading.value = false;
      }
    };

    // 选择历史记录
    const selectHistory = async (history: ChatHistory) => {
      
      dialogVisible.value = false;
      
      // 如果不是当前对话，则跳转
      if (history.id !== props.currentConversationId) {
        // 将当前会话ID保存到localStorage，用于页面加载时恢复
        localStorage.setItem('current_conversation_id', history.id);
        
        // 直接跳转到带有会话ID的页面，让目标页面负责恢复历史对话
        router.push(`/draft?conversation_id=${history.id}`);
        console.log('[HistoryDialog] 选择历史记录:', history.id);
        emit('select-history', history);
      }
    };

    // 监听对话框打开事件
    const onDialogOpen = async () => {
      if (dialogVisible.value) {
        await fetchHistories();
      }
    };

    // 监听对话框可见性变化
    const watchDialog = (val: boolean) => {
      if (val) {
        onDialogOpen();
      }
    };

    // 计算属性：用户是否已登录
    const isLoggedIn = computed(() => authStore.isAuthenticated);

    onMounted(() => {
      // 初始加载，不自动获取历史记录，等对话框打开时再加载
    });

    return {
      dialogVisible,
      histories,
      loading,
      error,
      formatPrompt,
      formatDate,
      fetchHistories,
      selectHistory,
      isLoggedIn,
      watchDialog
    };
  },
  watch: {
    dialogVisible(val) {
      this.watchDialog(val);
    }
  }
});
</script>

<style scoped>
.history-dialog {
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  overflow: hidden;
}

.dialog-title {
  border-bottom: 1px solid rgba(101, 76, 140, 0.2);
  background-color: #f8f5ff;
}

.history-list {
  max-height: 70vh;
  overflow-y: auto;
  background-color: #fafafa;
  padding-bottom: 0;
}

.history-item {
  cursor: pointer;
  transition: background-color 0.2s;
  border-radius: 8px;
  margin: 0 8px 4px 8px;
}

.history-item:hover {
  background-color: rgba(101, 76, 140, 0.05);
}

.active-item {
  background-color: rgba(101, 76, 140, 0.1) !important;
  border-left: 3px solid #654c8c;
}

.history-avatar {
  margin-right: 12px;
}
</style> 