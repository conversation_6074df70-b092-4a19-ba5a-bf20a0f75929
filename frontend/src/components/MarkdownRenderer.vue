<template>
  <div class="markdown-body">
    <div v-html="renderedContent"></div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch, computed, onMounted } from "vue";
import MarkdownIt from "markdown-it";
import texmath from "markdown-it-texmath";
import katex from "katex";
import hljs from "highlight.js";
import DOMPurify from "dompurify";

// 导入样式
import "katex/dist/katex.min.css";
import "highlight.js/styles/github.css";

export default defineComponent({
  name: "StreamingMarkdownRenderer",
  props: {
    content: {
      type: String,
      required: true,
    },
    isComplete: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const renderedContent = ref("");
    const isStreaming = ref(!props.isComplete);

    // 初始化 markdown-it
    const md = new MarkdownIt({
      html: true,
      linkify: true,
      typographer: true,
      breaks: false,
      highlight: function (str, lang) {
        if (lang && hljs.getLanguage(lang)) {
          try {
            return hljs.highlight(str, { language: lang }).value;
          } catch (__) {}
        }
        return "";
      },
    });

    md.use(texmath, {
      engine: katex,
      delimiters: "dollars",
      katexOptions: {
        macros: {
          "\\RR": "\\mathbb{R}",
        },
      },
    });

    // 渲染 Markdown 内容
    const renderMarkdown = (content: string) => {
      try {
        if (!content) return "";
        
        // 预处理内容，规范化换行符
        let normalizedContent = content
          .replace(/\r\n/g, '\n')
          .replace(/\n{3,}/g, '\n\n')
          .trim();

        // 先处理引用内容，但不删除它们
        const references = {};
        normalizedContent.replace(
          /\[(\^[0-9]+)\]:\s*\[(.*?)\]\((.*?)\)/g,
          (match, refNum, title, url) => {
            references[refNum] = { title, url };
            console.log(match);
            return match; // 保留原始引用文本
          }
        );
        
        // 使用特殊Unicode字符序列作为标记，极低概率与文档内容冲突
        const marker = "⟦⟨ref:";
        const markerEnd = "⟩⟧";
        
        normalizedContent = normalizedContent.replace(
          /\[(\^[0-9]+)\]/g,
          (_, number) => `${marker}${number}${markerEnd}`
        );
        
        // 先进行Markdown渲染
        let html = md.render(normalizedContent);
        
        // 替换标记为引用元素
        html = html.replace(
          new RegExp(`${marker}(\\^[0-9]+)${markerEnd}`, 'g'),
          (_, number) => `<span class="reference-mark" 
            data-ref="${number}" 
            data-title="${references[number]?.title || ''}"
            data-url="${references[number]?.url || ''}"
          >${number.replace('^', '')}<span class="reference-tooltip">${references[number]?.title || ''}</span></span>`
        );


        return DOMPurify.sanitize(html, {
          ADD_TAGS: ['span', 'div'],
          ADD_ATTR: ['data-ref', 'data-title', 'data-url', 'target']
        });
      } catch (error) {
        console.error("Markdown rendering error:", error);
        return `<p>渲染错误: ${error.message}</p>`;
      }
    };

    // 监听内容变化
    watch(
      () => props.content,
      (newContent) => {
        if (newContent === undefined || newContent === null) {
          renderedContent.value = "";
          return;
        }

        // 直接渲染新内容
        renderedContent.value = renderMarkdown(newContent);

        // 更新流式状态
        isStreaming.value = !props.isComplete;
      },
      { immediate: true }
    );

    // 监听完成状态
    watch(
      () => props.isComplete,
      (complete) => {
        isStreaming.value = !complete;
      }
    );

    // 添加点击事件处理
    onMounted(() => {
      document.addEventListener('click', (e) => {
        const target = e.target as HTMLElement;
        const tooltip = target.closest('.reference-tooltip');
        if (tooltip) {
          const mark = tooltip.closest('.reference-mark') as HTMLElement;
          const url = mark?.dataset.url;
          if (url) {
            window.open(url, '_blank');
          }
        }
      });
    });


    return {
      renderedContent,
      isStreaming,
    };
  },
});
</script>

<style>
.markdown-body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial,
    sans-serif !important;
  font-size: 16px !important;
  line-height: 1.5 !important;
  word-wrap: break-word !important;
  padding: 4px !important;
  color: #24292e !important;
  white-space: normal !important;
  overflow-wrap: break-word !important;
}

/* 标题样式 */
.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
  margin-top: 12px !important;
  margin-bottom: 8px !important;
  font-weight: 600 !important;
  line-height: 1.5 !important;
}

.markdown-body h1 {
  font-size: 2em !important;
  padding-bottom: 0.2em !important;
  border-bottom: 1px solid #eaecef !important;
}

.markdown-body h2 {
  font-size: 1.6em !important;
  padding-bottom: 0.2em !important;
  border-bottom: 1px solid #eaecef !important;
}

.markdown-body h3 {
  font-size: 1.3em !important;
}
.markdown-body h4 {
  font-size: 1.1em !important;
}
.markdown-body h5 {
  font-size: 1em !important;
}
.markdown-body h6 {
  font-size: 0.95em !important;
  color: #6a737d !important;
}

/* 段落和列表样式 */
.markdown-body p,
.markdown-body ul,
.markdown-body ol {
  margin-top: 0 !important;
  margin-bottom: 8px !important;
  line-height: 1.5 !important;
}

.markdown-body ul,
.markdown-body ol {
  padding-left: 1.5em !important;
}

.markdown-body li {
  margin: 0 0 !important;
}

/* 代码样式 */
.markdown-body code {
  font-size: 80% !important;
  padding: 0.2em 0.4em !important;
  margin: 0 !important;
  background-color: rgba(27, 31, 35, 0.05) !important;
  border-radius: 3px !important;
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace !important;
}

.markdown-body pre {
  font-size: 80% !important;
  line-height: 1.5 !important;
  padding: 8px !important;
  overflow: auto !important;
  background-color: #f6f8fa !important;
  border-radius: 6px !important;
  margin-bottom: 8px !important;
}

.markdown-body pre code {
  padding: 0 !important;
  margin: 0 !important;
  background-color: transparent !important;
  border: 0 !important;
  word-break: normal !important;
  white-space: pre !important;
  font-size: 90% !important;
}

/* 引用样式 */
.markdown-body blockquote {
  margin: 0 0 8px !important;
  padding: 0 0.8em !important;
  color: #6a737d !important;
  border-left: 0.25em solid #dfe2e5 !important;
}

/* 表格样式 */
.markdown-body table {
  display: block !important;
  width: 100% !important;
  overflow: auto !important;
  margin-bottom: 8px !important;
  border-spacing: 0 !important;
  border-collapse: collapse !important;
}

.markdown-body table th,
.markdown-body table td {
  padding: 4px 10px !important;
  border: 1px solid #dfe2e5 !important;
}

.markdown-body table th {
  font-weight: 600 !important;
  background-color: #f6f8fa !important;
}

.markdown-body table tr:nth-child(2n) {
  background-color: #f6f8fa !important;
}

/* 链接样式 */
.markdown-body a {
  color: #0366d6 !important;
  text-decoration: none !important;
}

.markdown-body a:hover {
  text-decoration: underline !important;
}

/* 图片样式 */
.markdown-body img {
  max-width: 100% !important;
  box-sizing: border-box !important;
  border-radius: 4px !important;
}

/* 水平线样式 */
.markdown-body hr {
  height: 0.25em !important;
  padding: 0 !important;
  margin: 12px 0 !important;
  background-color: #e1e4e8 !important;
  border: 0 !important;
}

/* 行内元素样式 */
.markdown-body strong {
  font-weight: 600 !important;
}

.markdown-body em {
  font-style: italic !important;
}

.markdown-body del {
  color: #6a737d !important;
  text-decoration: line-through !important;
}

/* LaTeX 公式容器样式 */
.markdown-body .katex-display {
  margin: 0.5em 0 !important;
  overflow-x: auto !important;
  overflow-y: hidden !important;
}

/* 代码块标题样式 */
.markdown-body .code-header {
  background: #f1f1f1 !important;
  color: #24292e !important;
  font-size: 80% !important;
  padding: 0.5em 1em !important;
  border-top-left-radius: 6px !important;
  border-top-right-radius: 6px !important;
  border: 1px solid #e1e4e8 !important;
  border-bottom: none !important;
}

.cursor {
  display: inline-block !important;
  width: 2px !important;
  height: 1.2em !important;
  background-color: #333 !important;
  vertical-align: middle !important;
  margin-left: 2px !important;
}

.cursor.blink {
  animation: blink 1s step-end infinite !important;
}

.markdown-body .reference-mark {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 16px !important;
  height: 16px !important;
  font-size: 12px !important;
  border-radius: 50% !important;
  background-color: #e1e4e8 !important;
  color: #24292e !important;
  cursor: pointer !important;
  position: relative !important;
}

.markdown-body .reference-tooltip {
  display: none !important;
  position: absolute !important;
  bottom: 100% !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  padding: 6px 12px !important;
  background-color: #24292e !important;
  color: white !important;
  border-radius: 4px !important;
  font-size: 12px !important;
  white-space: nowrap !important;
  z-index: 1000 !important;
  margin-bottom: 8px !important;
  cursor: pointer !important;
  text-decoration: underline !important;
}

.markdown-body .reference-mark:hover .reference-tooltip {
  display: block !important;
}

/* 添加一个小箭头 */
.markdown-body .reference-tooltip::after {
  content: "" !important;
  position: absolute !important;
  top: 100% !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  border: 4px solid transparent !important;
  border-top-color: #24292e !important;
}

/* 只在有标题时显示提示 */
.markdown-body .reference-mark[data-title=""] .reference-tooltip {
  display: none !important;
}

@keyframes blink {
  from,
  to {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}
</style>
