<template>
  <v-dialog
    v-model="isVisible"
    max-width="500"
    persistent
    :scrim="true"
  >
    <v-card class="token-budget-dialog">
      <v-card-title class="d-flex align-center">
        <v-icon color="warning" class="mr-2">mdi-alert-circle</v-icon>
        <span>Token额度不足</span>
      </v-card-title>
      
      <v-card-text>
        <div class="budget-info">
          <v-alert
            type="warning"
            variant="tonal"
            class="mb-4"
          >
            <div class="alert-content">
              <div class="budget-details">
                <p class="mb-2">
                  <strong>剩余额度：</strong>{{ formatBudget(remainingBudget) }}
                </p>
                <p class="mb-2">
                  <strong>需要额度：</strong>{{ formatBudget(requiredBudget) }}
                </p>
                <p class="mb-0">
                  <strong>缺少额度：</strong>{{ formatBudget(requiredBudget - remainingBudget) }}
                </p>
              </div>
            </div>
          </v-alert>
          
          <div class="description">
            <p class="mb-3">
              您的Token使用额度已不足以完成此次操作。请联系管理员进行充值后再试。
            </p>
            
            <div class="contact-info">
              <v-chip
                color="primary"
                variant="tonal"
                size="small"
                class="mr-2 mb-2"
              >
                <v-icon start>mdi-email</v-icon>
                联系邮箱：<EMAIL>
              </v-chip>
              
              <v-chip
                color="primary"
                variant="tonal"
                size="small"
                class="mb-2"
              >
                <v-icon start>mdi-account-supervisor</v-icon>
                联系管理员充值
              </v-chip>
            </div>
          </div>
        </div>
      </v-card-text>
      
      <v-card-actions class="px-6 pb-4">
        <v-spacer></v-spacer>
        <v-btn
          color="primary"
          variant="text"
          @click="handleClose"
        >
          我知道了
        </v-btn>
        <v-btn
          color="primary"
          variant="elevated"
          @click="handleRefresh"
        >
          刷新页面
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

export interface TokenBudgetDialogProps {
  visible: boolean;
  remainingBudget?: number;
  requiredBudget?: number;
  errorMessage?: string;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'close'): void;
  (e: 'refresh'): void;
}

const props = withDefaults(defineProps<TokenBudgetDialogProps>(), {
  visible: false,
  remainingBudget: 0,
  requiredBudget: 0,
  errorMessage: ''
});

const emit = defineEmits<Emits>();

const isVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

const formatBudget = (budget: number): string => {
  return budget.toFixed(4);
};

const handleClose = () => {
  emit('close');
  emit('update:visible', false);
};

const handleRefresh = () => {
  emit('refresh');
  window.location.reload();
};
</script>

<style scoped>
.token-budget-dialog {
  border-radius: 12px;
}

.budget-details {
  font-family: 'Roboto Mono', monospace;
}

.alert-content {
  width: 100%;
}

.contact-info {
  margin-top: 16px;
}

.description {
  color: rgba(0, 0, 0, 0.7);
  line-height: 1.5;
}
</style> 