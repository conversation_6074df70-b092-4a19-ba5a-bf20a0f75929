<template>
  <teleport to="body">
    <div
      v-if="visible"
      class="context-menu"
      :style="{ left: x + 'px', top: y + 'px' }"
      @click.stop
    >
      <div class="context-menu-item" @click="$emit('add-child')">
        <v-icon size="16" class="menu-icon">mdi-plus</v-icon>
        <span>新增子笔记</span>
      </div>
      <div class="context-menu-item" @click="$emit('rename')">
        <v-icon size="16" class="menu-icon">mdi-pencil</v-icon>
        <span>重命名</span>
      </div>
      <div class="context-menu-item" @click="$emit('delete')">
        <v-icon size="16" class="menu-icon">mdi-delete</v-icon>
        <span>删除</span>
      </div>
    </div>
  </teleport>
</template>

<script>
import { watch } from 'vue'

export default {
  name: 'ContextMenu',
  props: {
    visible: {
      type: <PERSON>olean,
      default: false
    },
    x: {
      type: Number,
      default: 0
    },
    y: {
      type: Number,
      default: 0
    }
  },
  emits: ['add-child', 'rename', 'delete'],
  setup(props) {
    // 点击页面其他地方时隐藏菜单
    const handleClickOutside = (event) => {
      const contextMenu = document.querySelector('.context-menu')
      if (contextMenu && !contextMenu.contains(event.target)) {
        // 通过父组件的方法隐藏菜单
        document.removeEventListener('click', handleClickOutside)
      }
    }

    watch(() => props.visible, (newVal) => {
      if (newVal) {
        setTimeout(() => {
          document.addEventListener('click', handleClickOutside)
        }, 0)
      } else {
        document.removeEventListener('click', handleClickOutside)
      }
    })

    return {}
  }
}
</script>

<style scoped>
.context-menu {
  position: fixed;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 4px 0;
  min-width: 140px;
  z-index: 1000;
  font-size: 14px;
}

.context-menu-item {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: #333;
  transition: background-color 0.2s;
}

.context-menu-item:hover {
  background-color: #f5f5f5;
}

.context-menu-item:last-child:hover {
  color: #d32f2f;
}

.menu-icon {
  color: #666;
}

.context-menu-item:last-child .menu-icon {
  color: #d32f2f;
}
</style> 