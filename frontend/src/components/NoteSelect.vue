<template>
  <div class="note-select-tree">
    <div
      v-for="node in currentLevelNodes"
      :key="node.id"
      class="note-select-node"
    >
      <div
        class="note-select-node-content"
        :class="{ 'selected': node.id === selectedId }"
      >
        <span class="note-select-toggle" @click.stop="toggleNode(node)">
          <template v-if="hasChildren(node)">
            {{ node.expanded ? '▼' : '►' }}
          </template>
          <template v-else>
            •
          </template>
        </span>
        <span class="note-select-name" @click="$emit('node-selected', node)">
          {{ node.name }}
        </span>
      </div>
      <div v-if="node.expanded" class="note-select-children">
        <NoteSelect
          v-if="hasChildren(node)"
          :nodes="allNodes"
          :parent-id="node.id"
          :selected-id="selectedId"
          @node-selected="$emit('node-selected', $event)"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
export default defineComponent({
  name: 'NoteSelect',
  props: {
    nodes: {
      type: Array,
      required: true
    },
    parentId: {
      type: [String, Number],
      default: null
    },
    selectedId: {
      type: [String, Number],
      default: null
    }
  },
  computed: {
    currentLevelNodes(): any[] {
      return this.nodes.filter((node: any) => node.parentId === this.parentId);
    },
    allNodes(): any[] {
      return this.nodes;
    }
  },
  methods: {
    hasChildren(node: any) {
      return this.nodes.some((n: any) => n.parentId === node.id);
    },
    toggleNode(node: any) {
      const updated = this.nodes.map((n: any) => {
        if (n.id === node.id) {
          return { ...n, expanded: !n.expanded };
        }
        return n;
      });
      this.$emit('update:nodes', updated);
      node.expanded = !node.expanded;
    }
  }
});
</script>

<style scoped>
.note-select-tree {
  font-size: 15px;
  user-select: none;
  position: relative;
}
.note-select-node {
  margin-bottom: 2px;
}
.note-select-node-content {
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  border: 1px solid transparent;
  display: flex;
  align-items: center;
}
.note-select-node-content.selected {
  background-color: #e6e0f7;
  border: 1px solid #654C8C;
}
.note-select-node-content:hover {
  background-color: #f3f0fa;
}
.note-select-toggle {
  margin-right: 8px;
  color: #654C8C;
  width: 16px;
  text-align: center;
  cursor: pointer;
}
.note-select-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #333;
  font-weight: 500;
}
.note-select-children {
  margin-left: 18px;
}
</style> 