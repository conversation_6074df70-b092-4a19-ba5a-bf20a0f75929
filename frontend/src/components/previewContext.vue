<template>
  <v-dialog
    v-model="dialogVisible"
    :max-width="dialogWidth"
    :fullscreen="isFullscreen"
    transition="dialog-bottom-transition"
  >
    <v-card class="preview-dialog">
      <v-card-title class="d-flex align-center justify-space-between">
        <span>{{ title }}</span>
        <div class="d-flex align-center">
          <v-btn
            icon
            variant="text"
            @click="toggleFullscreen"
            class="mr-2"
          >
            <v-icon>{{ isFullscreen ? 'mdi-fullscreen-exit' : 'mdi-fullscreen' }}</v-icon>
          </v-btn>
          <v-btn
            icon
            variant="text"
            @click="closeDialog"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </div>
      </v-card-title>

      <v-card-text class="preview-content">
        <!-- PDF预览 -->
        <div v-if="fileType.includes('pdf')" class="pdf-container">
          <iframe
            v-if="contentUrl"
            :src="contentUrl"
            width="100%"
            height="650px"
            frameborder="0"
          ></iframe>
        </div>

        <!-- 图片预览 -->
        <div v-else-if="isImage" class="image-container">
          <img
            v-if="contentUrl"
            :src="contentUrl"
            :alt="title"
            class="preview-image"
          />
        </div>

        <!-- 文本预览 -->
        <div v-else-if="isText" class="text-container">
          <pre class="text-content">{{ content }}</pre>
        </div>

        <!-- 不支持的文件类型 -->
        <div v-else class="unsupported-container">
          <v-alert
            type="warning"
            variant="tonal"
            class="ma-4"
          >
            暂不支持预览此类型的文件
            <template v-slot:append>
              <v-btn
                variant="text"
                color="primary"
                @click="downloadFile"
              >
                下载文件
              </v-btn>
            </template>
          </v-alert>
        </div>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch } from 'vue';

export default defineComponent({
  name: 'PreviewContext',
  props: {
    // 是否显示对话框
    modelValue: {
      type: Boolean,
      required: true
    },
    // 上下文ID
    contextId: {
      type: String,
      required: true
    },
    // 会话ID
    conversationId: {
      type: String,
      required: true
    },
    // 文件标题
    title: {
      type: String,
      default: '文件预览'
    },
    // 文件类型
    fileType: {
      type: String,
      default: ''
    },
    // 文件内容URL
    contentUrl: {
      type: String,
      default: ''
    },
    // 文本内容
    content: {
      type: String,
      default: ''
    }
  },
  emits: ['update:modelValue', 'close'],
  setup(props, { emit }) {
    // 对话框可见性
    const dialogVisible = computed({
      get: () => props.modelValue,
      set: (value) => emit('update:modelValue', value)
    });

    // 是否全屏
    const isFullscreen = ref(false);

    // 对话框宽度
    const dialogWidth = computed(() => {
      if (isFullscreen.value) return '100%';
      if (isImage.value) return '80%';
      return '60%';
    });

    // 判断是否为图片
    const isImage = computed(() => {
      const ispng = props.fileType.toLowerCase().includes('png');
      const isjpg = props.fileType.toLowerCase().includes('jpg');
      const isjpeg = props.fileType.toLowerCase().includes('jpeg');
      const isgif = props.fileType.toLowerCase().includes('gif');
      const iswebp = props.fileType.toLowerCase().includes('webp');
      return ispng || isjpg || isjpeg || isgif || iswebp;
    });

    // 判断是否为文本
    const isText = computed(() => {
      const textTypes = ['txt', 'md', 'json', 'xml', 'html', 'css', 'js'];
      const isText = props.fileType.toLowerCase().includes('plain');
      const isMd = props.fileType.toLowerCase().includes('markdown');
      return isText || isMd;
    });

    // 切换全屏
    const toggleFullscreen = () => {
      isFullscreen.value = !isFullscreen.value;
    };

    // 关闭对话框
    const closeDialog = () => {
      dialogVisible.value = false;
      emit('close');
    };

    // 下载文件
    const downloadFile = () => {
      if (props.contentUrl) {
        const link = document.createElement('a');
        link.href = props.contentUrl;
        link.download = props.title;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    };

    // 监听对话框关闭
    watch(dialogVisible, (newValue) => {
      if (!newValue) {
        isFullscreen.value = false;
      }
    });

    return {
      dialogVisible,
      isFullscreen,
      dialogWidth,
      isImage,
      isText,
      toggleFullscreen,
      closeDialog,
      downloadFile
    };
  }
});
</script>

<style scoped>
.preview-dialog {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.preview-content {
  flex: 1;
  overflow: hidden;
  padding: 0;
  position: relative;
}

.pdf-container,
.image-container,
.text-container {
  height: 650px;
  overflow: auto;
}

.pdf-container iframe {
  border: none;
}

.image-container {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.text-container {
  padding: 16px;
  background-color: #f5f5f5;
}

.text-content {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
}

.unsupported-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 自定义滚动条样式 */
.preview-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.preview-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.preview-content::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

.preview-content::-webkit-scrollbar-thumb:hover {
  background: #555;
}
</style>