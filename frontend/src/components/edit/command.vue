<template>
  <div v-if="modelValue" class="command-container" :style="containerStyle">
    <div 
      class="command-header" 
      @mousedown="startDrag"
    >
      <span class="command-title">{{ title }}</span>
      <span class="close-btn" @click="handleClose">&times;</span>
    </div>
    <div class="command-content">
      <textarea
        ref="inputField"
        v-model="commandText"
        class="command-text-input"
        :placeholder="placeholder"
        @keyup.enter="handleCommand"
        @keyup.esc="handleClose"
        rows="3"
      ></textarea>
      <div class="command-footer">
        <div class="enter-hint">
          <span class="enter-key">⏎</span>
          <span class="hint-text">按回车发送</span>
        </div>
        <button class="send-btn" @click="handleCommand">
          <span class="send-icon">↗</span>
          <span>发送</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, watch, computed, onUnmounted } from 'vue';

export default defineComponent({
  name: 'CommandInput',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    position: {
      type: Object,
      default: () => ({ top: 0, left: 0 })
    },
    title: {
      type: String,
      default: '输入指令'
    },
    placeholder: {
      type: String,
      default: '输入指令...'
    }
  },
  emits: ['update:modelValue', 'command', 'close'],
  
  setup(props, { emit }) {
    const inputField = ref(null);
    const commandText = ref('');
    const isDragging = ref(false);
    const dragOffset = ref({ x: 0, y: 0 });
    const currentPosition = ref({
      x: props.position.left,
      y: props.position.top
    });
    
    // 监听 props.position 的变化
    watch(() => props.position, (newPos) => {
      if (!isDragging.value) {
        currentPosition.value = {
          x: newPos.left,
          y: newPos.top
        };
      }
    }, { deep: true });

    const containerStyle = computed(() => ({
      top: `${currentPosition.value.y}px`,
      left: `${currentPosition.value.x}px`,
      // 当正在拖动时，提高z-index以确保在最上层
      zIndex: isDragging.value ? '1010' : '1005'
    }));

    const startDrag = (e: MouseEvent) => {
      if (e.target instanceof HTMLElement && e.target.classList.contains('close-btn')) {
        return;
      }
      
      isDragging.value = true;
      dragOffset.value = {
        x: e.clientX - currentPosition.value.x,
        y: e.clientY - currentPosition.value.y
      };
      
      document.addEventListener('mousemove', handleDrag);
      document.addEventListener('mouseup', stopDrag);
    };

    const handleDrag = (e: MouseEvent) => {
      if (isDragging.value) {
        e.preventDefault();
        currentPosition.value = {
          x: e.clientX - dragOffset.value.x,
          y: e.clientY - dragOffset.value.y
        };
      }
    };

    const stopDrag = () => {
      isDragging.value = false;
      document.removeEventListener('mousemove', handleDrag);
      document.removeEventListener('mouseup', stopDrag);
    };

    const handleClose = () => {
      commandText.value = '';
      emit('update:modelValue', false);
      emit('close');
    };

    const handleCommand = () => {
      if (commandText.value.trim()) {
        emit('command', commandText.value);
        handleClose();
      }
    };

    // 监听显示状态变化，聚焦输入框
    watch(() => props.modelValue, (newVal) => {
      if (newVal) {
        setTimeout(() => {
          inputField.value?.focus();
        }, 100);
      }
    });

    // 拖动时阻止文本选择
    const preventTextSelection = (e: MouseEvent) => {
      if (isDragging.value) {
        e.preventDefault();
      }
    };

    // 组件挂载时添加事件
    onMounted(() => {
      document.addEventListener('selectstart', preventTextSelection);
    });

    // 组件卸载时移除事件
    onUnmounted(() => {
      document.removeEventListener('selectstart', preventTextSelection);
      document.removeEventListener('mousemove', handleDrag);
      document.removeEventListener('mouseup', stopDrag);
    });

    return {
      commandText,
      containerStyle,
      handleClose,
      handleCommand,
      inputField,
      startDrag
    };
  }
});
</script>

<style scoped>
.command-container {
  position: fixed;
  background-color: white;
  border: none;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  pointer-events: auto;
  /* z-index通过计算属性动态设置 */
  width: 400px;
  user-select: none; /* 防止拖动时选中文本 */
}

.command-header {
  background-color: rgba(124, 77, 255, 0.8);
  color: white;
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  cursor: move; /* 指示可拖动 */
}

.command-title {
  font-weight: 500;
}

.close-btn {
  cursor: pointer;
  font-size: 18px;
  line-height: 1;
  padding: 0 4px;
}

.close-btn:hover {
  opacity: 0.8;
}

.command-content {
  padding: 12px;
}

.command-text-input {
  user-select: text; /* 确保文本区域可以选中文本 */
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  background-color: #fafafa;
  resize: none; /* 禁用调整大小 */
  font-family: inherit; /* 继承字体 */
  line-height: 1.5;
  margin-bottom: 8px;
  transition: border-color 0.2s;
}

.command-text-input:focus {
  border-color: #7c4dff;
  background-color: white;
}

.command-text-input::placeholder {
  color: #aaa;
}

.command-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4px;
}

.enter-hint {
  display: flex;
  align-items: center;
  color: #888;
  font-size: 12px;
}

.enter-key {
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 0 5px;
  margin-right: 5px;
  font-size: 14px;
  color: #666;
  background-color: #f5f5f5;
}

.hint-text {
  font-size: 12px;
}

.send-btn {
  background-color: #7c4dff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 13px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.2s;
}

.send-btn:hover {
  background-color: #6a3de8;
  transform: translateY(-1px);
}

.send-btn:active {
  transform: translateY(0);
}

.send-icon {
  margin-right: 4px;
  font-size: 14px;
}
</style>