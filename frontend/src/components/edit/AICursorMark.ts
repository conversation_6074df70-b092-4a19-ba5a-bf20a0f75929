import { Extension } from '@tiptap/core'
import { Plugin, PluginKey } from 'prosemirror-state'
import { Decoration, DecorationSet } from 'prosemirror-view'

/**
 * AICursorMark扩展 - 将特定的Markdown标记渲染为AI光标
 */
export interface AICursorMarkOptions {
  // Markdown中表示AI光标的标记
  markToken: string
  // 光标颜色
  cursorColor: string
  // 光标闪烁动画持续时间(ms)
  blinkDuration: number
  // 将标记替换为实际光标
  replaceToken: boolean
  // 光标标签
  cursorLabel: string
  // 光标大小
  cursorSize: number
  // 是否启用视口外指示器
  enableOffscreenIndicator: boolean
}

export const AICursorMark = Extension.create<AICursorMarkOptions>({
  name: 'aiCursorMark',

  addOptions() {
    return {
      markToken: '|AI|', // 默认标记
      cursorColor: '#7c4dff', // 紫色光标
      blinkDuration: 800, // 闪烁间隔
      replaceToken: true, // 替换标记
      cursorLabel: 'Deep Cognition', // 光标标签
      cursorSize: 1.5, // 光标大小放大倍数
      enableOffscreenIndicator: true, // 启用视口外指示器
    }
  },

  addStorage() {
    return {
      updateOptions: (options: Partial<AICursorMarkOptions>) => {
        Object.assign(this.options, options)
        return this.options
      }
    }
  },

  addProseMirrorPlugins() {
    const pluginKey = new PluginKey('aiCursorMark')
    const { markToken, cursorColor, blinkDuration, replaceToken, cursorLabel, cursorSize, enableOffscreenIndicator } = this.options

    // 确保副本对象，避免闭包问题
    const options = {
      markToken, cursorColor, blinkDuration, replaceToken, cursorLabel, cursorSize, enableOffscreenIndicator
    }

    // 创建指示器元素
    let offscreenIndicator: HTMLElement | null = null
    
    // 检查元素是否在视口内
    const isInViewport = (element: HTMLElement): boolean => {
      const rect = element.getBoundingClientRect()
      return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
      )
    }
    
    // 更新指示器位置
    const updateIndicatorPosition = (cursorElement: HTMLElement) => {
      if (!options.enableOffscreenIndicator) return
      
      // 如果指示器不存在，创建它
      if (!offscreenIndicator) {
        offscreenIndicator = document.createElement('div')
        offscreenIndicator.classList.add('ai-cursor-offscreen-indicator')
        offscreenIndicator.innerHTML = `
          <div class="indicator-arrow">↓</div>
          <div class="indicator-label">${options.cursorLabel}</div>
        `
        offscreenIndicator.style.position = 'fixed'
        offscreenIndicator.style.zIndex = '9999'
        offscreenIndicator.style.background = options.cursorColor
        offscreenIndicator.style.color = 'white'
        offscreenIndicator.style.padding = '5px 10px'
        offscreenIndicator.style.borderRadius = '4px'
        offscreenIndicator.style.fontWeight = 'bold'
        offscreenIndicator.style.fontSize = '12px'
        offscreenIndicator.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)'
        offscreenIndicator.style.display = 'flex'
        offscreenIndicator.style.alignItems = 'center'
        offscreenIndicator.style.justifyContent = 'center'
        offscreenIndicator.style.flexDirection = 'column'
        offscreenIndicator.style.transition = 'all 0.3s ease'
        offscreenIndicator.style.pointerEvents = 'none'
        document.body.appendChild(offscreenIndicator)
      }
      
      // 检查光标是否在视口内
      if (isInViewport(cursorElement)) {
        // 在视口内，隐藏指示器
        offscreenIndicator.style.display = 'none'
        return
      }
      
      // 显示指示器
      offscreenIndicator.style.display = 'flex'
      
      // 获取光标位置
      const rect = cursorElement.getBoundingClientRect()
      const viewportHeight = window.innerHeight || document.documentElement.clientHeight
      const viewportWidth = window.innerWidth || document.documentElement.clientWidth
      
      // 计算位置和方向
      let top = 0
      let left = 0
      let arrow = '↓'
      
      if (rect.top < 0) {
        // 光标在上方
        top = 10
        left = Math.max(0, Math.min(rect.left, viewportWidth - 100))
        arrow = '↑'
      } else if (rect.top > viewportHeight) {
        // 光标在下方
        top = viewportHeight - 60
        left = Math.max(0, Math.min(rect.left, viewportWidth - 100))
        arrow = '↓'
      } else if (rect.left < 0) {
        // 光标在左侧
        top = Math.max(0, Math.min(rect.top, viewportHeight - 60))
        left = 10
        arrow = '→'
      } else {
        // 光标在右侧
        top = Math.max(0, Math.min(rect.top, viewportHeight - 60))
        left = viewportWidth - 100
        arrow = '←'
      }
      
      // 更新指示器位置和箭头
      offscreenIndicator.style.top = `${top}px`
      offscreenIndicator.style.left = `${left}px`
      const arrowElement = offscreenIndicator.querySelector('.indicator-arrow')
      if (arrowElement) {
        arrowElement.textContent = arrow
      }
    }
    
    return [
      new Plugin({
        key: pluginKey,
        state: {
          init() {
            return { decorationSet: DecorationSet.empty, positions: [] }
          },
          apply: (tr, value, oldState, newState) => {
            // 如果文档改变，重新扫描
            if (tr.docChanged) {
              // 查找文档中的光标标记
              const results = findCursorPositions(newState.doc, options)
              return results
            }
            
            // 如果不是新的文档，但需要调整位置
            if (tr.docChanged && value.decorationSet) {
              // 重新映射装饰位置
              return {
                decorationSet: value.decorationSet.map(tr.mapping, tr.doc),
                positions: value.positions
              }
            }
            
            // 维持当前状态
            return value
          }
        },
        props: {
          decorations(state) {
            return this.getState(state).decorationSet
          },
        },
        view: (view) => {
          return {
            update: () => {
              // 如果指示器存在，检查光标是否在视口内
              if (options.enableOffscreenIndicator) {
                const cursor = document.querySelector('.ai-cursor-mark')
                if (cursor instanceof HTMLElement) {
                  updateIndicatorPosition(cursor)
                }
              }
            },
            destroy: () => {
              // 移除指示器
              if (offscreenIndicator) {
                offscreenIndicator.remove()
                offscreenIndicator = null
              }
            }
          }
        }
      }),
    ]
  },

  // 添加全局CSS
  onCreate() {
    if (!document.getElementById('ai-cursor-mark-style')) {
      const style = document.createElement('style')
      style.id = 'ai-cursor-mark-style'
      style.textContent = `
        @keyframes aiCursorBlink {
          0%, 100% { opacity: 1; }
          50% { opacity: 0; }
        }
        
        .ai-cursor-mark {
          user-select: none;
          pointer-events: none;
        }
        
        .ai-cursor-highlight {
          font-weight: bold;
        }
        
        .ai-cursor-offscreen-indicator {
          display: none;
          animation: pulse 1.5s infinite ease-in-out;
        }
        
        .indicator-arrow {
          font-size: 18px;
          font-weight: bold;
        }
        
        .indicator-label {
          font-size: 10px;
          white-space: nowrap;
        }
        
        @keyframes pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.1); }
        }
      `
      document.head.appendChild(style)
    }
  },

  // 清理
  onDestroy() {
    const style = document.getElementById('ai-cursor-mark-style')
    if (style) {
      style.remove()
    }
    
    // 移除视口外指示器
    const indicator = document.querySelector('.ai-cursor-offscreen-indicator')
    if (indicator) {
      indicator.remove()
    }
  }
})

// 提取为独立函数，避免this绑定问题
function findCursorPositions(doc, options) {
  const { markToken, cursorColor, blinkDuration, replaceToken, cursorLabel, cursorSize } = options
  const positions = []
  const decorations = []
  
  // 遍历文档节点
  doc.descendants((node, pos) => {
    if (node.isText) {
      const text = node.text || ''
      let index = text.indexOf(markToken)
      
      // 查找所有标记位置
      while (index !== -1) {
        const from = pos + index
        const to = from + markToken.length
        
        // 记录位置
        positions.push({ from, to })
        
        // 创建光标装饰
        if (replaceToken) {
          // 替换标记为光标装饰
          decorations.push(
            Decoration.widget(from, () => {
              // 创建光标元素
              const cursor = document.createElement('span')
              cursor.classList.add('ai-cursor-mark')
              cursor.textContent = '|'
              cursor.style.color = cursorColor
              cursor.style.animation = `aiCursorBlink ${blinkDuration}ms infinite`
              cursor.style.fontWeight = 'bold'
              cursor.style.position = 'relative'
              cursor.style.display = 'inline-block'
              cursor.style.fontSize = `${cursorSize}em` // 增大光标大小
              
              // 添加标签
              if (cursorLabel) {
                const label = document.createElement('span')
                label.classList.add('ai-cursor-label')
                label.textContent = cursorLabel
                label.style.position = 'absolute'
                label.style.top = '-25px' // 调整标签位置以适应更大的文字
                label.style.left = '50%'
                label.style.transform = 'translateX(-50%)' // 居中标签
                label.style.background = cursorColor
                label.style.color = 'white'
                label.style.fontSize = '12px' // 增大标签字体
                label.style.padding = '3px 8px'
                label.style.borderRadius = '4px'
                label.style.pointerEvents = 'none'
                label.style.whiteSpace = 'nowrap'
                cursor.appendChild(label)
              }
              
              return cursor
            })
          )
          
          // 隐藏原始标记
          decorations.push(
            Decoration.inline(from, to, {
              style: 'display: none',
            })
          )
        } else {
          // 仅高亮标记，不替换
          decorations.push(
            Decoration.inline(from, to, {
              class: 'ai-cursor-highlight',
              style: `color: ${cursorColor}; font-weight: bold; font-size: ${cursorSize}em;`,
            })
          )
        }
        
        // 查找下一个标记
        index = text.indexOf(markToken, index + markToken.length)
      }
    }
  })
  
  return { 
    decorationSet: DecorationSet.create(doc, decorations),
    positions
  }
}

export default AICursorMark 