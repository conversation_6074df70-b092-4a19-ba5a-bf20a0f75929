<template>
  <div class="editor-container">
    <editor-content class="editor-content-wrapper" :editor="editor" />
    <TextSelectionToolbar editorSelector=".editor-container" @cut="handleCut" @edit="handleEdit" />
  </div>
</template>

<script lang="ts">
import { defineComponent, onBeforeUnmount, shallowRef, onMounted, watch, nextTick } from 'vue'
import { Editor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'
import { Markdown } from 'tiptap-markdown'
import TextSelectionToolbar from './comment.vue'
// 导入表格相关扩展
import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableHeader from '@tiptap/extension-table-header'
import TableCell from '@tiptap/extension-table-cell'
// 导入链接扩展
import Link from '@tiptap/extension-link'
// 导入代码高亮扩展
import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight'
import { common, createLowlight } from 'lowlight'
// 导入常用语言高亮
import javascript from 'highlight.js/lib/languages/javascript'
import typescript from 'highlight.js/lib/languages/typescript'
import python from 'highlight.js/lib/languages/python'
import java from 'highlight.js/lib/languages/java'
import css from 'highlight.js/lib/languages/css'
import html from 'highlight.js/lib/languages/xml'
import json from 'highlight.js/lib/languages/json'
import bash from 'highlight.js/lib/languages/bash'
import sql from 'highlight.js/lib/languages/sql'
// 导入AI编辑指示器扩展
import AIMarkdownDecorator from './AIMarkdownDecorator'
// 导入AI光标标记扩展
import AICursorMark from './AICursorMark'
// 导入自定义数学公式扩展
import MathRenderer from './MathRenderer'
// 导入KaTeX样式
import 'katex/dist/katex.min.css'
// 导入KaTeX
import katex from 'katex'

// 创建lowlight实例
const lowlight = createLowlight(common)
// 注册语言高亮
lowlight.register('javascript', javascript)
lowlight.register('typescript', typescript)
lowlight.register('python', python)
lowlight.register('java', java)
lowlight.register('css', css)
lowlight.register('html', html)
lowlight.register('json', json)
lowlight.register('bash', bash)
lowlight.register('sql', sql)

export default defineComponent({
  components: {
    EditorContent,
    TextSelectionToolbar
  },
  props: {
    initialContent: {
      type: String,
      default: ''
    },
    // 添加AI编辑位置属性
    aiEditingStart: {
      type: Number,
      default: null
    },
    aiEditingEnd: {
      type: Number,
      default: null
    },
    // 是否启用AI编辑指示器
    aiEditingEnabled: {
      type: Boolean,
      default: false
    },
    // 添加可编辑状态属性
    editable: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:content', 'edit-command'],
  setup(props, { emit }) {
    // 使用shallowRef避免性能问题
    const editor = shallowRef(null)
    
    // 初始化编辑器
    editor.value = new Editor({
      extensions: [
        StarterKit.configure({
          // 自定义代码块，使用高亮版本替代
          codeBlock: false,
        }),
        Placeholder.configure({
          placeholder: '在此输入Markdown内容...',
        }),
        // 添加表格相关扩展
        Table.configure({
          resizable: true,
          HTMLAttributes: {
            class: 'markdown-table',
          },
        }),
        TableRow,
        TableHeader,
        TableCell,
        // 添加链接扩展
        Link.configure({
          openOnClick: true,
          HTMLAttributes: {
            class: 'markdown-link',
            target: '_blank',
            rel: 'noopener noreferrer',
          },
          // 自动检测链接
          autolink: true,
          // 验证URL格式
          validate: href => /^https?:\/\//.test(href),
        }),
        // 添加代码块高亮扩展
        CodeBlockLowlight.configure({
          lowlight,
          HTMLAttributes: {
            class: 'code-block-wrapper',
          },
        }),
        // 添加自定义数学公式扩展
        MathRenderer.configure({
          katexOptions: {
            output: 'html',
            throwOnError: false,
            trust: false,
            strict: false,
          }
        }),
        Markdown.configure({
          html: true,
          tightLists: true,
          tightListClass: 'tight',
          bulletListMarker: '-',
          linkify: true,
          // 确保表格的正确解析
          transformPastedText: true,
          // 确保Markdown中的表格能够正确导入
          transformCopiedText: true,
        }),
        // 添加AI编辑指示器扩展
        AIMarkdownDecorator.configure({
          enabled: props.aiEditingEnabled,
          startPosition: props.aiEditingStart,
          endPosition: props.aiEditingEnd,
          marker: '✎', // 使用编辑符号作为标记
          markerColor: '#7c4dff', // 紫色标记
          backgroundColor: 'rgba(124, 77, 255, 0.1)', // 浅紫色背景
        }),
        // 添加AI光标标记扩展
        AICursorMark.configure({
          markToken: '|AI|', // AI光标标记
          cursorColor: '#7c4dff', // 紫色光标
          blinkDuration: 800, // 闪烁间隔
          replaceToken: true, // 替换标记
          cursorLabel: 'AI', // 光标标签
        }),
      ],
      content: "",
      autofocus: true,
      editable: false,
      injectCSS: false,
      editorProps: {
        handlePaste: (view, event) => {
          const text = event.clipboardData?.getData('text/plain') || '';
          
          // 检查是否包含 Markdown 特殊字符，直接使用 insertContent 插入内容
          if (text.includes('#') || text.includes('*') || text.includes('```') || text.includes('-') || text.includes('>') || text.includes('|')) {
            if (editor.value) {
              // 直接插入内容，让 Markdown 扩展自动处理
              editor.value.commands.insertContent(text);
              return true;
            }
          }
          
          return false;
        }
      },
      onUpdate: ({ editor }) => {
        if (typeof editor.storage.markdown.getMarkdown === 'function') {
          const markdown = editor.storage.markdown.getMarkdown();
          emit('update:content', markdown);
        } else {
          const html = editor.getHTML();
          emit('update:content', html);
        }
      },
    })
    // console.log('初始化内容(raw):', props.initialContent);
    // 初始化时设置内容，直接使用 setContent
    if (props.initialContent) {
      editor.value.commands.setContent(props.initialContent);
      // console.log('初始化内容:', props.initialContent);
    }
    
    // 调试：检查扩展是否正确加载
    console.log('编辑器扩展列表:', editor.value.extensionManager.extensions.map(ext => ext.name));
    
    // 新增：监听外部内容变化，自动同步到编辑器
    watch(
      () => props.initialContent,
      (newContent) => {
        // console.log('监听内容变化:', newContent);
        if (editor.value && newContent !== undefined) {
          // 使用 setContent 方法设置内容
          editor.value.commands.setContent(newContent || '');
          // console.log('同步内容:', newContent);
        }
      }
    );
    
    // 监听AI编辑区域变化
    watch(() => [props.aiEditingStart, props.aiEditingEnd, props.aiEditingEnabled], 
      ([start, end, enabled]) => {
        if (!editor.value) return;
        
        nextTick(() => {
          editor.value.storage.aiMarkdownDecorator.updateOptions({
            startPosition: start,
            endPosition: end,
            enabled: enabled
          });
          editor.value.view.dispatch(editor.value.view.state.tr);
        });
      }, 
      { immediate: true }
    );
    
    // 设置编辑器是否可编辑
    const setEditable = (editable: boolean) => {
      if (editor.value) {
        editor.value.setEditable(false);
      }
    };
    
    // 监听editable属性变化
    watch(() => props.editable, (editable) => {
      setEditable(editable);
    });
    
    // 添加监听自定义剪切事件
    onMounted(() => {
      const editorElement = document.querySelector('.ProseMirror');
      if (editorElement) {
        editorElement.addEventListener('cut-selection', (event: CustomEvent) => {
          if (editor.value && event.detail && event.detail.text) {
            // 获取当前选择范围
            const { from, to } = editor.value.state.selection;
            // 删除选中内容
            editor.value.chain().deleteRange({ from, to }).run();
          }
        });
      }
    });
    
    // 在组件销毁前移除事件监听器
    onBeforeUnmount(() => {
      const editorElement = document.querySelector('.ProseMirror');
      if (editorElement) {
        editorElement.removeEventListener('cut-selection', null);
      }
      
      if (editor.value) {
        editor.value.destroy();
      }
    });
    
    // 提供编辑器操作方法
    const selectText = (from: number, to: number) => {
      editor.value.chain().focus().setTextSelection({ from, to }).run()
    }
    
    const deleteRange = (from: number, to: number) => {
      editor.value.chain().focus().deleteRange({ from, to }).run()
    }
    
    const insertContentAt = (position: number, content: string) => {
      // 直接使用 insertContentAt，让 Markdown 扩展自动处理格式
      editor.value.chain().focus().insertContentAt(position, content).run()
    }
    
    // 设置AI编辑区域
    const setAIEditingRegion = (start: number, end: number) => {
      if (editor.value) {
        editor.value.storage.aiMarkdownDecorator.updateOptions({
          startPosition: start,
          endPosition: end,
          enabled: true
        });
        editor.value.view.dispatch(editor.value.view.state.tr);
      }
    }
    
    // 清除AI编辑区域
    const clearAIEditingRegion = () => {
      if (editor.value) {
        editor.value.storage.aiMarkdownDecorator.updateOptions({
          enabled: false,
          startPosition: null,
          endPosition: null
        });
        editor.value.view.dispatch(editor.value.view.state.tr);
      }
    }
    
    // 表格相关方法
    const insertTable = () => {
      editor.value.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
    }
    
    const addColumnBefore = () => {
      editor.value.chain().focus().addColumnBefore().run();
    }
    
    const addColumnAfter = () => {
      editor.value.chain().focus().addColumnAfter().run();
    }
    
    const deleteColumn = () => {
      editor.value.chain().focus().deleteColumn().run();
    }
    
    const addRowBefore = () => {
      editor.value.chain().focus().addRowBefore().run();
    }
    
    const addRowAfter = () => {
      editor.value.chain().focus().addRowAfter().run();
    }
    
    const deleteRow = () => {
      editor.value.chain().focus().deleteRow().run();
    }
    
    const deleteTable = () => {
      editor.value.chain().focus().deleteTable().run();
    }
    
    const toggleHeaderCell = () => {
      editor.value.chain().focus().toggleHeaderCell().run();
    }
    
    const mergeOrSplitCells = () => {
      const { selection } = editor.value.state;
      // 检查是否选中了多个单元格
      if (selection.ranges && selection.ranges.length > 0) {
        editor.value.chain().focus().mergeCells().run();
      } else {
        editor.value.chain().focus().splitCell().run();
      }
    }
    
    // 处理剪切操作
    const handleCut = (text: string) => {
      if (editor.value) {
        const { from, to } = editor.value.state.selection;
        editor.value.chain().deleteRange({ from, to }).run();
      }
    };

    // 处理编辑操作
    const handleEdit = (data: { text: string, command: string, selection: { from: number, to: number }, formattedMessage?: string }) => {
      if (editor.value) {
        const { text, command, selection, formattedMessage } = data;
        console.log('编辑文本:', data);
        
        // 如果有formattedMessage，则通过事件发送到父组件
        if (formattedMessage) {
          emit('edit-command', formattedMessage);
          return;
        }
        
        // 处理表格相关命令
        if (command.startsWith('/table')) {
          const tableCommand = command.replace('/table', '').trim();
          
          switch (tableCommand) {
            case 'insert':
              insertTable();
              break;
            case 'row+':
              addRowAfter();
              break;
            case 'row-':
              addRowBefore();
              break;
            case 'delrow':
              deleteRow();
              break;
            case 'col+':
              addColumnAfter();
              break;
            case 'col-':
              addColumnBefore();
              break;
            case 'delcol':
              deleteColumn();
              break;
            case 'del':
              deleteTable();
              break;
            case 'header':
              toggleHeaderCell();
              break;
            case 'merge':
              mergeOrSplitCells();
              break;
            default:
              // 如果是创建带有特定行列数的表格
              const match = tableCommand.match(/^(\d+)x(\d+)$/);
              if (match) {
                const rows = parseInt(match[1], 10);
                const cols = parseInt(match[2], 10);
                editor.value.chain().focus().insertTable({ 
                  rows, 
                  cols, 
                  withHeaderRow: true 
                }).run();
              }
              break;
          }
          return;
        }
        
        // 处理其他命令
        // ...其他命令处理逻辑
      }
    };
    
    return {
      editor,
      selectText,
      deleteRange,
      insertContentAt,
      insertTable,
      addColumnBefore,
      addColumnAfter,
      deleteColumn,
      addRowBefore,
      addRowAfter,
      deleteRow,
      deleteTable,
      toggleHeaderCell,
      mergeOrSplitCells,
      handleCut,
      handleEdit,
      setAIEditingRegion,
      clearAIEditingRegion,
      setEditable
    }
  }
})
</script>

<style>
.editor-container {
  border: none;
  border-radius: 0;
  padding: 0;
  width: 100%;
  background-color: white;
  display: flex;
  flex-direction: column;
  overflow: visible; /* 允许内容溢出 */
  box-sizing: border-box; /* 确保盒模型尺寸计算正确 */
}

.editor-content-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

/* 让编辑器内容区域占满整个容器 */
.editor-container .ProseMirror-focused,
.editor-container .ProseMirror {
  outline: none;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  flex-grow: 1; /* 允许根据内容增长 */
  overflow-y: visible; /* 改为visible，不使用滚动条 */
  font-family: 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #333;
  line-height: 1.6;
  background-color: #fefefe;
  background-image: linear-gradient(#f8f8f8 1px, transparent 1px);
  background-size: 100% 26px;
  background-position: 0 1px;
}

/* 确保空编辑器也能显示一个可点击区域 */
.ProseMirror:empty::after {
  content: '';
  display: block;
  min-height: 30vh; /* 设置一个最小高度 */
  width: 100%;
}

/* TipTap 基础样式 */
.ProseMirror {
  outline: none;
  padding: 16px;
  box-sizing: border-box;
  width: 100%;
  letter-spacing: 0.02em;
}

.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

.ProseMirror p {
  margin: 0 0 1.1em 0;
  line-height: 1.6;
}

.ProseMirror h1 {
  font-size: 2em;
  font-weight: 600;
  margin: 1.2em 0 0.6em 0;
  padding-bottom: 0.2em;
  border-bottom: 1px solid #eee;
  color: #444;
}

.ProseMirror h2 {
  font-size: 1.5em;
  font-weight: 600;
  margin: 1em 0 0.5em 0;
  color: #555;
}

.ProseMirror h3 {
  font-size: 1.25em;
  font-weight: 600;
  margin: 0.8em 0 0.4em 0;
  color: #555;
}

.ProseMirror h4 {
  font-size: 1.1em;
  font-weight: 600;
  margin: 0.6em 0 0.3em 0;
  color: #555;
}

.ProseMirror ul,
.ProseMirror ol {
  padding-left: 1.8em;
  margin: 0.5em 0 1em 0;
}

.ProseMirror li {
  margin-bottom: 0.3em;
}

.ProseMirror li p {
  margin: 0.2em 0;
}

.ProseMirror blockquote {
  border-left: 3px solid #654C8C;
  padding: 0.2em 0 0.2em 1em;
  margin: 1em 0;
  color: #555;
  background-color: rgba(101, 76, 140, 0.05);
  font-style: italic;
}

/* 改进代码块样式 */
.ProseMirror pre {
  background: #2d2d2d;
  color: #f8f8f2;
  padding: 0.8em 1em;
  border-radius: 5px;
  font-family: 'Fira Code', 'Source Code Pro', 'Consolas', monospace;
  font-size: 0.9em;
  overflow-x: auto;
  margin: 1em 0;
  position: relative;
}

/* 代码块语言标签 */
.ProseMirror pre::before {
  content: attr(data-language);
  position: absolute;
  top: 2px;
  right: 8px;
  font-size: 0.7em;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(0, 0, 0, 0.3);
  padding: 2px 6px;
  border-radius: 3px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* 行号样式 */
.ProseMirror pre .line-number {
  display: inline-block;
  width: 2em;
  text-align: right;
  margin-right: 1em;
  color: rgba(255, 255, 255, 0.4);
  user-select: none;
}

.ProseMirror code {
  background: rgba(101, 76, 140, 0.08);
  border-radius: 3px;
  padding: 0.2em 0.4em;
  font-family: 'Fira Code', 'Source Code Pro', 'Consolas', monospace;
  font-size: 0.9em;
  color: #654C8C;
}

/* 改进链接样式 */
.ProseMirror a {
  color: #7c4dff;
  text-decoration: none;
  border-bottom: 1px dotted #7c4dff;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.ProseMirror a:hover {
  color: #6200ea;
  border-bottom: 1px solid #6200ea;
  background-color: rgba(124, 77, 255, 0.05);
}

/* 链接悬停提示 */
.ProseMirror a::after {
  content: "🔗";
  display: inline-block;
  margin-left: 3px;
  font-size: 0.8em;
  opacity: 0.5;
  transition: opacity 0.2s ease;
}

.ProseMirror a:hover::after {
  opacity: 1;
}

/* 自定义链接类 */
.ProseMirror .markdown-link {
  position: relative;
}

.ProseMirror hr {
  border: none;
  border-top: 1px solid #eee;
  margin: 1.5em 0;
}

/* 添加草稿纸感觉 */
.ProseMirror p::selection,
.ProseMirror h1::selection,
.ProseMirror h2::selection,
.ProseMirror h3::selection,
.ProseMirror li::selection,
.ProseMirror blockquote::selection,
.ProseMirror code::selection {
  background-color: rgba(124, 77, 255, 0.2);
}

/* 表格样式改进 */
.ProseMirror table {
  border-collapse: collapse;
  margin: 1.2em 0;
  overflow: hidden;
  width: 100%;
  max-width: 100%; /* 添加最大宽度限制 */
  table-layout: fixed;
  box-sizing: border-box; /* 确保边框不会增加宽度 */
  background-color: white;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  border-radius: 5px;
}

.ProseMirror table td,
.ProseMirror table th {
  border: 1px solid #e9e9e9;
  box-sizing: border-box;
  padding: 0.8em;
  position: relative;
  vertical-align: top;
  min-width: 100px;
  word-break: break-word;
}

.ProseMirror table th {
  background-color: #f5f5f5;
  font-weight: 600;
  text-align: left;
  color: #444;
  border-bottom: 2px solid #7c4dff;
}

.ProseMirror table tr:nth-child(2n) {
  background-color: rgba(248, 248, 248, 0.5);
}

.ProseMirror table .selectedCell {
  background-color: rgba(124, 77, 255, 0.1); /* 使用紫色主题色 */
  position: relative;
  border: 2px solid #7c4dff !important;
}

/* 可调整列宽 */
.ProseMirror table .column-resize-handle {
  background-color: #7c4dff;
  bottom: 0;
  position: absolute;
  right: -2px;
  pointer-events: none;
  top: 0;
  width: 4px;
}

/* 表格拖动样式 */
.ProseMirror .tableWrapper {
  overflow-x: auto; /* 允许表格水平滚动 */
  max-width: 100%; /* 限制最大宽度 */
  margin: 1.2em 0;
  width: 100%;
  box-sizing: border-box;
  border-radius: 5px;
}

/* 确保空表格单元格有最小高度 */
.ProseMirror table td p,
.ProseMirror table th p {
  margin: 0;
  min-height: 1em;
}

/* 代码高亮样式 */
.code-block-wrapper {
  position: relative;
  margin: 1em 0;
  color: #e0e0e0;
  background: #1e1e1e;  /* 稍微调暗背景色 */
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
  overflow: hidden;
}

.code-block-wrapper pre {
  margin: 0;
  padding: 1.2em;
  overflow-x: auto;
  font-family: 'Fira Code', 'Source Code Pro', 'Consolas', monospace;
  line-height: 1.5;
  tab-size: 2;
  color: #e0e0e0;  /* 添加默认文本颜色 */
}

.code-block-wrapper code {
  background: transparent;
  padding: 0;
  font-family: inherit;
  color: #e0e0e0 !important;
  border-radius: 0;
}

/* 各种语法高亮颜色 */
.hljs-comment,
.hljs-quote {
  color: #6a9955 !important;  /* 更柔和的绿色 */
  font-style: italic;
}

.hljs-keyword,
.hljs-selector-tag {
  color: #569cd6 !important;  /* 更亮的蓝色 */
}

.hljs-string,
.hljs-attribute,
.hljs-symbol,
.hljs-bullet {
  color: #ce9178 !important;  /* 更柔和的橙色 */
}

.hljs-function,
.hljs-section,
.hljs-name {
  color: #dcdcaa !important;  /* 更亮的黄色 */
}

.hljs-tag {
  color: #569cd6 !important;   /* 与关键字相同的蓝色 */
}

.hljs-number,
.hljs-literal,
.hljs-variable,
.hljs-template-variable {
  color: #b5cea8 !important;  /* 更柔和的绿色 */
}

.hljs-regexp,
.hljs-link {
  color: #d16969 !important;  /* 更柔和的红色 */
}

.hljs-title,
.hljs-class,
.hljs-type {
  color: #4ec9b0 !important;  /* 更亮的青色 */
}

/* AI编辑指示器样式 */
.ai-markdown-marker {
  user-select: none;
  pointer-events: none;
}

/* 数学公式样式 */
.ProseMirror .math-node {
  display: inline-block;
  margin: 0 2px;
  padding: 2px 4px;
  background-color: rgba(124, 77, 255, 0.05);
  border-radius: 3px;
  border: 1px solid rgba(124, 77, 255, 0.1);
  transition: all 0.2s ease;
}

.ProseMirror .math-node:hover {
  background-color: rgba(124, 77, 255, 0.1);
  border-color: rgba(124, 77, 255, 0.2);
}

.ProseMirror .math-node .katex {
  font-size: 1em;
}

.ProseMirror .math-node .katex-display {
  margin: 0.5em 0;
  text-align: center;
}

/* 确保KaTeX渲染正确 */
.ProseMirror .katex .katex-html {
  display: inline-block;
}

.ProseMirror .katex-display .katex-html {
  display: block;
  text-align: center;
}

/* 数学公式错误提示 */
.ProseMirror .katex-error {
  color: #dc3545;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 3px;
  padding: 2px 4px;
}
</style>