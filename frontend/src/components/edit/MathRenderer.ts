import { Extension } from '@tiptap/core'
import { Plugin, PluginKey } from 'prosemirror-state'
import { Decoration, DecorationSet } from 'prosemirror-view'
import katex from 'katex'

/**
 * 自定义数学公式渲染扩展
 * 能够检测并渲染 $...$ (内联) 和 $$...$$ (块级) 数学公式
 */
export interface MathRendererOptions {
    // KaTeX 配置选项
    katexOptions: any
}

// 创建数学公式元素的独立函数
function createMathElement(latex: string, isBlock: boolean, katexOptions: any) {
    const container = document.createElement(isBlock ? 'div' : 'span')
    container.classList.add('math-rendered')

    if (isBlock) {
        container.classList.add('math-block')
        container.style.cssText = `
      display: block;
      text-align: center;
      margin: 1em 0;
    `
    } else {
        container.classList.add('math-inline')
        container.style.cssText = `
      display: inline-block;
      margin: 0 2px;
      vertical-align: middle;
    `
    }

    try {
        // 使用 KaTeX 渲染数学公式
        katex.render(latex, container, {
            ...katexOptions,
            displayMode: isBlock
        })

        // 添加样式
        container.style.backgroundColor = 'rgba(124, 77, 255, 0.05)'
        container.style.borderRadius = '3px'
        container.style.padding = isBlock ? '8px' : '2px 4px'
        container.style.border = '1px solid rgba(124, 77, 255, 0.1)'
        container.style.transition = 'all 0.2s ease'

        // 添加悬停效果
        container.addEventListener('mouseenter', () => {
            container.style.backgroundColor = 'rgba(124, 77, 255, 0.1)'
            container.style.borderColor = 'rgba(124, 77, 255, 0.2)'
        })

        container.addEventListener('mouseleave', () => {
            container.style.backgroundColor = 'rgba(124, 77, 255, 0.05)'
            container.style.borderColor = 'rgba(124, 77, 255, 0.1)'
        })

        // 添加标题提示
        container.title = `数学公式: ${latex}`

    } catch (error) {
        // 渲染错误时显示原始内容
        container.textContent = isBlock ? `$$${latex}$$` : `$${latex}$`
        container.style.cssText = `
      color: #dc3545;
      background-color: #f8d7da;
      border: 1px solid #f5c6cb;
      border-radius: 3px;
      padding: 2px 4px;
      display: ${isBlock ? 'block' : 'inline-block'};
    `
        container.title = `数学公式渲染错误: ${error instanceof Error ? error.message : String(error)}`
    }

    return container
}

// 文本位置到文档位置的映射函数
function mapTextPositionToDoc(doc: any, textPos: number) {
    // 直接扫描文档位置，找到对应的文本位置
    for (let docPos = 0; docPos <= doc.content.size; docPos++) {
        const textToPos = doc.textBetween(0, docPos, '\n');

        if (textToPos.length === textPos) {
            return docPos;
        }

        if (textToPos.length > textPos) {
            // 如果超过了目标位置，返回前一个位置
            return Math.max(0, docPos - 1);
        }
    }

    // 如果没有找到，返回文档末尾
    return doc.content.size;
}

export const MathRenderer = Extension.create<MathRendererOptions>({
    name: 'mathRenderer',

    addOptions() {
        return {
            katexOptions: {
                output: 'html',
                throwOnError: false,
                trust: false,
                strict: false,
            }
        }
    },

    addProseMirrorPlugins() {
        const pluginKey = new PluginKey('mathRenderer')
        const options = this.options

        return [
            new Plugin({
                key: pluginKey,
                props: {
                    decorations: (state) => {
                        const decorations = []
                        const doc = state.doc

                        // 使用 textBetween 获取整个文档的纯文本内容
                        const fullText = doc.textBetween(0, doc.content.size, '\n')
                        console.log('文档文本长度:', fullText.length, '文档大小:', doc.content.size);

                        // 查找所有数学公式模式
                        const mathPatterns = [
                            // 块级公式 $$...$$
                            {
                                regex: /\$\$([^$]+?)\$\$/g,
                                isBlock: true
                            },
                            // 内联公式 $...$（但不匹配 $$...$$）
                            {
                                regex: /(?<!\$)\$([^$\n]+?)\$(?!\$)/g,
                                isBlock: false
                            }
                        ]

                        mathPatterns.forEach(({ regex, isBlock }) => {
                            let match
                            while ((match = regex.exec(fullText)) !== null) {
                                const textStartPos = match.index
                                const textEndPos = match.index + match[0].length
                                const mathContent = match[1]

                                console.log('数学公式:', {
                                    type: isBlock ? 'block' : 'inline',
                                    content: mathContent.substring(0, 20) + (mathContent.length > 20 ? '...' : ''),
                                    textRange: [textStartPos, textEndPos]
                                });

                                // 将文本位置映射回文档位置
                                const docStartPos = mapTextPositionToDoc(doc, textStartPos)
                                const docEndPos = mapTextPositionToDoc(doc, textEndPos)

                                if (docStartPos !== null && docEndPos !== null) {
                                    // 验证映射的正确性
                                    const mappedText = doc.textBetween(docStartPos, docEndPos);
                                    console.log('映射验证:', {
                                        expected: match[0],
                                        actual: mappedText,
                                        matches: mappedText === match[0],
                                        docRange: [docStartPos, docEndPos]
                                    });

                                    if (mappedText === match[0]) {
                                        // 只有在映射正确时才创建装饰
                                        const mathElement = createMathElement(mathContent, isBlock, options.katexOptions)

                                        // 添加装饰 - 使用 widget 替换原始文本
                                        decorations.push(
                                            Decoration.widget(docStartPos, mathElement, {
                                                side: 0,
                                                marks: [],
                                                ignoreSelection: true
                                            })
                                        )

                                        // 隐藏原始的数学公式文本
                                        decorations.push(
                                            Decoration.inline(docStartPos, docEndPos, {
                                                style: 'display: none;'
                                            })
                                        )
                                    } else {
                                        console.warn('映射不匹配，跳过此公式');
                                    }
                                }
                            }
                        })

                        return DecorationSet.create(doc, decorations)
                    },
                },

                // 监听文档变化，重新计算装饰
                appendTransaction: (transactions, oldState, newState) => {
                    // 如果文档内容发生变化，触发装饰更新
                    const docChanged = transactions.some(tr => tr.docChanged)
                    if (docChanged) {
                        // 触发重新计算装饰
                        return newState.tr
                    }
                    return null
                }
            }),
        ]
    },

    // 添加全局样式
    onCreate() {
        const style = document.createElement('style')
        style.id = 'math-renderer-style'
        style.textContent = `
      .math-rendered {
        user-select: none;
        cursor: default;
      }
      
      .math-rendered .katex {
        font-size: 1em;
      }
      
      .math-block .katex-display {
        margin: 0;
      }
      
      .math-inline .katex {
        font-size: 1em;
      }
    `

        if (!document.getElementById('math-renderer-style')) {
            document.head.appendChild(style)
        }
    },

    // 清理样式
    onDestroy() {
        const style = document.getElementById('math-renderer-style')
        if (style) {
            style.remove()
        }
    }
})

export default MathRenderer 