import { Extension } from '@tiptap/core'
import { Plugin, PluginKey } from 'prosemirror-state'
import { Decoration, DecorationSet } from 'prosemirror-view'

/**
 * AIMarkdownDecorator扩展 - 使用Markdown语法标记AI正在编辑的区域
 */
export interface AIMarkdownDecoratorOptions {
  // 编辑区域的起始位置
  startPosition: number | null
  // 编辑区域的结束位置
  endPosition: number | null
  // 编辑区域标记符号
  marker: string
  // 编辑区域标记颜色
  markerColor: string
  // 编辑区域背景颜色
  backgroundColor: string
  // 是否启用
  enabled: boolean
}

export const AIMarkdownDecorator = Extension.create<AIMarkdownDecoratorOptions>({
  name: 'aiMarkdownDecorator',

  addOptions() {
    return {
      startPosition: null,
      endPosition: null,
      marker: '✎', // 使用编辑符号作为标记
      markerColor: '#7c4dff', // 紫色标记
      backgroundColor: 'rgba(124, 77, 255, 0.1)', // 浅紫色背景
      enabled: false,
    }
  },

  addProseMirrorPlugins() {
    const pluginKey = new PluginKey('aiMarkdownDecorator')
    
    return [
      new Plugin({
        key: pluginKey,
        props: {
          decorations: (state) => {
            // 如果未启用或位置未设置，不显示任何装饰
            if (!this.options.enabled || this.options.startPosition === null || this.options.endPosition === null) {
              return DecorationSet.empty
            }
            
            const { startPosition, endPosition, marker, markerColor, backgroundColor } = this.options
            const decorations = []
            
            // 在开始位置添加标记
            decorations.push(
              Decoration.widget(startPosition, () => {
                const span = document.createElement('span')
                span.classList.add('ai-markdown-marker')
                span.textContent = marker
                span.style.color = markerColor
                span.style.fontWeight = 'bold'
                span.style.marginRight = '2px'
                span.setAttribute('data-ai-editing', 'start')
                return span
              }, { side: 0 })
            )
            
            // 在结束位置添加标记
            decorations.push(
              Decoration.widget(endPosition, () => {
                const span = document.createElement('span')
                span.classList.add('ai-markdown-marker')
                span.textContent = marker
                span.style.color = markerColor
                span.style.fontWeight = 'bold'
                span.style.marginLeft = '2px'
                span.setAttribute('data-ai-editing', 'end')
                return span
              }, { side: 1 })
            )
            
            // 如果区域有效，添加背景高亮
            if (startPosition < endPosition) {
              decorations.push(
                Decoration.inline(startPosition, endPosition, {
                  style: `background-color: ${backgroundColor};`,
                })
              )
            }
            
            return DecorationSet.create(state.doc, decorations)
          },
        },
      }),
    ]
  },

  // 添加全局CSS
  onCreate() {
    const style = document.createElement('style')
    style.id = 'ai-markdown-decorator-style'
    style.textContent = `
      .ai-markdown-marker {
        display: inline;
        vertical-align: middle;
        user-select: none;
        pointer-events: none;
        font-size: 1.1em;
      }
    `
    document.head.appendChild(style)
  },

  // 清理
  onDestroy() {
    const style = document.getElementById('ai-markdown-decorator-style')
    if (style) {
      style.remove()
    }
  },

  // 使用存储替代命令，让外部直接修改选项
  addStorage() {
    return {
      updateOptions: (options: Partial<AIMarkdownDecoratorOptions>) => {
        // 更新选项
        Object.assign(this.options, options)
        return this.options
      }
    }
  }
})

export default AIMarkdownDecorator 