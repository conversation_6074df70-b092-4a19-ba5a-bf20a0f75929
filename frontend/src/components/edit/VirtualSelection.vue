<template>
  <div v-if="visible" class="virtual-selection-container" @mousedown="handleContainerClick">
    <div 
      v-for="(rect, index) in selectionRects" 
      :key="index"
      class="virtual-selection-highlight"
      :style="{
        top: `${rect.top}px`,
        left: `${rect.left}px`,
        width: `${rect.width}px`,
        height: `${rect.height}px`
      }"
    ></div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch, onMounted, onUnmounted } from 'vue';

export interface SelectionRect {
  top: number;
  left: number;
  width: number;
  height: number;
}

export interface SelectionRange {
  startContainer: Node;
  startOffset: number;
  endContainer: Node;
  endOffset: number;
  range?: Range;
}

export default defineComponent({
  name: 'VirtualSelection',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectionRects: {
      type: Array as () => SelectionRect[],
      default: () => []
    },
    selectionRange: {
      type: Object as () => SelectionRange | null,
      default: null
    },
    preventClickSelector: {
      type: String,
      default: '.command-container'
    },
    editableSelector: {
      type: String,
      default: '.editor-container'
    }
  },
  emits: ['cancel'],
  setup(props, { emit }) {
    const isClickingOnCommand = ref(false);
    
    // 处理容器点击事件，允许点击穿透到可编辑区域
    const handleContainerClick = (e: MouseEvent) => {
      // 阻止冒泡但允许穿透
      e.stopPropagation();
      
      // 检查点击是否在允许点击的区域内
      if (e.target instanceof Element) {
        const commandContainer = document.querySelector(props.preventClickSelector);
        
        // 如果点击在命令容器上，不处理
        if (commandContainer && commandContainer.contains(e.target)) {
          isClickingOnCommand.value = true;
          return;
        }
        
        // 如果点击在可编辑区域上，传递点击事件但同时取消选区
        const editableArea = document.querySelector(props.editableSelector);
        if (editableArea) {
          // 取消虚拟选区
          emit('cancel');
          
          // 允许事件传递到可编辑区域
          e.preventDefault = () => {}; // 阻止阻止默认行为
          e.stopPropagation = () => {}; // 阻止阻止冒泡
        }
      }
    };

    // 处理全局点击事件，判断是否应该取消选区
    const handleGlobalClick = (e: MouseEvent) => {
      if (!props.visible) return;
      
      // 如果正在点击命令容器，重置状态并返回
      if (isClickingOnCommand.value) {
        isClickingOnCommand.value = false;
        return;
      }
      
      // 检查点击是否在允许点击的区域内
      if (e.target instanceof Element) {
        const preventSelectors = props.preventClickSelector.split(',').map(s => s.trim());
        const isInPreventArea = preventSelectors.some(selector => {
          if (e.target instanceof Element) {
            return !!e.target.closest(selector);
          }
          return false;
        });
        
        // 如果点击的不是预防区域，取消选区
        if (!isInPreventArea) {
          emit('cancel');
        }
      }
    };

    // 组件挂载时添加点击事件监听
    onMounted(() => {
      document.addEventListener('mousedown', handleGlobalClick);
    });

    // 组件卸载时移除事件监听
    onUnmounted(() => {
      document.removeEventListener('mousedown', handleGlobalClick);
    });

    return {
      handleContainerClick
    };
  }
});
</script>

<style scoped>
.virtual-selection-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: auto; /* 改为auto以捕获点击事件 */
  z-index: 999;
}

.virtual-selection-highlight {
  position: absolute;
  background-color: rgba(124, 77, 255, 0.3);
  pointer-events: none;
  border-radius: 2px;
  box-shadow: 0 0 2px rgba(124, 77, 255, 0.5);
}
</style> 