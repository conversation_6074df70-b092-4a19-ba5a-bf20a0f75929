<template>
  <div class="comment-container">
    <div v-if="showToolbar" ref="toolbar" class="selection-toolbar" :style="toolbarStyle">
      <div class="toolbar-actions">
        <span class="action-btn" @click="showCommandInput">编辑所选文本</span>
      </div>
    </div>

    <!-- 使用独立的虚拟选区组件 -->
    <VirtualSelection
      :visible="isEditing"
      :selection-rects="selectionRects"
      :selection-range="selectionRange"
      :prevent-click-selector="'.command-container, .selection-toolbar'"
      :editable-selector="editorSelector"
      @cancel="cancelEditing"
    />

    <CommandInput
      v-model="showCommand"
      :position="commandPosition"
      @command="handleCommand"
      @close="handleCommandClose"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onUnmounted, computed } from 'vue';
import CommandInput from './command.vue';
import VirtualSelection, { SelectionRange, SelectionRect } from './VirtualSelection.vue';

export default defineComponent({
  components: {
    CommandInput,
    VirtualSelection
  },
  name: 'TextSelectionToolbar',
  props: {
    editorSelector: {
      type: String,
      default: '.editor-container'
    }
  },
  emits: ['cut', 'edit'],
  setup(props, { emit }) {
    const showToolbar = ref(false);
    const selection = ref(null);
    const selectionRange = ref<SelectionRange | null>(null);
    const toolbarPosition = ref({ top: 0, left: 0 });
    const toolbar = ref(null);
    const showCommand = ref(false);
    const commandPosition = ref({ top: 0, left: 0 });
    const isEditing = ref(false);
    const selectionRects = ref<SelectionRect[]>([]);
    
    // 计算工具栏样式
    const toolbarStyle = computed(() => {
      return {
        top: `${toolbarPosition.value.top}px`,
        left: `${toolbarPosition.value.left}px`
      };
    });

    // 获取选中的文本
    const getSelectedText = () => {
      if (window.getSelection) {
        return window.getSelection().toString();
      }
      return '';
    };

    // 保存当前选择状态
    const saveSelection = () => {
      const sel = window.getSelection();
      if (!sel || sel.rangeCount === 0) return null;
      
      try {
        const range = sel.getRangeAt(0);
        selectionRange.value = {
          startContainer: range.startContainer,
          startOffset: range.startOffset,
          endContainer: range.endContainer,
          endOffset: range.endOffset,
          range: range.cloneRange()
        };
        
        // 获取选区的所有矩形
        const rects = range.getClientRects();
        const rectArray: SelectionRect[] = [];
        for (let i = 0; i < rects.length; i++) {
          const rect = rects[i];
          rectArray.push({
            top: rect.top,
            left: rect.left,
            width: rect.width,
            height: rect.height
          });
        }
        selectionRects.value = rectArray;
        
        return selectionRange.value;
      } catch (e) {
        console.error('保存选区失败:', e);
        return null;
      }
    };

    // 恢复选择状态
    const restoreSelection = () => {
      if (!selectionRange.value) return;
      
      const sel = window.getSelection();
      sel.removeAllRanges();
      
      try {
        // 尝试恢复原始范围
        const newRange = document.createRange();
        newRange.setStart(selectionRange.value.startContainer, selectionRange.value.startOffset);
        newRange.setEnd(selectionRange.value.endContainer, selectionRange.value.endOffset);
        sel.addRange(newRange);
      } catch (e) {
        console.error('无法恢复选择:', e);
      }
    };

    // 创建虚拟选择高亮
    const createVirtualSelection = () => {
      isEditing.value = true;
      // 保存选区位置信息已经在saveSelection中完成
    };

    // 取消编辑模式
    const cancelEditing = () => {
      cleanupEditingMode();
      showCommand.value = false;
    };
    
    // 更新工具栏位置
    const updateToolbarPosition = () => {
      const sel = window.getSelection();
      if (!sel || sel.rangeCount === 0) return;
      
      // 获取选区范围
      const range = sel.getRangeAt(0);
      const rect = range.getBoundingClientRect();
      
      // 检查是否在编辑器内
      const editor = document.querySelector(props.editorSelector);
      if (!editor) return;
      
      const editorRect = editor.getBoundingClientRect();
      if (
        rect.top < editorRect.top || 
        rect.bottom > editorRect.bottom || 
        rect.left < editorRect.left || 
        rect.right > editorRect.right
      ) {
        if (!isEditing.value) {
          showToolbar.value = false;
        }
        return;
      }
      
      // 设置工具栏位置（在选区上方居中）
      if (toolbar.value) {
        const toolbarWidth = toolbar.value.offsetWidth;
        toolbarPosition.value = {
          top: rect.top - 30, // 减小高度
          left: rect.left + (rect.width / 2) - (toolbarWidth / 2)
        };
      }
    };
    
    // 处理选择变化
    const handleSelectionChange = () => {
      // 如果正在编辑模式，则不响应选择变化
      if (isEditing.value) return;
      
      const selectedText = getSelectedText();
      
      if (selectedText && selectedText.trim().length > 0) {
        selection.value = selectedText;
        showToolbar.value = true;
        // 保存选择状态
        saveSelection();
        // An idle setTimeout to ensure DOM is updated
        setTimeout(() => {
          updateToolbarPosition();
        }, 0);
      } else {
        // 如果没有选中文本，就隐藏工具栏
        showToolbar.value = false;
      }
    };
    
    // 显示指令输入窗口
    const showCommandInput = () => {
      if (selection.value) {
        // 确保保存了当前选择
        saveSelection();
        
        const sel = window.getSelection();
        if (!sel || sel.rangeCount === 0) return;
        
        const range = sel.getRangeAt(0);
        const rect = range.getBoundingClientRect();
        
        // 计算命令框的位置，保证在可见区域内
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;
        
        let top = rect.top - 40;
        let left = rect.left;
        
        // 确保不超出顶部
        if (top < 10) top = 10;
        
        // 确保不超出右侧（假设命令框宽度为400px）
        if (left + 400 > viewportWidth) {
          left = viewportWidth - 410;
        }
        
        commandPosition.value = { top, left };
        
        // 先创建虚拟选择高亮，再显示命令框
        createVirtualSelection();
        showCommand.value = true;
        showToolbar.value = false;
      }
    };
    
    // 处理命令
    const handleCommand = (command: string) => {
      if (selection.value && selectionRange.value) {
        // 使用新的格式组装要发送的数据
        emit('edit', {
          text: selection.value,
          command: command,
          selection: {
            from: selectionRange.value.startOffset,
            to: selectionRange.value.endOffset
          },
          // 添加格式化后的消息
          formattedMessage: `用户选中的修改段落: ${selection.value}\n用户的修改意见: ${command}`
        });
        
        // 处理完命令后，保持选区不变，只关闭命令输入框
        showCommand.value = false;
      }
    };
    
    const handleCommandClose = () => {
      showCommand.value = false;
    };
    
    // 清理编辑模式
    const cleanupEditingMode = () => {
      isEditing.value = false;
    };
    
    // 挂载组件时添加事件监听
    onMounted(() => {
      document.addEventListener('selectionchange', handleSelectionChange);
      window.addEventListener('resize', updateToolbarPosition);
      window.addEventListener('scroll', updateToolbarPosition);
      
      // 添加mouseup事件监听，确保鼠标释放后检查选择状态
      document.addEventListener('mouseup', () => {
        // 使用setTimeout是为了确保选择已完成
        setTimeout(handleSelectionChange, 10);
      });
    });
    
    // 组件卸载时移除事件监听
    onUnmounted(() => {
      document.removeEventListener('selectionchange', handleSelectionChange);
      document.removeEventListener('mouseup', () => setTimeout(handleSelectionChange, 10));
      window.removeEventListener('resize', updateToolbarPosition);
      window.removeEventListener('scroll', updateToolbarPosition);
      
      // 确保清理所有可能的事件监听器
      cleanupEditingMode();
    });

    return {
      showToolbar,
      toolbarStyle,
      toolbar,
      showCommand,
      commandPosition,
      showCommandInput,
      handleCommand,
      handleCommandClose,
      isEditing,
      selectionRects,
      selectionRange,
      cancelEditing
    };
  }
});
</script>

<style scoped>
.comment-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
}

.selection-toolbar {
  position: absolute;
  background-color: #7c4dff; /* 紫色背景 */
  border-radius: 4px;
  padding: 5px 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  pointer-events: auto;
  z-index: 1001;
  transition: all 0.2s ease;
  font-size: 13px;
}

.toolbar-actions {
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn {
  color: white;
  padding: 3px 10px;
  cursor: pointer;
  user-select: none;
  font-weight: 500;
  transition: transform 0.1s ease;
  display: inline-flex;
  align-items: center;
}

.action-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  transform: translateY(-1px);
}

.action-btn:active {
  transform: translateY(0);
}
</style>