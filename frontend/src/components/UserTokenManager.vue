<template>
  <div class="user-token-manager">
    <div class="header">
      <h2>用户Token管理</h2>
      <div class="header-actions">
        <!-- 批量操作按钮 -->
        <div v-if="selectedUsers.length > 0" class="batch-actions">
          <span class="selected-count">已选择 {{ selectedUsers.length }} 个用户</span>
          <button @click="showBatchBudgetDialog" class="batch-btn budget-btn">
            批量修改预算
          </button>
          <button @click="showBatchUsageDialog" class="batch-btn usage-btn">
            批量修改使用量
          </button>
          <button @click="batchSyncSelectedUsers" class="batch-btn sync-btn" :disabled="syncing">
            {{ syncing ? '同步中...' : '批量同步' }}
          </button>
          <button @click="clearSelection" class="batch-btn clear-btn">
            取消选择
          </button>
        </div>
        <!-- 全局操作按钮 -->
        <div class="global-actions">
          <button @click="syncAllUsers" class="sync-all-btn" :disabled="syncing">
            {{ syncing ? '同步中...' : '全量同步' }}
          </button>
          <button @click="refreshData" class="refresh-btn" :disabled="loading">
            <span v-if="loading">刷新中...</span>
            <span v-else>刷新数据</span>
          </button>
        </div>
      </div>
    </div>

    <div v-if="loading && !users.length" class="loading">
      <p>正在加载用户数据...</p>
    </div>

    <div v-else-if="error" class="error">
      <p>{{ error }}</p>
      <button @click="refreshData" class="retry-btn">重试</button>
    </div>

    <div v-else-if="users.length === 0" class="empty">
      <p>暂无用户数据</p>
    </div>

    <div v-else class="content">
      <!-- 统计概览 -->
      <div class="stats-overview">
        <div class="stat-card">
          <h3>总用户数</h3>
          <p class="stat-value">{{ users.length }}</p>
        </div>
        <div class="stat-card">
          <h3>总预算</h3>
          <p class="stat-value">{{ totalBudget.toFixed(2) }}</p>
        </div>
        <div class="stat-card">
          <h3>总使用量</h3>
          <p class="stat-value">{{ totalUsage.toFixed(2) }}</p>
        </div>
        <div class="stat-card">
          <h3>总剩余</h3>
          <p class="stat-value">{{ totalRemaining.toFixed(2) }}</p>
        </div>
      </div>

      <!-- 用户列表 -->
      <div class="user-table">
        <table>
          <thead>
            <tr>
              <th class="checkbox-column">
                <input 
                  type="checkbox" 
                  :checked="isAllSelected" 
                  @change="toggleSelectAll"
                  class="select-checkbox"
                />
              </th>
              <th>用户名</th>
              <th>用户ID</th>
              <th>预算上限</th>
              <th>已使用</th>
              <th>剩余额度</th>
              <th>使用率</th>
              <th>状态</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="user in users" :key="user.user_id" :class="{ 'over-budget': user.usage_percentage > 100, 'selected': selectedUsers.includes(user.user_id) }">
              <td class="checkbox-column">
                <input 
                  type="checkbox" 
                  :checked="selectedUsers.includes(user.user_id)"
                  @change="toggleUserSelection(user.user_id)"
                  class="select-checkbox"
                />
              </td>
              <td class="username">{{ user.username }}</td>
              <td class="user-id">{{ user.user_id.substring(0, 8) }}...</td>
              <td class="budget">
                <span v-if="!user.editingBudget">{{ user.token_budget.toFixed(2) }}</span>
                <input 
                  v-else 
                  v-model.number="user.newBudget" 
                  type="number" 
                  step="0.01" 
                  min="0"
                  class="edit-input"
                  @keyup.enter="saveBudget(user)"
                  @keyup.esc="cancelEditBudget(user)"
                />
              </td>
              <td class="usage">
                <span v-if="!user.editingUsage">{{ user.current_token_usage.toFixed(2) }}</span>
                <input 
                  v-else 
                  v-model.number="user.newUsage" 
                  type="number" 
                  step="0.01" 
                  min="0"
                  class="edit-input"
                  @keyup.enter="saveUsage(user)"
                  @keyup.esc="cancelEditUsage(user)"
                />
              </td>
              <td class="remaining" :class="{ 'negative': user.remaining_budget < 0 }">
                {{ user.remaining_budget.toFixed(2) }}
              </td>
              <td class="percentage">
                <div class="progress-bar">
                  <div 
                    class="progress-fill" 
                    :style="{ width: Math.min(user.usage_percentage, 100) + '%' }"
                    :class="{ 'over-limit': user.usage_percentage > 100 }"
                  ></div>
                </div>
                <span class="percentage-text" :class="{ 'over-limit': user.usage_percentage > 100 }">
                  {{ user.usage_percentage.toFixed(1) }}%
                </span>
              </td>
              <td class="status">
                <span 
                  class="status-badge" 
                  :class="{ 
                    'normal': user.usage_percentage <= 80, 
                    'warning': user.usage_percentage > 80 && user.usage_percentage <= 100,
                    'danger': user.usage_percentage > 100 
                  }"
                >
                  {{ getStatusText(user.usage_percentage) }}
                </span>
              </td>
              <td class="actions">
                <div class="action-buttons">
                  <!-- 预算编辑按钮 -->
                  <button 
                    v-if="!user.editingBudget" 
                    @click="startEditBudget(user)" 
                    class="edit-btn"
                    title="编辑预算"
                  >
                    编辑预算
                  </button>
                  <div v-else class="edit-actions">
                    <button @click="saveBudget(user)" class="save-btn" :disabled="user.saving">保存</button>
                    <button @click="cancelEditBudget(user)" class="cancel-btn">取消</button>
                  </div>

                  <!-- 使用量编辑按钮 -->
                  <button 
                    v-if="!user.editingUsage" 
                    @click="startEditUsage(user)" 
                    class="edit-btn"
                    title="编辑使用量"
                  >
                    编辑使用量
                  </button>
                  <div v-else class="edit-actions">
                    <button @click="saveUsage(user)" class="save-btn" :disabled="user.saving">保存</button>
                    <button @click="cancelEditUsage(user)" class="cancel-btn">取消</button>
                  </div>

                  <!-- 重置按钮 -->
                  <button 
                    @click="resetUsage(user)" 
                    class="reset-btn"
                    :disabled="user.saving"
                    title="重置使用量为0"
                  >
                    重置使用量
                  </button>

                  <!-- 同步按钮 -->
                  <button 
                    @click="syncSingleUser(user)" 
                    class="sync-btn"
                    :disabled="user.saving"
                    title="从session记录同步使用量"
                  >
                    同步使用量
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 确认对话框 -->
    <div v-if="showConfirmDialog" class="modal-overlay" @click="closeConfirmDialog">
      <div class="modal" @click.stop>
        <h3>确认操作</h3>
        <p>{{ confirmMessage }}</p>
        <div class="modal-actions">
          <button @click="confirmAction" class="confirm-btn" :disabled="confirming">
            {{ confirming ? '处理中...' : '确认' }}
          </button>
          <button @click="closeConfirmDialog" class="cancel-btn">取消</button>
        </div>
      </div>
    </div>

    <!-- 批量修改预算对话框 -->
    <div v-if="showBatchBudgetModal" class="modal-overlay" @click="closeBatchBudgetDialog">
      <div class="modal" @click.stop>
        <h3>批量修改Token预算</h3>
        <p>将为选中的 {{ selectedUsers.length }} 个用户设置新的Token预算上限</p>
        <div class="input-group">
          <label for="batchBudget">新的预算上限:</label>
          <input 
            id="batchBudget"
            v-model.number="batchBudgetValue" 
            type="number" 
            step="0.01" 
            min="0"
            class="batch-input"
            placeholder="请输入新的预算值"
          />
        </div>
        <div class="modal-actions">
          <button @click="executeBatchBudgetUpdate" class="confirm-btn" :disabled="batchProcessing || !batchBudgetValue || batchBudgetValue < 0">
            {{ batchProcessing ? '处理中...' : '确认修改' }}
          </button>
          <button @click="closeBatchBudgetDialog" class="cancel-btn">取消</button>
        </div>
      </div>
    </div>

    <!-- 批量修改使用量对话框 -->
    <div v-if="showBatchUsageModal" class="modal-overlay" @click="closeBatchUsageDialog">
      <div class="modal" @click.stop>
        <h3>批量修改Token使用量</h3>
        <p>将为选中的 {{ selectedUsers.length }} 个用户设置新的Token使用量</p>
        <div class="input-group">
          <label for="batchUsage">新的使用量:</label>
          <input 
            id="batchUsage"
            v-model.number="batchUsageValue" 
            type="number" 
            step="0.01" 
            min="0"
            class="batch-input"
            placeholder="请输入新的使用量"
          />
        </div>
        <div class="modal-actions">
          <button @click="executeBatchUsageUpdate" class="confirm-btn" :disabled="batchProcessing || batchUsageValue === undefined || batchUsageValue < 0">
            {{ batchProcessing ? '处理中...' : '确认修改' }}
          </button>
          <button @click="closeBatchUsageDialog" class="cancel-btn">取消</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { authService, type UserTokenInfo } from '@/api/authService';

// 扩展用户信息接口，添加编辑状态
interface ExtendedUserTokenInfo extends UserTokenInfo {
  editingBudget?: boolean;
  editingUsage?: boolean;
  newBudget?: number;
  newUsage?: number;
  saving?: boolean;
}

// 响应式数据
const users = ref<ExtendedUserTokenInfo[]>([]);
const loading = ref(false);
const error = ref('');

// 多选相关
const selectedUsers = ref<string[]>([]);

// 同步相关
const syncing = ref(false);

// 确认对话框
const showConfirmDialog = ref(false);
const confirmMessage = ref('');
const confirmAction = ref<() => void>(() => {});
const confirming = ref(false);

// 批量操作对话框
const showBatchBudgetModal = ref(false);
const showBatchUsageModal = ref(false);
const batchBudgetValue = ref<number>();
const batchUsageValue = ref<number>();
const batchProcessing = ref(false);

// 计算属性
const totalBudget = computed(() => {
  return users.value.reduce((sum, user) => sum + user.token_budget, 0);
});

const totalUsage = computed(() => {
  return users.value.reduce((sum, user) => sum + user.current_token_usage, 0);
});

const totalRemaining = computed(() => {
  return users.value.reduce((sum, user) => sum + user.remaining_budget, 0);
});

const isAllSelected = computed(() => {
  return users.value.length > 0 && selectedUsers.value.length === users.value.length;
});

// 获取状态文本
const getStatusText = (percentage: number): string => {
  if (percentage <= 80) return '正常';
  if (percentage <= 100) return '警告';
  return '超限';
};

// 多选功能
const toggleSelectAll = () => {
  if (isAllSelected.value) {
    selectedUsers.value = [];
  } else {
    selectedUsers.value = users.value.map(user => user.user_id);
  }
};

const toggleUserSelection = (userId: string) => {
  const index = selectedUsers.value.indexOf(userId);
  if (index > -1) {
    selectedUsers.value.splice(index, 1);
  } else {
    selectedUsers.value.push(userId);
  }
};

const clearSelection = () => {
  selectedUsers.value = [];
};

// 批量操作对话框
const showBatchBudgetDialog = () => {
  batchBudgetValue.value = undefined;
  showBatchBudgetModal.value = true;
};

const closeBatchBudgetDialog = () => {
  showBatchBudgetModal.value = false;
  batchBudgetValue.value = undefined;
};

const showBatchUsageDialog = () => {
  batchUsageValue.value = undefined;
  showBatchUsageModal.value = true;
};

const closeBatchUsageDialog = () => {
  showBatchUsageModal.value = false;
  batchUsageValue.value = undefined;
};

// 执行批量更新
const executeBatchBudgetUpdate = async () => {
  if (!batchBudgetValue.value || batchBudgetValue.value < 0) {
    alert('请输入有效的预算值');
    return;
  }

  try {
    batchProcessing.value = true;
    const response = await authService.batchUpdateUserTokenBudget(selectedUsers.value, batchBudgetValue.value);
    
    // 更新本地数据
    if (response.data.updated_users) {
      response.data.updated_users.forEach((updatedUser: UserTokenInfo) => {
        const userIndex = users.value.findIndex(u => u.user_id === updatedUser.user_id);
        if (userIndex > -1) {
          users.value[userIndex] = { ...users.value[userIndex], ...updatedUser };
        }
      });
    }
    
    // 显示结果
    let message = response.message;
    if (response.data.failed_users && response.data.failed_users.length > 0) {
      message += `\n失败的用户: ${response.data.failed_users.map((f: any) => f.user_id).join(', ')}`;
    }
    alert(message);
    
    closeBatchBudgetDialog();
    clearSelection();
  } catch (err: any) {
    console.error('批量更新预算失败:', err);
    alert('批量更新预算失败: ' + (err.message || '未知错误'));
  } finally {
    batchProcessing.value = false;
  }
};

const executeBatchUsageUpdate = async () => {
  if (batchUsageValue.value === undefined || batchUsageValue.value < 0) {
    alert('请输入有效的使用量值');
    return;
  }

  try {
    batchProcessing.value = true;
    const response = await authService.batchUpdateUserTokenUsage(selectedUsers.value, batchUsageValue.value);
    
    // 更新本地数据
    if (response.data.updated_users) {
      response.data.updated_users.forEach((updatedUser: UserTokenInfo) => {
        const userIndex = users.value.findIndex(u => u.user_id === updatedUser.user_id);
        if (userIndex > -1) {
          users.value[userIndex] = { ...users.value[userIndex], ...updatedUser };
        }
      });
    }
    
    // 显示结果
    let message = response.message;
    if (response.data.failed_users && response.data.failed_users.length > 0) {
      message += `\n失败的用户: ${response.data.failed_users.map((f: any) => f.user_id).join(', ')}`;
    }
    alert(message);
    
    closeBatchUsageDialog();
    clearSelection();
  } catch (err: any) {
    console.error('批量更新使用量失败:', err);
    alert('批量更新使用量失败: ' + (err.message || '未知错误'));
  } finally {
    batchProcessing.value = false;
  }
};

// 加载用户数据
const loadUsers = async () => {
  try {
    loading.value = true;
    error.value = '';
    
    const response = await authService.getAllUsersTokenOverview();
    users.value = response.data.map((user: UserTokenInfo) => ({
      ...user,
      editingBudget: false,
      editingUsage: false,
      saving: false
    }));
  } catch (err: any) {
    error.value = err.message || '加载用户数据失败';
    console.error('加载用户数据失败:', err);
  } finally {
    loading.value = false;
  }
};

// 刷新数据
const refreshData = () => {
  loadUsers();
};

// 开始编辑预算
const startEditBudget = (user: ExtendedUserTokenInfo) => {
  user.editingBudget = true;
  user.newBudget = user.token_budget;
};

// 取消编辑预算
const cancelEditBudget = (user: ExtendedUserTokenInfo) => {
  user.editingBudget = false;
  user.newBudget = undefined;
};

// 保存预算
const saveBudget = async (user: ExtendedUserTokenInfo) => {
  if (user.newBudget === undefined || user.newBudget < 0) {
    alert('请输入有效的预算值');
    return;
  }

  try {
    user.saving = true;
    const response = await authService.updateUserTokenBudget(user.user_id, user.newBudget);
    
    // 更新本地数据
    user.token_budget = response.data.token_budget;
    user.remaining_budget = response.data.remaining_budget;
    user.usage_percentage = response.data.usage_percentage;
    
    user.editingBudget = false;
    user.newBudget = undefined;
    
    console.log('预算更新成功:', response.message);
  } catch (err: any) {
    console.error('更新预算失败:', err);
    alert('更新预算失败: ' + (err.message || '未知错误'));
  } finally {
    user.saving = false;
  }
};

// 开始编辑使用量
const startEditUsage = (user: ExtendedUserTokenInfo) => {
  user.editingUsage = true;
  user.newUsage = user.current_token_usage;
};

// 取消编辑使用量
const cancelEditUsage = (user: ExtendedUserTokenInfo) => {
  user.editingUsage = false;
  user.newUsage = undefined;
};

// 保存使用量
const saveUsage = async (user: ExtendedUserTokenInfo) => {
  if (user.newUsage === undefined || user.newUsage < 0) {
    alert('请输入有效的使用量值');
    return;
  }

  try {
    user.saving = true;
    const response = await authService.updateUserTokenUsage(user.user_id, user.newUsage);
    
    // 更新本地数据
    user.current_token_usage = response.data.current_token_usage;
    user.remaining_budget = response.data.remaining_budget;
    user.usage_percentage = response.data.usage_percentage;
    
    user.editingUsage = false;
    user.newUsage = undefined;
    
    console.log('使用量更新成功:', response.message);
  } catch (err: any) {
    console.error('更新使用量失败:', err);
    alert('更新使用量失败: ' + (err.message || '未知错误'));
  } finally {
    user.saving = false;
  }
};

// 重置使用量
const resetUsage = (user: ExtendedUserTokenInfo) => {
  confirmMessage.value = `确定要重置用户 "${user.username}" 的token使用量为0吗？`;
  confirmAction.value = async () => {
    try {
      confirming.value = true;
      const response = await authService.resetUserTokenUsage(user.user_id);
      
      // 更新本地数据
      user.current_token_usage = response.data.current_token_usage;
      user.remaining_budget = response.data.remaining_budget;
      user.usage_percentage = response.data.usage_percentage;
      
      console.log('使用量重置成功:', response.message);
      closeConfirmDialog();
    } catch (err: any) {
      console.error('重置使用量失败:', err);
      alert('重置使用量失败: ' + (err.message || '未知错误'));
    } finally {
      confirming.value = false;
    }
  };
  showConfirmDialog.value = true;
};

// 关闭确认对话框
const closeConfirmDialog = () => {
  showConfirmDialog.value = false;
  confirmMessage.value = '';
  confirmAction.value = () => {};
  confirming.value = false;
};

// 批量同步选中用户
const batchSyncSelectedUsers = async () => {
  if (selectedUsers.value.length === 0) {
    alert('请先选择要同步的用户');
    return;
  }

  try {
    syncing.value = true;
    let successCount = 0;
    let failedUsers = [];

    for (const userId of selectedUsers.value) {
      try {
        const response = await authService.syncUserTokenUsageFromSessions(userId);
        if (response.status === 'success') {
          successCount++;
          // 更新本地用户数据
          const userIndex = users.value.findIndex(u => u.user_id === userId);
          if (userIndex > -1) {
            const updatedInfo = await authService.getUserTokenInfo(userId);
            users.value[userIndex] = { ...users.value[userIndex], ...updatedInfo };
          }
        }
      } catch (err: any) {
        console.error(`同步用户 ${userId} 失败:`, err);
        failedUsers.push(userId);
      }
    }

    let message = `批量同步完成: 成功同步 ${successCount}/${selectedUsers.value.length} 个用户`;
    if (failedUsers.length > 0) {
      message += `\n失败的用户: ${failedUsers.join(', ')}`;
    }
    alert(message);

    clearSelection();
  } catch (err: any) {
    console.error('批量同步失败:', err);
    alert('批量同步失败: ' + (err.message || '未知错误'));
  } finally {
    syncing.value = false;
  }
};

// 全量同步所有用户
const syncAllUsers = async () => {
  const confirmed = confirm('确定要同步所有用户的token使用量吗？这可能需要一些时间。');
  if (!confirmed) return;

  try {
    syncing.value = true;
    const response = await authService.syncAllUsersTokenUsageFromSessions();
    
    if (response.status === 'success') {
      alert(response.message);
      // 刷新数据以显示最新的同步结果
      await loadUsers();
    } else {
      alert('全量同步失败');
    }
  } catch (err: any) {
    console.error('全量同步失败:', err);
    alert('全量同步失败: ' + (err.message || '未知错误'));
  } finally {
    syncing.value = false;
  }
};

// 同步单个用户
const syncSingleUser = async (user: ExtendedUserTokenInfo) => {
  const confirmed = confirm('确定要从session记录同步使用量吗？');
  if (!confirmed) return;

  try {
    const response = await authService.syncUserTokenUsageFromSessions(user.user_id);
    if (response.status === 'success') {
      alert(response.message);
      // 刷新数据以显示最新的同步结果
      await loadUsers();
    } else {
      alert('同步失败');
    }
  } catch (err: any) {
    console.error('同步失败:', err);
    alert('同步失败: ' + (err.message || '未知错误'));
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadUsers();
});
</script>

<style scoped>
.user-token-manager {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e5e5e5;
}

.header h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.global-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.sync-all-btn {
  padding: 8px 16px;
  background-color: #17a2b8;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.sync-all-btn:hover:not(:disabled) {
  background-color: #138496;
}

.sync-all-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.refresh-btn {
  padding: 8px 16px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.refresh-btn:hover:not(:disabled) {
  background-color: #0056b3;
}

.refresh-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.batch-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.batch-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.budget-btn {
  background-color: #28a745;
  color: white;
}

.budget-btn:hover:not(:disabled) {
  background-color: #1e7e34;
}

.usage-btn {
  background-color: #ffc107;
  color: #212529;
}

.usage-btn:hover:not(:disabled) {
  background-color: #e0a800;
}

.sync-btn {
  background-color: #6c757d;
  color: white;
}

.sync-btn:hover:not(:disabled) {
  background-color: #5a6268;
}

.sync-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.clear-btn {
  background-color: #6c757d;
  color: white;
}

.clear-btn:hover:not(:disabled) {
  background-color: #545b62;
}

.batch-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.selected-count {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-right: 10px;
}

.loading, .error, .empty {
  text-align: center;
  padding: 40px;
  color: #6c757d;
}

.error {
  color: #dc3545;
}

.retry-btn {
  margin-top: 10px;
  padding: 8px 16px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  border: 1px solid #e9ecef;
}

.stat-card h3 {
  margin: 0 0 10px 0;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
}

.stat-value {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.user-table {
  overflow-x: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

th, td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e5e5e5;
}

th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
  position: sticky;
  top: 0;
}

tr:hover {
  background-color: #f8f9fa;
}

tr.over-budget {
  background-color: #fff5f5;
}

tr.selected {
  background-color: #e3f2fd;
}

.checkbox-column {
  width: 40px;
  text-align: center;
}

.select-checkbox {
  transform: scale(1.2);
  cursor: pointer;
}

.username {
  font-weight: 500;
  color: #333;
}

.user-id {
  font-family: monospace;
  color: #6c757d;
  font-size: 12px;
}

.budget, .usage {
  font-weight: 500;
}

.remaining.negative {
  color: #dc3545;
  font-weight: bold;
}

.progress-bar {
  width: 100px;
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 4px;
}

.progress-fill {
  height: 100%;
  background-color: #28a745;
  transition: width 0.3s ease;
}

.progress-fill.over-limit {
  background-color: #dc3545;
}

.percentage-text {
  font-size: 12px;
  font-weight: 500;
}

.percentage-text.over-limit {
  color: #dc3545;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.normal {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.warning {
  background-color: #fff3cd;
  color: #856404;
}

.status-badge.danger {
  background-color: #f8d7da;
  color: #721c24;
}

.actions {
  min-width: 200px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.edit-btn, .reset-btn, .save-btn, .cancel-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  min-width: 80px;
}

.edit-btn {
  background-color: #007bff;
  color: white;
}

.edit-btn:hover {
  background-color: #0056b3;
}

.reset-btn {
  background-color: #ffc107;
  color: #212529;
}

.reset-btn:hover:not(:disabled) {
  background-color: #e0a800;
}

.save-btn {
  background-color: #28a745;
  color: white;
}

.save-btn:hover:not(:disabled) {
  background-color: #1e7e34;
}

.cancel-btn {
  background-color: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background-color: #545b62;
}

.edit-actions {
  display: flex;
  gap: 4px;
}

.edit-input {
  width: 80px;
  padding: 4px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 12px;
}

.edit-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background-color: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 400px;
  width: 90%;
}

.modal h3 {
  margin: 0 0 16px 0;
  color: #333;
}

.modal p {
  margin: 0 0 20px 0;
  color: #666;
  line-height: 1.5;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.confirm-btn {
  padding: 8px 16px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.confirm-btn:hover:not(:disabled) {
  background-color: #c82333;
}

.confirm-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.input-group {
  margin-bottom: 20px;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.batch-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.batch-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}
</style> 