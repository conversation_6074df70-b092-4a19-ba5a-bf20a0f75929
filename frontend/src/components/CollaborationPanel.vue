<template>
  <div class="collaboration-panel">
    <!-- 协作者头像栏 -->
    <div class="collaborators-bar" v-if="collaborators.length > 0">
      <div class="collaborators-list">
        <v-tooltip 
          v-for="collaborator in visibleCollaborators" 
          :key="collaborator.user_id"
          :text="`${collaborator.username} - ${getStatusText(collaborator)}`"
          location="bottom"
        >
          <template v-slot:activator="{ props }">
            <v-avatar
              v-bind="props"
              :size="32"
              :color="collaborator.color"
              class="collaborator-avatar"
              :class="{ 'active': collaborator.is_active }"
            >
              <span class="text-white font-weight-bold">
                {{ getInitials(collaborator.username) }}
              </span>
              <div 
                v-if="collaborator.is_active" 
                class="activity-indicator"
                :style="{ backgroundColor: collaborator.color }"
              ></div>
            </v-avatar>
          </template>
        </v-tooltip>
        
        <!-- 更多协作者指示器 -->
        <v-avatar
          v-if="collaborators.length > maxVisibleCollaborators"
          :size="32"
          color="grey-lighten-1"
          class="more-indicator"
        >
          <span class="text-grey-darken-2">
            +{{ collaborators.length - maxVisibleCollaborators }}
          </span>
        </v-avatar>
      </div>
      
      <!-- 协作状态指示器 -->
      <div class="collaboration-status">
        <v-chip
          :color="connectionStatus === 'connected' ? 'success' : 'warning'"
          size="small"
          variant="flat"
        >
          <v-icon start>
            {{ connectionStatus === 'connected' ? 'mdi-account-multiple' : 'mdi-account-multiple-outline' }}
          </v-icon>
          {{ getConnectionStatusText() }}
        </v-chip>
      </div>
    </div>

    <!-- 协作详情面板（可折叠） -->
    <v-expansion-panels v-if="showDetails" class="collaboration-details">
      <v-expansion-panel>
        <v-expansion-panel-title>
          <div class="d-flex align-center">
            <v-icon class="mr-2">mdi-account-group</v-icon>
            协作详情 ({{ collaborators.length }} 人在线)
          </div>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <div class="collaborators-detail-list">
            <div 
              v-for="collaborator in collaborators" 
              :key="collaborator.user_id"
              class="collaborator-item"
            >
              <div class="d-flex align-center">
                <v-avatar
                  :size="24"
                  :color="collaborator.color"
                  class="mr-3"
                >
                  <span class="text-white text-caption">
                    {{ getInitials(collaborator.username) }}
                  </span>
                </v-avatar>
                
                <div class="collaborator-info flex-grow-1">
                  <div class="collaborator-name">{{ collaborator.username }}</div>
                  <div class="collaborator-status text-caption text-grey-darken-1">
                    {{ getDetailedStatus(collaborator) }}
                  </div>
                </div>
                
                <div class="collaborator-actions">
                  <v-chip
                    :color="collaborator.is_active ? 'success' : 'grey'"
                    size="x-small"
                    variant="flat"
                  >
                    {{ collaborator.is_active ? '在线' : '离线' }}
                  </v-chip>
                </div>
              </div>
            </div>
          </div>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>

    <!-- 协作邀请按钮 -->
    <div class="collaboration-actions" v-if="canManage">
      <v-btn
        variant="outlined"
        size="small"
        @click="$emit('invite-collaborator')"
        class="invite-btn"
      >
        <v-icon start>mdi-account-plus</v-icon>
        邀请协作
      </v-btn>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'

export default {
  name: 'CollaborationPanel',
  props: {
    documentId: {
      type: String,
      required: true
    },
    collaborators: {
      type: Array,
      default: () => []
    },
    connectionStatus: {
      type: String,
      default: 'disconnected'
    },
    canManage: {
      type: Boolean,
      default: false
    },
    showDetails: {
      type: Boolean,
      default: false
    },
    maxVisibleCollaborators: {
      type: Number,
      default: 5
    }
  },
  emits: ['invite-collaborator'],
  setup(props) {
    const visibleCollaborators = computed(() => {
      return props.collaborators.slice(0, props.maxVisibleCollaborators)
    })

    const getInitials = (name) => {
      if (!name) return '?'
      const words = name.split(' ')
      if (words.length >= 2) {
        return (words[0][0] + words[1][0]).toUpperCase()
      }
      return name.substring(0, 2).toUpperCase()
    }

    const getStatusText = (collaborator) => {
      if (!collaborator.is_active) return '离线'
      if (collaborator.cursor_position !== null) return '正在编辑...'
      return '在线'
    }

    const getDetailedStatus = (collaborator) => {
      if (!collaborator.is_active) {
        return `最后活跃：${formatTime(collaborator.last_active)}`
      }
      if (collaborator.cursor_position !== null) {
        return `光标位置：第 ${Math.floor(collaborator.cursor_position / 100) + 1} 行附近`
      }
      return '当前在线，查看文档中'
    }

    const getConnectionStatusText = () => {
      switch (props.connectionStatus) {
        case 'connected':
          return '已连接'
        case 'connecting':
          return '连接中...'
        case 'disconnected':
          return '未连接'
        case 'syncing':
          return '同步中...'
        default:
          return '未知状态'
      }
    }

    const formatTime = (timestamp) => {
      if (!timestamp) return '未知'
      const date = new Date(timestamp)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return `${Math.floor(diff / 60000)} 分钟前`
      if (diff < 86400000) return `${Math.floor(diff / 3600000)} 小时前`
      return date.toLocaleDateString()
    }

    return {
      visibleCollaborators,
      getInitials,
      getStatusText,
      getDetailedStatus,
      getConnectionStatusText,
      formatTime
    }
  }
}
</script>

<style scoped>
.collaboration-panel {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.collaborators-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.collaborators-list {
  display: flex;
  align-items: center;
  gap: 4px;
}

.collaborator-avatar {
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.collaborator-avatar.active {
  border-color: currentColor;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.2);
}

.collaborator-avatar:hover {
  transform: scale(1.1);
}

.activity-indicator {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 2px solid white;
}

.more-indicator {
  font-size: 10px;
  cursor: pointer;
}

.collaboration-status {
  margin-left: 12px;
}

.collaboration-details {
  margin-top: 8px;
}

.collaborators-detail-list {
  padding: 8px 0;
}

.collaborator-item {
  padding: 8px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.collaborator-item:last-child {
  border-bottom: none;
}

.collaborator-name {
  font-weight: 500;
  font-size: 14px;
}

.collaborator-status {
  margin-top: 2px;
}

.collaboration-actions {
  padding: 8px 12px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.invite-btn {
  width: 100%;
}
</style> 