<template>
  <!-- 反馈弹窗容器 -->
  <div class="feedback-modal-overlay">
    <div class="report-feedback-container">
      <!-- 标题和关闭按钮 -->
      <div class="feedback-header">
        <h2>报告反馈</h2>
        <button class="close-button" @click="close">×</button>
      </div>
      
      <!-- 反馈内容区域 -->
      <div class="feedback-content">
        <!-- 评分部分 -->
        <div class="rating-section">
          <h3>您对这份报告的满意度如何？</h3>
          <div class="rating-options">
            <div 
              v-for="option in ratingOptions" 
              :key="option.value"
              :class="['rating-option', { 'selected': feedback.rating === option.value }]"
              @click="selectRating(option.value)"
            >
              <div class="rating-icon">{{ option.icon }}</div>
              <div class="rating-label">{{ option.label }}</div>
            </div>
          </div>
        </div>
        
        <!-- 评论部分 -->
        <div class="comment-section">
          <h3>您有什么具体的反馈或建议吗？（可选）</h3>
          <textarea 
            v-model="feedback.comment" 
            placeholder="请输入您的反馈或建议..."
            rows="4"
          ></textarea>
        </div>
      </div>
      
      <!-- 底部按钮区域 -->
      <div class="feedback-footer">
        <button class="submit-button" @click="submitFeedback" :disabled="!feedback.rating">
          提交反馈
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, reactive } from 'vue';
import { researchApi } from '@/api/chat';
onMounted(() => {
  console.log(props.sessionId)
})
// 组件属性定义
const props = defineProps({
  sessionId: {
    type: String,
    required: true
  }
});

// 定义组件事件
const emit = defineEmits(['close', 'submitted']);

// 评分选项
const ratingOptions = [
  { value: 'very_satisfied', label: '非常满意', icon: '😃' },
  { value: 'satisfied', label: '满意', icon: '🙂' },
  { value: 'neutral', label: '一般', icon: '😐' },
  { value: 'unsatisfied', label: '不满意', icon: '🙁' },
  { value: 'very_unsatisfied', label: '非常不满意', icon: '😞' }
];

// 反馈数据
const feedback = reactive({
  rating: '',
  comment: '',
  timestamp: new Date().toISOString()
});

// 选择评分
const selectRating = (rating) => {
  feedback.rating = rating;
};

// 提交反馈
const submitFeedback = async () => {
  if (!feedback.rating) return;
  
  try {
    // 更新时间戳为当前时间
    feedback.timestamp = new Date().toISOString();
    feedback.sessionId = props.sessionId;
    // 提交反馈到后端
    await researchApi.submitReportFeedback(feedback);
    
    // 提交成功后通知父组件
    emit('submitted', feedback);
    
    // 关闭反馈窗口
    close();
  } catch (error) {
    console.error('提交反馈失败:', error);
    // 这里可以添加错误提示
  }
};

// 关闭反馈窗口
const close = () => {
  emit('close');
};
</script>

<style scoped>
.feedback-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.report-feedback-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 600px;
  padding: 24px;
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.feedback-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.rating-section, .comment-section {
  margin-bottom: 24px;
}

.rating-section h3, .comment-section h3 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
}

.rating-options {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.rating-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  flex: 1;
}

.rating-option:hover {
  background-color: #f5f5f5;
}

.rating-option.selected {
  background-color: #e6f7ff;
  border: 1px solid #1890ff;
}

.rating-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.rating-label {
  font-size: 14px;
}

textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
}

.feedback-footer {
  display: flex;
  justify-content: flex-end;
}

.submit-button {
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.submit-button:hover {
  background-color: #40a9ff;
}

.submit-button:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}
</style>