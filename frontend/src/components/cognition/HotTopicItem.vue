<template>
  <div
    class="hot-topic-item"
    :class="{
      'top-rank': rank <= 3,
      'new-topic': topic.isNew,
      'clicked': isClicked
    }"
    @click="handleClick"
  >
    <div class="rank-indicator">
      <!-- 前三名特殊标识 -->
      <div class="rank-badge" v-if="rank <= 3">
        <v-icon
          :size="rank === 1 ? 16 : 14"
          :color="getRankBadgeColor()"
          class="rank-crown"
        >
          {{ getRankIcon() }}
        </v-icon>
        <span
          class="rank-number special-rank"
          :class="{
            'rank-1': rank === 1,
            'rank-2': rank === 2,
            'rank-3': rank === 3
          }"
        >
          {{ rank }}
        </span>
      </div>
      <!-- 普通排名 -->
      <span
        v-else
        class="rank-number"
      >
        {{ rank }}
      </span>

      <!-- 排名变化指示器 -->
      <div class="rank-change-indicator" v-if="topic.rankChange">
        <v-icon
          :size="10"
          :color="getRankChangeColor()"
          class="rank-change-icon"
        >
          {{ getRankChangeIcon() }}
        </v-icon>
      </div>
    </div>
    
    <div class="topic-content">
      <div class="topic-name">
        {{ formatTopicName(topic.topic) }}
      </div>
      
      <div class="topic-stats">
        <!-- 认知数量 -->
        <div class="stat-item">
          <v-icon size="12" color="primary">mdi-fire</v-icon>
          <span class="stat-value">{{ topic.count }}条</span>
          <span class="heat-level">{{ getHeatLevel() }}</span>
        </div>

        <!-- 热度变化显示 -->
        <div class="stat-item" v-if="topic.heatChangePercent !== undefined">
          <v-icon
            size="10"
            :color="getHeatChangeColor()"
            class="heat-change-icon"
          >
            {{ getHeatChangeIcon() }}
          </v-icon>
          <span
            class="heat-change-value"
            :class="getHeatChangeClass()"
          >
            {{ formatHeatChange() }}
          </span>
        </div>

        <!-- 占比显示 -->
        <div class="stat-item">
          <span class="stat-percentage-label">占比</span>
          <span class="stat-percentage">{{ topic.percentage.toFixed(1) }}%</span>
        </div>
      </div>

      <!-- 新兴话题标识 -->
      <div class="new-topic-badge" v-if="topic.isNew">
        <v-chip
          size="x-small"
          color="warning"
          variant="elevated"
          class="new-chip"
        >
          <v-icon size="10" class="new-star">mdi-star</v-icon>
          NEW
        </v-chip>
      </div>
    </div>
    
    <!-- 悬停显示的详细信息 -->
    <v-tooltip
      activator="parent"
      location="bottom"
    >
      <div class="topic-tooltip">
        <div class="tooltip-title">{{ formatTopicName(topic.topic) }}</div>
        <div class="tooltip-stats">
          <div class="tooltip-stat-item">
            <span class="tooltip-label">📊 认知数量:</span>
            <span>{{ topic.count }}条</span>
          </div>
          <div class="tooltip-stat-item">
            <span class="tooltip-label">📈 占总体比例:</span>
            <span>{{ topic.percentage.toFixed(1) }}%</span>
          </div>
          <div v-if="topic.rankChange" class="tooltip-stat-item">
            <span class="tooltip-label">🏆 排名变化:</span>
            <span>{{ getRankChangeText() }}</span>
          </div>
          <div v-if="topic.heatChangePercent !== undefined" class="tooltip-stat-item">
            <span class="tooltip-label">🔥 热度变化:</span>
            <span>{{ formatHeatChangeForTooltip() }}</span>
          </div>
          <div v-if="topic.isNew" class="new-indicator">🌟 新兴话题</div>
        </div>
      </div>
    </v-tooltip>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 响应式状态
const isClicked = ref(false)

const props = defineProps({
  topic: {
    type: Object,
    required: true
  },
  rank: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['click'])

// 格式化topic名称
const formatTopicName = (topic) => {
  if (!topic) return '未知话题'
  
  // 将连字符替换为空格，首字母大写，不截断长度
  return topic.replace(/-/g, ' ')
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}



// 获取排名变化图标
const getRankChangeIcon = () => {
  const rankChange = props.topic.rankChange
  switch (rankChange) {
    case 'up':
      return 'mdi-arrow-up'
    case 'down':
      return 'mdi-arrow-down'
    case 'new':
      return 'mdi-star'
    case 'stable':
      return 'mdi-equal'
    default:
      return 'mdi-equal'
  }
}

// 获取排名变化颜色
const getRankChangeColor = () => {
  const rankChange = props.topic.rankChange
  switch (rankChange) {
    case 'up':
      return 'success'
    case 'down':
      return 'error'
    case 'new':
      return 'warning'
    case 'stable':
      return 'primary'
    default:
      return 'primary'
  }
}

// 获取排名变化文本
const getRankChangeText = () => {
  const rankChange = props.topic.rankChange
  const currentRank = props.topic.currentRank || props.rank
  const previousRank = props.topic.previousRank

  switch (rankChange) {
    case 'up':
      return `从第${previousRank}名上升到第${currentRank}名`
    case 'down':
      return `从第${previousRank}名下降到第${currentRank}名`
    case 'new':
      return '新上榜'
    default:
      return `保持第${currentRank}名`
  }
}

// 获取热度变化图标
const getHeatChangeIcon = () => {
  const change = props.topic.heatChangePercent || 0
  if (change > 0) {
    return 'mdi-trending-up'
  } else if (change < 0) {
    return 'mdi-trending-down'
  }
  return 'mdi-trending-neutral'
}

// 获取热度变化颜色
const getHeatChangeColor = () => {
  const change = props.topic.heatChangePercent || 0
  if (change > 0) {
    return 'success'
  } else if (change < 0) {
    return 'error'
  }
  return 'grey'
}

// 获取热度变化样式类
const getHeatChangeClass = () => {
  const change = props.topic.heatChangePercent || 0
  if (change > 0) {
    return 'heat-increase'
  } else if (change < 0) {
    return 'heat-decrease'
  }
  return 'heat-stable'
}

// 格式化热度变化
const formatHeatChange = () => {
  const change = props.topic.heatChangePercent || 0
  const sign = change > 0 ? '+' : ''
  return `${sign}${change.toFixed(1)}%`
}

// 格式化热度变化（用于悬浮提示）
const formatHeatChangeForTooltip = () => {
  const change = props.topic.heatChangePercent || 0
  const sign = change > 0 ? '+' : ''
  return `认知数量${sign}${change.toFixed(1)}%`
}

// 获取热度等级标识
const getHeatLevel = () => {
  const count = props.topic.count
  if (count >= 100) {
    return '🔥🔥🔥' // 超热门
  } else if (count >= 50) {
    return '🔥🔥' // 很热门
  } else if (count >= 20) {
    return '🔥' // 热门
  }
  return '' // 普通
}

// 获取排名图标
const getRankIcon = () => {
  switch (props.rank) {
    case 1:
      return 'mdi-crown'
    case 2:
      return 'mdi-medal'
    case 3:
      return 'mdi-star-circle'
    default:
      return 'mdi-numeric'
  }
}

// 获取排名徽章颜色
const getRankBadgeColor = () => {
  switch (props.rank) {
    case 1:
      return '#B8860B' // 深金色，更容易看清
    case 2:
      return '#708090' // 深银色
    case 3:
      return '#FF6B35' // 橙色，区别于铜色
    default:
      return 'primary'
  }
}

// 处理点击事件
const handleClick = () => {
  // 触发点击动画
  isClicked.value = true

  // 发出点击事件
  emit('click')

  // 重置动画状态
  setTimeout(() => {
    isClicked.value = false
  }, 200)
}
</script>

<style scoped>
.hot-topic-item {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  padding: 8px 10px;
  border-radius: 8px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 0;
  width: 100%;
  min-height: 56px;
}

.hot-topic-item:hover {
  background: #e2e8f0;
  border-color: #cbd5e1;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.hot-topic-item.clicked {
  transform: scale(0.98) translateY(1px);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
  transition: all 0.1s ease-out;
}

.hot-topic-item:active {
  transform: scale(0.98) translateY(1px);
}

.hot-topic-item.top-rank {
  background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 50%, #fb923c 100%);
  border-color: #fb923c;
  box-shadow: 0 2px 8px rgba(251, 146, 60, 0.2);
}

.hot-topic-item.top-rank:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(251, 146, 60, 0.3);
}

.hot-topic-item.top-rank.clicked {
  transform: scale(0.96) translateY(2px);
  box-shadow: 0 2px 8px rgba(251, 146, 60, 0.4);
}

.hot-topic-item.top-rank:active {
  transform: scale(0.96) translateY(2px);
}

.hot-topic-item.new-topic {
  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 50%, #fde047 100%);
  border-color: #eab308;
  box-shadow: 0 2px 8px rgba(234, 179, 8, 0.2);
  position: relative;
  overflow: hidden;
}

.hot-topic-item.new-topic::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: new-topic-shine 3s infinite;
}

.hot-topic-item.new-topic:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(234, 179, 8, 0.3);
}

@keyframes new-topic-shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

.rank-indicator {
  flex-shrink: 0;
  margin-top: 2px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  min-width: 32px;
}

.rank-badge {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  position: relative;
}

.rank-crown {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.rank-change-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  background: white;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.rank-change-icon {
  animation: rank-change-pulse 2s infinite;
}

.special-rank {
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.special-rank.rank-1 {
  color: #B8860B;
  font-size: 14px;
  font-weight: 800;
}

.special-rank.rank-2 {
  color: #708090;
  font-size: 13px;
  font-weight: 700;
}

.special-rank.rank-3 {
  color: #FF6B35;
  font-size: 12px;
  font-weight: 700;
}

@keyframes rank-change-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

.rank-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 11px;
  font-weight: 700;
  border-radius: 50%;
  background: #64748b;
  color: white;
}

.rank-number.rank-1 {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #92400e;
  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

.rank-number.rank-2 {
  background: linear-gradient(135deg, #c0c0c0 0%, #e5e5e5 100%);
  color: #374151;
  box-shadow: 0 2px 4px rgba(192, 192, 192, 0.3);
}

.rank-number.rank-3 {
  background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(205, 127, 50, 0.3);
}

.topic-content {
  flex: 1;
  min-width: 0;
  position: relative;
}

.new-topic-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  z-index: 1;
}

.new-chip {
  animation: new-topic-glow 2s infinite alternate;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3) !important;
}

/* 五角星样式：移除动画，保持静止 */

@keyframes new-topic-glow {
  0% {
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
    transform: scale(1);
  }
  100% {
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.5);
    transform: scale(1.02);
  }
}

/* 移除五角星旋转动画 */

.topic-name {
  font-size: 12px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 2px;
  line-height: 1.3;
  word-wrap: break-word;
  word-break: break-word;
  max-height: 2.6em; /* 允许最多2行显示 */
  overflow: hidden;
}

.topic-stats {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 2px;
}

.stat-value, .stat-percentage {
  font-size: 10px;
  font-weight: 500;
  color: #64748b;
}

.stat-percentage-label {
  font-size: 10px;
  font-weight: 500;
  color: #64748b;
  margin-right: 2px;
}

.heat-level {
  font-size: 8px;
  margin-left: 2px;
  line-height: 1;
}

.heat-change-value {
  font-size: 10px;
  font-weight: 600;
}

.heat-change-value.heat-increase {
  color: #16a34a;
}

.heat-change-value.heat-decrease {
  color: #dc2626;
}

.heat-change-value.heat-stable {
  color: #3b82f6;
}

.heat-change-icon {
  animation: heat-change-pulse 2s infinite;
}

@keyframes heat-change-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

.trend-icon {
  animation: trend-pulse 2s infinite;
}

@keyframes trend-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Tooltip样式 */
.topic-tooltip {
  max-width: 200px;
}

.tooltip-title {
  font-weight: 600;
  margin-bottom: 4px;
  color: #ffffff;
}

.tooltip-stats {
  font-size: 12px;
  line-height: 1.4;
}

.tooltip-stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  padding: 2px 0;
}

.tooltip-label {
  font-weight: 500;
  color: #e2e8f0;
  margin-right: 8px;
  min-width: 80px;
}

.new-indicator {
  color: #f59e0b;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hot-topic-item {
    padding: 5px 8px;
  }
  
  .topic-name {
    font-size: 11px;
  }
  
  .stat-value, .stat-percentage {
    font-size: 9px;
  }
}
</style> 