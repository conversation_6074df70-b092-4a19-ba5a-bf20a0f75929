<template>
  <div 
    class="hot-topic-item" 
    :class="{ 'top-rank': rank <= 3 }"
    @click="$emit('click')"
  >
    <div class="rank-indicator">
      <span 
        class="rank-number"
        :class="{
          'rank-1': rank === 1,
          'rank-2': rank === 2, 
          'rank-3': rank === 3
        }"
      >
        {{ rank }}
      </span>
    </div>
    
    <div class="topic-content">
      <div class="topic-name">
        {{ formatTopicName(topic.topic) }}
      </div>
      
      <div class="topic-stats">
        <div class="stat-item">
          <v-icon size="12" color="primary">mdi-fire</v-icon>
          <span class="stat-value">{{ topic.count }}</span>
        </div>
        
        <div class="stat-item">
          <v-icon 
            size="12" 
            :color="getTrendColor()"
            class="trend-icon"
          >
            {{ getTrendIcon() }}
          </v-icon>
          <span class="stat-percentage">{{ topic.percentage.toFixed(1) }}%</span>
        </div>
      </div>
    </div>
    
    <!-- 悬停显示的详细信息 -->
    <v-tooltip 
      activator="parent" 
      location="bottom"
      :text="`${formatTopicName(topic.topic)}: ${topic.count}条认知，占比${topic.percentage.toFixed(1)}%`"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  topic: {
    type: Object,
    required: true
  },
  rank: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['click'])

// 格式化topic名称
const formatTopicName = (topic) => {
  if (!topic) return '未知话题'
  
  // 将连字符替换为空格，首字母大写，不截断长度
  return topic.replace(/-/g, ' ')
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

// 获取趋势图标
const getTrendIcon = () => {
  const trend = props.topic.trend || 'stable'
  switch (trend) {
    case 'up':
      return 'mdi-trending-up'
    case 'down':
      return 'mdi-trending-down'
    default:
      return 'mdi-trending-neutral'
  }
}

// 获取趋势颜色
const getTrendColor = () => {
  const trend = props.topic.trend || 'stable'
  switch (trend) {
    case 'up':
      return 'success'
    case 'down':
      return 'error'
    default:
      return 'grey'
  }
}
</script>

<style scoped>
.hot-topic-item {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  padding: 8px 10px;
  border-radius: 8px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 0;
  width: 100%;
  min-height: 56px;
}

.hot-topic-item:hover {
  background: #e2e8f0;
  border-color: #cbd5e1;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.hot-topic-item.top-rank {
  background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 50%, #fb923c 100%);
  border-color: #fb923c;
  box-shadow: 0 2px 8px rgba(251, 146, 60, 0.2);
}

.hot-topic-item.top-rank:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(251, 146, 60, 0.3);
}

.rank-indicator {
  flex-shrink: 0;
  margin-top: 2px;
}

.rank-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 11px;
  font-weight: 700;
  border-radius: 50%;
  background: #64748b;
  color: white;
}

.rank-number.rank-1 {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #92400e;
  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

.rank-number.rank-2 {
  background: linear-gradient(135deg, #c0c0c0 0%, #e5e5e5 100%);
  color: #374151;
  box-shadow: 0 2px 4px rgba(192, 192, 192, 0.3);
}

.rank-number.rank-3 {
  background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(205, 127, 50, 0.3);
}

.topic-content {
  flex: 1;
  min-width: 0;
}

.topic-name {
  font-size: 12px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 2px;
  line-height: 1.3;
  word-wrap: break-word;
  word-break: break-word;
  max-height: 2.6em; /* 允许最多2行显示 */
  overflow: hidden;
}

.topic-stats {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 2px;
}

.stat-value, .stat-percentage {
  font-size: 10px;
  font-weight: 500;
  color: #64748b;
}

.trend-icon {
  animation: trend-pulse 2s infinite;
}

@keyframes trend-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hot-topic-item {
    padding: 5px 8px;
  }
  
  .topic-name {
    font-size: 11px;
  }
  
  .stat-value, .stat-percentage {
    font-size: 9px;
  }
}
</style> 