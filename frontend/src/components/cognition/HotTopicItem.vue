<template>
  <div 
    class="hot-topic-item" 
    :class="{ 'top-rank': rank <= 3 }"
    @click="$emit('click')"
  >
    <div class="rank-indicator">
      <span 
        class="rank-number"
        :class="{
          'rank-1': rank === 1,
          'rank-2': rank === 2, 
          'rank-3': rank === 3
        }"
      >
        {{ rank }}
      </span>
    </div>
    
    <div class="topic-content">
      <div class="topic-header">
        <div class="topic-name">
          {{ formatTopicName(topic.topic) }}
        </div>

        <!-- 新兴话题标识 -->
        <v-chip
          v-if="topic.isNew"
          size="x-small"
          color="success"
          variant="elevated"
          class="new-badge"
        >
          NEW
        </v-chip>

        <!-- 排名变化指示器 -->
        <div v-if="topic.rankChange !== 0" class="rank-change">
          <v-icon
            :size="10"
            :color="topic.rankChange > 0 ? 'success' : 'error'"
            class="rank-change-icon"
          >
            {{ topic.rankChange > 0 ? 'mdi-arrow-up' : 'mdi-arrow-down' }}
          </v-icon>
          <span
            class="rank-change-text"
            :class="{ 'rank-up': topic.rankChange > 0, 'rank-down': topic.rankChange < 0 }"
          >
            {{ Math.abs(topic.rankChange) }}
          </span>
        </div>
      </div>

      <div class="topic-stats">
        <div class="stat-item">
          <v-icon size="12" color="primary">mdi-fire</v-icon>
          <span class="stat-value">{{ topic.count }}</span>

          <!-- 热度变化百分比 -->
          <span
            v-if="topic.heatChangePercent !== 0"
            class="heat-change"
            :class="{ 'heat-up': topic.heatChangePercent > 0, 'heat-down': topic.heatChangePercent < 0 }"
          >
            {{ topic.heatChangePercent > 0 ? '+' : '' }}{{ topic.heatChangePercent }}%
          </span>
        </div>

        <div class="stat-item">
          <v-icon
            size="12"
            :color="getTrendColor()"
            class="trend-icon"
          >
            {{ getTrendIcon() }}
          </v-icon>
          <span class="stat-percentage">{{ topic.percentage.toFixed(1) }}%</span>
        </div>

        <!-- 热度分数 -->
        <div class="stat-item heat-score">
          <v-icon size="10" color="orange">mdi-thermometer</v-icon>
          <span class="heat-score-value">{{ topic.heatScore || 0 }}</span>
        </div>
      </div>
    </div>
    
    <!-- 悬停显示的详细信息 -->
    <v-tooltip
      activator="parent"
      location="bottom"
    >
      <div class="tooltip-content">
        <div class="tooltip-title">{{ formatTopicName(topic.topic) }}</div>
        <div class="tooltip-stats">
          <div>📊 {{ topic.count }}条认知 ({{ topic.percentage.toFixed(1) }}%)</div>
          <div v-if="topic.heatScore">🔥 热度分数: {{ topic.heatScore }}</div>
          <div v-if="topic.isNew" class="tooltip-new">🆕 新兴话题</div>
          <div v-else-if="topic.rankChange !== 0" class="tooltip-rank">
            📈 排名{{ topic.rankChange > 0 ? '上升' : '下降' }}{{ Math.abs(topic.rankChange) }}位
          </div>
          <div v-if="topic.heatChangePercent !== 0" class="tooltip-heat">
            🌡️ 热度{{ topic.heatChangePercent > 0 ? '上升' : '下降' }}{{ Math.abs(topic.heatChangePercent) }}%
          </div>
        </div>
      </div>
    </v-tooltip>
  </div>
</template>

<script setup>

const props = defineProps({
  topic: {
    type: Object,
    required: true
  },
  rank: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['click'])

// 格式化topic名称
const formatTopicName = (topic) => {
  if (!topic) return '未知话题'
  
  // 将连字符替换为空格，首字母大写，不截断长度
  return topic.replace(/-/g, ' ')
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

// 获取趋势图标
const getTrendIcon = () => {
  const trend = props.topic.trend || 'stable'
  switch (trend) {
    case 'up':
      return 'mdi-trending-up'
    case 'down':
      return 'mdi-trending-down'
    default:
      return 'mdi-trending-neutral'
  }
}

// 获取趋势颜色
const getTrendColor = () => {
  const trend = props.topic.trend || 'stable'
  switch (trend) {
    case 'up':
      return 'success'
    case 'down':
      return 'error'
    default:
      return 'grey'
  }
}
</script>

<style scoped>
.hot-topic-item {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  padding: 8px 10px;
  border-radius: 8px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 0;
  width: 100%;
  min-height: 56px;
}

.hot-topic-item:hover {
  background: #e2e8f0;
  border-color: #cbd5e1;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.hot-topic-item.top-rank {
  background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 50%, #fb923c 100%);
  border-color: #fb923c;
  box-shadow: 0 2px 8px rgba(251, 146, 60, 0.2);
}

.hot-topic-item.top-rank:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(251, 146, 60, 0.3);
}

.rank-indicator {
  flex-shrink: 0;
  margin-top: 2px;
}

.rank-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 11px;
  font-weight: 700;
  border-radius: 50%;
  background: #64748b;
  color: white;
}

.rank-number.rank-1 {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #92400e;
  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

.rank-number.rank-2 {
  background: linear-gradient(135deg, #c0c0c0 0%, #e5e5e5 100%);
  color: #374151;
  box-shadow: 0 2px 4px rgba(192, 192, 192, 0.3);
}

.rank-number.rank-3 {
  background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(205, 127, 50, 0.3);
}

.topic-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.topic-header {
  display: flex;
  align-items: center;
  gap: 4px;
  min-height: 16px;
}

.topic-name {
  font-size: 12px;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.3;
  word-wrap: break-word;
  word-break: break-word;
  max-height: 2.6em; /* 允许最多2行显示 */
  overflow: hidden;
  flex: 1;
}

.new-badge {
  font-size: 8px !important;
  height: 14px !important;
  min-width: 24px !important;
  font-weight: 700;
  animation: new-pulse 2s infinite;
}

@keyframes new-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.rank-change {
  display: flex;
  align-items: center;
  gap: 1px;
  padding: 1px 3px;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.05);
}

.rank-change-text {
  font-size: 8px;
  font-weight: 700;
}

.rank-change-text.rank-up {
  color: #16a34a;
}

.rank-change-text.rank-down {
  color: #dc2626;
}

.topic-stats {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 2px;
  position: relative;
}

.stat-value, .stat-percentage {
  font-size: 10px;
  font-weight: 500;
  color: #64748b;
}

.heat-change {
  font-size: 8px;
  font-weight: 600;
  margin-left: 2px;
  padding: 1px 3px;
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.05);
}

.heat-change.heat-up {
  color: #16a34a;
  background: rgba(22, 163, 74, 0.1);
}

.heat-change.heat-down {
  color: #dc2626;
  background: rgba(220, 38, 38, 0.1);
}

.heat-score {
  background: rgba(251, 146, 60, 0.1);
  padding: 1px 4px;
  border-radius: 4px;
  border: 1px solid rgba(251, 146, 60, 0.2);
}

.heat-score-value {
  font-size: 9px;
  font-weight: 600;
  color: #ea580c;
}

.trend-icon {
  animation: trend-pulse 2s infinite;
}

@keyframes trend-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Tooltip样式 */
.tooltip-content {
  text-align: left;
  max-width: 200px;
}

.tooltip-title {
  font-weight: 600;
  font-size: 13px;
  margin-bottom: 4px;
  color: #1e293b;
}

.tooltip-stats {
  font-size: 11px;
  line-height: 1.4;
  color: #64748b;
}

.tooltip-stats > div {
  margin-bottom: 2px;
}

.tooltip-new {
  color: #16a34a;
  font-weight: 600;
}

.tooltip-rank {
  color: #2563eb;
  font-weight: 600;
}

.tooltip-heat {
  color: #ea580c;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hot-topic-item {
    padding: 5px 8px;
  }
  
  .topic-name {
    font-size: 11px;
  }
  
  .stat-value, .stat-percentage {
    font-size: 9px;
  }
}
</style> 