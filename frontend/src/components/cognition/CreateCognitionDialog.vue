<template>
  <v-dialog :model-value="modelValue" max-width="900" scrollable>
    <v-card class="create-cognition-card">
      <div class="dialog-gradient-bar"></div>
      <v-card-title>
        <span class="dialog-title">
          <v-icon color="primary" class="mr-2">mdi-plus</v-icon>
          创建新认知
        </span>
        <v-spacer />
        <v-btn icon @click="handleClose"><v-icon>mdi-close</v-icon></v-btn>
      </v-card-title>
      <v-card-text>
        <div class="section-label"><v-icon color="primary">mdi-alert-circle-outline</v-icon> 必填项（至少填写中文版本）</div>
        <v-form ref="form" v-model="isFormValid">
          <!-- 认知摘要 -->
          <div class="bilingual-field">
            <v-text-field
              v-model="formData.abstract_zh"
              label="认知摘要（中文） *"
              variant="solo"
              :rules="abstractZhRules"
              counter="200"
              maxlength="200"
              class="mb-2 input-shadow"
              clearable
              prepend-inner-icon="mdi-lightbulb-outline"
              color="primary"
            />
            <v-text-field
              v-model="formData.abstract_en"
              label="认知摘要（英文）"
              variant="solo"
              counter="200"
              maxlength="200"
              class="mb-4 input-shadow"
              clearable
              prepend-inner-icon="mdi-lightbulb-outline"
              color="primary"
            />
          </div>
          
          <!-- 核心问题 -->
          <div class="bilingual-field">
            <v-text-field
              v-model="formData.question_zh"
              label="核心问题（中文） *"
              variant="solo"
              :rules="questionZhRules"
              counter="300"
              maxlength="300"
              class="mb-2 input-shadow"
              clearable
              prepend-inner-icon="mdi-help-circle-outline"
              color="success"
            />
            <v-text-field
              v-model="formData.question_en"
              label="核心问题（英文）"
              variant="solo"
              counter="300"
              maxlength="300"
              class="mb-4 input-shadow"
              clearable
              prepend-inner-icon="mdi-help-circle-outline"
              color="success"
            />
          </div>
          
          <!-- 答案/结论 -->
          <div class="bilingual-field">
            <v-textarea
              v-model="formData.answer_zh"
              label="答案/结论（中文） *"
              variant="solo"
              :rules="answerZhRules"
              counter="2000"
              maxlength="2000"
              class="mb-2 input-shadow"
              clearable
              auto-grow
              prepend-inner-icon="mdi-check-circle-outline"
              color="info"
            />
            <v-textarea
              v-model="formData.answer_en"
              label="答案/结论（英文）"
              variant="solo"
              counter="2000"
              maxlength="2000"
              class="mb-4 input-shadow"
              clearable
              auto-grow
              prepend-inner-icon="mdi-check-circle-outline"
              color="info"
            />
          </div>
          
          <!-- Topic标签选择 -->
          <div class="topic-section">
            <div class="section-label mt-4 mb-3">
              <v-icon color="primary">mdi-tag-multiple</v-icon> 
              话题标签（可选）
            </div>
            
            <!-- Primary Topic -->
            <v-select
              v-model="formData.primary_topic"
              :items="primaryTopics"
              label="主要话题"
              variant="solo"
              clearable
              class="mb-3 input-shadow"
              prepend-inner-icon="mdi-tag"
              color="primary"
              placeholder="选择一个主要话题"
            />
            
            <!-- Related Topics -->
            <v-select
              v-model="formData.related_topics"
              :items="secondaryTopics"
              label="相关话题"
              variant="solo"
              multiple
              clearable
              class="mb-4 input-shadow"
              prepend-inner-icon="mdi-tag-multiple"
              color="secondary"
              placeholder="选择相关话题（最多2个）"
              :rules="relatedTopicsRules"
            >
              <template v-slot:selection="{ item, index }">
                <v-chip
                  v-if="index < 2"
                  class="ma-1"
                  color="secondary"
                  size="small"
                >
                  {{ item.title }}
                </v-chip>
                <span v-if="index === 2" class="text-grey text-caption align-self-center">
                  (+{{ formData.related_topics.length - 2 }} 其他)
                </span>
              </template>
            </v-select>
          </div>
          
          <div class="section-label mt-6"><v-icon color="grey">mdi-dots-horizontal</v-icon> 选填项</div>
          <v-text-field
            v-model="formData.source"
            label="来源/参考"
            variant="solo"
            counter="200"
            maxlength="200"
            class="mb-4 input-shadow"
            clearable
            prepend-inner-icon="mdi-source-branch"
            color="purple"
          />
          <v-text-field
            v-model="formData.blogger"
            label="博主/作者"
            variant="solo"
            counter="100"
            maxlength="100"
            class="mb-4 input-shadow"
            clearable
            prepend-inner-icon="mdi-account"
            color="teal"
          />
          <v-text-field
            v-model="formData.link"
            label="相关链接"
            variant="solo"
            counter="500"
            maxlength="500"
            class="mb-4 input-shadow"
            clearable
            prepend-inner-icon="mdi-link"
            color="indigo"
          />
        </v-form>
      </v-card-text>
      <v-card-actions>
        <v-spacer />
        <v-btn text class="cancel-btn" @click="handleClose">取消</v-btn>
        <v-btn class="main-btn" :disabled="!isFormValid || isSubmitting" :loading="isSubmitting" @click="handleSubmit">
          发布认知
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits, watch } from 'vue'
import { useCognitionStore } from '@/stores/cognition'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'created'])

const cognitionStore = useCognitionStore()

// 表单状态
const form = ref(null)
const isFormValid = ref(false)
const isSubmitting = ref(false)

// 可用的topics
const availableTopics = ref([])
const primaryTopics = ref([])
const secondaryTopics = ref([])

// Topic颜色映射
const topicColors = {
  'test1': {
    color: '#FF6B6B',
    background: '#FFE5E5'
  },
  'test2': {
    color: '#4ECDC4', 
    background: '#E5F9F7'
  }
}

// 表单数据
const formData = reactive({
  abstract_zh: '',
  abstract_en: '',
  question_zh: '',
  question_en: '',
  answer_zh: '',
  answer_en: '',
  source: '',
  blogger: '',
  link: '',
  primary_topic: null,
  related_topics: []
})

// 验证规则 - 中文版本为必填
const abstractZhRules = [
  v => !!v || '认知摘要（中文）不能为空',
  v => (v && v.length >= 10) || '认知摘要至少10个字符',
  v => (v && v.length <= 200) || '认知摘要不能超过200个字符'
]

const questionZhRules = [
  v => !!v || '核心问题（中文）不能为空',
  v => (v && v.length >= 5) || '核心问题至少5个字符',
  v => (v && v.length <= 300) || '核心问题不能超过300个字符'
]

const answerZhRules = [
  v => !!v || '答案/结论（中文）不能为空',
  v => (v && v.length >= 10) || '答案/结论至少10个字符',
  v => (v && v.length <= 2000) || '答案/结论不能超过2000个字符'
]

const relatedTopicsRules = [
  v => v.length <= 2 || '最多只能选择2个相关话题'
]

// 监听对话框打开/关闭，重置表单
watch(() => props.modelValue, async (newValue) => {
  if (newValue) {
    resetForm()
    // 加载可用的topics
    await loadAvailableTopics()
  }
})

function resetForm() {
  formData.abstract_zh = ''
  formData.abstract_en = ''
  formData.question_zh = ''
  formData.question_en = ''
  formData.answer_zh = ''
  formData.answer_en = ''
  formData.source = ''
  formData.blogger = ''
  formData.link = ''
  formData.primary_topic = null
  formData.related_topics = []
  isSubmitting.value = false
  if (form.value) {
    form.value.resetValidation()
  }
}

function handleClose() {
  emit('update:modelValue', false)
}

async function handleSubmit() {
  if (!isFormValid.value) return
  try {
    isSubmitting.value = true
    const newCognition = await cognitionStore.createCognition({
      abstract_zh: formData.abstract_zh.trim(),
      abstract_en: formData.abstract_en.trim() || '',
      question_zh: formData.question_zh.trim(),
      question_en: formData.question_en.trim() || '',
      answer_zh: formData.answer_zh.trim(),
      answer_en: formData.answer_en.trim() || '',
      source: formData.source.trim() || '',
      blogger: formData.blogger.trim() || '',
      link: formData.link.trim() || '',
      primary_topic: formData.primary_topic,
      related_topics: formData.related_topics || []
    })
    emit('created', newCognition)
    emit('update:modelValue', false)
  } catch (error) {
    console.error('创建认知失败:', error)
  } finally {
    isSubmitting.value = false
  }
}

// 加载可用的topics
async function loadAvailableTopics() {
  try {
    // 使用新的认知主题结构
    const newTopicStructure = {
      hierarchy: [
        {
          primary_topic: "Architecture",
          related_topic: [
            "sparse attention",
            "linear attention", 
            "hybrid attention",
            "long context",
            "mixture-of-experts",
            "auto-regressive multimodal",
            "diffusion-based multimodal",
            "transfusion-based multimodal"
          ]
        },
        {
          primary_topic: "LLMOps",
          related_topic: [
            "Human Annotation",
            "Data Filtering",
            "Data Synthesis",
            "Cognition Engineering",
            "Human-AI Collaboration",
            "Pretraining Recipe",
            "Scaling Law",
            "continue pretraining",
            "long-context training",
            "supervised fine-tuning",
            "reinforcement learning",
            "self-iterative learning",
            "Prompt Engineering",
            "Context Engineering",
            "Benchmark",
            "Evaluation Metric"
          ]
        },
        {
          primary_topic: "Reasoning",
          related_topic: [
            "long thought training",
            "reinforcement learning scaling",
            "efficient reasoning"
          ]
        },
        {
          primary_topic: "Agent",
          related_topic: [
            "agent environment",
            "agent reward",
            "tool use",
            "multi-agent",
            "human-agent interaction",
            "memory management",
            "agentic RL",
            "agentic SFT"
          ]
        },
        {
          primary_topic: "System Efficiency",
          related_topic: [
            "distributed training",
            "RL training infra",
            "quantization and pruning",
            "knowledge distillation",
            "speculative-decoding",
            "kv-cache management"
          ]
        },
        {
          primary_topic: "RL Scaling",
          related_topic: [
            "code start",
            "RL data selection",
            "reward model",
            "entropy control"
          ]
        },
        {
          primary_topic: "Safety & Security",
          related_topic: [
            "safety alignment",
            "Privacy",
            "Explanation",
            "Emerging Risks"
          ]
        }
      ]
    }
    
    // 提取一级分类作为primary topics
    primaryTopics.value = newTopicStructure.hierarchy.map(category => ({
      title: category.primary_topic.replace(/-/g, ' '),
      value: category.primary_topic
    }))
    
    // 提取所有二级分类作为related topics
    const allSecondaryTopics = []
    newTopicStructure.hierarchy.forEach(category => {
      category.related_topic.forEach(topic => {
        allSecondaryTopics.push({
          title: topic.replace(/-/g, ' ').replace(/\(/g, ' ('),
          value: topic
        })
      })
    })
    secondaryTopics.value = allSecondaryTopics
    
    // 构建可用话题列表
    availableTopics.value = newTopicStructure.hierarchy.flatMap(category => [
      category.primary_topic,
      ...category.related_topic
    ])
    
    console.log('创建对话框：新的认知主题结构已加载')
    
  } catch (error) {
    console.error('加载可用topics失败:', error)
    // 使用默认值
    primaryTopics.value = [
      { title: 'Architecture', value: 'Architecture' },
      { title: 'LLMOps', value: 'LLMOps' },
      { title: 'Reasoning', value: 'Reasoning' },
      { title: 'Agent', value: 'Agent' },
      { title: 'System Efficiency', value: 'System Efficiency' },
      { title: 'RL Scaling', value: 'RL Scaling' },
      { title: 'Safety & Security', value: 'Safety & Security' }
    ]
    secondaryTopics.value = []
    availableTopics.value = []
  }
}

function getTopicChipStyle(value) {
  const colors = topicColors[value] || {
    color: '#667eea',
    background: 'rgba(102, 126, 234, 0.1)'
  }
  
  return {
    color: colors.color,
    backgroundColor: colors.background
  }
}
</script>

<style scoped>
.create-cognition-card {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 16px !important;
  overflow: hidden;
  position: relative;
}

.dialog-gradient-bar {
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #667eea 100%);
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

.bilingual-field {
  margin-bottom: 16px;
}

.bilingual-field .v-text-field,
.bilingual-field .v-textarea {
  margin-bottom: 8px;
}

.bilingual-field .v-text-field:last-child,
.bilingual-field .v-textarea:last-child {
  margin-bottom: 0;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.dialog-title {
  font-size: 20px;
  font-weight: bold;
  display: flex;
  align-items: center;
}
.section-label {
  font-size: 15px;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
}
.input-shadow :deep(.v-input__control) {
  border-radius: 12px !important;
  box-shadow: 0 2px 8px 0 rgba(102,126,234,0.08);
  background: #f8fafd !important;
}
.mb-4 {
  margin-bottom: 16px;
}
.mt-6 {
  margin-top: 32px;
}
.cancel-btn {
  color: #888 !important;
  background: #f3f3f3 !important;
  border-radius: 8px;
  margin-right: 8px;
}
.main-btn {
  background: linear-gradient(90deg, #667eea 0%, #42e9f5 100%) !important;
  color: #fff !important;
  font-weight: bold;
  border-radius: 8px;
  box-shadow: 0 2px 8px 0 rgba(102,126,234,0.12);
  padding: 0 24px;
  text-transform: none;
}
.main-btn:disabled {
  background: #bfc9e6 !important;
  color: #fff !important;
}
.topics-select :deep(.v-chip) {
  margin: 2px 4px 2px 0 !important;
  border-radius: 12px !important;
  font-weight: 600 !important;
}
.topics-select :deep(.v-chip .v-icon) {
  margin-right: 4px !important;
}
@media (max-width: 600px) {
  .dialog-title { font-size: 16px; }
  .mb-4 { margin-bottom: 10px; }
  .create-cognition-card { border-radius: 8px; }
  .main-btn, .cancel-btn { padding: 0 12px; font-size: 14px; }
}
</style> 