<template>
  <v-dialog
    v-model="dialogVisible"
    max-width="600"
    persistent
    scrollable
    location="right"
  >
    <v-card 
      class="hot-ranking-dialog" 
      ref="dialogRef"
    >
      <!-- 弹窗标题栏 -->
      <v-card-title class="dialog-header">
        <div class="header-content">
          <div class="header-left">
            <v-icon color="error" size="24" class="fire-icon">mdi-fire</v-icon>
            <h3 class="dialog-title">实时热榜</h3>
            <v-chip 
              size="small" 
              color="error" 
              variant="outlined"
              class="live-indicator"
            >
              LIVE
            </v-chip>
          </div>
          
          <v-btn
            icon="mdi-close"
            variant="text"
            size="small"
            @click="$emit('update:modelValue', false)"
            class="close-btn"
          />
        </div>
        
        <!-- 时间筛选 -->
        <div class="time-filter-section">
          <v-btn-toggle
            v-model="currentTimeFilter"
            density="compact"
            class="time-toggle"
            mandatory
            @update:model-value="handleTimeFilterChange"
          >
            <v-btn value="day" size="small">今日</v-btn>
            <v-btn value="week" size="small">本周</v-btn>
            <v-btn value="month" size="small">本月</v-btn>
          </v-btn-toggle>
        </div>
      </v-card-title>

      <v-divider />

      <v-card-text class="dialog-content">
        <div v-if="loading" class="loading-section">
          <v-progress-circular indeterminate size="48" color="primary" />
          <div class="loading-text">加载热榜数据中...</div>
        </div>

        <div v-else-if="error" class="error-section">
          <v-icon color="error" size="48">mdi-alert-circle</v-icon>
          <div class="error-title">加载失败</div>
          <div class="error-text">{{ error }}</div>
          <v-btn 
            color="primary" 
            variant="elevated"
            @click="refreshData"
            class="retry-btn"
          >
            重新加载
          </v-btn>
        </div>

        <div v-else class="ranking-content">
          <!-- 热门话题部分 -->
          <div class="topics-section">
            <div class="section-header">
              <v-icon size="20" color="primary">mdi-tag-multiple</v-icon>
              <span class="section-title">热门话题</span>
              <span class="section-count">({{ Math.min(hotTopics.length, 6) }})</span>
            </div>
            
            <div class="topics-grid">
              <HotTopicItem
                v-for="(topic, index) in hotTopics.slice(0, 6)"
                :key="topic.topic"
                :topic="topic"
                :rank="index + 1"
                @click="handleTopicClick(topic.topic)"
              />
            </div>
          </div>

          <v-divider class="section-divider" />

          <!-- 热门认知部分 -->
          <div class="cognitions-section">
            <div class="section-header">
              <v-icon size="20" color="secondary">mdi-lightbulb</v-icon>
              <span class="section-title">热门认知</span>
              <span class="section-count">({{ Math.min(hotCognitions.length, 10) }})</span>
            </div>
            
            <div class="cognitions-list" ref="cognitionsScrollRef">
              <HotCognitionItem
                v-for="(cognition, index) in hotCognitions.slice(0, 10)"
                :key="cognition.id"
                :cognition="cognition"
                :rank="index + 1"
                @click="handleCognitionClick(cognition)"
              />
            </div>
          </div>
        </div>
      </v-card-text>

      <!-- 底部信息 -->
      <v-divider />
      <div class="dialog-footer">
        <div class="update-info" v-if="lastUpdateTime">
          <v-icon size="14" color="grey">mdi-clock-outline</v-icon>
          <span class="update-text">更新于 {{ formatUpdateTime(lastUpdateTime) }}</span>
        </div>
        
        <v-btn
          variant="text"
          size="small"
          color="primary"
          @click="$emit('show-trend-dialog')"
          class="trend-detail-btn"
        >
          <v-icon size="16">mdi-chart-line</v-icon>
          趋势分析
        </v-btn>
      </div>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { useHotRankingStore } from '@/stores/hotRanking'
import { storeToRefs } from 'pinia'
import { computed, nextTick, onUnmounted, ref, watch } from 'vue'
import HotCognitionItem from './HotCognitionItem.vue'
import HotTopicItem from './HotTopicItem.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'topic-click', 'cognition-click', 'show-trend-dialog'])

// Store
const hotRankingStore = useHotRankingStore()
const { 
  hotTopics, 
  hotCognitions, 
  loading, 
  error, 
  lastUpdateTime, 
  currentTimeFilter 
} = storeToRefs(hotRankingStore)

// Refs
const cognitionsScrollRef = ref(null)
const dialogRef = ref(null)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 自动刷新定时器
let refreshTimer = null

// 处理时间筛选变化
const handleTimeFilterChange = async (newFilter) => {
  if (newFilter) {
    await hotRankingStore.fetchHotRankingWithComparison(newFilter)
  }
}

// 处理topic点击
const handleTopicClick = (topic) => {
  emit('topic-click', topic)
  // 关闭弹窗
  emit('update:modelValue', false)
}

// 处理认知点击
const handleCognitionClick = (cognition) => {
  emit('cognition-click', cognition)
  // 不关闭弹窗，让用户可以继续浏览
}

// 刷新数据
const refreshData = async () => {
  await hotRankingStore.fetchHotRankingWithComparison(currentTimeFilter.value)
}

// 格式化更新时间
const formatUpdateTime = (time) => {
  if (!time) return ''
  const now = new Date()
  const updateTime = new Date(time)
  const diffMinutes = Math.floor((now - updateTime) / (1000 * 60))
  
  if (diffMinutes < 1) return '刚刚'
  if (diffMinutes < 60) return `${diffMinutes}分钟前`
  
  const diffHours = Math.floor(diffMinutes / 60)
  if (diffHours < 24) return `${diffHours}小时前`
  
  return updateTime.toLocaleDateString()
}

// 自定义滚动处理
const handleScroll = (event) => {
  if (!cognitionsScrollRef.value) return
  
  // 阻止默认滚动
  event.preventDefault()
  
  // 滚动认知列表区域
  const cognitionsSection = cognitionsScrollRef.value
  const scrollStep = 40 // 增加滚动步进，让滚动更明显
  const delta = event.deltaY > 0 ? scrollStep : -scrollStep
  
  cognitionsSection.scrollBy({
    top: delta,
    behavior: 'smooth'
  })
}

// 监听弹窗打开，加载数据
watch(dialogVisible, async (newValue) => {
  if (newValue) {
    // 弹窗打开时快速加载数据 (优先从缓存)
    if (!hotRankingStore.hotTopics.length || hotRankingStore.isDataStale) {
      await hotRankingStore.fetchHotRankingWithComparison()
    } else {
      // 如果有缓存数据，立即显示，然后在后台刷新
      setTimeout(() => {
        hotRankingStore.fetchHotRankingWithComparison(hotRankingStore.currentTimeFilter, false)
      }, 100)
    }

    // 启动自动刷新 (30分钟间隔)
    refreshTimer = setInterval(() => {
      hotRankingStore.fetchHotRankingWithComparison(currentTimeFilter.value)
    }, 30 * 60 * 1000)
    
    // 添加滚动监听器到整个弹窗
    await nextTick()
    if (dialogRef.value) {
      dialogRef.value.addEventListener('wheel', handleScroll, { passive: false })
    }
  } else {
    // 弹窗关闭时清理
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
    
    if (dialogRef.value) {
      dialogRef.value.removeEventListener('wheel', handleScroll)
    }
  }
})

// 组件卸载时清理
onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
  
  if (dialogRef.value) {
    dialogRef.value.removeEventListener('wheel', handleScroll)
  }
})
</script>

<style scoped>
.hot-ranking-dialog {
  height: 92vh;
  max-height: 850px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  user-select: none; /* 防止文本选择影响滚动 */
}

.dialog-header {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
  color: white;
  padding: 20px 24px 16px;
  flex-shrink: 0;
  user-select: auto; /* 允许标题区域文本选择 */
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.fire-icon {
  animation: flicker 2s infinite alternate;
}

@keyframes flicker {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

.dialog-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  color: white;
}

.live-indicator {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.close-btn {
  color: white !important;
}

.time-filter-section {
  display: flex;
  justify-content: center;
}

.time-toggle {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.time-toggle .v-btn {
  color: #333 !important;
  font-size: 12px;
  min-width: 60px;
  font-weight: 500;
}

.time-toggle .v-btn--active {
  background: rgba(102, 126, 234, 0.9) !important;
  color: white !important;
  font-weight: 600;
}

.dialog-content {
  flex: 1;
  padding: 0 !important;
  overflow: hidden;
}

.loading-section, .error-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  gap: 16px;
}

.loading-text, .error-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.error-text {
  font-size: 14px;
  color: #666;
  text-align: center;
}

.ranking-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  touch-action: pan-y; /* 改善触摸滚动体验 */
}

.topics-section {
  padding: 24px 28px;
  flex-shrink: 0;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-count {
  font-size: 12px;
  font-weight: 500;
  color: #666;
}

.topics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 14px;
  grid-auto-rows: min-content;
  align-items: start;
}

.section-divider {
  margin: 0 24px;
  opacity: 0.3;
}

.cognitions-section {
  padding: 24px 28px;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.cognitions-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding-right: 10px;
  scroll-behavior: smooth;
  scroll-snap-type: y proximity;
  will-change: scroll-position; /* 优化滚动性能 */
  user-select: auto; /* 允许内容文本选择 */
}

/* 自定义滚动条 */
.cognitions-list::-webkit-scrollbar {
  width: 6px;
}

.cognitions-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.cognitions-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.cognitions-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  background: #f8fafc;
  flex-shrink: 0;
}

.update-info {
  display: flex;
  align-items: center;
  gap: 6px;
}

.update-text {
  font-size: 12px;
  color: #666;
}

.trend-detail-btn {
  gap: 4px;
}
</style> 