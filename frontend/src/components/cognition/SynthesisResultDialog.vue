<template>
  <v-dialog 
    :model-value="modelValue" 
    @update:model-value="handleClose"
    max-width="900"
    scrollable
    persistent
    @click:outside="handleClose"
  >
    <v-card v-if="cognition" class="synthesis-result-dialog">
      <!-- 炫酷的头部 -->
      <v-card-title class="detail-header">
        <div class="header-content">
          <div class="header-title">
            <div class="title-icon">
              <v-icon size="28" color="white">mdi-auto-fix</v-icon>
            </div>
            <div class="title-text">
              <span class="main-title">🧠 认知合成结果</span>
              <span class="sub-title">AI驱动的认知融合与创新</span>
            </div>
          </div>
          <v-btn icon variant="text" @click="handleClose" class="close-btn">
            <v-icon color="white">mdi-close</v-icon>
          </v-btn>
        </div>
      </v-card-title>

      <!-- 内容区域 -->
      <v-card-text class="detail-content">
        <div class="hero-section" v-if="getDisplayText('abstract')">
          <div class="hero-icon">💡</div>
          <h2 class="hero-title">核心洞察</h2>
          <div class="hero-content">
            <MarkdownRenderer :content="getDisplayText('abstract')" :is-complete="true" />
          </div>
        </div>

        <!-- 三栏布局：来源分析、矛盾分析、合成理由 -->
        <div class="analysis-grid">
          <!-- 来源分析 -->
          <div class="analysis-card source-card" v-if="cognition.source_analysis">
            <div class="card-header">
              <v-icon color="#667eea">mdi-source-merge</v-icon>
              <h3>🔍 来源解析</h3>
            </div>
            <div class="card-content">
              <MarkdownRenderer :content="cognition.source_analysis" :is-complete="true" />
            </div>
          </div>

          <!-- 矛盾分析 -->
          <div class="analysis-card conflicts-card" v-if="cognition.contradictions">
            <div class="card-header">
              <v-icon color="#e74c3c">mdi-lightning-bolt</v-icon>
              <h3>⚡ 矛盾张力</h3>
            </div>
            <div class="card-content">
              <MarkdownRenderer :content="cognition.contradictions" :is-complete="true" />
            </div>
          </div>

          <!-- 合成理由 -->
          <div class="analysis-card reasoning-card" v-if="cognition.synthesis_reasoning">
            <div class="card-header">
              <v-icon color="#f39c12">mdi-brain</v-icon>
              <h3>🎯 合成逻辑</h3>
            </div>
            <div class="card-content">
              <MarkdownRenderer :content="cognition.synthesis_reasoning" :is-complete="true" />
            </div>
          </div>
        </div>

        <!-- 问题与答案 - 主要内容 -->
        <div class="qa-section">
          <!-- 合成问题 -->
          <div class="qa-item question-item" v-if="getDisplayText('question')">
            <div class="qa-header question-header">
              <div class="qa-icon question-icon">
                <v-icon color="white">mdi-help-circle</v-icon>
              </div>
              <h3>🤔 合成问题</h3>
            </div>
            <div class="qa-content question-content">
              <MarkdownRenderer :content="getDisplayText('question')" :is-complete="true" />
            </div>
          </div>

          <!-- 合成答案 -->
          <div class="qa-item answer-item" v-if="getDisplayText('answer')">
            <div class="qa-header answer-header">
              <div class="qa-icon answer-icon">
                <v-icon color="white">mdi-lightbulb</v-icon>
              </div>
              <h3>💡 综合答案</h3>
            </div>
            <div class="qa-content answer-content">
              <MarkdownRenderer :content="getDisplayText('answer')" :is-complete="true" />
            </div>
          </div>
        </div>

        <!-- 元思考部分 -->
        <div class="meta-thinking" v-if="getDisplayText('think')">
          <div class="thinking-header">
            <v-icon color="#9b59b6">mdi-thought-bubble</v-icon>
            <h3>🧩 元思考</h3>
          </div>
          <div class="thinking-content">
            <MarkdownRenderer :content="getDisplayText('think')" :is-complete="true" />
          </div>
        </div>

        <!-- 来源信息 -->
        <div class="source-info">
          <div class="source-item">
            <v-icon color="#34495e">mdi-source-branch</v-icon>
            <span><strong>来源：</strong>{{ cognition.source }}</span>
          </div>
          <div class="source-item">
            <v-icon color="#e67e22">mdi-tag</v-icon>
            <span><strong>类型：</strong>{{ cognition.tag || '合成认知' }}</span>
          </div>
          <div class="source-item" v-if="cognition.source_cognition_ids">
            <v-icon color="#3498db">mdi-link</v-icon>
            <span><strong>基于：</strong>{{ cognition.source_cognition_ids.length }}个源认知</span>
          </div>
        </div>
      </v-card-text>

      <v-card-actions class="detail-actions">
        <v-spacer></v-spacer>
        <v-btn variant="outlined" @click="handleClose" class="action-btn">
          <v-icon left>mdi-close</v-icon>
          关闭
        </v-btn>
        <v-btn color="primary" variant="elevated" @click="handleSave" class="action-btn save-btn">
          <v-icon left>mdi-content-save</v-icon>
          保存到认知库
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import { useCognitionStore } from '@/stores/cognition'
import { useRouter } from 'vue-router'
import MarkdownRenderer from '@/components/MarkdownRenderer.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  cognition: {
    type: Object,
    default: null
  },
  language: {
    type: String,
    default: 'zh'
  }
})

const emit = defineEmits(['update:modelValue', 'close', 'saved'])
const cognitionStore = useCognitionStore()
const router = useRouter()

function getDisplayText(field) {
  if (!props.cognition) return ''
  const zhField = `${field}_zh`
  const enField = `${field}_en`
  if (props.language === 'zh') {
    return props.cognition[zhField] || props.cognition[enField] || ''
  } else {
    return props.cognition[enField] || props.cognition[zhField] || ''
  }
}

function handleClose() {
  emit('update:modelValue', false)
  emit('close')
}

async function handleSave() {
  if (!props.cognition) return
  try {
    const dataToSave = { ...props.cognition }
    delete dataToSave.id // Remove temp ID
    
    const newCognition = await cognitionStore.createCognition(dataToSave)
    if (newCognition) {
      emit('saved', newCognition)
      handleClose()
    }
  } catch (error) {
    console.error('保存合成认知失败:', error)
  }
}
</script>

<style scoped>
.synthesis-result-dialog {
  max-height: 95vh;
  border-radius: 8px;
  overflow: hidden;
}

.detail-header {
  background: #2c3e50;
  color: white;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.title-text {
  display: flex;
  flex-direction: column;
}

.main-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 2px;
}

.sub-title {
  font-size: 13px;
  opacity: 0.8;
  color: #bdc3c7;
}

.close-btn {
  background: rgba(255, 255, 255, 0.1);
}

.detail-content {
  padding: 24px !important;
  background: #fafafa;
}

.hero-section {
  padding: 20px;
  background: white;
  border-radius: 8px;
  margin-bottom: 24px;
  border: 1px solid #e9ecef;
  border-left: 4px solid #3498db;
}

.hero-icon {
  font-size: 48px;
  margin-bottom: 16px;
  text-align: center;
}

.hero-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
  text-align: center;
}

.hero-content {
  font-size: 16px;
  line-height: 1.6;
  color: #34495e;
}

.analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.analysis-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
  border-left: 4px solid;
}

.source-card {
  border-left-color: #3498db;
}

.conflicts-card {
  border-left-color: #e74c3c;
}

.reasoning-card {
  border-left-color: #f39c12;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.card-header h3 {
  font-size: 15px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.card-content {
  font-size: 14px;
  line-height: 1.6;
  color: #5a6c7d;
}

.qa-section {
  margin-bottom: 24px;
}

.qa-item {
  background: white;
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid #e9ecef;
}

.qa-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.question-header {
  color: #3498db;
}

.answer-header {
  color: #27ae60;
}

.qa-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qa-header h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.qa-content {
  padding: 16px;
  font-size: 15px;
  line-height: 1.7;
  color: #2c3e50;
}

.meta-thinking {
  background: linear-gradient(135deg, #f3e5f5, #e1bee7);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 32px;
  border: 1px solid rgba(155, 89, 182, 0.2);
}

.thinking-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.thinking-header h3 {
  font-size: 15px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.thinking-content {
  font-size: 14px;
  line-height: 1.6;
  color: #5a6c7d;
}

.source-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.source-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #5a6c7d;
}

.detail-actions {
  padding: 16px 24px;
  background: white;
  border-top: 1px solid #e9ecef;
}

.action-btn {
  padding: 0 20px;
  height: 36px;
  border-radius: 6px;
  text-transform: none;
  font-weight: 500;
  gap: 6px;
}

.save-btn {
  background: #3498db;
  color: white;
}

.save-btn:hover {
  background: #2980b9;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .analysis-grid {
    grid-template-columns: 1fr;
  }
  
  .detail-content {
    padding: 16px !important;
  }
  
  .hero-section {
    padding: 16px;
  }
  
  .source-info {
    flex-direction: column;
    gap: 8px;
  }
}
</style> 