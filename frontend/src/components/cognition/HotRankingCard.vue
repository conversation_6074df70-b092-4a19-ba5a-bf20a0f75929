<template>
    <div class="hot-ranking-container">
      <v-card class="hot-ranking-card" elevation="2">
        <!-- 热榜标题栏 -->
        <div class="ranking-header">
          <div class="header-left">
            <v-icon color="error" size="20" class="fire-icon">mdi-fire</v-icon>
            <h3 class="ranking-title">实时热榜</h3>
            <v-chip 
              size="small" 
              color="error" 
              variant="outlined"
              class="live-indicator"
            >
              LIVE
            </v-chip>
          </div>
          
          <div class="header-right">
            <!-- 时间筛选 -->
            <v-btn-toggle
              v-model="currentTimeFilter"
              density="compact"
              class="time-toggle"
              mandatory
              @update:model-value="handleTimeFilterChange"
            >
              <v-btn value="day" size="small">今日</v-btn>
              <v-btn value="week" size="small">本周</v-btn>
              <v-btn value="month" size="small">本月</v-btn>
            </v-btn-toggle>
            
            <!-- 更多详情按钮 -->
            <v-btn
              variant="text"
              size="small"
              color="primary"
              @click="$emit('show-trend-dialog')"
              class="more-btn"
            >
              <v-icon size="16">mdi-trending-up</v-icon>
              详情
            </v-btn>
          </div>
        </div>
  
        <v-divider />
  
        <!-- 热榜内容 -->
        <div class="ranking-content">
          <div v-if="loading" class="loading-section">
            <v-progress-circular indeterminate size="24" color="primary" />
            <span class="loading-text">加载中...</span>
          </div>
  
          <div v-else-if="error" class="error-section">
            <v-icon color="error" size="20">mdi-alert-circle</v-icon>
            <span class="error-text">{{ error }}</span>
            <v-btn 
              size="small" 
              variant="text" 
              color="primary" 
              @click="refreshData"
            >
              重试
            </v-btn>
          </div>
  
          <div v-else class="ranking-sections">
            <!-- 热门Topics部分 -->
            <div class="topics-section">
              <div class="section-header">
                <v-icon size="16" color="primary">mdi-tag-multiple</v-icon>
                <span class="section-title">热门话题</span>
                <v-chip
                  v-if="hasNewTopics"
                  size="x-small"
                  color="warning"
                  variant="outlined"
                  class="new-topics-indicator"
                >
                  <v-icon size="10">mdi-star</v-icon>
                  {{ newTopicsCount }}个新话题
                </v-chip>
              </div>
              <div class="topics-grid">
                <HotTopicItem
                  v-for="(topic, index) in hotTopics.slice(0, 6)"
                  :key="topic.topic"
                  :topic="topic"
                  :rank="index + 1"
                  @click="handleTopicClick(topic.topic)"
                />
              </div>
            </div>
  
            <!-- 分隔线 -->
            <v-divider vertical class="section-divider" />
  
            <!-- 热门认知部分 -->
            <div class="cognitions-section">
              <div class="section-header">
                <v-icon size="16" color="secondary">mdi-lightbulb</v-icon>
                <span class="section-title">热门认知</span>
                <span class="cognition-count">({{ hotCognitions.length }})</span>
              </div>
                          <div class="cognitions-scrollable" ref="cognitionsScrollRef">
              <HotCognitionItem
                v-for="(cognition, index) in hotCognitions.slice(0, 10)"
                :key="cognition.id"
                :cognition="cognition"
                :rank="index + 1"
                @click="handleCognitionClick(cognition)"
              />
            </div>
            </div>
          </div>
        </div>
  
        <!-- 最后更新时间 -->
        <div class="update-time" v-if="lastUpdateTime">
          <v-icon size="12" color="grey">mdi-clock-outline</v-icon>
          <span class="time-text">更新于 {{ formatUpdateTime(lastUpdateTime) }}</span>
        </div>
      </v-card>
    </div>
  </template>
  
  <script setup>
  import { useHotRankingStore } from '@/stores/hotRanking'
import { storeToRefs } from 'pinia'
import { computed, nextTick, onMounted, onUnmounted, ref } from 'vue'
import HotCognitionItem from './HotCognitionItem.vue'
import HotTopicItem from './HotTopicItem.vue'
  
  const emit = defineEmits(['topic-click', 'cognition-click', 'show-trend-dialog'])

// Store
const hotRankingStore = useHotRankingStore()
const { 
  hotTopics, 
  hotCognitions, 
  loading, 
  error, 
  lastUpdateTime, 
  currentTimeFilter 
} = storeToRefs(hotRankingStore)

// Refs
const cognitionsScrollRef = ref(null)

// 计算属性
const hasNewTopics = computed(() => {
  return hotTopics.value.some(topic => topic.isNew)
})

const newTopicsCount = computed(() => {
  return hotTopics.value.filter(topic => topic.isNew).length
})

// 自动刷新定时器
let refreshTimer = null
  
  // 处理时间筛选变化
  const handleTimeFilterChange = async (newFilter) => {
    if (newFilter) {
      await hotRankingStore.fetchHotRankingWithComparison(newFilter)
    }
  }
  
  // 处理topic点击
  const handleTopicClick = (topic) => {
    emit('topic-click', topic)
  }
  
  // 处理认知点击
  const handleCognitionClick = (cognition) => {
    emit('cognition-click', cognition)
  }
  
  // 刷新数据
  const refreshData = async () => {
    await hotRankingStore.fetchHotRankingWithComparison(currentTimeFilter.value)
  }
  
  // 格式化更新时间
const formatUpdateTime = (time) => {
  if (!time) return ''
  const now = new Date()
  const updateTime = new Date(time)
  const diffMinutes = Math.floor((now - updateTime) / (1000 * 60))
  
  if (diffMinutes < 1) return '刚刚'
  if (diffMinutes < 60) return `${diffMinutes}分钟前`
  
  const diffHours = Math.floor(diffMinutes / 60)
  if (diffHours < 24) return `${diffHours}小时前`
  
  return updateTime.toLocaleDateString()
}

// 自定义滚动处理
const handleScroll = (event) => {
  if (!cognitionsScrollRef.value) return
  
  event.preventDefault()
  
  // 减小滚动步进，每次滚动大约25px
  const scrollStep = 25
  const delta = event.deltaY > 0 ? scrollStep : -scrollStep
  
  cognitionsScrollRef.value.scrollBy({
    top: delta,
    behavior: 'smooth'
  })
}
  
  // 组件挂载
onMounted(async () => {
  // 初始加载数据（带对比数据）
  await hotRankingStore.fetchHotRankingWithComparison()

  // 启动自动刷新 (5分钟)
  refreshTimer = setInterval(() => {
    hotRankingStore.fetchHotRankingWithComparison(currentTimeFilter.value)
  }, 5 * 60 * 1000)
  
  // 添加自定义滚动监听器
  await nextTick()
  if (cognitionsScrollRef.value) {
    cognitionsScrollRef.value.addEventListener('wheel', handleScroll, { passive: false })
  }
})
  
  // 组件卸载时清理定时器
onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
  
  // 移除滚动监听器
  if (cognitionsScrollRef.value) {
    cognitionsScrollRef.value.removeEventListener('wheel', handleScroll)
  }
})
  </script>
  
  <style scoped>
  .hot-ranking-container {
    margin-bottom: 20px;
  }
  
  .hot-ranking-card {
    border-radius: 12px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid rgba(0, 0, 0, 0.08);
    overflow: hidden;
  }
  
  .ranking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
    color: white;
  }
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .fire-icon {
    animation: flicker 2s infinite alternate;
  }
  
  @keyframes flicker {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
  }
  
  .ranking-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    color: white;
  }
  
  .live-indicator {
    animation: pulse 2s infinite;
  }
  
  @keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .time-toggle {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
  
  .time-toggle .v-btn {
    color: #333 !important;
    font-size: 12px;
    min-width: 40px;
    font-weight: 500;
    transition: all 0.2s ease;
  }
  
  .time-toggle .v-btn--active {
    background: rgba(102, 126, 234, 0.9) !important;
    color: white !important;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  }
  
  .more-btn {
    color: white !important;
    font-size: 12px;
    background: rgba(255, 255, 255, 0.15) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    transition: all 0.2s ease;
  }
  
  .more-btn:hover {
    background: rgba(255, 255, 255, 0.25) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    transform: translateY(-1px);
  }
  
  .ranking-content {
    padding: 16px 20px;
    min-height: 220px;
    max-height: 220px;
  }
  
  .loading-section, .error-section {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    height: 80px;
    color: #666;
  }
  
  .ranking-sections {
    display: flex;
    gap: 20px;
    height: 100%;
  }
  
  .topics-section {
    flex: 1;
  }
  
  .cognitions-section {
    flex: 1.8;
  }
  
  .section-divider {
    opacity: 0.3;
  }
  
  .section-header {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 12px;
  }
  
  .section-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
  }
  
  .cognition-count {
    font-size: 11px;
    font-weight: 500;
    color: #666;
    margin-left: 4px;
  }
  
  .topics-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 8px;
    height: 160px;
  }
  
  .cognitions-scrollable {
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: 160px;
  overflow-y: auto;
  padding-right: 4px;
  scroll-behavior: smooth;
  scroll-snap-type: y mandatory;
}
  
  /* 自定义滚动条 */
  .cognitions-scrollable::-webkit-scrollbar {
    width: 4px;
  }
  
  .cognitions-scrollable::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
  }
  
  .cognitions-scrollable::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
  }
  
  .cognitions-scrollable::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
  
  .update-time {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 8px;
    background: #f8fafc;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
  }
  
  .time-text {
    font-size: 11px;
    color: #666;
  }
  
  /* 响应式设计 */
  @media (max-width: 768px) {
    .ranking-header {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;
    }
    
    .header-right {
      width: 100%;
      justify-content: space-between;
    }
    
    .ranking-sections {
      flex-direction: column;
      gap: 16px;
    }
    
    .section-divider {
      display: none;
    }
    
    .topics-grid {
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(3, 1fr);
      height: 160px;
    }
    
    .cognitions-scrollable {
      height: 140px;
    }
    
    .ranking-content {
      min-height: 240px;
      max-height: 240px;
    }
  }
  </style> 