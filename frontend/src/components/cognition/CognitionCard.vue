<template>
  <div class="cognition-card"
    :class="{ 'synthesis-mode': isInSynthesisMode, 'selected': isSelected }"
    @click="handleCognitionClick"
  >
    <div v-if="isInSynthesisMode" class="selection-overlay">
      <v-icon v-if="isSelected" class="checkmark-icon" color="white" size="48">mdi-check-circle</v-icon>
      <v-icon v-else class="plus-icon" color="white" size="48">mdi-plus-circle-outline</v-icon>
    </div>
    <div v-if="!cognition.read_status || !cognition.read_status.is_read" class="unread-indicator"></div>
    <div class="card-content">
      <!-- <div v-if="authorName" class="author-sculpt-container">
        <div class="author-avatar-sculpt" :style="{ backgroundColor: getAvatarColor(authorName) }">
          <span class="avatar-initial">{{ authorInitial }}</span>
        </div>
        <div class="author-title-sculpt">
          <span class="author-name-text">{{ authorName }}</span>
        </div>
      </div> -->
      <div v-if="cognition.source in celebrityList" class="author-sculpt-container">
        <div class="author-avatar-sculpt" :style="{ backgroundImage: `url(${getCelebrityImageUrl(celebrityList[cognition.source].image)})` }"></div>
        <div class="author-title-sculpt">
          <span class="author-name-text">{{ cognition.source }}</span>
        </div>
      </div>
      <!-- 来源、验证和话题标签 -->
      <div class="tags-container">
        <!-- 验证/猜想标签 -->
        <div class="verification-tag" :class="getVerificationClass" v-if="getVerificationStatus">
          <v-icon size="12">{{ getVerificationIcon }}</v-icon>
          {{ getVerificationText }}
        </div>
        
        <!-- 来源标签 -->
        <div class="source-tag" v-if="cognition.source">
          <v-icon size="12">mdi-source-branch</v-icon>
          {{ cognition.source }}
        </div>

        <!-- 主要话题标签 -->
        <div v-if="cognition.primary_topic" class="topic-tag-small" @click.stop="$emit('topic-click', cognition.primary_topic)">
          <v-icon size="12">mdi-tag</v-icon>
          {{ cognition.primary_topic.replace(/-/g, ' ') }}
        </div>
      </div>

      <!-- 认知封面图片或摘要 -->
      <div class="cover-content-container">
        <!-- 统一的加载指示器 -->
        <div v-if="isLoading" class="loading-indicator">
          <v-progress-circular indeterminate color="primary" size="48"></v-progress-circular>
          <p>{{ loadingText }}</p>
        </div>
        
        <!-- 内容区域，在加载完成时显示 -->
        <div v-show="!isLoading">
          <!-- 如果图片成功加载，则显示图片 -->
          <img 
            v-if="coverUrl && isImageLoaded && !imageLoadError" 
            :src="coverUrl" 
            :alt="getDisplayText('abstract') || getDisplayText('question')" 
            class="cover-image"
          />
          
          <!-- 如果没有封面或图片加载失败，则显示摘要 -->
          <div v-else
               class="abstract" 
               :class="{ 
                 'english-text': isEnglishText,
                 'fallback': imageLoadError || imageLoadState === 'error',
                 ...getAbstractThemeClass()
               }">
            {{ getDisplayText('abstract') || getDisplayText('question') }}
          </div>
        </div>

        <!-- 隐藏的图片用于预加载和状态跟踪 -->
        <img v-if="coverUrl" :src="coverUrl" @load="handleImageLoad" @error="handleImageError" style="display: none;" />
      </div>
    </div>
    
    <!-- 底部操作栏 -->
    <div class="card-actions" @click.stop>
      <div class="actions-top-row">
        <div class="actions-left">
          <!-- 点赞 -->
          <div 
            class="vote-btn"
            :class="{ active: cognition.user_vote === 'like' }"
            @click="handleVote('like')"
          >
            <v-icon 
              size="18" 
              :color="cognition.user_vote === 'like' ? '#ff4757' : 'grey-darken-1'"
            >
              mdi-heart{{ cognition.user_vote === 'like' ? '' : '-outline' }}
            </v-icon>
            <span>{{ cognition.likes || cognition.like_count || 0 }}</span>
          </div>
          
          <!-- 评论数 -->
          <div class="comment-count">
            <v-icon size="18" color="grey-darken-1">mdi-comment-outline</v-icon>
            <span>{{ getCommentsCount(cognition.comments) }}</span>
          </div>
          
          <!-- 收藏按钮 -->
          <FavoriteButton
            :cognition-id="cognition.id"
            :favorite-status="cognition.favorite_status || { is_favorited: false, collections: [] }"
            @favorited="handleFavorited"
            @unfavorited="handleUnfavorited"
          />
          
          <!-- 已读状态按钮 -->
          <!-- <ReadStatusButton
            :cognition-id="cognition.id"
            :read-status="cognition.read_status || { is_read: false, read_at: null }"
            @read-status-changed="handleReadStatusChanged"
          /> -->
        </div>

        <div class="actions-right">
          <div class="publish-time">{{ formatTime(cognition.raw_at) }}</div>
        </div>
      </div>

      <div class="actions-bottom-row" v-if="authStore.isAdmin">
        <v-btn
          size="small"
          variant="outlined"
          color="primary"
          @click="handleEdit"
          class="admin-btn"
        >
          <v-icon size="16">mdi-pencil</v-icon>
          编辑
        </v-btn>
        <v-btn
          size="small"
          variant="outlined"
          color="error"
          @click="handleDelete"
          class="admin-btn"
        >
          <v-icon size="16">mdi-delete</v-icon>
          删除
        </v-btn>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, computed, ref, watch } from 'vue'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { useAuthStore } from '@/stores/authStore'
import FavoriteButton from './FavoriteButton.vue'
import ReadStatusButton from './ReadStatusButton.vue'
import { cognitionAPI } from '@/api/cognition'
import { Logger } from 'sass'
import { convertTOSImageUrl } from '@/api/documents'

// 新增：动态获取名人图片
function getCelebrityImageUrl(imageName) {
  // Vite处理动态资源路径的推荐方式
  // 确保图片放在 /src/assets/ 目录下
  return new URL(`../../assets/${imageName}`, import.meta.url).href;
}

// 图片加载状态
const imageLoadError = ref(false)
const isImageLoaded = ref(false)

const props = defineProps({
  cognition: {
    type: Object,
    required: true
  },
  language: {
    type: String,
    default: 'zh'
  },
  isInSynthesisMode: {
    type: Boolean,
    default: false
  },
  isSelected: {
    type: Boolean,
    default: false
  },
  getCoverImage: {
    type: Function,
    default: null
  },
  imageLoadState: {
    type: String,
    default: 'idle'
  }
})

const emit = defineEmits(['click', 'vote', 'edit', 'delete', 'topic-click', 'read-status-changed'])

const authStore = useAuthStore()

const authorName = computed(() => props.cognition.author_name || '');

const celebrityList = {
  "Sam Altman": {
    image: "sam.jpg"
  },
  "Elon Musk": {
    image: "musk.jpg"
  },
  "Andrej Karpathy": {
    image: 'karpathy.jpg'
  },
  "Jason Wei": {
    image: "jason.png"
  }
};

const authorInitial = computed(() => {
  if (!authorName.value) return '';
  const trimmedName = authorName.value.trim();
  if (!trimmedName) return '';
  const firstChar = trimmedName.charAt(0);
  if (/[a-zA-Z]/.test(firstChar)) {
    return firstChar.toUpperCase();
  }
  return firstChar;
});

function getAvatarColor(name) {
  if (!name) return '#cccccc'; // A neutral default color
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
    hash = hash & hash; // Ensure 32-bit integer
  }
  // Use HSL for a nice color palette
  const hue = hash % 360;
  return `hsl(${hue}, 55%, 50%)`;
}

const coverUrl = computed(() => getCoverImageUrl())

// 统一的加载状态
const isLoading = computed(() => {
  // 1. 父组件正在生成图片
  if (props.imageLoadState === 'loading') {
    return true;
  }
  // 2. 有图片URL，但尚未加载完成或失败
  if (coverUrl.value && !isImageLoaded.value && !imageLoadError.value) {
    return true;
  }
  return false;
});

const loadingText = computed(() => {
  if (props.imageLoadState === 'loading') {
    return '正在生成图片...';
  }
  return '正在加载图片...';
});

// 监视封面URL的变化，以重置加载状态
watch(coverUrl, (newUrl) => {
  if (newUrl) {
    isImageLoaded.value = false;
    imageLoadError.value = false;
  }
}, { immediate: true });

// 根据当前语言获取显示文本
function getDisplayText(field) {
  const zhField = `${field}_zh`
  const enField = `${field}_en`
  
  if (props.language === 'zh') {
    return props.cognition[zhField] || props.cognition[enField] || ''
  } else {
    return props.cognition[enField] || props.cognition[zhField] || ''
  }
}

// 验证状态相关计算属性
const getVerificationStatus = computed(() => {
  // 如果有tag字段，显示标签
  return props.cognition.tag && (props.cognition.tag === '验证' || props.cognition.tag === '猜想')
})

const getVerificationClass = computed(() => {
  if (props.cognition.tag === '验证') {
    return 'verified'
  } else if (props.cognition.tag === '猜想') {
    return 'hypothesis'
  }
  return ''
})

const getVerificationIcon = computed(() => {
  if (props.cognition.tag === '验证') {
    return 'mdi-check-circle'
  } else if (props.cognition.tag === '猜想') {
    return 'mdi-lightbulb-outline'
  }
  return ''
})

const getVerificationText = computed(() => {
  return props.cognition.tag || ''
})

// 检测是否主要是英文文本
const isEnglishText = computed(() => {
  const text = getDisplayText('abstract') || getDisplayText('question') || ''
  if (!text) return false
  
  // 统计英文字符和中文字符的比例
  const englishChars = text.match(/[a-zA-Z]/g) || []
  const chineseChars = text.match(/[\u4e00-\u9fff]/g) || []
  
  // 如果英文字符数量大于中文字符数量，认为是英文文本
  return englishChars.length > chineseChars.length
})

function handleVote(voteType) {
  emit('vote', props.cognition.id, voteType)
}

function handleEdit() {
  emit('edit', props.cognition)
}

function handleDelete() {
  const displayText = getDisplayText('abstract') || getDisplayText('question')
  if (confirm(`确定要删除认知"${displayText}"吗？此操作不可撤销。`)) {
    emit('delete', props.cognition.id)
  }
}

function handleFavorited() {
  // 可以在这里添加一些视觉反馈
  console.log('认知已收藏')
}

function handleUnfavorited() {
  // 可以在这里添加一些视觉反馈
  console.log('认知已取消收藏')
}

// 处理已读状态改变
function handleReadStatusChanged(data) {
  // 向父组件传递已读状态变化事件
  emit('read-status-changed', data)
}

// 处理认知卡片点击（自动设置为已读）
async function handleCognitionClick() {
  try {
    // 如果当前认知未读，自动设置为已读
    const currentReadStatus = props.cognition.read_status || { is_read: false, read_at: null }
    if (!currentReadStatus.is_read) {
      await cognitionAPI.setReadStatus(props.cognition.id, true)
      
      // 触发已读状态改变事件
      emit('read-status-changed', {
        cognitionId: props.cognition.id,
        readStatus: { is_read: true, read_at: new Date() }
      })
    }
    
    // 发出点击事件
    emit('click', props.cognition)
  } catch (error) {
    console.error('设置已读状态失败:', error)
    // 即使设置已读失败，也继续执行点击事件
    emit('click', props.cognition)
  }
}

function formatTime(dateString) {
  try {
    if (!dateString) return '未知时间'
    const date = new Date(dateString)
    return formatDistanceToNow(date, { 
      addSuffix: true, 
      locale: zhCN 
    })
  } catch (error) {
    return '未知时间'
  }
}

function getCommentsCount(comments) {
  return Array.isArray(comments) ? comments.length : 0
}

// 获取封面图片URL
function getCoverImageUrl() {
  // // 1. 优先检查认知数据中是否有封面图片URL字段
  // if (props.cognition.cover_url && props.cognition.source != "认知合成") {
  //   return convertTOSImageUrl(props.cognition.cover_url)
  // }
  
  // // 2. 如果有动态生成的图片，使用props传入的函数处理
  // if (props.getCoverImage) {
  //   return props.getCoverImage(props.cognition)
  // }
  
  // 3. 如果没有封面图片，返回null，将显示摘要作为后备
  return null
}

// 图片加载成功处理
function handleImageLoad() {
  isImageLoaded.value = true;
}

// 处理图片加载错误
function handleImageError() {
  console.log('封面图片加载失败，将自动切换到显示摘要文本')
  imageLoadError.value = true
}

// 根据认知类型获取摘要主题样式
function getAbstractThemeClass() {
  if (imageLoadError.value || props.imageLoadState === 'error') {
    return {} // 失败状态使用fallback样式
  }
  
  const tag = props.cognition.tag
  const topic = props.cognition.primary_topic || ''
  
  // 根据标签选择主题
  if (tag === '验证') {
    return { 'theme-research': true }
  } else if (tag === '猜想') {
    return { 'theme-hypothesis': true }
  } else if (tag === '洞察') {
    return { 'theme-insight': true }
  }
  
  // 根据主题关键词选择
  const topicLower = topic.toLowerCase()
  if (topicLower.includes('ai') || topicLower.includes('artificial') || topicLower.includes('intelligence')) {
    return { 'theme-ai': true }
  } else if (topicLower.includes('tech') || topicLower.includes('code') || topicLower.includes('algorithm')) {
    return { 'theme-tech': true }
  }
  
  // 默认主题
  return {}
}
</script>

<style scoped>
.cognition-card {
  background: linear-gradient(145deg, #ffffff 0%, #fafbff 100%);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(102, 103, 171, 0.1);
  padding: 24px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
}

.cognition-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(102, 103, 171, 0.15);
}

.cognition-card.synthesis-mode {
  cursor: pointer;
}

.cognition-card.selected {
  border-color: #667eea;
  box-shadow: 0 12px 40px rgba(102, 103, 171, 0.25);
}

.selection-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0);
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 1;
}

.cognition-card.synthesis-mode:hover .selection-overlay {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.3);
}

.cognition-card.selected .selection-overlay {
  opacity: 1;
  background-color: rgba(102, 126, 234, 0.7);
}

.selection-overlay > .v-icon {
  transform: scale(0.5);
  opacity: 0;
  transition: all 0.3s ease;
}

.cognition-card.synthesis-mode:hover .selection-overlay > .v-icon,
.cognition-card.selected .selection-overlay > .v-icon {
  transform: scale(1);
  opacity: 1;
}

.card-content, .card-actions {
  transition: filter 0.3s ease;
  z-index: 0;
}

.card-content {
  margin-bottom: 20px;
}

.cover-content-container {
  position: relative;
  width: 100%;
  margin-bottom: 16px;
}

.cover-image {
  width: 100%;
  height: auto;
  border-radius: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: block;
  object-fit: cover;
}

.cover-image:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.abstract {
  font-size: 18px;
  font-weight: 600;
  line-height: 1.6;
  display: flow-root;
  padding: 24px;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9ff 100%);
  border-radius: 16px;
  border: 1px solid #e8ecf3;
  color: #2c3e50;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(102, 103, 171, 0.08);
}

.abstract:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 103, 171, 0.12);
  border-color: #d1d9e6;
}

.abstract.english-text {
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  letter-spacing: -0.5px;
}

/* 图片加载失败的后备样式 */
.abstract.fallback {
  background: linear-gradient(145deg, #fff5f5 0%, #fed7d7 100%);
  border: 2px dashed #feb2b2;
  text-align: center;
  color: #c53030;
}

.abstract.fallback::before {
  color: #e53e3e;
  opacity: 0.8;
}

/* 统一设计 */

.abstract:hover::before {
  opacity: 0.8;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  height: 200px;
  background: linear-gradient(135deg, #f8f9ff 0%, #e6efff 100%);
  border-radius: 12px;
  border: 2px dashed #667eea;
}

.loading-indicator p {
  margin-top: 16px;
  color: #667eea;
  font-size: 14px;
  font-weight: 500;
}

.verification-tag, .source-tag, .topic-tag-small {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.verification-tag.verified {
  background: rgba(46, 204, 113, 0.1);
  color: #27ae60;
  border: 1px solid rgba(46, 204, 113, 0.3);
}

.verification-tag.hypothesis {
  background: rgba(255, 165, 2, 0.1);
  color: #d35400;
  border: 1px solid rgba(255, 165, 2, 0.3);
}

.source-tag {
  background: rgba(255, 105, 135, 0.12);
  color: #ff6987;
  border: 1px solid rgba(255, 105, 135, 0.3);
}

.topic-tag-small {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 1px solid rgba(102, 126, 234, 0.3);
}

.topic-tag-small:hover {
  transform: translateY(-1px);
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.5);
}

.card-actions {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #eef2f7;
}

.actions-top-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.actions-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.actions-right {
  display: flex;
  align-items: center;
}

.publish-time {
  font-size: 12px;
  color: #90a4ae;
}

.vote-btn, .comment-count {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #546e7a;
  font-size: 14px;
  cursor: pointer;
  transition: color 0.2s ease;
}

.vote-btn:hover {
  color: #2c3e50;
}

.vote-btn.active .v-icon {
  animation: bounce 0.5s;
}

@keyframes bounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.3); }
}

.actions-bottom-row {
  display: flex;
  gap: 8px;
}

.admin-btn {
  gap: 6px;
  height: 28px !important;
  font-size: 12px;
  text-transform: none;
}

/* 新增：雕塑感环绕布局样式 */
.author-sculpt-container {
  float: left;
  width: 70px;
  position: relative;
  top: -5px;
  margin-right: 16px;
  margin-bottom: 5px;
  text-align: center;
  shape-outside: circle(45%);
}

.author-avatar-sculpt {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 3px solid #fff;
  box-shadow: 0 4px 12px rgba(0,0,0,0.12);
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 24px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.avatar-initial {
  line-height: 1; /* 帮助垂直对齐 */
}

.author-title-sculpt {
  display: flex;
  flex-direction: column;
  align-items: center;
  line-height: 1.2;
}

.author-name-text {
  font-size: 13px;
  font-weight: 700;
  color: #2c3e50;
  max-width: 64px; /* 容器宽度减去内边距 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.unread-indicator {
  position: absolute;
  top: 24px;
  right: 24px;
  width: 10px;
  height: 10px;
  background-color: #667eea;
  border-radius: 50%;
  z-index: 2;
}

.unread-indicator::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #667eea;
  animation: pulse 2s infinite;
  box-shadow: 0 0 5px rgba(102, 126, 234, 0.8);
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.8);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}
</style> 