<template>
  <div class="favorite-btn-container" @click.stop>
    <div 
      class="favorite-btn"
      :class="{ favorited: isFavorited }"
      @click="handleFavoriteClick"
    >
      <v-icon 
        size="18" 
        :color="isFavorited ? '#FFD700' : 'grey-darken-1'"
      >
        {{ isFavorited ? 'mdi-star' : 'mdi-star-outline' }}
      </v-icon>
    </div>
    
    <!-- 收藏夹选择弹窗 -->
    <v-dialog v-model="showDialog" max-width="450px">
      <v-card>
        <v-card-title class="dialog-title">
          <v-icon class="title-icon">mdi-star</v-icon>
          选择收藏到的认知库
        </v-card-title>
        
        <v-card-text>
          <div class="collections-list">
            <div 
              v-for="collection in collections" 
              :key="collection.id"
              class="collection-item"
              @click="selectCollection(collection)"
            >
              <div class="collection-info">
                <div class="collection-name">
                  <v-icon size="16" class="collection-icon">
                    {{ collection.is_default ? 'mdi-heart' : 'mdi-folder' }}
                  </v-icon>
                  {{ collection.name }}
                </div>
                <div class="collection-count">{{ collection.cognition_count }} 个认知</div>
              </div>
              <!-- 多选框 -->
              <v-checkbox
                :model-value="isCollectionSelected(collection.id)"
                color="primary"
                hide-details
                density="compact"
                @click.stop="selectCollection(collection)"
              />
            </div>
            
            <!-- 创建新认知库 -->
            <div class="create-collection" @click="showCreateForm = !showCreateForm">
              <v-icon size="16">mdi-plus</v-icon>
              创建新认知库
            </div>
            
            <!-- 创建认知库表单 -->
            <div v-if="showCreateForm" class="create-form">
              <v-text-field
                v-model="newCollectionName"
                label="认知库名称"
                variant="outlined"
                density="compact"
                hide-details
                @keyup.enter="createCollection"
              />
              <div class="form-actions">
                <v-btn size="small" @click="showCreateForm = false">取消</v-btn>
                <v-btn 
                  size="small" 
                  color="primary" 
                  @click="createCollection"
                  :disabled="!newCollectionName.trim()"
                >
                  创建
                </v-btn>
              </div>
            </div>
          </div>
        </v-card-text>
        
        <v-card-actions>
          <div class="selected-info">
            已选择 {{ selectedCollections.length }} 个认知库
          </div>
          <v-spacer />
          <v-btn text @click="showDialog = false">取消</v-btn>
          <v-btn 
            color="primary" 
            @click="confirmFavorite"
            :disabled="false"
          >
            {{ selectedCollections.length === 0 ? '取消收藏' : '确认收藏' }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useCognitionStore } from '@/stores/cognition'

const props = defineProps({
  cognitionId: {
    type: String,
    required: true
  },
  favoriteStatus: {
    type: Object,
    default: () => ({ is_favorited: false, collections: [] })
  }
})

const emit = defineEmits(['favorited', 'unfavorited'])

const cognitionStore = useCognitionStore()

// 响应式数据
const showDialog = ref(false)
const showCreateForm = ref(false)
const collections = ref([])
const selectedCollections = ref([])
const favoriteStatus = ref({ is_favorited: false, collections: [] })
const newCollectionName = ref('')

// 计算属性
const isFavorited = computed(() => favoriteStatus.value.is_favorited)

// 方法
async function loadCollections() {
  try {
    collections.value = await cognitionStore.getUserCollections()
  } catch (error) {
    console.error('加载收藏夹失败:', error)
  }
}

function isCollectionSelected(collectionId) {
  return selectedCollections.value.includes(collectionId)
}

function selectCollection(collection) {
  const index = selectedCollections.value.indexOf(collection.id)
  if (index > -1) {
    selectedCollections.value.splice(index, 1)
  } else {
    selectedCollections.value.push(collection.id)
  }
}

async function createCollection() {
  if (!newCollectionName.value.trim()) return
  
  try {
    const newCollection = await cognitionStore.createCollection({
      name: newCollectionName.value.trim(),
      description: ''
    })
    
    collections.value.unshift(newCollection)
    newCollectionName.value = ''
    showCreateForm.value = false
  } catch (error) {
    console.error('创建收藏夹失败:', error)
  }
}

async function confirmFavorite() {
  try {
    // 获取当前收藏状态
    const currentCollections = new Set(favoriteStatus.value.collections)
    const newCollections = new Set(selectedCollections.value)
    
    // 需要添加的收藏夹
    const toAdd = [...newCollections].filter(id => !currentCollections.has(id))
    
    // 需要移除的收藏夹
    const toRemove = [...currentCollections].filter(id => !newCollections.has(id))
    
    // 执行添加操作
    for (const collectionId of toAdd) {
      await cognitionStore.addToFavorites(props.cognitionId, collectionId)
    }
    
    // 执行移除操作
    for (const collectionId of toRemove) {
      await cognitionStore.removeFromFavorites(props.cognitionId, collectionId)
    }
    
    // 更新状态
    favoriteStatus.value.is_favorited = selectedCollections.value.length > 0
    favoriteStatus.value.collections = [...selectedCollections.value]
    
    showDialog.value = false
    
    // 触发事件
    if (favoriteStatus.value.is_favorited) {
      emit('favorited')
    } else {
      emit('unfavorited')
    }
    
  } catch (error) {
    console.error('收藏操作失败:', error)
  }
}

async function handleFavoriteClick() {
  // 如果还没有加载收藏夹列表，先加载
  if (collections.value.length === 0) {
    await loadCollections()
  }
  
  showDialog.value = true
}

// 生命周期
onMounted(async () => {
  // 使用传入的收藏状态，避免额外的API请求
  favoriteStatus.value = { ...props.favoriteStatus }
  selectedCollections.value = [...props.favoriteStatus.collections]
  
  // 只在需要时加载收藏夹列表
  if (showDialog.value) {
    await loadCollections()
  }
})

// 监听props变化
watch(() => props.favoriteStatus, (newStatus) => {
  favoriteStatus.value = { ...newStatus }
  selectedCollections.value = [...newStatus.collections]
}, { deep: true })
</script>

<style scoped>
.favorite-btn-container {
  display: inline-block;
}

.favorite-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
}

.favorite-btn:hover {
  background: rgba(0, 0, 0, 0.05);
}

.favorite-btn.favorited {
  background: rgba(255, 215, 0, 0.1);
}

.dialog-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #2c3e50;
}

.title-icon {
  color: #FFD700;
}

.collections-list {
  max-height: 300px;
  overflow-y: auto;
}

.collection-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.collection-item:hover {
  background: rgba(102, 126, 234, 0.05);
}

.collection-info {
  flex: 1;
}

.collection-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}

.collection-icon {
  color: #667eea;
}

.collection-count {
  font-size: 12px;
  color: #7f8c8d;
}

.create-collection {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  color: #667eea;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-weight: 500;
  margin-top: 8px;
  border: 1px dashed #667eea;
}

.create-collection:hover {
  background: rgba(102, 126, 234, 0.05);
}

.create-form {
  margin-top: 12px;
  padding: 12px;
  background: rgba(247, 249, 252, 0.8);
  border-radius: 8px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 12px;
}

.selected-info {
  font-size: 14px;
  color: #667eea;
  font-weight: 500;
}
</style> 