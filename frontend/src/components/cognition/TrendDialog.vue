<template>
  <v-dialog
    v-model="dialogVisible"
    max-width="1200"
    persistent
    scrollable
  >
    <v-card>
      <v-card-title class="d-flex justify-space-between align-center">
        <div class="trend-title">
          <v-icon color="primary" class="mr-2">mdi-trending-up</v-icon>
          <span>{{ getTimeRangeTitle() }}热点趋势</span>
        </div>
        <v-btn
          icon="mdi-close"
          variant="text"
          @click="$emit('update:modelValue', false)"
        />
      </v-card-title>

      <v-divider />

      <v-card-text class="pa-6">
        <div v-if="loading" class="d-flex justify-center align-center" style="height: 400px;">
          <div class="text-center">
            <v-progress-circular indeterminate size="64" color="primary" />
            <div class="mt-3 text-h6">加载趋势数据中...</div>
          </div>
        </div>

        <div v-else-if="error" class="d-flex justify-center align-center" style="height: 400px;">
          <div class="text-center">
            <v-icon size="64" color="error">mdi-alert-circle</v-icon>
            <div class="mt-3 text-h6 text-error">{{ error }}</div>
            <v-btn
              color="primary"
              class="mt-3"
              @click="loadTrendData"
            >
              重新加载
            </v-btn>
          </div>
        </div>

        <div v-else-if="!trendData || trendData.length === 0" class="d-flex justify-center align-center" style="height: 400px;">
          <div class="text-center">
            <v-icon size="64" color="grey">mdi-chart-line-variant</v-icon>
            <div class="mt-3 text-h6 text-grey">暂无趋势数据</div>
            <div class="mt-2 text-body-2 text-grey">该时间段内没有足够的数据进行趋势分析</div>
          </div>
        </div>

        <div v-else>
          <!-- 图表容器 -->
          <div ref="chartRef" class="chart-container"></div>
          
          <!-- 新增：三个月趋势图表 -->
          <div ref="quarterChartRef" class="big-chart-container" style="margin-top: 24px;"></div>
          
          <!-- 详细数据表格 -->
          <v-card class="mt-6" elevation="2">
            <v-card-title class="text-h6">
              <v-icon class="mr-2">mdi-table</v-icon>
              详细统计数据
            </v-card-title>
            <v-card-text>
              <v-data-table
                :headers="tableHeaders"
                :items="trendData"
                :items-per-page="10"
                class="elevation-0"
                density="compact"
              >
                <template v-slot:item.topic="{ item }">
                  <v-chip
                    :color="getTopicColor(item.topic)"
                    size="small"
                    class="ma-1"
                  >
                    {{ item.topic }}
                  </v-chip>
                </template>
                <template v-slot:item.percentage="{ item }">
                  <div class="d-flex align-center">
                    <v-progress-linear
                      :model-value="item.percentage"
                      height="6"
                      :color="getTopicColor(item.topic)"
                      class="mr-2"
                      style="width: 60px;"
                    />
                    <span class="text-body-2">{{ item.percentage.toFixed(1) }}%</span>
                  </div>
                </template>
              </v-data-table>
            </v-card-text>
          </v-card>
        </div>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { cognitionAPI } from '@/api/cognition'
import { provideSelection } from 'vuetify/lib/components/VDataTable/composables/select'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  timeFilter: {
    type: String,
    default: 'all'
  },
  selectedTopics: {
    type: Array,
    default: () => []
  },
  searchScope: {
    type: String,
    default: 'all'
  },
  sourceFilter: {
    type: String,
    default: 'all'
  }
})

const emit = defineEmits(['update:modelValue'])

// 响应式数据
const loading = ref(false)
const error = ref('')
const trendData = ref([])
const chartRef = ref(null)
let chartInstance = null
const quarterChartRef = ref(null)
let quarterChartInstance = null
const quarterTrendData = ref([])
const rawCognitions = ref([])

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表格表头
const tableHeaders = [
  { title: 'Topic', key: 'topic', sortable: true },
  { title: '数量', key: 'count', sortable: true },
  { title: '占比', key: 'percentage', sortable: true },
  { title: '趋势', key: 'trend', sortable: false }
]

// 颜色映射
const topicColors = {
  'Architecture': '#FF6B6B',
  'LLMOps': '#4ECDC4',
  'Reasoning': '#45B7D1',
  'Agent': '#96CEB4',
  'System Efficiency': '#FFEAA7',
  'RL Scaling': '#DDA0DD',
  'Safety & Security': '#FD79A8'
}

// 方法
const getTimeRangeTitle = () => {
  const timeMap = {
    'day': '最近一天',
    'week': '最近一周',
    'month': '最近一个月',
    'all': '全部时间'
  }
  return timeMap[props.timeFilter] || '全部时间'
}

const getTopicColor = (topic) => {
  // 首先检查是否是主要topic
  if (topicColors[topic]) {
    return topicColors[topic]
  }
  
  // 生成基于topic名称的颜色
  let hash = 0
  for (let i = 0; i < topic.length; i++) {
    hash = topic.charCodeAt(i) + ((hash << 5) - hash)
  }
  const hue = Math.abs(hash % 360)
  return `hsl(${hue}, 70%, 60%)`
}

const loadTrendData = async () => {
  if (!dialogVisible.value) return

  loading.value = true
  error.value = ''

  try {
    const params = {
      time_filter: props.timeFilter,
      search_scope: props.searchScope,
      source_filter: props.sourceFilter
    }
    if (props.selectedTopics.length > 0) {
      params.topics = props.selectedTopics
    }

    const response = await cognitionAPI.getTrendStats(params)
    if (response && response.data) {
      trendData.value = response.data.map(item => ({
        topic: item.topic || '未知',
        count: Number(item.count) || 0,
        percentage: Number(item.percentage) || 0,
        trend: item.trend || 'stable'
      })).filter(item => item.count > 0)
      rawCognitions.value = response.raw_cognitions || []
      await nextTick()
      setTimeout(() => {
        renderChart()
        renderQuarterChart()
      }, 50)
    } else {
      trendData.value = []
      rawCognitions.value = []
    }
  } catch (err) {
    error.value = '加载趋势数据失败，请稍后重试'
    trendData.value = []
    rawCognitions.value = []
  } finally {
    loading.value = false
  }
}

const loadQuarterTrendData = async () => {
  try {
    const params = {
      time_filter: props.timeFilter,
      search_scope: props.searchScope,
      source_filter: props.sourceFilter
    }
    if (props.selectedTopics.length > 0) {
      params.topics = props.selectedTopics
    }
    const response = await cognitionAPI.getTrendStats(params)
    if (response && response.quarter_stats) {
      // quarter_stats: [{ quarter: "2024-Q2", count: 10, items: [...] }, ...]
      quarterTrendData.value = response.quarter_stats.sort((a, b) => a.quarter.localeCompare(b.quarter))
      // 合并所有季度的 items 作为 rawCognitions
      rawCognitions.value = response.quarter_stats.flatMap(q => q.items)
      console.log('quarterTrendData:', quarterTrendData.value)
      console.log('rawCognitions:', rawCognitions.value)
    } else {
      quarterTrendData.value = []
      rawCognitions.value = []
      console.log('无数据')
    }
  } catch (err) {
    quarterTrendData.value = []
    rawCognitions.value = []
    console.error('loadQuarterTrendData error:', err)
  }
}

const renderChart = () => {
  if (!chartRef.value || !trendData.value.length) {
    console.log('图表渲染条件不满足:', { chartRef: !!chartRef.value, dataLength: trendData.value.length })
    return
  }

  console.log('开始渲染图表:', trendData.value)

  // 初始化图表
  if (chartInstance) {
    chartInstance.dispose()
  }
  
  try {
    chartInstance = echarts.init(chartRef.value)
    console.log('ECharts实例创建成功')

    // 准备图表数据
    const chartData = trendData.value.slice(0, 15) // 显示前15个
    const topics = chartData.map(item => item.topic)
    const counts = chartData.map(item => item.count)
    const colors = chartData.map(item => getTopicColor(item.topic))

    console.log('图表数据:', { topics, counts, colors })

    // 图表配置
    const option = {
      title: {
        text: `${getTimeRangeTitle()}Topic分布统计`,
        left: 'center',
        top: 15,
        textStyle: {
          fontSize: 20,
          fontWeight: 'bold',
          color: '#2c3e50'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#ddd',
        borderWidth: 1,
        textStyle: {
          color: '#333',
          fontSize: 13
        },
        formatter: (params) => {
          const data = params[0]
          const total = trendData.value.reduce((sum, item) => sum + item.count, 0)
          const percentage = ((data.value / total) * 100).toFixed(1)
          return `
            <div style="font-weight: bold; margin-bottom: 6px; color: #2c3e50; font-size: 14px;">${data.name}</div>
            <div style="margin-bottom: 2px;">数量: <span style="color: ${data.color}; font-weight: bold;">${data.value}</span></div>
            <div>占比: <span style="color: ${data.color}; font-weight: bold;">${percentage}%</span></div>
          `
        }
      },
      grid: {
        left: '7%',
        right: '4%',
        bottom: '35%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: topics,
        axisLabel: {
          interval: 0,
          rotate: 45,
          fontSize: 14,
          color: '#333',
          fontWeight: 'bold',
          margin: 18,
          formatter: (value) => {
            if (value.length > 15) {
              return value.substring(0, 13) + '...';
            }
            return value.replace(/-/g, ' ');
          }
        },
        axisTick: {
          alignWithLabel: true,
          length: 8
        },
        axisLine: {
          lineStyle: {
            color: '#333',
            width: 2
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '认知数量',
        nameLocation: 'middle',
        nameGap: 60,
        nameTextStyle: {
          fontSize: 14,
          color: '#333',
          fontWeight: 'bold'
        },
        axisLabel: {
          fontSize: 12,
          color: '#333',
          fontWeight: '500'
        },
        splitLine: {
          lineStyle: {
            color: '#e0e0e0',
            type: 'dashed'
          }
        },
        axisLine: {
          lineStyle: {
            color: '#333',
            width: 2
          }
        }
      },
      series: [{
        name: '认知数量',
        type: 'bar',
        data: counts.map((count, index) => ({
          value: count,
          itemStyle: {
            color: colors[index],
            borderRadius: [4, 4, 0, 0]
          }
        })),
        barWidth: '60%',
        label: {
          show: true,
          position: 'top',
          fontSize: 12,
          color: '#2c3e50',
          fontWeight: 'bold',
          distance: 5
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }

    console.log('图表配置:', option)
    chartInstance.setOption(option)
    console.log('图表渲染完成')

    // 强制刷新图表尺寸
    setTimeout(() => {
      if (chartInstance) {
        chartInstance.resize()
        console.log('图表尺寸已刷新')
      }
    }, 100)

  } catch (error) {
    console.error('图表渲染失败:', error)
  }
}

function getQuarterStr(date) {
  // date: Date对象
  const y = date.getFullYear()
  const m = date.getMonth()
  const q = Math.floor(m / 3) + 1
  return `${y}Q${q}`
}

const renderQuarterChart = () => {
  if (!quarterChartRef.value || !rawCognitions.value.length) return
  if (quarterChartInstance) quarterChartInstance.dispose()
  quarterChartInstance = echarts.init(quarterChartRef.value)

  // 1. 统计每季度每个topic的数量
  const quarterTopicCount = {} // {quarter: {topic: count}}
  const allQuartersSet = new Set()
  rawCognitions.value.forEach(item => {
    let date = item.raw_at ? new Date(item.raw_at) : null
    if (!date || isNaN(date.getTime())) return
    const quarter = getQuarterStr(date)
    allQuartersSet.add(quarter)
    let topics = []
    if (item.primary_topic) topics.push(item.primary_topic)
    if (item.related_topic) {
      if (Array.isArray(item.related_topic)) topics.push(...item.related_topic)
      else topics.push(item.related_topic)
    }
    topics.forEach(topic => {
      if (!topic) return
      if (!quarterTopicCount[quarter]) quarterTopicCount[quarter] = {}
      quarterTopicCount[quarter][topic] = (quarterTopicCount[quarter][topic] || 0) + 1
    })
  })

  const quarters = Array.from(allQuartersSet).sort()

  // 2. 统计每季度前5名topic合集
  const topTopicsSet = new Set()
  quarters.forEach(q => {
    const topicCount = quarterTopicCount[q] || {}
    Object.entries(topicCount)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .forEach(([topic]) => topTopicsSet.add(topic))
  })
  const filteredTopics = Array.from(topTopicsSet)

  // 3. 全局热度排序（用于默认高亮）
  const globalTopicCount = {}
  filteredTopics.forEach(topic => {
    globalTopicCount[topic] = 0
    quarters.forEach(q => {
      globalTopicCount[topic] += (quarterTopicCount[q]?.[topic] || 0)
    })
  })
  const sortedTopics = filteredTopics.sort((a, b) => globalTopicCount[b] - globalTopicCount[a])

  // 4. 默认高亮前5个
  const N = 5
  const selected = {}
  sortedTopics.forEach((topic, idx) => {
    selected[topic] = idx < N
  })

  // 5. 统计每季度总认知数
  const quarterAllTotals = {}
  quarters.forEach(q => {
    quarterAllTotals[q] = Object.values(quarterTopicCount[q] || {}).reduce((a, b) => a + b, 0)
  })

  // 6. 生成高区分度颜色
  const colorPalette = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#FFD166', '#A259F7', '#43AA8B', '#F76E11', '#3A86FF',
    '#FF006E', '#8338EC', '#FFB627', '#06D6A0', '#EF476F', '#118AB2', '#073B4C', '#F7B801',
    '#D7263D', '#1B9AAA', '#F46036', '#2E294E', '#E71D36', '#FF9F1C', '#1982C4', '#6A4C93',
    '#B5E48C', '#F9C74F', '#F9844A', '#43AA8B', '#577590', '#F3722C', '#277DA1', '#90BE6D'
  ]
  const topicColorMap = {}
  const total = sortedTopics.length
  const hStep = 137 // 质数跳步
  const sList = [70, 80, 60] // 饱和度分层
  const lList = [55, 45, 65] // 亮度分层
  sortedTopics.forEach((topic, idx) => {
    if (idx < colorPalette.length) {
      topicColorMap[topic] = colorPalette[idx]
    } else {
      // 多层跳变，避免相近
      const hue = (hStep * (idx - colorPalette.length)) % 360
      const s = sList[Math.floor((idx - colorPalette.length) / 24) % sList.length]
      const l = lList[Math.floor((idx - colorPalette.length) / 8) % lList.length]
      topicColorMap[topic] = `hsl(${hue}, ${s}%, ${l}%)`
    }
  })

  // 7. 构造 series，只画filteredTopics，指定颜色
  const series = sortedTopics.map(topic => ({
    name: topic,
    type: 'line',
    data: quarters.map(q => {
      const total = quarterAllTotals[q] || 1
      const count = (quarterTopicCount[q]?.[topic]) || 0
      return +(count / total * 100).toFixed(2)
    }),
    label: { show: false },
    lineStyle: { color: topicColorMap[topic] },
    itemStyle: { color: topicColorMap[topic] }
  }))

  // 8. 图表配置
  const legendRows = Math.ceil(sortedTopics.length / 6)
  const gridTop = 120 + legendRows * 25

  const option = {
    color: sortedTopics.map(topic => topicColorMap[topic]),
    title: {
      text: `季度热门Topic占比折线图`,
      left: 'center',
      top: 15,
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#2c3e50'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' },
      formatter: function(params) {
        const quarter = params[0].name
        const topicCount = quarterTopicCount[quarter] || {}
        const top5 = Object.entries(topicCount)
          .sort((a, b) => b[1] - a[1])
          .slice(0, 5)
        const total = Object.values(topicCount).reduce((a, b) => a + b, 0) || 1
        let html = `<b>${quarter}</b><br/>`
        top5.forEach(([topic, count]) => {
          const color = topicColorMap[topic] || '#bbb'
          const percent = ((count / total) * 100).toFixed(2)
          html += `<span style="display:inline-block;margin-right:8px;width:10px;height:10px;border-radius:50%;background:${color};vertical-align:middle"></span> ${topic}: ${percent}%<br/>`
        })
        html += `<span style="color:#888;font-size:12px;">
        显示该季度前5名topic的占比。折线未全部画出，<br/>
        可点击上方对应Topic图例画出或抹去折线。
        </span>`
        return html
      }
    },
    legend: {
      data: sortedTopics,
      selected,
      top: 65,
      itemGap: 20
    },
    graphic: [
      {
        type: 'text',
        right: 10,
        bottom: 80,
        style: {
          text: '注：至少一个季度占比前5才会在图例中，默认高亮全时段占比前' + N,
          font: '11px sans-serif',
          fill: '#888',
          textAlign: 'right'
        },
        bounding: 'raw',
        position: ['100%', '100%']
      }
    ],
    grid: { left: '7%', right: '4%', bottom: '15%', top: gridTop, containLabel: true },
    xAxis: {
      type: 'category',
      data: quarters,
      axisLabel: { fontSize: 13, color: '#333', fontWeight: 'bold' }
    },
    yAxis: {
      type: 'value',
      name: '季度占比（%）',
      min: 0,
      max: 'dataMax',
      interval: 20,
      axisLabel: { formatter: '{value}%' },
      nameLocation: 'middle',
      nameGap: 40,
      nameTextStyle: { fontSize: 13, color: '#333', fontWeight: 'bold' }
    },
    series
  }

  quarterChartInstance.setOption(option)
  setTimeout(() => { if (quarterChartInstance) quarterChartInstance.resize() }, 100)
}

// 监听对话框状态变化
watch(dialogVisible, async (newValue) => {
  if (newValue) {
    loadTrendData()
    await loadQuarterTrendData()
    await nextTick()
    renderQuarterChart()
  } else {
    // 对话框关闭时清理图表
    if (chartInstance) {
      chartInstance.dispose()
      chartInstance = null
    }
  }
})

// 窗口resize处理函数
const handleResize = () => {
  if (chartInstance) chartInstance.resize()
  if (quarterChartInstance) quarterChartInstance.resize()
}

// 组件挂载时添加resize监听器
onMounted(() => {
  // 添加窗口resize监听器
  window.addEventListener('resize', handleResize)
})

// 组件卸载时清理图表和监听器
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  if (quarterChartInstance) {
    quarterChartInstance.dispose()
    quarterChartInstance = null
  }
})
</script>

<style scoped>
.trend-title {
  display: flex;
  align-items: center;
  font-size: 1.25rem;
  font-weight: 600;
}

.v-data-table {
  border-radius: 8px;
}

.v-card {
  border-radius: 12px;
}

.v-dialog > .v-overlay__content {
  margin: 24px;
}

.chart-container {
  width: 100%;
  height: 450px;
  min-height: 450px;
  background: linear-gradient(to bottom, #fafafa, #f5f5f5);
  border-radius: 12px;
  border: 2px solid #e8e8e8;
  margin-bottom: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  position: relative;
}

.big-chart-container {
  width: 100%;
  height: 700px;
  min-height: 700px;
  background: linear-gradient(to bottom, #fafafa, #f5f5f5);
  border-radius: 12px;
  border: 2px solid #e8e8e8;
  margin-bottom: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  position: relative;
}
</style> 