<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>认知平台封面 - Safety Alignment</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
    />
    <style>
      @import url("https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@100;300;400;700;900&display=swap");
      body {
        font-family: "Noto Sans SC", sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      /* Forcing 9:16 aspect ratio for preview */
      .aspect-9-16 {
        width: 100%;
        padding-top: 177.77%; /* 16/9 = 1.7777 */
        position: relative;
      }
      .aspect-9-16 > .content {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
      }
      /* A helper class for slight rotation */
      .rotate-n15 {
        transform: rotate(-15deg);
      }
    </style>
  </head>
  <body class="bg-gray-900 flex items-center justify-center min-h-screen">
    <!-- Container to simulate a 9:16 phone screen -->
    <div class="w-full max-w-[375px] shadow-2xl">
      <div class="aspect-9-16">
        <!-- 
            ====================================================================
            ===                     海报画布开始                          ===
            ====================================================================
            *   主题应用: B - 洞见光棱 (Insight Prism)
            *   渐变色: from-orange-200 to-purple-300
            *   背景图标: fa-link, text-white, opacity-15
            -->
        <div
          class="content bg-gradient-to-b from-orange-200 to-purple-300 overflow-hidden relative text-white"
        >
          <!-- 1. 背景图标 (Background Icon) -->
          <i
            class="fa-solid fa-link absolute -right-20 -top-10 text-9xl text-white opacity-15 rotate-n15"
            style="font-size: 30rem"
          ></i>

          <!-- 2. 页眉 (Header) -->
          <div class="absolute top-6 left-6">
            <span
              class="bg-white/20 text-white font-semibold text-sm px-4 py-1.5 rounded-full backdrop-blur-sm"
            >
              猜想
            </span>
          </div>

          <!-- 3. 核心内容容器 (Main Content Container) -->
          <main
            class="w-full h-full flex flex-col justify-center items-center p-8 text-center z-10"
          >
            <!-- 3.1 英雄主题 (Hero Typography Engine Applied) -->
            <!-- 规则2 (M): "Safety Alignment" 16个字符, 拆分为2行, text-6xl -->
            <h1 class="font-black text-6xl leading-tight">
              <div>Safety</div>
              <div>Alignment</div>
            </h1>

            <!-- 3.2 洞见 (Insight) -->
            <p class="mt-8 text-2xl font-bold max-w-sm">
              技术看似独立，规模化时才显现隐藏耦合 🔗
            </p>

            <!-- 3.3 问题 (Question) -->
            <p class="mt-6 text-lg font-light text-white/70 max-w-md">
              能源和人工智能之间的哪种基本约束关系最初即使对于专注于这两个领域的专家来说也是不明显的，这揭示了技术相互依赖性的什么问题？
            </p>
          </main>

          <!-- 4. 页脚 (Footer) -->
          <footer class="absolute bottom-8 left-0 right-0 text-center z-10">
            <span class="text-white/70 text-base font-light">
              Source: Sam Altman
            </span>
          </footer>
        </div>
        <!-- ======================= 海报画布结束 ========================= -->
      </div>
    </div>
  </body>
</html>
