<template>
  <v-btn
    :color="isRead ? 'success' : 'grey-lighten-2'"
    :variant="isRead ? 'flat' : 'outlined'"
    size="small"
    class="read-status-btn"
    @click="toggleReadStatus"
    :loading="loading"
  >
    <v-icon size="16" :class="{ 'mr-1': !$vuetify.display.xs }">
      {{ isRead ? 'mdi-check-circle' : 'mdi-circle-outline' }}
    </v-icon>
    <span v-if="!$vuetify.display.xs">
      {{ isRead ? '已读' : '未读' }}
    </span>
  </v-btn>
</template>

<script setup>
import { ref, computed } from 'vue'
import { cognitionAPI } from '@/api/cognition'

const props = defineProps({
  cognitionId: {
    type: String,
    required: true
  },
  readStatus: {
    type: Object,
    default: () => ({ is_read: false, read_at: null })
  }
})

const emit = defineEmits(['read-status-changed'])

const loading = ref(false)

const isRead = computed(() => {
  return props.readStatus?.is_read || false
})

async function toggleReadStatus() {
  try {
    loading.value = true
    
    const newStatus = !isRead.value
    const response = await cognitionAPI.setReadStatus(props.cognitionId, newStatus)
    
    // 发出事件通知父组件状态已更改
    emit('read-status-changed', {
      cognitionId: props.cognitionId,
      readStatus: response
    })
    
  } catch (error) {
    console.error('切换已读状态失败:', error)
    // 可以在这里添加错误提示
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.read-status-btn {
  min-width: 72px !important;
  height: 32px !important;
  transition: all 0.2s ease;
}

.read-status-btn:hover {
  transform: scale(1.05);
}
</style> 