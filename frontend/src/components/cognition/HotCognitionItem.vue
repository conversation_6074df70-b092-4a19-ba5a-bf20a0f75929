<template>
  <div 
    class="hot-cognition-item" 
    :class="{ 'top-rank': rank <= 3 }"
    @click="$emit('click')"
  >
    <div class="rank-indicator">
      <span 
        class="rank-number"
        :class="{
          'rank-1': rank === 1,
          'rank-2': rank === 2, 
          'rank-3': rank === 3
        }"
      >
        {{ rank }}
      </span>
    </div>
    
    <div class="cognition-content">
      <div class="cognition-title">
        {{ getTitle() }}
      </div>
      
      <div class="cognition-meta">
        <div class="meta-item">
          <v-icon size="10" color="grey">mdi-clock-outline</v-icon>
          <span class="meta-text">{{ formatTime(cognition.raw_at) }}</span>
        </div>
        
        <div class="meta-item" v-if="cognition.primary_topic">
          <v-icon size="10" color="primary">mdi-tag</v-icon>
          <span class="meta-text">{{ formatTopicName(cognition.primary_topic) }}</span>
        </div>
        
        <div class="meta-item" v-if="cognition.source">
          <v-icon size="10" color="secondary">mdi-source-branch</v-icon>
          <span class="meta-text">{{ cognition.source }}</span>
        </div>
      </div>
      
      <div class="cognition-preview" v-if="getPreview()">
        {{ getPreview() }}
      </div>
    </div>
    
    <!-- 热度和收藏指示器 -->
    <div class="action-indicators">
      <v-icon
        size="16"
        color="error"
        class="heat-icon"
        @click.stop
      >
        mdi-fire
      </v-icon>
      <v-icon
        size="16"
        :color="isFavorited ? 'red' : 'grey-lighten-1'"
        class="favorite-icon"
        :class="{ 'loading': isLoading || isStatusLoading }"
        @click.stop="toggleFavorite"
        :disabled="isLoading || isStatusLoading"
      >
        {{ (isLoading || isStatusLoading) ? 'mdi-loading' : (isFavorited ? 'mdi-heart' : 'mdi-heart-outline') }}
      </v-icon>
    </div>
  </div>
</template>

<script setup>
import { useCognitionStore } from '@/stores/cognition'
import { onMounted, ref } from 'vue'

const cognitionStore = useCognitionStore()

const props = defineProps({
  cognition: {
    type: Object,
    required: true
  },
  rank: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['click', 'favorite'])

// 收藏状态
const isFavorited = ref(false)
const isLoading = ref(false)
const isStatusLoading = ref(true) // 收藏状态加载中
const defaultCollectionId = ref(null)

// 获取默认收藏夹ID
const getDefaultCollectionId = async () => {
  try {
    const collections = await cognitionStore.getUserCollections()
    // 查找默认收藏夹
    const defaultCollection = collections.find(c => c.is_default)
    if (defaultCollection) {
      defaultCollectionId.value = defaultCollection.id
    } else if (collections.length > 0) {
      // 如果没有默认收藏夹，使用第一个收藏夹
      defaultCollectionId.value = collections[0].id
    } else {
      // 如果没有收藏夹，创建一个默认收藏夹
      const newCollection = await cognitionStore.createCollection({
        name: '默认收藏夹',
        description: '系统自动创建的默认收藏夹'
      })
      defaultCollectionId.value = newCollection.id
    }
  } catch (error) {
    console.error('获取默认收藏夹失败:', error)
  }
}

// 获取收藏状态
const loadFavoriteStatus = async () => {
  try {
    isStatusLoading.value = true
    const status = await cognitionStore.getFavoriteStatus(props.cognition.id)
    isFavorited.value = status.is_favorited
  } catch (error) {
    console.error('获取收藏状态失败:', error)
    // 如果获取失败，默认为未收藏状态
    isFavorited.value = false
  } finally {
    isStatusLoading.value = false
  }
}

// 切换收藏状态
const toggleFavorite = async () => {
  if (isLoading.value || isStatusLoading.value) return

  try {
    isLoading.value = true

    // 记录操作前的状态，用于失败时回滚
    const previousState = isFavorited.value

    if (isFavorited.value) {
      // 先更新UI状态，提供即时反馈
      isFavorited.value = false

      // 取消收藏
      await cognitionStore.removeFromFavorites(props.cognition.id)
    } else {
      // 先更新UI状态，提供即时反馈
      isFavorited.value = true

      // 确保有默认收藏夹ID
      if (!defaultCollectionId.value) {
        await getDefaultCollectionId()
      }

      if (defaultCollectionId.value) {
        // 添加收藏 - 使用默认收藏夹
        await cognitionStore.addToFavorites(props.cognition.id, defaultCollectionId.value)
      } else {
        // 回滚状态
        isFavorited.value = previousState
        throw new Error('无法获取默认收藏夹')
      }
    }

    // 触发事件通知父组件
    emit('favorite', {
      cognition: props.cognition,
      isFavorited: isFavorited.value
    })
  } catch (error) {
    console.error('收藏操作失败:', error)
    // 回滚UI状态
    isFavorited.value = !isFavorited.value
    // 可以在这里显示错误提示
  } finally {
    isLoading.value = false
  }
}

// 组件挂载时并行获取收藏状态和默认收藏夹
onMounted(async () => {
  // 并行执行，减少等待时间
  await Promise.all([
    getDefaultCollectionId(),
    loadFavoriteStatus()
  ])
})

// 获取标题
const getTitle = () => {
  const title = props.cognition.abstract_zh || 
                props.cognition.question_zh || 
                props.cognition.title || 
                '未知认知'
  
  // 限制标题长度
  return title.length > 40 ? title.substring(0, 38) + '...' : title
}

// 获取预览内容
const getPreview = () => {
  const preview = props.cognition.content || 
                  props.cognition.abstract_zh || 
                  props.cognition.description || 
                  ''
  
  // 限制预览长度
  return preview.length > 60 ? preview.substring(0, 58) + '...' : preview
}

// 格式化时间
const formatTime = (timeString) => {
  if (!timeString) return '未知时间'
  
  try {
    const date = new Date(timeString)
    const now = new Date()
    const diffMinutes = Math.floor((now - date) / (1000 * 60))
    
    if (diffMinutes < 60) {
      return `${diffMinutes}分钟前`
    }
    
    const diffHours = Math.floor(diffMinutes / 60)
    if (diffHours < 24) {
      return `${diffHours}小时前`
    }
    
    const diffDays = Math.floor(diffHours / 24)
    if (diffDays < 7) {
      return `${diffDays}天前`
    }
    
    return date.toLocaleDateString()
  } catch (error) {
    return '未知时间'
  }
}

// 格式化topic名称
const formatTopicName = (topic) => {
  if (!topic) return ''
  
  return topic.replace(/-/g, ' ')
}
</script>

<style scoped>
.hot-cognition-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 12px 14px;
  border-radius: 8px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  flex-shrink: 0;
  min-height: 75px;
  scroll-snap-align: start;
}

.hot-cognition-item:hover {
  background: #e2e8f0;
  border-color: #cbd5e1;
  transform: translateY(-1px);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.hot-cognition-item.top-rank {
  background: #fef3c7;
  border-color: #f59e0b;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.1);
}

.hot-cognition-item.top-rank:hover {
  background: #fde68a;
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(245, 158, 11, 0.15);
}

.rank-indicator {
  flex-shrink: 0;
  margin-top: 2px;
}

.rank-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  font-size: 10px;
  font-weight: 700;
  border-radius: 50%;
  background: #64748b;
  color: white;
}

.rank-number.rank-1 {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #92400e;
  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

.rank-number.rank-2 {
  background: linear-gradient(135deg, #c0c0c0 0%, #e5e5e5 100%);
  color: #374151;
  box-shadow: 0 2px 4px rgba(192, 192, 192, 0.3);
}

.rank-number.rank-3 {
  background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(205, 127, 50, 0.3);
}

.cognition-content {
  flex: 1;
  min-width: 0;
}

.cognition-title {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 6px;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.cognition-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 2px;
}

.meta-text {
  font-size: 10px;
  color: #64748b;
  font-weight: 500;
}

.cognition-preview {
  font-size: 11px;
  color: #64748b;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.action-indicators {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  margin-top: 1px;
}

.heat-icon {
  animation: heat-flicker 2s infinite alternate;
  padding: 2px;
}

.favorite-icon {
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 4px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.favorite-icon:hover {
  transform: scale(1.1);
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.favorite-icon:active {
  transform: scale(0.95);
}

.favorite-icon:disabled {
  cursor: not-allowed;
  opacity: 0.6;
  background: rgba(255, 255, 255, 0.5);
}

.favorite-icon:disabled:hover {
  transform: none;
  background: rgba(255, 255, 255, 0.5);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.favorite-icon.loading {
  animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes heat-flicker {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1);
  }
  50% { 
    opacity: 0.7; 
    transform: scale(1.1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hot-cognition-item {
    padding: 8px 10px;
    gap: 8px;
  }
  
  .cognition-title {
    font-size: 12px;
  }
  
  .cognition-preview {
    font-size: 9px;
  }
  
  .meta-text {
    font-size: 8px;
  }
  
  .cognition-meta {
    gap: 6px;
  }
}
</style> 