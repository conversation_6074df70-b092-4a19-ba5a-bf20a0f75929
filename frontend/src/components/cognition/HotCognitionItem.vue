<template>
  <div 
    class="hot-cognition-item" 
    :class="{ 'top-rank': rank <= 3 }"
    @click="$emit('click')"
  >
    <div class="rank-indicator">
      <span 
        class="rank-number"
        :class="{
          'rank-1': rank === 1,
          'rank-2': rank === 2, 
          'rank-3': rank === 3
        }"
      >
        {{ rank }}
      </span>
    </div>
    
    <div class="cognition-content">
      <div class="cognition-title">
        {{ getTitle() }}
      </div>
      
      <div class="cognition-meta">
        <div class="meta-item">
          <v-icon size="10" color="grey">mdi-clock-outline</v-icon>
          <span class="meta-text">{{ formatTime(cognition.raw_at) }}</span>
        </div>
        
        <div class="meta-item" v-if="cognition.primary_topic">
          <v-icon size="10" color="primary">mdi-tag</v-icon>
          <span class="meta-text">{{ formatTopicName(cognition.primary_topic) }}</span>
        </div>
        
        <div class="meta-item" v-if="cognition.source">
          <v-icon size="10" color="secondary">mdi-source-branch</v-icon>
          <span class="meta-text">{{ cognition.source }}</span>
        </div>
      </div>
      
      <div class="cognition-preview" v-if="getPreview()">
        {{ getPreview() }}
      </div>
    </div>
    
    <!-- 热度指示器 -->
    <div class="heat-indicator">
      <v-icon size="14" color="error" class="heat-icon">mdi-fire</v-icon>
    </div>
  </div>
</template>

<script setup>

const props = defineProps({
  cognition: {
    type: Object,
    required: true
  },
  rank: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['click'])

// 获取标题
const getTitle = () => {
  const title = props.cognition.abstract_zh || 
                props.cognition.question_zh || 
                props.cognition.title || 
                '未知认知'
  
  // 限制标题长度
  return title.length > 40 ? title.substring(0, 38) + '...' : title
}

// 获取预览内容
const getPreview = () => {
  const preview = props.cognition.content || 
                  props.cognition.abstract_zh || 
                  props.cognition.description || 
                  ''
  
  // 限制预览长度
  return preview.length > 60 ? preview.substring(0, 58) + '...' : preview
}

// 格式化时间
const formatTime = (timeString) => {
  if (!timeString) return '未知时间'
  
  try {
    const date = new Date(timeString)
    const now = new Date()
    const diffMinutes = Math.floor((now - date) / (1000 * 60))
    
    if (diffMinutes < 60) {
      return `${diffMinutes}分钟前`
    }
    
    const diffHours = Math.floor(diffMinutes / 60)
    if (diffHours < 24) {
      return `${diffHours}小时前`
    }
    
    const diffDays = Math.floor(diffHours / 24)
    if (diffDays < 7) {
      return `${diffDays}天前`
    }
    
    return date.toLocaleDateString()
  } catch (error) {
    return '未知时间'
  }
}

// 格式化topic名称
const formatTopicName = (topic) => {
  if (!topic) return ''
  
  return topic.replace(/-/g, ' ')
}
</script>

<style scoped>
.hot-cognition-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 12px 14px;
  border-radius: 8px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  flex-shrink: 0;
  min-height: 75px;
  scroll-snap-align: start;
}

.hot-cognition-item:hover {
  background: #e2e8f0;
  border-color: #cbd5e1;
  transform: translateY(-1px);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.hot-cognition-item.top-rank {
  background: #fef3c7;
  border-color: #f59e0b;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.1);
}

.hot-cognition-item.top-rank:hover {
  background: #fde68a;
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(245, 158, 11, 0.15);
}

.rank-indicator {
  flex-shrink: 0;
  margin-top: 2px;
}

.rank-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  font-size: 10px;
  font-weight: 700;
  border-radius: 50%;
  background: #64748b;
  color: white;
}

.rank-number.rank-1 {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #92400e;
  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

.rank-number.rank-2 {
  background: linear-gradient(135deg, #c0c0c0 0%, #e5e5e5 100%);
  color: #374151;
  box-shadow: 0 2px 4px rgba(192, 192, 192, 0.3);
}

.rank-number.rank-3 {
  background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(205, 127, 50, 0.3);
}

.cognition-content {
  flex: 1;
  min-width: 0;
}

.cognition-title {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 6px;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.cognition-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 2px;
}

.meta-text {
  font-size: 10px;
  color: #64748b;
  font-weight: 500;
}

.cognition-preview {
  font-size: 11px;
  color: #64748b;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.heat-indicator {
  flex-shrink: 0;
  margin-top: 2px;
}

.heat-icon {
  animation: heat-flicker 2s infinite alternate;
}

@keyframes heat-flicker {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1);
  }
  50% { 
    opacity: 0.7; 
    transform: scale(1.1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hot-cognition-item {
    padding: 8px 10px;
    gap: 8px;
  }
  
  .cognition-title {
    font-size: 12px;
  }
  
  .cognition-preview {
    font-size: 9px;
  }
  
  .meta-text {
    font-size: 8px;
  }
  
  .cognition-meta {
    gap: 6px;
  }
}
</style> 