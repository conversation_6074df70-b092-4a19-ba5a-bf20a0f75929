<template>
  <v-dialog 
    :model-value="modelValue" 
    @update:model-value="handleClose"
    max-width="1400px"
    scrollable
    persistent
    @click:outside="handleClose"
  >
    <v-card v-if="cognition" class="cognition-detail">
      <!-- 头部 -->
      <v-card-title class="detail-header">
        <div class="header-content">
          <div class="author-info">
            <div class="author-avatar">
              {{ (cognition.author_name || 'U').charAt(0).toUpperCase() }}
            </div>
            <div class="author-details">
              <div class="author-name">{{ cognition.author_name || '匿名用户' }}</div>
              <div class="publish-time">{{ formatTime(cognition.raw_at) }}</div>
            </div>
          </div>
          
          <v-btn 
            icon 
            variant="text" 
            @click="handleClose"
            class="close-btn"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </div>
      </v-card-title>

      <div class="detail-body">
        <!-- 左侧：来源认知 -->
        <div v-if="sourceCognitions.length > 0" class="source-panel">
          <h3 class="section-title">
            <v-icon color="deep-purple">mdi-creation</v-icon>
            合成来源
          </h3>
          <div v-if="loadingSources" class="text-center mt-10">
            <v-progress-circular indeterminate color="primary"></v-progress-circular>
            <p class="mt-4">加载来源中...</p>
          </div>
          <div v-else class="recommendations-list">
            <v-card
              v-for="source in sourceCognitions"
              :key="source.id"
              class="recommendation-card mb-3"
              variant="outlined"
              @click="handleRecommendationClick(source.id)"
            >
              <div class="d-flex align-center recommendation-header">
                <v-card-title class="recommendation-title text-body-1">
                  {{ truncateText(source.abstract_zh || source.question_zh, 20) }}
                </v-card-title>
              </div>

              <div class="recommendation-tags d-flex align-center flex-wrap">
                <span v-if="source.tag" class="tag-chip tag">{{ source.tag }}</span>
                <span v-if="source.blogger" class="tag-chip blogger-tag">{{ source.blogger }}</span>
                <span v-if="source.primary_topic" class="tag-chip primary-topic" :style="getTopicStyle(source.primary_topic)">
                  {{ formatTopicName(source.primary_topic) }}
                </span>
              </div>
              
              <v-card-text v-if="source.answer_zh || source.answer_en" class="recommendation-preview">
                {{ truncateText(source.answer_zh || source.answer_en, 100) }}
              </v-card-text>
            </v-card>
          </div>
        </div>

        <!-- 右侧：内容区域 -->
        <v-card-text class="detail-content">
          <!-- 认知摘要 -->
          <div class="section">
            <h3 class="section-title">
              <v-icon color="primary">mdi-lightbulb-outline</v-icon>
              认知摘要
            </h3>
            <div class="content-text">
              <MarkdownRenderer 
                :content="getDisplayText('abstract')" 
                :is-complete="true" 
              />
            </div>
          </div>

          <!-- 问题 -->
          <div class="section" v-if="getDisplayText('question')">
            <h3 class="section-title">
              <v-icon color="green">mdi-help-circle-outline</v-icon>
              问题
            </h3>
            <div class="content-text question-text">
              <MarkdownRenderer 
                :content="getDisplayText('question')" 
                :is-complete="true" 
              />
            </div>
          </div>

          <!-- 答案 -->
          <div class="section" v-if="getDisplayText('answer')">
            <h3 class="section-title">
              <v-icon color="blue">mdi-check-circle-outline</v-icon>
              答案
            </h3>
            <div class="content-text answer-text">
              <MarkdownRenderer 
                :content="getDisplayText('answer')" 
                :is-complete="true" 
              />
            </div>
          </div>

          <!-- 元认知 -->
          <div class="section" v-if="getDisplayText('think')">
            <h3 class="section-title">
              <v-icon color="purple">mdi-brain</v-icon>
              元思考
            </h3>
            <div class="content-text think-text">
              <MarkdownRenderer 
                :content="getDisplayText('think')" 
                :is-complete="true" 
              />
            </div>
          </div>

          <!-- 来源和博主信息 -->
          <div class="meta-section">
            <!-- 验证/猜想标签 -->
            <div class="meta-item verification-meta">
              <h4 class="meta-title">
                <v-icon :color="getVerificationIconColor">{{ getVerificationIcon }}</v-icon>
                类型
              </h4>
              <div class="verification-tag" :class="getVerificationClass">
                <v-icon size="14">{{ getVerificationIcon }}</v-icon>
                {{ getVerificationText }}
              </div>
            </div>

            <!-- Topics标签 -->
            <div class="meta-item topics-meta" v-if="hasTopics">
              <h4 class="meta-title">
                <v-icon color="primary">mdi-tag-multiple</v-icon>
                话题标签
              </h4>
              <div class="topics-container">
                <!-- Primary Topic -->
                <div 
                  v-if="cognition.primary_topic" 
                  class="topic-tag primary-topic"
                  @click="$emit('topic-click', cognition.primary_topic)"
                >
                  {{ cognition.primary_topic }}
                </div>
                
                <!-- Related Topics -->
                <div 
                  v-for="topic in cognition.related_topics || []" 
                  :key="topic"
                  class="topic-tag related-topic"
                  @click="$emit('topic-click', topic)"
                >
                  {{ topic }}
                </div>
              </div>
            </div>

            <!-- 来源 -->
            <div class="meta-item" v-if="cognition.source">
              <h4 class="meta-title">
                <v-icon color="purple">mdi-source-branch</v-icon>
                来源
              </h4>
              <div class="source-tag">{{ cognition.source }}</div>
            </div>

            <!-- 博主 -->
            <div class="meta-item" v-if="cognition.blogger">
              <h4 class="meta-title">
                <v-icon color="teal">mdi-account</v-icon>
                博主
              </h4>
              <div class="blogger-tag">{{ cognition.blogger }}</div>
            </div>

            <!-- 链接 -->
            <div class="meta-item" v-if="cognition.link">
              <h4 class="meta-title">
                <v-icon color="indigo">mdi-link</v-icon>
                链接
              </h4>
              <a :href="cognition.link" target="_blank" class="link-tag">
                <v-icon size="16">mdi-open-in-new</v-icon>
                访问链接
              </a>
            </div>
            
            <!-- Topics标签暂时隐藏 -->
          </div>

          <!-- 在评论区域之前添加推荐区域 -->
          <!-- 认知推荐区域 -->
          <div class="recommendations-section">
            <h3 class="section-title">
              <v-icon color="indigo">mdi-lightbulb-group</v-icon>
              相关推荐
            </h3>
            
            <div v-if="loading" class="text-center">
              <v-progress-circular indeterminate color="primary"></v-progress-circular>
              <p>加载推荐中...</p>
            </div>
            
            <div v-else-if="recommendations.length > 0" class="recommendations-list">
              <v-card
                v-for="recommendation in recommendations"
                :key="recommendation.id"
                class="recommendation-card mb-3"
                variant="outlined"
                @click="handleRecommendationClick(recommendation.id)"
              >
                <div class="d-flex align-center recommendation-header">
                  <v-card-title class="recommendation-title text-body-1">
                    {{ truncateText(recommendation.title, 50) }}
                  </v-card-title>
                  <!-- 显示标签（如果有）-->
                  <div class="recommendation-tags">
                    <span 
                      v-if="recommendation.tag" 
                      class="tag-chip tag"
                    >
                      {{ recommendation.tag }}
                    </span>
                    
                    <!-- 显示博主标签（如果有） -->
                    <span 
                      v-if="recommendation.blogger" 
                      class="tag-chip blogger-tag"
                    >
                      {{ recommendation.blogger }}
                    </span>
                    
                    <!-- 显示主题标签 -->
                    <span 
                      v-if="recommendation.primary_topic" 
                      class="tag-chip primary-topic"
                      :style="getTopicStyle(recommendation.primary_topic)"
                    >
                      {{ formatTopicName(recommendation.primary_topic) }}
                    </span>
                  </div>
                </div>
                <v-card-text class="recommendation-preview">
                  {{ recommendation.preview }}
                </v-card-text>
              </v-card>
            </div>
            
            <div v-else class="no-recommendations text-center">
              <v-icon size="40" color="grey-lighten-2">mdi-lightbulb-off-outline</v-icon>
              <p>暂无相关推荐</p>
            </div>
          </div>

          <!-- 投票区域 -->
          <div class="vote-section">
            <div class="vote-actions">
              <!-- 点赞 -->
              <v-btn
                :variant="cognition.user_vote === 'like' ? 'flat' : 'outlined'"
                :color="cognition.user_vote === 'like' ? 'red' : 'grey'"
                @click="handleVote('like')"
                class="vote-button"
              >
                <v-icon>mdi-heart{{ cognition.user_vote === 'like' ? '' : '-outline' }}</v-icon>
                {{ cognition.likes || cognition.like_count || 0 }}
              </v-btn>

              <!-- 中立 -->
              <v-btn
                :variant="cognition.user_vote === 'neutral' ? 'flat' : 'outlined'"
                :color="cognition.user_vote === 'neutral' ? 'blue' : 'grey'"
                @click="handleVote('neutral')"
                class="vote-button"
              >
                <v-icon>mdi-minus-circle{{ cognition.user_vote === 'neutral' ? '' : '-outline' }}</v-icon>
                {{ cognition.neutral || cognition.neutral_count || 0 }}
              </v-btn>

              <!-- 点踩 -->
              <v-btn
                :variant="cognition.user_vote === 'dislike' ? 'flat' : 'outlined'"
                :color="cognition.user_vote === 'dislike' ? 'grey-darken-4' : 'grey'"
                @click="handleVote('dislike')"
                class="vote-button"
              >
                <v-icon>mdi-thumb-down{{ cognition.user_vote === 'dislike' ? '' : '-outline' }}</v-icon>
                {{ cognition.dislikes || cognition.dislike_count || 0 }}
              </v-btn>
            </div>
          </div>

          <!-- 评论区域 -->
          <div class="comments-section">
            <h3 class="section-title">
              <v-icon color="teal">mdi-comment-multiple-outline</v-icon>
              评论 ({{ cognition.comments?.length || 0 }})
            </h3>

            <!-- 添加顶级评论 -->
            <div class="add-comment">
              <v-textarea
                v-model="newComment"
                placeholder="写下你的想法..."
                variant="outlined"
                rows="3"
                hide-details
                class="comment-input"
              />
              <div class="comment-actions">
                <v-btn
                  color="primary"
                  variant="flat"
                  @click="handleAddTopLevelComment"
                  :disabled="!newComment.trim()"
                  class="submit-comment-btn"
                >
                  <v-icon>mdi-send</v-icon>
                  发表评论
                </v-btn>
              </div>
            </div>

            <!-- 评论列表 -->
            <div class="comments-list" v-if="cognition.comments?.length">
              <template v-for="comment in topLevelComments" :key="comment.id">
                <!-- 顶级评论 -->
                <div class="comment-item">
                <div class="comment-author">
                  <div class="comment-avatar">
                    {{ comment.username.charAt(0).toUpperCase() }}
                  </div>
                  <div class="comment-info">
                    <div class="comment-username">{{ comment.username }}</div>
                    <div class="comment-time">{{ formatTime(comment.created_at) }}</div>
                  </div>
                </div>
                <div class="comment-content">
                  <MarkdownRenderer 
                    :content="comment.content" 
                    :is-complete="true" 
                  />
                </div>
                  <div class="comment-footer">
                    <v-btn
                      variant="text"
                      size="small"
                      @click="toggleReply(comment.id)"
                      class="reply-btn"
                    >
                      <v-icon size="16">mdi-reply</v-icon>
                      回复
                    </v-btn>
                  </div>
                  
                  <!-- 该评论的回复输入框 -->
                  <div v-if="replyingToId === comment.id" class="reply-input-section">
                    <v-textarea
                      v-model="replyContent"
                      :placeholder="`回复 @${comment.username}`"
                      variant="outlined"
                      rows="2"
                      hide-details
                      class="reply-input"
                    />
                    <div class="reply-actions">
                      <v-btn
                        variant="text"
                        @click="cancelReply"
                        class="cancel-reply-btn"
                      >
                        取消
                      </v-btn>
                      <v-btn
                        color="primary"
                        variant="flat"
                        @click="handleReply(comment)"
                        :disabled="!replyContent.trim()"
                        class="submit-reply-btn"
                      >
                        <v-icon>mdi-send</v-icon>
                        回复
                      </v-btn>
                    </div>
                  </div>
                  
                  <!-- 递归渲染所有层级的回复 -->
                  <div v-if="getNestedReplies(comment.id).length > 0" class="replies-section">
                    <template v-for="reply in getNestedReplies(comment.id)" :key="reply.id">
                      <div class="reply-item" :style="{ marginLeft: (reply.level * 20) + 'px' }">
                        <div class="comment-author">
                          <div class="comment-avatar reply-avatar">
                            {{ reply.username.charAt(0).toUpperCase() }}
                          </div>
                          <div class="comment-info">
                            <div class="comment-username">
                              {{ reply.username }}
                              <span v-if="reply.reply_to_username" class="reply-to">
                                回复 @{{ reply.reply_to_username }}
                              </span>
                            </div>
                            <div class="comment-time">{{ formatTime(reply.created_at) }}</div>
                          </div>
                        </div>
                        <div class="comment-content">
                          <MarkdownRenderer 
                            :content="reply.content" 
                            :is-complete="true" 
                          />
                        </div>
                        <div class="comment-footer">
                          <v-btn
                            v-if="reply.level < 5"
                            variant="text"
                            size="small"
                            @click="toggleReply(reply.id, reply)"
                            class="reply-btn"
                          >
                            <v-icon size="16">mdi-reply</v-icon>
                            回复
                          </v-btn>
                        </div>
                        
                        <!-- 回复的回复输入框 -->
                        <div v-if="replyingToId === reply.id" class="reply-input-section">
                          <v-textarea
                            v-model="replyContent"
                            :placeholder="`回复 @${reply.username}`"
                            variant="outlined"
                            rows="2"
                            hide-details
                            class="reply-input"
                          />
                          <div class="reply-actions">
                            <v-btn
                              variant="text"
                              @click="cancelReply"
                              class="cancel-reply-btn"
                            >
                              取消
                            </v-btn>
                            <v-btn
                              color="primary"
                              variant="flat"
                              @click="handleReply(reply)"
                              :disabled="!replyContent.trim()"
                              class="submit-reply-btn"
                            >
                              <v-icon>mdi-send</v-icon>
                              回复
                            </v-btn>
                          </div>
                        </div>
                      </div>
                    </template>
                  </div>
              </div>
              </template>
            </div>
            
            <div v-else class="no-comments">
              <v-icon size="48" color="grey-lighten-2">mdi-comment-outline</v-icon>
              <p>还没有评论，来发表第一条评论吧！</p>
            </div>
          </div>
        </v-card-text>
      </div>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, computed, watch} from 'vue'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { cognitionAPI } from '@/api/cognition'
import MarkdownRenderer from '@/components/MarkdownRenderer.vue' 

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  cognition: {
    type: Object,
    default: null
  },
  language: {
    type: String,
    default: 'zh'
  }
})
const emit = defineEmits(['update:modelValue', 'vote', 'comment', 'close', 'topic-click', 'recommendation-click'])

const newComment = ref('')
const replyingTo = ref(null)
const replyingToId = ref(null)
const replyContent = ref('')

const recommendations = ref([])
const sourceCognitions = ref([])
const loading = ref(false)
const loadingSources = ref(false)
// 静态示例数据 - 不需要外部API调用
// const recommendations = ref([
//   {
//     id: "rec1",
//     title: "深度学习中的注意力机制",
//     preview: "注意力机制是深度学习中的重要概念，它模拟人类注意力的选择性特点，使模型能够专注于输入数据中最相关的部分...",
//     score: 0.89
//   },
//   {
//     id: "rec2", 
//     title: "Transformer架构详解",
//     preview: "Transformer是一种基于自注意力机制的神经网络架构，彻底改变了NLP领域...",
//     score: 0.75
//   },
//   {
//     id: "rec3",
//     title: "大型语言模型的涌现能力",
//     preview: "当模型规模达到一定阈值时，会出现一些未经专门训练的新能力，这种现象被称为涌现能力...",
//     score: 0.68
//   }
// ])

// 增加测试模式标志
const isTestMode = ref(false) // 设置为 true 使用模拟数据，false 使用真实API

// 在script部分添加格式化topic名称的方法
function formatTopicName(topic) {
  if (!topic) return '';
  return topic.replace(/-/g, ' ').replace(/\(/g, ' (');
}

watch(() => props.cognition?.id, async (newId, oldId) => {
  console.log('Cognition ID changed:', newId, oldId)
  if (newId && newId !== oldId) {
    if (isTestMode.value) {
      await loadMockRecommendations()
    } else {
      await fetchRecommendations()
    }
    // 获取来源认知
    if (props.cognition?.source_cognition_ids?.length > 0) {
      await fetchSourceCognitions()
    } else {
      sourceCognitions.value = []
    }
  }
})

async function loadMockRecommendations() {
  try {
    loading.value = true
    console.log('加载模拟推荐数据...')
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 使用模拟数据
    recommendations.value = [
      {
        id: 'rec-001',
        title: '大型语言模型如何理解上下文信息？',
        preview: '大型语言模型通过注意力机制和Transformer架构来捕捉文本中的上下文关系，这使它们能够理解长序列中的依赖关系...',
        score: 0.92
      },
      {
        id: 'rec-002',
        title: '为什么小型语言模型在特定领域表现优异？',
        preview: '小型语言模型经过特定领域的微调后，可以在资源受限的环境中提供高质量的结果。与通用大模型相比，它们更易于部署...',
        score: 0.85
      },
      {
        id: 'rec-003',
        title: 'AI模型的推理延迟与批处理的关系',
        preview: '批处理大小对AI模型推理性能有显著影响。增加批处理大小通常可以提高吞吐量，但同时会导致单个请求的延迟增加...',
        score: 0.74
      }
    ]
    
    console.log('模拟推荐数据加载完成', recommendations.value)
  } catch (error) {
    console.error('加载推荐失败:', error)
    recommendations.value = []
  } finally {
    loading.value = false
  }
}

// 获取相关推荐的函数
async function fetchRecommendations() {
  if (!props.cognition || !props.cognition.id) return
  
  console.log('Fetching recommendations for cognition ID:', props.cognition.id)
  loading.value = true
  try {
    // 调用API获取推荐
    const response = await cognitionAPI.getRecommendations(props.cognition.id)
    if (response && Array.isArray(response)) {
      recommendations.value = response
    } else {
      recommendations.value = []
    }
  } catch (error) {
    console.error('获取推荐失败:', error)
    recommendations.value = []
  } finally {
    loading.value = false
  }
}

// 获取来源认知的函数
async function fetchSourceCognitions() {
  if (!props.cognition?.source_cognition_ids?.length) return

  console.log('Fetching source cognitions for IDs:', props.cognition.source_cognition_ids)
  loadingSources.value = true
  try {
    const response = await cognitionAPI.getCognitionsByIds(props.cognition.source_cognition_ids)
    if (response && Array.isArray(response)) {
      sourceCognitions.value = response
      console.log('来源认知:', sourceCognitions.value)
    } else {
      sourceCognitions.value = []
    }
  } catch (error) {
    console.error('获取来源认知失败:', error)
    sourceCognitions.value = []
  } finally {
    loadingSources.value = false
  }
}

// 处理推荐点击事件
function handleRecommendationClick(recommendationId) {
  if (!recommendationId) {
    console.warn('推荐ID为空，无法处理点击事件')
    return
  } 
  console.log('点击了推荐:', recommendationId)
  
  // 为了优化用户体验，在加载推荐内容前先滚动到对话框顶部
  const detailCard = document.querySelector('.cognition-detail')
  if (detailCard) {
    detailCard.scrollTop = 0
  }
  
  emit('recommendation-click', recommendationId)
}

// 检查是否有topics
const hasTopics = computed(() => {
  return props.cognition?.primary_topic || (props.cognition?.related_topics && props.cognition.related_topics.length > 0)
})

// Topic颜色映射
const topicColors = {
  'AI': { color: '#FF6B6B', background: '#FFE5E5' },
  'Machine Learning': { color: '#4ECDC4', background: '#E5F9F7' },
  'Deep Learning': { color: '#45B7D1', background: '#E5F4FD' },
  'Computer Vision': { color: '#96CEB4', background: '#F0F9F5' },
  'Natural Language Processing': { color: '#FFEAA7', background: '#FDF8E7' },
  'Neural Networks': { color: '#DDA0DD', background: '#F4E8F4' },
  'Reinforcement Learning': { color: '#F39C12', background: '#FDF2E7' },
  'Data Science': { color: '#E17055', background: '#F9EEEC' },
  'Algorithm': { color: '#6C5CE7', background: '#EFEEFF' },
  'Programming': { color: '#00B894', background: '#E5F7F4' },
  'Research': { color: '#FD79A8', background: '#FEF0F5' },
  'Technology': { color: '#FDCB6E', background: '#FEF9E7' },
}

function getTopicStyle(topic) {
  const colors = topicColors[topic] || {
    color: '#667eea',
    background: 'rgba(102, 126, 234, 0.1)'
  }
  
  return {
    color: colors.color,
    backgroundColor: colors.background
  }
}

// 根据当前语言获取显示文本
function getDisplayText(field) {
  if (!props.cognition) return ''
  
  const zhField = `${field}_zh`
  const enField = `${field}_en`
  
  if (props.language === 'zh') {
    return props.cognition[zhField] || props.cognition[enField] || ''
  } else {
    return props.cognition[enField] || props.cognition[zhField] || ''
  }
}

// 计算属性：获取顶级评论
const topLevelComments = computed(() => {
  if (!props.cognition?.comments) return []
  return props.cognition.comments.filter(comment => !comment.parent_id)
})

// 验证状态相关计算属性
const getVerificationClass = computed(() => {
  const isArxiv = props.cognition?.source && props.cognition.source.toLowerCase().includes('arxiv')
  return isArxiv ? 'verified' : 'hypothesis'
})

const getVerificationIcon = computed(() => {
  const isArxiv = props.cognition?.source && props.cognition.source.toLowerCase().includes('arxiv')
  return isArxiv ? 'mdi-check-circle' : 'mdi-lightbulb-outline'
})

const getVerificationText = computed(() => {
  const isArxiv = props.cognition?.source && props.cognition.source.toLowerCase().includes('arxiv')
  return isArxiv ? '验证' : '猜想'
})

const getVerificationIconColor = computed(() => {
  const isArxiv = props.cognition?.source && props.cognition.source.toLowerCase().includes('arxiv')
  return isArxiv ? 'green' : 'orange'
})

function handleVote(voteType) {
  emit('vote', props.cognition.id, voteType)
}

async function handleAddTopLevelComment() {
  if (!newComment.value.trim()) return
  
  emit('comment', props.cognition.id, newComment.value.trim())
  
  newComment.value = ''
}

function formatTime(dateString) {
  try {
    const date = new Date(dateString)
    return formatDistanceToNow(date, { 
      addSuffix: true, 
      locale: zhCN 
    })
  } catch (error) {
    return '未知时间'
  }
}

function handleClose() {
  emit('update:modelValue', false)
  emit('close')
}

function toggleReply(commentId, comment = null) {
  if (replyingToId.value === commentId) {
    // 如果点击的是同一个回复按钮，则取消回复
    cancelReply()
    return
  }
  
  replyingToId.value = commentId
  
  // 如果没有传入comment对象，从comments中找到对应的评论
  if (!comment) {
    comment = props.cognition.comments.find(c => c.id === commentId)
  }
  
  replyingTo.value = comment
  
  // 自动聚焦到输入框
  setTimeout(() => {
    const textarea = document.querySelector('.reply-input textarea')
    if (textarea) {
      textarea.focus()
    }
  }, 100)
}

function cancelReply() {
  replyingToId.value = null
  replyingTo.value = null
  replyContent.value = ''
}

function getReplies(commentId) {
  if (!props.cognition?.comments) return []
  return props.cognition.comments.filter(c => c.parent_id === commentId)
}

async function handleReply(comment, parentComment = null) {
  if (!replyContent.value.trim()) return
  
  // parent_id直接指向被回复的评论ID
  const parentId = comment.id
  const replyToUsername = comment.username
  
  emit('comment', props.cognition.id, replyContent.value.trim(), parentId, replyToUsername)
  
  replyingToId.value = null
  replyingTo.value = null
  replyContent.value = ''
}

function getNestedReplies(commentId) {
  if (!props.cognition?.comments) return []
  
  const buildNestedReplies = (parentId, level = 1) => {
    const directReplies = props.cognition.comments.filter(c => c.parent_id === parentId)
    const result = []
    
    for (const reply of directReplies) {
      // 添加层级信息
      const replyWithLevel = { ...reply, level }
      result.push(replyWithLevel)
      
      // 递归获取子回复
      const childReplies = buildNestedReplies(reply.id, level + 1)
      result.push(...childReplies)
    }
    
    return result
  }
  
  return buildNestedReplies(commentId)
}

// 文本截断函数
function truncateText(text, maxLength) {
  if (!text) return '';
  if (text.length <= maxLength) {
    return text;
  }
  return text.substring(0, maxLength) + '...';
}
</script>

<style scoped>

/* 添加推荐区域的样式 */
.recommendations-section {
  margin: 32px 0;
  padding-top: 24px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* 推荐卡片布局优化 */
.recommendation-card {
  transition: transform 0.2s, box-shadow 0.2s;
  border-left: 4px solid #5c6bc0;
  cursor: pointer;
  margin-bottom: 16px;
  position: relative;
  overflow: hidden;
}

.recommendation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0,0,0,0.1);
}

/* 标题区域优化 */
.recommendation-header {
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.recommendation-title {
  font-weight: 600;
  line-height: 1.4;
  color: #2c3e50;
  padding: 0;
  margin: 0;
  font-size: 16px;
}

/* 标签容器优化 */
.recommendation-tags {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  margin-left: 12px;
}

/* 标签样式优化 */
.recommendation-tags .tag-chip {
  font-size: 10px;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
  height: 18px;
  line-height: 14px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  letter-spacing: 0.3px;
}

/* 博主标签样式 - 确保特异性更高 */
.recommendation-tags .blogger-tag {
  background-color: rgba(76, 175, 80, 0.15);
  color: #2e8540;
  font-weight: 600;
  border: 1px solid rgba(76, 175, 80, 0.2);
}

/* 普通标签样式 */
.recommendation-tags .tag {
  background-color: rgba(96, 125, 139, 0.15);
  color: #546e7a;
  border: 1px solid rgba(96, 125, 139, 0.2);
}

/* 主题标签样式 */
.recommendation-tags .primary-topic {
  background-color: rgba(33, 150, 243, 0.15);
  color: #1976d2;
  border: 1px solid rgba(33, 150, 243, 0.2);
}

/* 预览文本优化 */
.recommendation-preview {
  color: #606f7b;
  padding: 0 16px 16px;
  font-size: 14px;
  line-height: 1.6;
  margin-top: 6px;
}

/* 添加美化效果 - 卡片边缘渐变 */
.recommendation-card::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #5c6bc0, #8e99f3);
  opacity: 0;
  transition: opacity 0.3s;
}

.recommendation-card:hover::after {
  opacity: 1;
}

.no-recommendations {
  padding: 32px 0;
  color: #7f8c8d;
}

.no-recommendations p {
  margin-top: 12px;
  color: #95a5a6;
}

.cognition-detail {
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.detail-body {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.source-panel {
  width: 450px;
  flex-shrink: 0;
  border-right: 1px solid #e0e0e0;
  overflow-y: auto;
  padding: 24px;
  background: #f8f9fa;
}

.source-panel .section-title {
  margin-bottom: 24px;
}

.source-panel .recommendation-card {
  border-left-color: #8e44ad;
}

.detail-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  flex-shrink: 0;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
}

.author-details {
  flex: 1;
}

.author-name {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 2px;
}

.publish-time {
  font-size: 14px;
  color: #7f8c8d;
}

.close-btn {
  flex-shrink: 0;
}

.detail-content {
  padding: 24px !important;
  overflow-y: auto;
  flex-grow: 1;
}

.section {
  margin-bottom: 32px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16px;
}

.content-text {
  font-size: 15px;
  line-height: 1.7;
  color: #34495e;
  text-align: justify;
}

.question-text {
  background: rgba(52, 152, 219, 0.1);
  padding: 16px;
  border-radius: 12px;
  border-left: 4px solid #3498db;
  font-style: italic;
}

.answer-text {
  background: rgba(46, 204, 113, 0.1);
  padding: 16px;
  border-radius: 12px;
  border-left: 4px solid #2ecc71;
}

.think-text {
  background: rgba(155, 89, 182, 0.08);
  padding: 16px;
  border-radius: 12px;
  border-left: 4px solid #9b59b6;
  font-style: italic;
  color: #5a5a7d;
}

.source-tag {
  background: rgba(255, 105, 135, 0.15);
  color: #ff6987;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  display: inline-block;
}

.meta-section {
  margin: 24px 0;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.meta-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.meta-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.blogger-tag {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  display: inline-block;
  width: fit-content;
}

.link-tag {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  text-decoration: none;
  width: fit-content;
  transition: all 0.2s ease;
}

.link-tag:hover {
  background: rgba(52, 152, 219, 0.2);
  transform: translateY(-1px);
}

.verification-tag {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  flex-shrink: 0;
}

.verification-tag.verified {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.verification-tag.hypothesis {
  background: rgba(255, 152, 0, 0.1);
  color: #FF9800;
}

.verification-meta {
  margin-bottom: 16px;
}

.topics-container {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.topic-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.2s ease;
  cursor: pointer;
  border: 1px solid transparent;
}

.topic-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.primary-topic {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border-color: rgba(102, 126, 234, 0.3);
}

.primary-topic:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.5);
}

.related-topic {
  background: rgba(78, 205, 196, 0.1);
  color: #4ECDC4;
  border-color: rgba(78, 205, 196, 0.3);
}

.related-topic:hover {
  background: rgba(78, 205, 196, 0.2);
  border-color: rgba(78, 205, 196, 0.5);
}

.topics-meta {
  margin-bottom: 16px;
}

.vote-section {
  margin: 32px 0;
  padding: 24px;
  background: rgba(247, 249, 252, 0.8);
  border-radius: 16px;
}

.vote-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.vote-button {
  gap: 8px;
  padding: 8px 24px;
  border-radius: 24px;
  text-transform: none;
  font-weight: 500;
}

.comments-section {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 24px;
}

.add-comment {
  margin-bottom: 24px;
}

.comment-input {
  margin-bottom: 12px;
}

.comment-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.submit-comment-btn {
  gap: 8px;
  text-transform: none;
}

.comments-list {
  space-y: 16px;
}

.comment-item {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.comment-author {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.comment-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
}

.comment-info {
  flex: 1;
}

.comment-username {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 2px;
}

.comment-time {
  font-size: 12px;
  color: #7f8c8d;
}

.comment-content {
  font-size: 14px;
  line-height: 1.6;
  color: #34495e;
  text-align: justify;
}

.no-comments {
  text-align: center;
  padding: 40px 20px;
  color: #7f8c8d;
}

.no-comments p {
  margin-top: 8px;
  font-size: 14px;
}

.comment-footer {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.reply-btn {
  color: #7f8c8d !important;
  font-size: 12px !important;
  text-transform: none !important;
  padding: 4px 8px !important;
  min-width: auto !important;
}

.reply-btn:hover {
  background: rgba(102, 126, 234, 0.1) !important;
  color: #667eea !important;
}

.replies-section {
  margin-top: 16px;
  border-left: 2px solid rgba(102, 126, 234, 0.2);
  padding-left: 16px;
}

.nested-comment {
  background: rgba(247, 249, 252, 0.3);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
}

.nested-comment::before {
  content: '';
  position: absolute;
  left: -2px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(
    to bottom,
    rgba(102, 126, 234, 0.5),
    rgba(102, 126, 234, 0.2)
  );
  border-radius: 1px;
}

.reply-item {
  background: rgba(247, 249, 252, 0.5);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.reply-avatar {
  width: 28px !important;
  height: 28px !important;
  font-size: 12px !important;
}

.reply-to {
  color: #667eea;
  font-size: 12px;
  font-weight: 500;
  margin-left: 8px;
}

.reply-input-section {
  margin-top: 16px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.reply-input {
  margin-bottom: 12px;
}

.reply-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.submit-reply-btn {
  gap: 8px;
  text-transform: none;
}

.cancel-reply-btn {
  text-transform: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-content {
    padding: 16px !important;
  }
  
  .section {
    margin-bottom: 24px;
  }
  
  .vote-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .vote-button {
    width: 100%;
  }
  
  .replies-section {
    margin-left: 12px;
    padding-left: 12px;
  }
  
  .comment-actions,
  .reply-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .reply-item {
    padding: 8px;
  }
  
  .reply-to {
    display: block;
    margin-left: 0;
    margin-top: 2px;
  }
}
</style> 