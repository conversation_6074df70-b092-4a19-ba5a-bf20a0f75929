<template>
  <v-card class="emerging-topics-card" elevation="2">
    <!-- 标题栏 -->
    <div class="card-header">
      <div class="header-left">
        <v-icon color="success" size="20" class="emerging-icon">mdi-trending-up</v-icon>
        <h3 class="card-title">新兴话题</h3>
        <v-chip 
          size="small" 
          color="success" 
          variant="outlined"
          class="emerging-indicator"
        >
          HOT
        </v-chip>
      </div>
      
      <v-btn
        variant="text"
        size="small"
        color="primary"
        @click="$emit('show-all')"
        class="show-all-btn"
      >
        <v-icon size="16">mdi-arrow-right</v-icon>
        查看全部
      </v-btn>
    </div>

    <v-divider />

    <!-- 内容 -->
    <div class="card-content">
      <div v-if="loading" class="loading-section">
        <v-progress-circular indeterminate size="24" color="primary" />
        <span class="loading-text">检测中...</span>
      </div>

      <div v-else-if="emergingTopics.length === 0" class="empty-section">
        <v-icon color="grey" size="32">mdi-magnify</v-icon>
        <span class="empty-text">暂无新兴话题</span>
      </div>

      <div v-else class="topics-list">
        <div
          v-for="(topic, index) in emergingTopics.slice(0, 5)"
          :key="topic.topic"
          class="emerging-topic-item"
          @click="$emit('topic-click', topic.topic)"
        >
          <div class="topic-rank">
            <v-icon size="12" color="success">mdi-star</v-icon>
            <span class="rank-text">{{ index + 1 }}</span>
          </div>
          
          <div class="topic-info">
            <div class="topic-name">{{ formatTopicName(topic.topic) }}</div>
            <div class="topic-stats">
              <span class="stat-item">
                <v-icon size="10" color="primary">mdi-fire</v-icon>
                {{ topic.count }}
              </span>
              <span class="stat-item">
                <v-icon size="10" color="secondary">mdi-percent</v-icon>
                {{ topic.percentage.toFixed(1) }}%
              </span>
              <span class="new-badge">NEW</span>
            </div>
          </div>
          
          <div class="topic-trend">
            <v-icon size="16" color="success" class="trend-icon">mdi-trending-up</v-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部信息 -->
    <div class="card-footer" v-if="lastUpdateTime">
      <v-icon size="12" color="grey">mdi-clock-outline</v-icon>
      <span class="update-text">更新于 {{ formatUpdateTime(lastUpdateTime) }}</span>
    </div>
  </v-card>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  emergingTopics: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  lastUpdateTime: {
    type: Number,
    default: null
  }
})

const emit = defineEmits(['topic-click', 'show-all'])

// 格式化topic名称
const formatTopicName = (topic) => {
  if (!topic) return '未知话题'
  
  return topic.replace(/-/g, ' ')
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

// 格式化更新时间
const formatUpdateTime = (time) => {
  if (!time) return ''
  const now = new Date()
  const updateTime = new Date(time)
  const diffMinutes = Math.floor((now - updateTime) / (1000 * 60))
  
  if (diffMinutes < 1) return '刚刚'
  if (diffMinutes < 60) return `${diffMinutes}分钟前`
  
  const diffHours = Math.floor(diffMinutes / 60)
  if (diffHours < 24) return `${diffHours}小时前`
  
  return updateTime.toLocaleDateString()
}
</script>

<style scoped>
.emerging-topics-card {
  border-radius: 12px;
  background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%);
  border: 1px solid rgba(34, 197, 94, 0.2);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #16a34a 0%, #22c55e 100%);
  color: white;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.emerging-icon {
  animation: emerging-pulse 2s infinite;
}

@keyframes emerging-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: white;
}

.emerging-indicator {
  animation: hot-glow 2s infinite alternate;
}

@keyframes hot-glow {
  0% { box-shadow: 0 0 5px rgba(34, 197, 94, 0.5); }
  100% { box-shadow: 0 0 15px rgba(34, 197, 94, 0.8); }
}

.show-all-btn {
  color: white !important;
  background: rgba(255, 255, 255, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

.card-content {
  padding: 16px 20px;
  min-height: 120px;
}

.loading-section, .empty-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  height: 80px;
  color: #666;
}

.topics-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.emerging-topic-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px;
  border-radius: 8px;
  background: rgba(34, 197, 94, 0.05);
  border: 1px solid rgba(34, 197, 94, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

.emerging-topic-item:hover {
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.15);
}

.topic-rank {
  display: flex;
  align-items: center;
  gap: 2px;
  flex-shrink: 0;
}

.rank-text {
  font-size: 10px;
  font-weight: 600;
  color: #16a34a;
}

.topic-info {
  flex: 1;
  min-width: 0;
}

.topic-name {
  font-size: 13px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.topic-stats {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 10px;
  font-weight: 500;
  color: #64748b;
}

.new-badge {
  font-size: 8px;
  font-weight: 700;
  color: #16a34a;
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 4px;
  padding: 1px 4px;
}

.topic-trend {
  flex-shrink: 0;
}

.trend-icon {
  animation: trend-bounce 2s infinite;
}

@keyframes trend-bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-2px); }
}

.card-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 8px;
  background: #f8fafc;
  border-top: 1px solid rgba(34, 197, 94, 0.1);
}

.update-text {
  font-size: 11px;
  color: #666;
}
</style>
