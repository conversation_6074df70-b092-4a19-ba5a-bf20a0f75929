<template>
  <div class="hot-ranking-test">
    <h3>热榜功能测试</h3>
    
    <div class="test-controls">
      <v-btn @click="testBasicFetch" color="primary" class="mr-2">
        测试基础获取
      </v-btn>
      <v-btn @click="testComparisonFetch" color="secondary" class="mr-2">
        测试对比数据获取
      </v-btn>
      <v-btn @click="testDirectAPI" color="info" class="mr-2">
        测试直接API调用
      </v-btn>
      <v-btn @click="clearTestResults" color="warning">
        清空结果
      </v-btn>
    </div>

    <div class="test-results" v-if="testResults.length > 0">
      <h4>测试结果：</h4>
      <div v-for="(result, index) in testResults" :key="index" class="test-result">
        <strong>{{ result.title }}</strong>
        <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
      </div>
    </div>

    <div class="enhanced-topics" v-if="enhancedTopics.length > 0">
      <h4>增强话题数据预览：</h4>
      <div v-for="topic in enhancedTopics.slice(0, 5)" :key="topic.topic" class="topic-preview">
        <div class="topic-info">
          <span class="topic-name">{{ topic.topic }}</span>
          <span class="topic-count">{{ topic.count }}</span>
          <span class="rank-change" :class="topic.rankChange">
            {{ getRankChangeText(topic) }}
          </span>
          <span class="heat-change" v-if="topic.heatChangePercent !== 0">
            {{ topic.heatChangePercent > 0 ? '+' : '' }}{{ topic.heatChangePercent }}%
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useHotRankingStore } from '@/stores/hotRanking'
import { ref } from 'vue'

const hotRankingStore = useHotRankingStore()
const testResults = ref([])
const enhancedTopics = ref([])

const testBasicFetch = async () => {
  try {
    await hotRankingStore.fetchHotRanking('day', true, false)
    testResults.value.push({
      title: '基础获取测试',
      data: {
        topicsCount: hotRankingStore.hotTopics.length,
        cognitionsCount: hotRankingStore.hotCognitions.length,
        sampleTopic: hotRankingStore.hotTopics[0] || null
      }
    })
  } catch (error) {
    testResults.value.push({
      title: '基础获取测试 - 错误',
      data: error.message
    })
  }
}

const testComparisonFetch = async () => {
  try {
    console.log('开始测试对比数据获取...')
    await hotRankingStore.fetchHotRankingWithComparison('day', true)
    enhancedTopics.value = hotRankingStore.hotTopics

    console.log('获取到的热榜数据:', hotRankingStore.hotTopics)

    testResults.value.push({
      title: '对比数据获取测试',
      data: {
        topicsCount: hotRankingStore.hotTopics.length,
        enhancedFields: hotRankingStore.hotTopics[0] ? Object.keys(hotRankingStore.hotTopics[0]) : [],
        sampleEnhancedTopic: hotRankingStore.hotTopics[0] || null,
        hasRankChange: hotRankingStore.hotTopics.some(t => t.rankChange && t.rankChange !== 'stable'),
        hasNewTopics: hotRankingStore.hotTopics.some(t => t.isNew),
        hasHeatChange: hotRankingStore.hotTopics.some(t => t.heatChangePercent !== 0)
      }
    })
  } catch (error) {
    console.error('对比数据获取测试失败:', error)
    testResults.value.push({
      title: '对比数据获取测试 - 错误',
      data: error.message
    })
  }
}

const testDirectAPI = async () => {
  try {
    console.log('开始测试直接API调用...')

    // 导入API
    const { cognitionAPI } = await import('@/api/cognition')

    // 测试当前数据
    const currentResponse = await cognitionAPI.getTrendStats({
      time_filter: 'day',
      search_scope: 'all',
      source_filter: 'all'
    })

    // 测试对比数据
    const comparisonResponse = await cognitionAPI.getTrendStats({
      time_filter: 'yesterday',
      search_scope: 'all',
      source_filter: 'all'
    })

    console.log('当前数据响应:', currentResponse)
    console.log('对比数据响应:', comparisonResponse)

    testResults.value.push({
      title: '直接API调用测试',
      data: {
        currentDataCount: currentResponse?.data?.length || 0,
        comparisonDataCount: comparisonResponse?.data?.length || 0,
        currentSample: currentResponse?.data?.[0] || null,
        comparisonSample: comparisonResponse?.data?.[0] || null,
        currentTotal: currentResponse?.total || 0,
        comparisonTotal: comparisonResponse?.total || 0
      }
    })
  } catch (error) {
    console.error('直接API调用测试失败:', error)
    testResults.value.push({
      title: '直接API调用测试 - 错误',
      data: error.message
    })
  }
}

const clearTestResults = () => {
  testResults.value = []
  enhancedTopics.value = []
}

const getRankChangeText = (topic) => {
  switch (topic.rankChange) {
    case 'up': return `↗️ 上升 (${topic.previousRank} → ${topic.currentRank})`
    case 'down': return `↘️ 下降 (${topic.previousRank} → ${topic.currentRank})`
    case 'new': return '⭐ 新出现'
    case 'stable': return `➖ 持平 (${topic.currentRank})`
    default: return '未知'
  }
}
</script>

<style scoped>
.hot-ranking-test {
  padding: 20px;
  max-width: 800px;
}

.test-controls {
  margin: 20px 0;
}

.test-results {
  margin-top: 20px;
}

.test-result {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.test-result pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}

.enhanced-topics {
  margin-top: 20px;
}

.topic-preview {
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 6px;
}

.topic-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.topic-name {
  font-weight: bold;
  color: #333;
}

.topic-count {
  background: #e3f2fd;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.rank-change {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

.rank-change.up {
  background: #e8f5e8;
  color: #2e7d32;
}

.rank-change.down {
  background: #ffebee;
  color: #c62828;
}

.rank-change.new {
  background: #fff3e0;
  color: #ef6c00;
}

.rank-change.stable {
  background: #f5f5f5;
  color: #666;
}

.heat-change {
  font-size: 11px;
  font-weight: bold;
  color: #1976d2;
}
</style>
