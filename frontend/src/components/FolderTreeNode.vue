<template>
  <div class="tree-node" :class="{ 'selected': selectedId === node.id }">
    <!-- 节点内容 -->
    <div 
      class="tree-node-content"
      @click="selectNode"
      :class="{
        'is-selected': selectedId === node.id,
        'is-folder': node.has_children
      }"
    >
      <div class="tree-node-header">
        <!-- 展开/收起按钮 -->
        <div class="tree-node-toggle" @click.stop="toggleExpanded">
          <v-icon 
            v-if="node.has_children"
            size="16" 
            class="expand-icon"
            :class="{ 'expanded': isExpanded }"
          >
            mdi-chevron-right
          </v-icon>
          <v-icon 
            v-else
            size="14" 
            class="leaf-icon"
          >
            mdi-file-document-outline
          </v-icon>
        </div>
        
        <!-- 节点名称 -->
        <div class="tree-node-name">
          <v-icon 
            :size="node.has_children ? 18 : 16" 
            :color="node.has_children ? '#FFA726' : '#42A5F5'"
            class="mr-2"
          >
            {{ node.has_children ? 'mdi-folder' : 'mdi-file-document' }}
          </v-icon>
          <span class="node-text">{{ node.title }}</span>
        </div>
      </div>
    </div>
    
    <!-- 子节点 -->
    <div v-if="isExpanded && node.children && node.children.length > 0" class="tree-node-children">
      <folder-tree-node
        v-for="child in node.children"
        :key="child.id"
        :node="child"
        :selected-id="selectedId"
        @node-selected="$emit('node-selected', $event)"
      />
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'

export default {
  name: 'FolderTreeNode',
  props: {
    node: {
      type: Object,
      required: true
    },
    selectedId: {
      type: String,
      default: null
    }
  },
  emits: ['node-selected'],
  setup(props, { emit }) {
    const isExpanded = ref(false)
    
    const toggleExpanded = () => {
      if (props.node.has_children) {
        isExpanded.value = !isExpanded.value
      }
    }
    
    const selectNode = () => {
      emit('node-selected', props.node.id)
      
      // 如果是文件夹且未展开，则自动展开
      if (props.node.has_children && !isExpanded.value) {
        isExpanded.value = true
      }
    }
    
    return {
      isExpanded,
      toggleExpanded,
      selectNode
    }
  }
}
</script>

<style scoped>
.tree-node {
  margin-bottom: 2px;
}

.tree-node-content {
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  position: relative;
}

.tree-node-content:hover {
  background-color: #f0f4ff;
  border-color: rgba(103, 126, 234, 0.1);
}

.tree-node-content.is-selected {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.tree-node-content.is-selected:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.tree-node-content.is-selected .node-text {
  color: white;
  font-weight: 500;
}

.tree-node-content.is-selected .expand-icon,
.tree-node-content.is-selected .leaf-icon {
  color: white !important;
}

.tree-node-header {
  display: flex;
  align-items: center;
  width: 100%;
}

.tree-node-toggle {
  margin-right: 8px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 3px;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
}

.tree-node-toggle:hover {
  background-color: rgba(103, 126, 234, 0.1);
}

.expand-icon {
  transition: transform 0.2s ease;
  color: #667eea;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.leaf-icon {
  color: #94A3B8;
  opacity: 0.8;
}

.tree-node-name {
  flex: 1;
  display: flex;
  align-items: center;
  min-width: 0;
}

.node-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #374151;
  font-weight: 450;
  font-size: 0.875rem;
  line-height: 1.4;
}

.tree-node-children {
  margin-left: 16px;
  padding-left: 8px;
  border-left: 1px dashed rgba(148, 163, 184, 0.3);
  margin-top: 4px;
}

.tree-node-content.is-folder {
  /* 文件夹特殊样式 */
}

/* 深层嵌套的渐变效果 */
.tree-node-children .tree-node-children {
  border-left-color: rgba(148, 163, 184, 0.2);
}

.tree-node-children .tree-node-children .tree-node-children {
  border-left-color: rgba(148, 163, 184, 0.1);
}

/* 响应式设计 */
@media (max-width: 640px) {
  .tree-node-content {
    padding: 6px 8px;
  }
  
  .node-text {
    font-size: 0.8rem;
  }
  
  .tree-node-children {
    margin-left: 12px;
    padding-left: 6px;
  }
}
</style> 