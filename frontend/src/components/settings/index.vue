<template>
  <div class="settings-dialog">
    <v-menu
      v-model="menuOpen"
      :close-on-content-click="false"
      location="bottom end"
      transition="scale-transition"
    >
      <template v-slot:activator="{ props }">
        <v-btn
          v-bind="props"
          icon
          variant="text"
          color="#654C8C"
          size="small"
          class="tool-btn"
          title="设置"
        >
          <v-icon>mdi-cog</v-icon>
        </v-btn>
      </template>

      <v-card min-width="300px" class="settings-menu-card">
        <v-card-title class="text-center pb-0">
          <span class="text-h6">模型设置</span>
        </v-card-title>
        
        <v-card-text class="pt-4">
          <!-- 思考模型设置 -->
          <div class="setting-section">
            <div class="setting-title">思考模型 (Thinking Models)</div>
            <v-select
              v-model="settings.thinkingModel"
              :items="thinkingModelOptions"
              label="选择思考模型"
              variant="outlined"
              density="compact"
              @update:model-value="updateSettings('thinkingModel')"
            ></v-select>
          </div>
          
          <!-- 草稿模型设置 -->
          <div class="setting-section">
            <div class="setting-title">草稿模型 (Drafting Models)</div>
            <v-select
              v-model="settings.draftingModel"
              :items="draftingModelOptions"
              label="选择草稿模型"
              variant="outlined"
              density="compact"
              @update:model-value="updateSettings('draftingModel')"
            ></v-select>
          </div>
        </v-card-text>
        
        <v-divider></v-divider>
        
        <v-card-actions class="justify-center pa-3">
          <v-btn
            color="primary"
            variant="tonal"
            @click="saveSettings"
          >
            保存设置
          </v-btn>
          <!-- <v-btn
            color="grey"
            variant="tonal"
            @click="resetSettings"
          >
            重置默认
          </v-btn> -->
        </v-card-actions>
      </v-card>
    </v-menu>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, onMounted } from 'vue';
import { useAuthStore } from '@/stores/authStore';
import { researchApi } from '@/api/chat';
export default defineComponent({
  name: 'SettingsDialog',
  emits: ['update-settings'],
  setup(props, { emit }) {
    const authStore = useAuthStore();
    const menuOpen = ref(false);
    
    // 设置选项
    const settings = reactive({
      thinkingModel: 'gpt-4',
      draftingModel: 'gpt-3.5-turbo',
    });
    
    // 思考模型选项
    const thinkingModelOptions = ref([
    ]);
    
    // 草稿模型选项
    const draftingModelOptions = ref([
    ]);
    
    // 初始化设置
    onMounted(async () => {
      const savedSettings = await researchApi.getAvailableModels();
      if (savedSettings) {
        const availableModelList = savedSettings.available_models.map((model: any) => ({
          title: model.id,
          value: model.id
        }));
        thinkingModelOptions.value = availableModelList;
        draftingModelOptions.value = availableModelList;
        settings.thinkingModel = savedSettings.default_models.action_selection_model;
        if (savedSettings.current_models.hasOwnProperty('action_selection_model')) {
          settings.thinkingModel = savedSettings.current_models.action_selection_model;
        }
        settings.draftingModel = savedSettings.default_models.report_editing_model;
        if (savedSettings.current_models.hasOwnProperty('report_editing_model')) {
          settings.draftingModel = savedSettings.current_models.report_editing_model;
        }
      }
    });
    
    // 更新设置
    const updateSettings = (key: string) => {
      // 通知父组件设置已更新
    //   emit('update-settings', { key, value: settings[key as keyof typeof settings] });
    };
    
    // 保存设置
    const saveSettings = () => {
      localStorage.setItem('modelSettings', JSON.stringify(settings));
      menuOpen.value = false;
      // 通知应用设置已全部保存
      emit('update-settings', { settings });
    };
    
    // 重置设置
    const resetSettings = () => {
      settings.thinkingModel = 'gpt-4';
      settings.draftingModel = 'gpt-3.5-turbo';
      
      localStorage.removeItem('modelSettings');
      // 通知应用设置已重置
      emit('update-settings', { settings });
    };

    return {
      menuOpen,
      settings,
      thinkingModelOptions,
      draftingModelOptions,
      updateSettings,
      saveSettings,
      resetSettings,
    };
  }
});
</script>

<style scoped>
.settings-dialog {
  display: inline-block;
}

.settings-menu-card {
  overflow: visible;
  max-width: 400px;
}

.setting-section {
  margin-bottom: 16px;
}

.setting-title {
  font-weight: 500;
  margin-bottom: 8px;
  color: rgba(0, 0, 0, 0.6);
}
</style>