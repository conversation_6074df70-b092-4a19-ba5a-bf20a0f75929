<template>
  <div class="token-usage-manager">
    <div class="header-section">
      <h2>Token 用量统计</h2>
      <div class="actions">
        <button class="btn-refresh" @click="loadTokenUsage" :disabled="isLoading">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': isLoading }"></i>
          刷新数据
        </button>
      </div>
    </div>

    <div class="summary-cards">
      <div class="card">
        <div class="card-title">总用户数</div>
        <div class="card-value">{{ summary.total_users || 0 }}</div>
      </div>
      <div class="card">
        <div class="card-title">总会话数</div>
        <div class="card-value">{{ summary.total_sessions || 0 }}</div>
      </div>
      <div class="card">
        <div class="card-title">总输入Token</div>
        <div class="card-value">{{ formatNumber(summary.total_input_tokens || 0) }}</div>
      </div>
      <div class="card">
        <div class="card-title">总输出Token</div>
        <div class="card-value">{{ formatNumber(summary.total_output_tokens || 0) }}</div>
      </div>
      <div class="card highlight">
        <div class="card-title">总成本 (¥)</div>
        <div class="card-value">{{ formatCurrency(summary.total_cost || 0) }}</div>
      </div>
    </div>

    <div class="usage-table">
      <table>
        <thead>
          <tr>
            <th>用户ID</th>
            <th>会话数</th>
            <th>输入Token</th>
            <th>输出Token</th>
            <th>总Token</th>
            <th>成本 ($)</th>
            <th>最近使用</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="isLoading">
            <td colspan="7" class="loading">
              <i class="fas fa-spinner fa-spin"></i> 加载中...
            </td>
          </tr>
          <tr v-else-if="usageData.length === 0">
            <td colspan="7" class="no-data">暂无用户Token使用数据</td>
          </tr>
          <tr v-for="user in usageData" :key="user._id">
            <td>{{ user.username }}</td>
            <td>{{ user.sessions_count }}</td>
            <td>{{ formatNumber(user.total_input_tokens) }}</td>
            <td>{{ formatNumber(user.total_output_tokens) }}</td>
            <td>{{ formatNumber(user.total_input_tokens + user.total_output_tokens) }}</td>
            <td class="cost">{{ formatCurrency(user.total_cost) }}</td>
            <td>{{ formatDate(user.latest_usage) }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { researchApi } from '@/api/chat';

interface UserTokenUsage {
  _id: string;
  username: string;
  sessions_count: number;
  total_input_tokens: number;
  total_output_tokens: number;
  total_cost: number;
  latest_usage: string;
}

interface Summary {
  total_users: number;
  total_sessions: number;
  total_input_tokens: number;
  total_output_tokens: number;
  total_cost: number;
}

// 状态
const usageData = ref<UserTokenUsage[]>([]);
const isLoading = ref(false);
const summary = ref<Summary>({
  total_users: 0,
  total_sessions: 0,
  total_input_tokens: 0,
  total_output_tokens: 0,
  total_cost: 0
});

// 加载Token使用统计数据
const loadTokenUsage = async () => {
  isLoading.value = true;
  try {
    const response = await researchApi.getAllUsersTokenUsage();
    usageData.value = response.data || [];
    summary.value = response.summary || {
      total_users: 0,
      total_sessions: 0,
      total_input_tokens: 0,
      total_output_tokens: 0,
      total_cost: 0
    };
  } catch (error) {
    console.error('获取Token使用统计失败:', error);
    // 可以添加提示信息
  } finally {
    isLoading.value = false;
  }
};

// 格式化数字，添加千位分隔符
const formatNumber = (num: number): string => {
  return num.toLocaleString('zh-CN');
};

// 格式化货币，保留2位小数
const formatCurrency = (num: number): string => {
  return num.toFixed(2);
};

// 格式化日期
const formatDate = (dateString: string): string => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN');
};

// 组件挂载后加载数据
onMounted(() => {
  loadTokenUsage();
});
</script>

<style scoped>
.token-usage-manager {
  width: 100%;
  padding: 20px;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-section h2 {
  margin: 0;
  font-size: 24px;
}

.btn-refresh {
  background-color: #3699ff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-refresh:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.summary-cards {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.card {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-width: 160px;
  flex: 1;
}

.card.highlight {
  background-color: #3699ff;
  color: white;
}

.card-title {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 8px;
}

.card.highlight .card-title {
  color: rgba(255, 255, 255, 0.8);
}

.card-value {
  font-size: 24px;
  font-weight: 600;
}

.usage-table {
  width: 100%;
  overflow-x: auto;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

th {
  background-color: #f8f9fa;
  font-weight: 600;
  position: sticky;
  top: 0;
}

.cost {
  font-weight: 600;
  color: #e74c3c;
}

.loading, .no-data {
  text-align: center;
  padding: 20px;
  color: #6c757d;
}
</style> 