import { ref, reactive } from 'vue';

// Token budget错误信息接口
interface TokenBudgetError {
  remainingBudget: number;
  requiredBudget: number;
  message: string;
}

// Token budget状态
interface TokenBudgetState {
  isDialogVisible: boolean;
  errorInfo: TokenBudgetError | null;
}

export function useTokenBudget() {
  // 状态管理
  const state = reactive<TokenBudgetState>({
    isDialogVisible: false,
    errorInfo: null
  });

  /**
   * 解析token budget错误信息
   * @param error 错误对象
   * @returns 解析后的错误信息，如果不是token budget错误则返回null
   */
  const parseTokenBudgetError = (error: any): TokenBudgetError | null => {
    // 检查是否是HTTP 402错误（Payment Required）
    if (error?.response?.status !== 402) {
      return null;
    }

    const errorMessage = error?.response?.data?.detail || error?.message || '';
    
    // 检查错误消息是否包含token budget相关信息
    if (!errorMessage.includes('Token') && !errorMessage.includes('额度')) {
      return null;
    }

    // 尝试从错误消息中提取数字信息
    let remainingBudget = 0;
    let requiredBudget = 0;

    try {
      // 匹配剩余额度的正则表达式
      const remainingMatch = errorMessage.match(/剩余额度[：:]\s*([\d.]+)/);
      if (remainingMatch) {
        remainingBudget = parseFloat(remainingMatch[1]);
      }

      // 匹配需要额度的正则表达式
      const requiredMatch = errorMessage.match(/需要额度[：:]\s*([\d.]+)/);
      if (requiredMatch) {
        requiredBudget = parseFloat(requiredMatch[1]);
      }

      // 如果没有匹配到具体数字，尝试英文格式
      if (remainingBudget === 0 && requiredBudget === 0) {
        const remainingEnMatch = errorMessage.match(/remaining.*?budget[：:]?\s*([\d.]+)/i);
        const requiredEnMatch = errorMessage.match(/required.*?budget[：:]?\s*([\d.]+)/i);
        
        if (remainingEnMatch) {
          remainingBudget = parseFloat(remainingEnMatch[1]);
        }
        if (requiredEnMatch) {
          requiredBudget = parseFloat(requiredEnMatch[1]);
        }
      }
    } catch (e) {
      console.warn('[Token Budget] 解析错误信息失败:', e);
    }

    return {
      remainingBudget,
      requiredBudget,
      message: errorMessage
    };
  };

  /**
   * 检查错误是否为token budget不足错误
   * @param error 错误对象
   * @returns 是否为token budget错误
   */
  const isTokenBudgetError = (error: any): boolean => {
    return parseTokenBudgetError(error) !== null;
  };

  /**
   * 处理token budget错误
   * @param error 错误对象
   * @returns 是否成功处理了token budget错误
   */
  const handleTokenBudgetError = (error: any): boolean => {
    const budgetError = parseTokenBudgetError(error);
    
    if (!budgetError) {
      return false;
    }

    // 更新状态并显示弹窗
    state.errorInfo = budgetError;
    state.isDialogVisible = true;

    console.warn('[Token Budget] Token额度不足:', budgetError);
    
    return true;
  };

  /**
   * 关闭token budget弹窗
   */
  const closeTokenBudgetDialog = () => {
    state.isDialogVisible = false;
    state.errorInfo = null;
  };

  /**
   * 刷新页面（用于重新检查token状态）
   */
  const refreshPage = () => {
    window.location.reload();
  };

  /**
   * 重置token budget状态
   */
  const resetTokenBudgetState = () => {
    state.isDialogVisible = false;
    state.errorInfo = null;
  };

  return {
    // 状态
    tokenBudgetState: state,
    
    // 方法
    parseTokenBudgetError,
    isTokenBudgetError,
    handleTokenBudgetError,
    closeTokenBudgetDialog,
    refreshPage,
    resetTokenBudgetState
  };
} 