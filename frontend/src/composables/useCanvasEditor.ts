import { ref, Ref } from 'vue';
import { Editor } from '@tiptap/vue-3';

interface TagReplacement {
  search: string;
  replace: string;
}

interface AnimationOptions {
  // 是否启用动画
  enabled: boolean;
  // 删除字符的间隔时间(ms)
  deleteDelay: number;
  // 添加字符的间隔时间(ms)
  insertDelay: number;
  // 删除与添加之间的延迟(ms)
  pauseDelay: number;
  // 动画结束后的回调
  onComplete?: () => void;
  // 动画进度回调
  onProgress?: (progress: number) => void;
}

// 将AnimationOptions导出为类型
export type { AnimationOptions, TagReplacement };

// 默认动画选项
const DEFAULT_ANIMATION_OPTIONS: AnimationOptions = {
  enabled: true,
  deleteDelay: 20,
  insertDelay: 20,
  pauseDelay: 300
};

export function useCanvasEditor() {
  // 编辑器引用
  const editor: Ref<Editor | null> = ref(null);
  
  // 当前内容
  const currentContent = ref('');
  
  // 动画中标志
  const isAnimating = ref(false);
  
  // 设置编辑器实例
  const setEditor = (editorInstance: Editor) => {
    editor.value = editorInstance;
  };
  
  // 更新内容
  const updateContent = (newContent: string) => {
    // console.log('[DEBUG useCanvasEditor] 更新内容:', newContent.substring(0, 50) + "...");
    currentContent.value = newContent;
  };
  
  // 获取markdown内容
  const getMarkdownContent = (): string => {
    if (!editor.value) {
      return currentContent.value;
    }
    
    try {
      // 优先使用编辑器的markdown存储获取markdown内容
      if (typeof editor.value.storage.markdown?.getMarkdown === 'function') {
        return editor.value.storage.markdown.getMarkdown();
      }
      
      // 回退到当前内容
      return currentContent.value;
    } catch (error) {
      console.error('获取Markdown内容失败:', error);
      return currentContent.value;
    }
  };
  
  // 设置markdown内容
  const setMarkdownContent = (markdown: string): boolean => {
    if (!editor.value) {
      // console.warn('[DEBUG useCanvasEditor] 设置内容失败: 编辑器未初始化');
      return false;
    }
    
    try {
      // console.log('[DEBUG useCanvasEditor] 设置Markdown内容:', markdown.substring(0, 50) + "...");
      
      // @ts-ignore - 某些版本的Tiptap可能有这个方法
      if (editor.value.commands.setMarkdown && typeof editor.value.commands.setMarkdown === 'function') {
        // @ts-ignore
        editor.value.commands.setMarkdown(markdown);
        currentContent.value = markdown;
        // console.log('[DEBUG useCanvasEditor] 通过setMarkdown方法设置内容成功');
        return true;
      }
      
      // 如果没有setMarkdown方法，则尝试设置HTML
      // console.log('[DEBUG useCanvasEditor] setMarkdown方法不存在，尝试setContent');
      editor.value.commands.setContent(markdown);
      currentContent.value = markdown;
      // console.log('[DEBUG useCanvasEditor] 通过setContent方法设置内容成功');
      return true;
    } catch (error) {
      // console.error('设置Markdown内容失败:', error);
      return false;
    }
  };
  
  // 在Markdown源码中搜索文本并替换
  const replaceAllTextInMarkdown = (search: string, replace: string): boolean => {
    if (!editor.value || !search) {
      return false;
    }
    
    try {
      // 获取当前Markdown内容
      const markdownContent = getMarkdownContent();
      
      // 检查是否有匹配项
      if (!markdownContent.includes(search)) {
        return false;
      }
      
      // 执行替换并更新内容
      const newContent = markdownContent.split(search).join(replace);
      
      // 设置新内容
      return setMarkdownContent(newContent);
    } catch (error) {
      // console.error('在Markdown中替换文本失败:', error);
      return false;
    }
  };
  
  // 逐字替换实现（带动画效果）
  const animatedReplaceInMarkdown = async (
    search: string, 
    replace: string, 
    options: Partial<AnimationOptions> = {}
  ): Promise<boolean> => {
    // 如果编辑器不可用或搜索为空，则返回false
    if (!editor.value || !search) {
      return false;
    }
    
    // 合并选项
    const animOptions: AnimationOptions = {
      ...DEFAULT_ANIMATION_OPTIONS,
      ...options
    };
    
    // 如果不启用动画，则直接使用普通替换
    if (!animOptions.enabled) {
      return replaceAllTextInMarkdown(search, replace);
    }
    
    try {
      // 设置动画状态
      isAnimating.value = true;
      
      // 获取原始内容
      const originalContent = getMarkdownContent();
      
      // 检查是否有匹配项
      if (!originalContent.includes(search)) {
        isAnimating.value = false;
        return false;
      }
      
      // 创建一个副本，用于在动画过程中修改内容
      let tempContent = originalContent;
      
      // 查找所有匹配位置
      const positions: number[] = [];
      let lastIndex = 0;
      let index = -1;
      
      while ((index = tempContent.indexOf(search, lastIndex)) !== -1) {
        positions.push(index);
        lastIndex = index + search.length;
      }
      
      // 如果没有匹配位置，则返回false
      if (positions.length === 0) {
        isAnimating.value = false;
        return false;
      }
      
      // 计算动画总步骤
      const totalSteps = positions.length * (search.length + replace.length);
      let currentStep = 0;
      
      // 从最后一个位置开始处理，避免位置偏移
      for (let i = positions.length - 1; i >= 0; i--) {
        const start = positions[i];
        const end = start + search.length;
        
        // 保存当前位置前后的文本，以便后续拼接
        const prefix = tempContent.substring(0, start);
        const suffix = tempContent.substring(end);
        
        // 动态替换文本（先删除）
        for (let j = end - start - 1; j >= 0; j--) {
          const content = prefix + search.substring(0, j) + suffix;
          setMarkdownContent(content);
          tempContent = content; // 更新临时内容
          
          // 更新进度
          currentStep++;
          if (animOptions.onProgress) {
            animOptions.onProgress(currentStep / totalSteps);
          }
          
          // 等待删除延迟
          await new Promise(resolve => setTimeout(resolve, animOptions.deleteDelay));
        }
        
        // 等待暂停延迟
        await new Promise(resolve => setTimeout(resolve, animOptions.pauseDelay));
        
        // 动态替换文本（后添加）
        for (let j = 0; j <= replace.length; j++) {
          const content = prefix + replace.substring(0, j) + suffix;
          setMarkdownContent(content);
          tempContent = content; // 更新临时内容
          
          // 更新进度
          currentStep++;
          if (animOptions.onProgress) {
            animOptions.onProgress(currentStep / totalSteps);
          }
          
          // 最后一个字符不需要延迟
          if (j < replace.length) {
            // 等待插入延迟
            await new Promise(resolve => setTimeout(resolve, animOptions.insertDelay));
          }
        }
      }
      
      // 最后再设置一次完整内容，确保格式一致
      const finalContent = originalContent.split(search).join(replace);
      setMarkdownContent(finalContent);
      
      // 动画完成
      isAnimating.value = false;
      
      // 调用完成回调
      if (animOptions.onComplete) {
        animOptions.onComplete();
      }
      
      return true;
    } catch (error) {
      console.error('动画替换失败:', error);
      isAnimating.value = false;
      
      // 出错时尝试使用普通替换
      return replaceAllTextInMarkdown(search, replace);
    }
  };
  
  // 处理标签替换 <search>text</search> -> <replace>text</replace>
  const processTagReplacement = (tagData: TagReplacement, animOptions?: Partial<AnimationOptions>): Promise<boolean> | boolean => {
    const { search, replace } = tagData;
    
    if (!editor.value || !search) {
      return false;
    }
    
    // 根据是否提供了动画选项决定使用哪种替换方式
    if (animOptions) {
      return animatedReplaceInMarkdown(search, replace, animOptions);
    } else {
      return replaceAllTextInMarkdown(search, replace);
    }
  };
  
  // 解析标签字符串 - 处理 <search>text</search> <replace>newText</replace> 格式
  const parseAndReplaceTags = (
    tagString: string, 
    animOptions?: Partial<AnimationOptions>
  ): Promise<boolean> | boolean => {
    // 提取搜索标签内容
    const searchMatch = tagString.match(/<search>(.*?)<\/search>/);
    if (!searchMatch || !searchMatch[1]) {
      return false;
    }
    
    // 提取替换标签内容
    const replaceMatch = tagString.match(/<replace>(.*?)<\/replace>/);
    if (!replaceMatch || !replaceMatch[1]) {
      return false;
    }
    
    return processTagReplacement({
      search: searchMatch[1],
      replace: replaceMatch[1]
    }, animOptions);
  };
  
  // 获取当前内容
  const getContent = (): string => {
    return getMarkdownContent();
  };
  
  // 设置内容
  const setContent = (content: string): boolean => {
    return setMarkdownContent(content);
  };
  
  return {
    editor,
    currentContent,
    isAnimating,
    setEditor,
    updateContent,
    replaceAllTextInMarkdown,
    animatedReplaceInMarkdown,
    processTagReplacement,
    parseAndReplaceTags,
    getContent,
    setContent,
    getMarkdownContent,
    setMarkdownContent
  };
} 