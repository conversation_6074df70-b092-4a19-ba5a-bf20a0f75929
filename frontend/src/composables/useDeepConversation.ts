// useDeepConversation.ts
import type { UserPreferences } from '@/api/chat';
import { researchApi, chatApi } from '@/api/chat';
import { DIFF_DELETE, DIFF_EQUAL, DIFF_INSERT, diff_match_patch } from 'diff-match-patch';
import { v4 as uuidv4 } from 'uuid';
import { reactive, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useTokenBudget } from './useTokenBudget';
// import { CanvasEditorItem, CanvasItem, ChatMessageItem } from '@/types/canvas';

// ===================== 基础数据结构 =====================

// 基础消息接口
interface BaseItem {
  id: string;
  timestamp: number;
  type: string;
  draft_id?: string;
}

// 文件描述接口
interface FileDescription {
  name: string;
  size?: number;
  type?: string;
  lastModified?: number;
  isRemote?: boolean;
  contextId?: string;
  metadata?: {
    sourceContextId?: string;
  };
}

// 扩展文件类型
type ExtendedFile = File | FileDescription;

export type { ExtendedFile, FileDescription }
// ===================== Canvas数据结构 =====================

// Canvas消息类型 - 普通聊天消息
interface ChatMessageItem extends BaseItem {
  type: 'chat';
  content: string;
  isUser: boolean;
}

// Canvas消息类型 - 编辑器内容
interface CanvasEditorItem extends BaseItem {
  type: 'editor';
  content: string;
  // 版本历史管理
  versions: Array<{
    version: number;
    content: string;
    timestamp: number;
    round?: number;
    draft_id?: string;
  }>;
  currentVersion: number; // 当前显示的版本索引（从0开始）
  // 可以添加其他编辑器相关属性，如光标位置、选择范围等
  cursorPosition?: number;
  selection?: { from: number, to: number };
}

// Canvas消息类型 - 待澄清问题
interface ClarificationItem extends BaseItem {
  type: 'clarification';
  questions: Array<{
    id: number;
    content: string;
    options?: string[];
  }>;
  activeQuestionIndex?: number;
  isAnswered?: boolean;
}

// Canvas项目联合类型
type CanvasItem = ChatMessageItem | CanvasEditorItem | ClarificationItem;

export type { BaseItem, CanvasEditorItem, CanvasItem, ChatMessageItem, ClarificationItem };

// ===================== Interface数据结构 =====================

// 思考区域模型
interface ThoughtItem extends BaseItem {
  type: 'thought';
  label: string;
  icon: string;
  completionInfo: string;
  content: string[];
  isExpanded?: boolean;
  stage?: string;
  round?: number;
  acting?: string;
}

// 观察区域模型
interface ObservationItem extends BaseItem {
  type: 'observation';
  label: string;
  icon: string;
  content: {
    searchQuery: string;
  };
  stage?: string;
  round?: number;
}

// 行动区域预览项
interface PreviewItem {
  title: string;
  url: string;
  snippet: string;
  summary: string;
  useful_information: string;
  liked?: boolean; // 新增：用户是否点赞该内容
}

// 行动区域模型
interface ActionItem extends BaseItem {
  type: 'action';
  label: string;
  icon: string;
  content: {
    browsing: string;
    previews: PreviewItem[];
  };
  stage?: string;
  round?: number;
}

// 用户查询模型
interface UserQueryItem extends BaseItem {
  type: 'userquery';
  label: string;
  icon: string;
  content: string;
  round?: number;
}

interface SystemItem extends BaseItem {
  type: 'system';
  label: string;
  icon: string;
  content: string;
  round?: number;
}

// 模型动作模型 - 用于展示编辑草稿等模型动作
interface ModelActionItem extends BaseItem {
  type: 'modelaction';
  label: string;
  icon: string;
  content: {
    actionType: 'edit_draft' | 'create_draft' | 'revise_draft';
    description: string;
    beforeContent?: string;  // 编辑前的内容（可选）
    afterContent?: string;   // 编辑后的内容（可选）
    changes?: {
      added: number;    // 添加的字符数
      deleted: number;  // 删除的字符数
      modified: number; // 修改的段落数
    };
  };
  stage?: string;
  round?: number;
  isCompleted?: boolean;  // 是否已完成
}

// Interface项目联合类型
type InterfaceItem = ThoughtItem | ObservationItem | ActionItem | UserQueryItem | SystemItem | ModelActionItem;

// 导出接口类型
export type { ActionItem, InterfaceItem, ObservationItem, PreviewItem, SystemItem, ThoughtItem, UserQueryItem, ModelActionItem };


// ===================== 响应处理类型 =====================

// 处理响应的类型
export interface ExtendedDeepCognitionResponse {
  agent?: string;
  stage?: string;
  content_type?: string;
  action?: string;
  questions?: any[];
  content?: any;
  draft_id?: string;
  text?: string;
  error?: string;
  replay?: boolean;
  round?: number;
  url?: string;
}

// ===================== 会话状态管理 =====================

// ===================== Draft历史记录 =====================
interface DraftHistory {
  content: string;
  round: number;
  timestamp: number;
}

export function useDeepConversation() {
  // ========== 全局状态 ==========
  const isProcessing = ref(false);
  const chatError = ref('');
  const currentDraftId = ref<string | null>(null);
  const currentConversationId = ref<string | null>(null);
  const currentDraftRound = ref<number>(0);
  const currentFeedbackRound = ref<number>(0);
  const draftHistory = reactive<DraftHistory[]>([]);
  const diffParts = ref<{ text: string, type: number, status: 'pending' | 'animating' | 'complete' }[]>([]);
  const showReportFeedback = ref(false);
  const currentSessionId = ref<string | null>(null);
  const isCognitionEnabled = ref(false);
  const isSearchEnabled = ref(false);
  const userPreferences = reactive<UserPreferences>({
    professional: 3,
    critical: 3,
    comparison: 3,
    organization: 3,
    cutting_edge: 3,
    coverage: 3,
    depth: 3
  });
  const closeReportFeedback = () => {
    showReportFeedback.value = false;
  };

  // ========== 用户偏好设置相关方法 ==========
  const updateUserPreferences = (newPreferences: Partial<UserPreferences>) => {
    console.log('[useDeepConversation] 接收到偏好设置更新:', newPreferences);
    console.log('[useDeepConversation] 更新前的userPreferences:', { ...userPreferences });
    Object.assign(userPreferences, newPreferences);
    console.log('[useDeepConversation] 更新后的userPreferences:', { ...userPreferences });
  };

  // ========== Token Budget 管理 ==========
  const {
    tokenBudgetState,
    handleTokenBudgetError,
    closeTokenBudgetDialog,
    refreshPage,
    resetTokenBudgetState
  } = useTokenBudget();

  // 创建文件列表
  const uploadedFiles = ref<ExtendedFile[]>([]);
  const loadingStates = ref<boolean[]>([]);

  // 添加同步状态标志
  const isSyncing = ref(false);

  // 添加文件相关方法
  const addUploadedFile = (file: File) => {
    uploadedFiles.value = [...uploadedFiles.value, file];
    loadingStates.value = [...loadingStates.value, false];
  };

  const removeUploadedFile = (index: number) => {
    uploadedFiles.value = uploadedFiles.value.filter((_, i) => i !== index);
    loadingStates.value = loadingStates.value.filter((_, i) => i !== index);
  };

  const clearUploadedFiles = () => {
    uploadedFiles.value = [];
    loadingStates.value = [];
  };

  const updateResearchStatus = (enable_cognition_search: boolean) => {
    console.log('[Deep Conversation] 更新认知搜索状态form:', enable_cognition_search);
    isCognitionEnabled.value = enable_cognition_search;
    if (!currentConversationId.value) {
      return;
    }
    console.log('[Deep Conversation] 更新认知搜索状态:', isCognitionEnabled.value);
    researchApi.updateResearchStatus({
      session_id: currentConversationId.value,
      enable_cognition_search: enable_cognition_search
    });
  };

  // 添加搜索状态更新函数
  const updateSearchStatus = (enable_search: boolean) => {
    console.log('[Deep Conversation] 更新搜索状态from:', enable_search);
    isSearchEnabled.value = enable_search;
    // 如果有当前会话ID，立即同步状态到后端
    if (currentConversationId.value) {
      console.log('[Deep Conversation] 更新搜索状态:', isSearchEnabled.value);
      // 调用搜索状态更新API
      researchApi.updateSearchStatus({
        session_id: currentConversationId.value,
        enable_search: enable_search
      });
    }
  };

  // 创建同步函数（移除防抖）
  const syncFilesWithBackend = async (newFiles: typeof uploadedFiles.value, oldFiles: typeof uploadedFiles.value) => {
    // 直接使用最新的 currentConversationId.value
    const currentId = currentConversationId.value;
    // 如果没有当前会话ID，不进行同步
    if (!currentId) {
      return;
    }

    try {
      isSyncing.value = true;
      // 重置所有loading状态为false
      loadingStates.value = new Array(newFiles.length).fill(false);

      // 获取当前所有上下文
      const contexts = await researchApi.getContexts(currentId);
      const existingContexts = contexts.contexts || [];

      // 找出需要删除的上下文（在旧列表中存在但在新列表中不存在的文件）
      const filesToDelete = oldFiles.filter(oldFile => {
        // 如果是远程文件，检查contextId
        if ('isRemote' in oldFile && oldFile.isRemote) {
          return !newFiles.some(newFile =>
            'isRemote' in newFile &&
            newFile.isRemote &&
            newFile.contextId === oldFile.contextId
          );
        }
        // 如果是本地文件，检查文件名
        return !newFiles.some(newFile =>
          !('isRemote' in newFile) &&
          newFile instanceof File &&
          newFile.name === (oldFile as File).name
        );
      });

      // 找出需要添加的文件（在新列表中存在但在旧列表中不存在的文件）
      const filesToAdd = newFiles.filter(newFile => {
        // 如果是远程文件，检查contextId
        if ('isRemote' in newFile && newFile.isRemote) {
          return !oldFiles.some(oldFile =>
            'isRemote' in oldFile &&
            oldFile.isRemote &&
            oldFile.contextId === newFile.contextId
          );
        }
        // 如果是本地文件，检查文件名
        return !oldFiles.some(oldFile =>
          !('isRemote' in oldFile) &&
          oldFile instanceof File &&
          oldFile.name === (newFile as File).name
        );
      });

      // 处理需要删除的上下文
      for (const file of filesToDelete) {
        if ('isRemote' in file && file.isRemote && file.contextId) {
          const contextToDelete = existingContexts.find(ctx =>
            ctx.context_id === file.contextId
          );
          if (contextToDelete) {
            console.log(`[Deep Conversation] 删除文件上下文: ${file.name}`);
            await researchApi.deleteContext(currentId, contextToDelete.context_id);
          }
        }
      }

      // 处理需要添加的文件
      for (const file of filesToAdd) {
        if (file instanceof File) {
          console.log(`[Deep Conversation] 添加文件: ${file.name}`);
          // 获取文件在数组中的索引
          const fileIndex = newFiles.findIndex(f =>
            f instanceof File && f.name === file.name
          );
          if (fileIndex !== -1) {
            // 更新loading状态
            loadingStates.value[fileIndex] = true;
            console.log(`[Deep Conversation] 更新loading状态: ${fileIndex} 为 true`);
            try {
              await researchApi.uploadFile(currentId, file);
            } finally {
              // 无论成功失败都重置loading状态
              loadingStates.value[fileIndex] = false;
              console.log(`[Deep Conversation] 更新loading状态: ${fileIndex} 为 false`);
            }
          }
        }
      }

      // 同步完成后，重新获取所有上下文以确保状态一致
      const updatedContexts = await researchApi.getContexts(currentId);
      console.log('[Deep Conversation] 文件同步完成，当前上下文列表:', updatedContexts);

      // 将上下文转换为统一的远程文件格式
      const remoteFiles = updatedContexts.contexts.map(ctx => ({
        name: ctx.context_id || '未知文件',
        type: ctx.metadata?.file_type || 'application/octet-stream',
        size: ctx.metadata?.file_size || 0,
        lastModified: ctx.metadata?.last_modified || Date.now(),
        isRemote: true,
        contextId: ctx.context_id
      }));

      // 检查是否有实际变化
      const hasChanges = remoteFiles.some(remoteFile => {
        return !uploadedFiles.value.some(currentFile =>
          'isRemote' in currentFile &&
          currentFile.isRemote &&
          currentFile.contextId === remoteFile.contextId
        );
      });

      // 只有在有变化时才更新
      if (hasChanges) {
        console.log('[Deep Conversation] 检测到文件列表变化，更新文件列表');
        uploadedFiles.value = remoteFiles;
      } else {
        console.log('[Deep Conversation] 文件列表无变化，保持当前状态');
      }

    } catch (error) {
      console.error('[Deep Conversation] 文件同步失败:', error);
    } finally {
      isSyncing.value = false;
    }
  };

  // 使用 watch 替代 watchEffect
  watch(uploadedFiles, (newFiles, oldFiles) => {
    const currentId = currentConversationId.value;

    if (currentId && newFiles.length > 0) {
      // 使用 setTimeout 确保在下一个事件循环中执行，避免可能的竞态条件
      setTimeout(() => {
        syncFilesWithBackend(newFiles, oldFiles);
      }, 0);
    }
  });

  // 用于存储当前编辑器的源文本（不含标记）
  const currentSourceText = ref<string>("");

  // 标记是否有编辑器，用于决定使用哪个API
  const hasEditor = ref<boolean>(false);

  // 请求计数器，用于决定使用哪个API
  const requestCounter = ref<number>(0);

  // 用于防抖控制的计时器引用
  let updateDebounceTimer: number | null = null;
  // 防抖延迟时间(毫秒)
  const DEBOUNCE_DELAY = 300;
  // 最新一次准备更新的编辑器ID和内容
  let pendingEditorId: string | null = null;
  let pendingEditorContent: string | null = null;

  // 新增：用于存储待处理的报告片段，等待全部接收完成后统一处理
  const pendingReportChunks = reactive<{
    chunks: string[],
    editorId: string | null,
    round: number,
    processing: boolean
  }>({
    chunks: [],
    editorId: null,
    round: 0,
    processing: false
  });

  // 新增：动画相关状态
  const animationState = reactive({
    playing: false,
    currentStep: 0,
    totalSteps: 0,
    stepInterval: 150, // 动画步骤间隔（毫秒）
  });

  // ========== Canvas数据流 ==========
  const canvasItems = reactive<CanvasItem[]>([
  ]);

  // ========== Interface数据流 ==========
  const interfaceItems = reactive<InterfaceItem[]>([]);

  const router = useRouter();

  // ========== 方法：添加Canvas项目 ==========
  const addChatMessage = (content: string, isUser: boolean): string => {
    const id = uuidv4();
    const newItem: ChatMessageItem = {
      id,
      timestamp: Date.now(),
      type: 'chat',
      content,
      isUser
    };

    // canvasItems.push(newItem);
    console.log(`[Deep Conversation] 添加新的聊天消息: ${isUser ? '用户' : 'AI'}`);
    return id;
  };

  const updateChatMessage = (id: string, content: string): boolean => {
    // console.log(`[Deep Conversation] 尝试更新消息ID: ${id}, 内容: ${content.substring(0, 30)}...`);

    // 查找匹配项
    const index = canvasItems.findIndex(item => item.id === id && item.type === 'chat');
    if (index === -1) {
      // console.error(`[Deep Conversation] 未找到消息ID: ${id}`);
      return false;
    }

    const item = canvasItems[index];
    if (item.type !== 'chat') {
      // console.error(`[Deep Conversation] 找到的项不是聊天消息: ${item.type}`);
      return false;
    }

    // 使用数组方法更新，以确保触发响应式更新
    const updatedItem = { ...item, content };
    // console.log(`[Deep Conversation] 更新前: ${JSON.stringify(item).substring(0, 50)}...`);

    // 替换数组中的项
    canvasItems.splice(index, 1, updatedItem as ChatMessageItem);

    // console.log(`[Deep Conversation] 已更新消息ID: ${id}, 索引: ${index}, 当前canvasItems长度: ${canvasItems.length}`);
    return true;
  };

  const updateEditorContent = (editorId: string, content: string, versionIndex?: number) => {
    const found = canvasItems.find(item => item.id === editorId);
    if (found && found.type === 'editor') {
      const editorItem = found as CanvasEditorItem;

      if (versionIndex !== undefined) {
        // 更新指定版本的内容（用于切换版本显示）
        editorItem.currentVersion = versionIndex;
        editorItem.content = editorItem.versions[versionIndex]?.content || content;
      } else {
        // 更新当前内容（用于动画或实时更新）
        editorItem.content = content;
      }
    }
  };

  const addEditorItem = (content: string): string => {
    const id = uuidv4();
    const newItem: CanvasEditorItem = {
      id,
      timestamp: Date.now(),
      type: 'editor',
      content,
      versions: [],
      currentVersion: 0
    };

    canvasItems.push(newItem);
    // 设置标志，表示已有编辑器
    hasEditor.value = true;
    // console.log(`[Deep Conversation] 添加新的编辑器内容`);
    return id;
  };

  const findLatestEditorItem = (): CanvasEditorItem | undefined => {
    // 倒序查找第一个编辑器项
    for (let i = canvasItems.length - 1; i >= 0; i--) {
      if (canvasItems[i].type === 'editor') {
        return canvasItems[i] as CanvasEditorItem;
      }
    }
    return undefined;
  };

  const addSystemItem = (content: string): string => {
    const id = uuidv4();
    const newItem: SystemItem = {
      id,
      timestamp: Date.now(),
      type: 'system',
      label: '系统消息',
      icon: new URL('@/assets/empty.png', import.meta.url).href,
      content
    };

    interfaceItems.push(newItem);
    return id;
  };

  // 添加澄清问题项
  const addClarificationItem = (questions: Array<{ id: number, content: string, options?: string[] }>): string => {
    const id = uuidv4();
    const newItem: ClarificationItem = {
      id,
      timestamp: Date.now(),
      type: 'clarification',
      questions,
      activeQuestionIndex: 0,
      isAnswered: false
    };

    canvasItems.push(newItem);
    console.log(`[Deep Conversation] 添加新的澄清问题, 共${questions.length}个问题`);
    return id;
  };

  // 更新澄清问题项
  const updateClarificationItem = (id: string, update: Partial<ClarificationItem>): boolean => {
    const index = canvasItems.findIndex(item => item.id === id && item.type === 'clarification');
    if (index === -1) return false;

    Object.assign(canvasItems[index], update);
    return true;
  };

  // 移除澄清问题项
  const removeClarificationItem = (id: string): boolean => {
    const index = canvasItems.findIndex(item => item.id === id && item.type === 'clarification');
    if (index === -1) return false;

    canvasItems.splice(index, 1);
    return true;
  };

  // ========== 方法：添加Interface项目 ==========
  const addThoughtItem = (params: {
    label: string,
    icon: string,
    completionInfo: string,
    content: string[],
    isExpanded?: boolean,
    draft_id?: string,
    stage?: string,
    round?: number,
    acting?: string
  }): string => {
    const id = uuidv4();
    const newItem: ThoughtItem = {
      id,
      timestamp: Date.now(),
      type: 'thought',
      ...params
    };

    interfaceItems.push(newItem);
    return id;
  };

  const addObservationItem = (params: {
    label: string,
    icon: string,
    searchQuery: string,
    draft_id?: string,
    stage?: string,
    round?: number
  }): string => {
    const id = uuidv4();
    const newItem: ObservationItem = {
      id,
      timestamp: Date.now(),
      type: 'observation',
      label: params.label,
      icon: params.icon,
      content: {
        searchQuery: params.searchQuery
      },
      draft_id: params.draft_id,
      stage: params.stage,
      round: params.round
    };

    interfaceItems.push(newItem);
    return id;
  };

  const addActionItem = (params: {
    label: string,
    icon: string,
    browsing: string,
    previews: PreviewItem[],
    draft_id?: string,
    stage?: string,
    round?: number
  }): string => {
    const id = uuidv4();
    const newItem: ActionItem = {
      id,
      timestamp: Date.now(),
      type: 'action',
      label: params.label,
      icon: params.icon,
      content: {
        browsing: params.browsing,
        previews: params.previews
      },
      draft_id: params.draft_id,
      stage: params.stage,
      round: params.round
    };

    interfaceItems.push(newItem);
    return id;
  };

  // 添加用户查询项
  const addUserQueryItem = (params: {
    label: string,
    icon: string,
    content: string,
    round: number,
    draft_id?: string
  }): string => {
    // 查找是否存在相同round的userquery项
    const existingIndex = interfaceItems.findIndex(
      item => item.type === 'userquery' && item.round === params.round
    );

    // 如果找到了相同round的项，则更新其内容并返回其ID
    if (existingIndex !== -1) {
      const existingItem = interfaceItems[existingIndex] as UserQueryItem;
      existingItem.content = params.content;
      existingItem.label = params.label;
      existingItem.icon = params.icon;
      existingItem.timestamp = Date.now();
      if (params.draft_id) existingItem.draft_id = params.draft_id;

      return existingItem.id;
    }

    // 如果没找到，创建新项并添加到列表末尾
    const id = uuidv4();
    const newItem: UserQueryItem = {
      id,
      timestamp: Date.now(),
      type: 'userquery',
      label: params.label,
      icon: params.icon,
      content: params.content,
      draft_id: params.draft_id,
      round: params.round
    };

    interfaceItems.push(newItem);
    return id;
  };

  // 添加模型动作项
  const addModelActionItem = (params: {
    label: string,
    icon: string,
    actionType: 'edit_draft' | 'create_draft' | 'revise_draft',
    description: string,
    beforeContent?: string,
    afterContent?: string,
    changes?: {
      added: number;
      deleted: number;
      modified: number;
    },
    stage?: string,
    round?: number,
    draft_id?: string,
    isCompleted?: boolean
  }): string => {
    // 查找是否存在相同round和actionType的模型动作项
    const existingIndex = interfaceItems.findIndex(
      item => item.type === 'modelaction' &&
        item.round === params.round &&
        (item as ModelActionItem).content.actionType === params.actionType
    );

    // 如果找到了相同的项，则更新其内容并返回其ID
    if (existingIndex !== -1) {
      const existingItem = interfaceItems[existingIndex] as ModelActionItem;
      existingItem.content = {
        actionType: params.actionType,
        description: params.description,
        beforeContent: params.beforeContent,
        afterContent: params.afterContent,
        changes: params.changes
      };
      existingItem.label = params.label;
      existingItem.icon = params.icon;
      existingItem.timestamp = Date.now();
      existingItem.isCompleted = params.isCompleted;
      if (params.draft_id) existingItem.draft_id = params.draft_id;
      if (params.stage) existingItem.stage = params.stage;

      return existingItem.id;
    }

    // 如果没找到，创建新项并添加到列表末尾
    const id = uuidv4();
    const newItem: ModelActionItem = {
      id,
      timestamp: Date.now(),
      type: 'modelaction',
      label: params.label,
      icon: params.icon,
      content: {
        actionType: params.actionType,
        description: params.description,
        beforeContent: params.beforeContent,
        afterContent: params.afterContent,
        changes: params.changes
      },
      stage: params.stage,
      round: params.round,
      draft_id: params.draft_id,
      isCompleted: params.isCompleted || false
    };

    interfaceItems.push(newItem);
    console.log(`[Deep Conversation] 添加模型动作项: ${params.actionType}, 轮次: ${params.round}`);
    return id;
  };

  const updateInterfaceItem = (id: string, update: Partial<InterfaceItem>): boolean => {
    const index = interfaceItems.findIndex(item => item.id === id);
    if (index === -1) return false;

    // 根据类型进行不同的更新
    const item = interfaceItems[index];

    if (update.type && update.type !== item.type) {
      console.error(`[Deep Conversation] 无法更改项目类型 ${item.type} -> ${update.type}`);
      return false;
    }

    // 合并更新
    Object.assign(interfaceItems[index], update);
    return true;
  };

  const toggleThoughtExpanded = (id: string): boolean => {
    const item = interfaceItems.find(item => item.id === id && item.type === 'thought');
    if (!item || item.type !== 'thought') return false;

    item.isExpanded = !item.isExpanded;
    return true;
  };

  // ========== 处理用户输入 ==========
  const handleUserInput = async (text: string) => {
    if (!text.trim() || isProcessing.value) return;
    try {
      // 设置处理中状态
      // isProcessing.value = true;
      chatError.value = '';

      const aiMessageId = prepareAIResponseUI();

      // 3. 发送请求并处理响应
      await sendChatRequest(text, aiMessageId);
      // }
    } catch (error) {
      handleError(error, '处理用户输入失败');
    }
  };

  // ---- 拆分的用户输入处理辅助函数 ----

  // 添加用户消息并返回ID
  const addUserMessage = (text: string): string => {
    return addChatMessage(text, true);
  };


  // 准备AI响应UI（根据对话轮次创建消息或编辑器）
  const prepareAIResponseUI = (): string => {
    let aiMessageId: string;
    aiMessageId = prepareEditorTemplate();
    return aiMessageId;
  };

  // 准备编辑器模板
  const prepareEditorTemplate = (): string => {
    // 查找最新的编辑器项
    const editorItem = findLatestEditorItem();

    // 如果已存在编辑器，使用现有编辑器；否则创建新的
    if (editorItem) {
      // console.log('[Deep Conversation] 使用现有编辑器项，ID:', editorItem.id);
      return editorItem.id;
    } else {
      // 创建新的编辑器项作为草稿样板
      const draftTemplateContent = "";
      const newEditorId = addEditorItem(draftTemplateContent);
      // console.log('[Deep Conversation] 创建草稿样板，ID:', newEditorId);
      return newEditorId;
    }
  };
  // 处理编辑请求的统一入口
  const handleEditRequest = async (formattedMessage: string) => {
    console.log('DraftPage: 处理编辑请求');
    console.log('DraftPage: 编辑请求内容:', formattedMessage.substring(0, 100) + '...');

    // 判断是否为整页编辑请求
    const isFullPageEdit = formattedMessage.includes('[整页编辑请求]');

    try {
      if (isFullPageEdit) {
        console.log('DraftPage: 处理整页编辑请求');
        // 调用新的编辑请求接口
        const response = await chatApi.submitEditRequest(currentConversationId.value, {
          edit_content: formattedMessage,
          edit_type: "full_page_edit"
        });
        console.log('DraftPage: 整页编辑请求已提交:', response);
      } else {
        console.log('DraftPage: 处理选区编辑请求');
        // 调用新的编辑请求接口
        const response = await chatApi.submitEditRequest(currentConversationId.value, {
          edit_content: formattedMessage,
          edit_type: "selection_edit"
        });
        console.log('DraftPage: 选区编辑请求已提交:', response);
      }
      addUserQueryItem({
        label: `用户`,
        icon: new URL('@/assets/search.png', import.meta.url).href,
        content: formattedMessage,
        round: currentFeedbackRound.value
      });
      currentFeedbackRound.value++;
    } catch (error) {
      console.error('DraftPage: 编辑请求失败:', error);
    }
  };
  // 发送聊天请求并处理响应
  const sendChatRequest = async (text: string, aiMessageId: string) => {
    try {
      // 增加请求计数
      requestCounter.value++;

      // 判断是否是第一次请求或继续请求
      // const isFirstRequest = requestCounter.value <= 1;
      // 尝试获取研究状态，如果返回404则表示没有之前的会话
      let hasExistingSession = false;
      try {
        if (currentConversationId.value) {
          const status = await researchApi.getResearchStatus(currentConversationId.value);
          hasExistingSession = true;
        } else {
          hasExistingSession = false;
        }
      } catch (error) {
        if (error.response && error.response.status === 404) {
          hasExistingSession = false;
        }
      }

      if (!hasExistingSession) {
        // 第一次请求，调用startResearch
        const createSessionResponse = await researchApi.createSessionV2({
          user_id: "123",
          metadata: {
            question: text,
            language: "zh",
          }
        });

        // 获取会话ID
        const sessionId = createSessionResponse.session_id;
        currentConversationId.value = sessionId;

        // 如果有上传的文件，先同步到服务器
        if (uploadedFiles.value.length > 0) {
          try {
            for (const file of uploadedFiles.value) {
              if (file instanceof File) {
                // 获取文件在数组中的索引
                const fileIndex = uploadedFiles.value.findIndex(f =>
                  f instanceof File && f.name === file.name
                );
                if (fileIndex !== -1) {
                  // 更新loading状态
                  loadingStates.value[fileIndex] = true;
                  console.log(`[Deep Conversation] 更新loading状态: ${fileIndex} 为 true`);
                  try {
                    await researchApi.uploadFile(sessionId, file);
                  } finally {
                    // 无论成功失败都重置loading状态
                    loadingStates.value[fileIndex] = false;
                    console.log(`[Deep Conversation] 更新loading状态: ${fileIndex} 为 false`);
                  }
                }
              }
            }
            console.log('[Deep Conversation] 文件同步完成');
          } catch (error) {
            console.error('[Deep Conversation] 文件同步失败:', error);
          }
        }

        // 第二次仅仅为了启动连接
        const startResearchResponse = await researchApi.startResearchV2({
          session_id: sessionId,
          question: text,
          user_preferences: userPreferences,
          enable_cognition_search: isCognitionEnabled.value,
          enable_search: isSearchEnabled.value
        });


        // 如果现在有了会话ID，更新URL
        if (currentConversationId.value) {
          console.log(`[Draft] 更新URL参数，添加会话ID: ${currentConversationId.value}`);
          router.push({
            path: '/draft',
            query: { conversation_id: currentConversationId.value }
          });
        }

        // addUserQueryItem({
        //   label: `用户`,
        //   icon: new URL('@/assets/search.png', import.meta.url).href,
        //   content: text,
        //   round: currentDraftRound.value + 1
        // });

        console.log(`[Deep Conversation] 研究任务已启动，会话ID: ${sessionId}`);

        // 连接WebSocket获取实时进展
        connectResearchWebSocket(sessionId, aiMessageId);

      } else {
        // 后续请求，通过WebSocket连接已存在的会话
        if (!currentConversationId.value) {
          throw new Error("会话ID不存在，无法继续对话");
        }

        // 如果用户发送了新问题，先提交反馈
        await researchApi.submitFeedback(currentConversationId.value, text);

        console.log(`[Deep Conversation] 已提交用户反馈，会话ID: ${currentConversationId.value}`);
        // 恢复研究任务
        await researchApi.resumeResearch(currentConversationId.value);

        console.log(`[Deep Conversation] 已提交用户反馈并恢复研究任务，会话ID: ${currentConversationId.value}`);
      }

      addUserQueryItem({
        label: `用户`,
        icon: new URL('@/assets/search.png', import.meta.url).href,
        content: text,
        round: currentFeedbackRound.value
      });
      currentFeedbackRound.value++;
    } catch (error) {
      console.error(`[Deep Conversation] 发送聊天请求失败: ${error}`);
      handleRequestError(error, aiMessageId);
    }
  };

  // 连接WebSocket监听研究进展
  const connectResearchWebSocket = (sessionId: string, aiMessageId: string) => {
    // 确保不会创建重复的WebSocket连接
    researchApi.disconnectWebSocket(sessionId);

    console.log(`[Deep Conversation] 开始连接WebSocket, 会话ID: ${sessionId}, aiMessageId: ${aiMessageId}`);

    // 创建WebSocket连接
    const socket = researchApi.connectWebSocket(sessionId, {
      onOpen: (event) => {
        console.log(`[Deep Conversation] WebSocket连接已打开, 会话ID: ${sessionId}, 准备接收消息`);
        // 连接建立后，主动请求一次状态更新
        setTimeout(() => {
          researchApi.getResearchStatus(sessionId).then(status => {
            console.log(`[Deep Conversation] 获取研究状态: `, status);
          }).catch(err => {
            console.error(`[Deep Conversation] 获取研究状态失败: `, err);
          });
        }, 500);
      },
      onMessage: (message) => {
        handleWebSocketMessage(message, aiMessageId);
      },
      onError: (error) => {
        console.error(`[Deep Conversation] WebSocket错误: ${sessionId}`, error);
        handleRequestError(error, aiMessageId);
      },
      onClose: () => {
        console.log(`[Deep Conversation] WebSocket连接已关闭，会话ID: ${sessionId}`);
        // 尝试重新连接
        // setTimeout(() => {
        //   console.log(`[Deep Conversation] 尝试重新连接WebSocket: ${sessionId}`);
        //   const newSocket = researchApi.connectWebSocket(sessionId, {
        //     onMessage: (message) => handleWebSocketMessage(message, aiMessageId),
        //     onError: (error) => handleRequestError(error, aiMessageId),
        //     onClose: () => {
        //       console.log(`[Deep Conversation] 重连的WebSocket也已关闭，会话ID: ${sessionId}`);
        //     }
        //   });
        // }, 3000);
      }
    });

    console.log(`[Deep Conversation] WebSocket连接已创建，会话ID: ${sessionId}, 等待连接建立`);
  };

  // 处理WebSocket消息
  const handleWebSocketMessage = (message: any, aiMessageId: string) => {
    try {
      // 根据消息类型处理
      const messageType = message.type || "unknown";

      switch (messageType) {
        case "batch_updates":
          // 处理批量更新消息
          handleBatchUpdates(message.messages, aiMessageId);
          break;

        case "update":
          // 处理更新消息
          if (message.content) {
            handleResponseChunk(message.content, aiMessageId);
          }
          break;
        case "clarification":
          // 处理澄清问题
          handleClarificationMessage(message, aiMessageId);
          break;

        case "system":
          // 处理系统消息 special case
          handleSystemMessage(message.content, aiMessageId);
          break;

        case "error":
          // 处理错误消息
          console.log(`[Deep Conversation] 收到错误消息: ${message.message}`);
          handleRequestError(new Error(message.message || "未知错误"), aiMessageId);
          break;

        case "status":
          // 处理状态更新
          console.log(`[Deep Conversation] 研究状态更新: ${message.status} - ${message.message}`);
          if (message.status === "running") {
            // handleRequestComplete(currentConversationId.value || "");
            isProcessing.value = true;
          } else if (message.status === "paused") {
            // handlePauseStream(aiMessageId);
            isProcessing.value = false;
          }
          break;

        case "content":
          // 处理更新消息
          if (message.content) {
            handleResponseChunk(message.content, aiMessageId);
          }
          break;

        default:
          break;
        // console.log(`[Deep Conversation] 未知的消息类型: ${messageType}`);
      }
    } catch (error) {
      console.error('[Deep Conversation] 处理WebSocket消息出错:', error);
      handleRequestError(error, aiMessageId);
    }
  };

  // 处理系统消息
  const handleSystemMessage = (message: any, aiMessageId: string) => {
    addSystemItem(message.content);
  }

  // 处理澄清问题消息
  const handleClarificationMessage = (message: any, aiMessageId: string) => {
    // console.log(`[Deep Conversation] 处理澄清问题消息: ${JSON.stringify(message).substring(0, 200)}...`);
    try {
      if (message.questions && Array.isArray(message.questions)) {
        console.log('[Deep Conversation] 收到澄清问题消息:', message);
        const questions = message.questions.map((q: any) => ({
          id: q.id || 0,
          content: q.content.question_content || '',
          options: q.content.question_options || []
        }));
        const clarificationItem: ClarificationItem = {
          id: message.id || 0,
          questions: questions,
          isAnswered: false,
          activeQuestionIndex: 0,
          type: 'clarification',
          timestamp: Date.now()
        };

        // 触发外部处理函数，通知Draft组件更新弹出框
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('handle-clarification', {
            detail: { item: clarificationItem }
          }));
        }
      }
    } catch (error) {
      console.error('[Deep Conversation] 处理澄清问题消息出错:', error);
    }
  };


  // 处理编辑报告
  const handleEditReport = (chunk: ExtendedDeepCognitionResponse, aiMessageId: string, last: boolean) => {
    // 获取编辑报告内容
    const reportContent = chunk.content || "";

    console.log(`[handleEditReport] 开始处理编辑报告，aiMessageId=${aiMessageId}, last=${last}, contentLength=${reportContent.length}`);
    console.log(`[handleEditReport] 当前状态：draftHistory.length=${draftHistory.length}, currentDraftRound=${currentDraftRound.value}`);

    // 获取轮次信息
    const round = typeof chunk.round === 'number' ? chunk.round : currentDraftRound.value;
    console.log(`[handleEditReport] 轮次信息：chunk.round=${chunk.round}, 使用轮次=${round}`);

    // 检查是否是首次编辑（没有历史记录）
    const isFirstEdit = draftHistory.length === 0 && currentDraftRound.value === 0;
    console.log(`[handleEditReport] 是否首次编辑：${isFirstEdit}`);

    // 获取编辑前的内容，用于计算差异
    let beforeContent = "";
    const editorItem = findLatestEditorItem();
    if (editorItem && editorItem.versions.length > 0) {
      beforeContent = editorItem.versions[editorItem.versions.length - 1].content;
    }

    // 在轮次开始时创建或更新模型动作项
    const actionType = isFirstEdit ? 'create_draft' : 'edit_draft';
    const actionDescription = isFirstEdit ? '正在创建草稿...' : `正在编辑草稿 (第${round}轮)...`;

    addModelActionItem({
      label: '模型动作',
      icon: new URL('@/assets/brain.png', import.meta.url).href,
      actionType: actionType,
      description: actionDescription,
      beforeContent: beforeContent,
      stage: 'edit_report',
      round: round,
      draft_id: chunk.draft_id,
      isCompleted: false
    });

    // 检查轮次是否变化
    if (round !== currentDraftRound.value) {
      console.log(`[handleEditReport] 轮次变化：${currentDraftRound.value} -> ${round}`);

      // 轮次变化，保存上一轮的历史记录
      const editorItem = findLatestEditorItem();
      if (editorItem) {
        console.log(`[handleEditReport] 找到编辑器项：${editorItem.id}, 内容长度=${editorItem.content.length}`);

        // 只有当内容长度有意义时才保存历史
        if (currentSourceText.value.length > 100) {
          console.log(`[handleEditReport] 保存历史记录，轮次=${currentDraftRound.value}, 内容长度=${currentSourceText.value.length}`);

          draftHistory.push({
            content: currentSourceText.value, // 使用源文本
            round: currentDraftRound.value,
            timestamp: Date.now()
          });

          // 限制历史记录数量，避免内存占用过多
          if (draftHistory.length > 5) {
            console.log(`[handleEditReport] 移除最旧的历史记录，当前历史数量=${draftHistory.length}`);
            draftHistory.shift(); // 移除最旧的历史记录
          }
        } else {
          console.log(`[handleEditReport] 内容长度不足，不保存历史记录：${currentSourceText.value.length}`);
        }
      } else {
        console.log(`[handleEditReport] 未找到编辑器项`);
      }

      // 更新当前轮次
      console.log(`[handleEditReport] 更新当前轮次：${currentDraftRound.value} -> ${round}`);
      currentDraftRound.value = round;

      // 如果是轮次变化，重置源文本
      if (!isFirstEdit) {
        console.log(`[handleEditReport] 轮次变化且非首次编辑，重置源文本`);
        currentSourceText.value = "";
      }
    }

    // 将当前报告内容添加到源文本
    const prevLength = currentSourceText.value.length;
    currentSourceText.value += reportContent;
    console.log(`[handleEditReport] 更新源文本：${prevLength} -> ${currentSourceText.value.length} (增加了 ${reportContent.length} 字符)`);
    const extract_text = extractTagContent(currentSourceText.value, "article")[0];
    console.log(`[handleEditReport] 提取的文本：${extract_text}`);
    if (extract_text) {
      currentSourceText.value = extract_text;
    }
    // 根据aiMessageId判断是更新已有编辑器还是创建新编辑器
    const found = canvasItems.find(item => item.id === aiMessageId && item.type === 'editor');
    let editorId = aiMessageId;

    if (!found) {
      console.log(`[handleEditReport] 未找到编辑器项 ${aiMessageId}，创建新的编辑器项`);
      // 如果没有找到编辑器，创建一个新的
      editorId = addEditorItem("");
      console.log(`[handleEditReport] 创建了新的编辑器项 ${editorId}`);
    } else {
      console.log(`[handleEditReport] 找到已有编辑器项 ${aiMessageId}`);
    }

    // 如果收到完成信号，创建新版本并完成模型动作项
    if (last && currentSourceText.value.length > 0) {
      console.log(`[handleEditReport] 收到完成信号，创建新版本`);

      // 获取编辑器项检查是否需要创建新版本
      const currentEditorItem = canvasItems.find(item => item.id === editorId && item.type === 'editor') as CanvasEditorItem;
      if (currentEditorItem) {
        // 检查当前内容是否与最新版本不同
        const latestVersion = currentEditorItem.versions[currentEditorItem.versions.length - 1];
        if (!latestVersion || latestVersion.content !== currentSourceText.value) {
          // 创建新版本
          addEditorVersion(editorId, currentSourceText.value, round, chunk.draft_id);

          // 计算内容变化统计
          const beforeContentFinal = beforeContent;
          const afterContentFinal = currentSourceText.value;
          const changes = calculateContentChanges(beforeContentFinal, afterContentFinal);

          // 更新模型动作项为已完成状态
          addModelActionItem({
            label: '模型动作',
            icon: new URL('@/assets/brain.png', import.meta.url).href,
            actionType: actionType,
            description: isFirstEdit ? '草稿创建完成' : `草稿编辑完成 (第${round}轮)`,
            beforeContent: beforeContentFinal,
            afterContent: afterContentFinal,
            changes: changes,
            stage: 'edit_report',
            round: round,
            draft_id: chunk.draft_id,
            isCompleted: true
          });

          // 找到上一轮的内容（用于差异比较）
          let previousContent = "";
          if (currentEditorItem.versions.length > 1) {
            previousContent = currentEditorItem.versions[currentEditorItem.versions.length - 2].content;
          }

          // 只在最新版本显示差异动画
          if (currentEditorItem.currentVersion === currentEditorItem.versions.length - 1) {
            console.log(`[handleEditReport] 在最新版本显示差异动画`);
            if (chunk.replay === true) {
              handleNoAnimatedDiffDisplay(editorId, previousContent, currentSourceText.value);
            } else {
              handleAnimatedDiffDisplay(editorId, previousContent, currentSourceText.value);
            }
          }
        } else {
          console.log(`[handleEditReport] 内容无变化，不创建新版本`);
        }
      }
    } else {
      // 流式更新当前内容（只在最新版本显示）
      if (isLatestEditorVersion(editorId)) {
        console.log(`[handleEditReport] 流式更新最新版本内容`);
        updateEditorContent(editorId, currentSourceText.value + "|AI|");
      }
    }
  };

  // 计算内容变化统计
  const calculateContentChanges = (beforeContent: string, afterContent: string) => {
    const beforeLength = beforeContent.length;
    const afterLength = afterContent.length;

    // 简单的统计计算
    const lengthDiff = afterLength - beforeLength;
    const added = lengthDiff > 0 ? lengthDiff : 0;
    const deleted = lengthDiff < 0 ? Math.abs(lengthDiff) : 0;

    // 计算修改的段落数（简化计算，按换行符分割）
    const beforeParagraphs = beforeContent.split('\n').length;
    const afterParagraphs = afterContent.split('\n').length;
    const modified = Math.abs(afterParagraphs - beforeParagraphs);

    return {
      added,
      deleted,
      modified
    };
  };
  /**
   * 从文本中提取指定标签之间的内容
   * @param text 要处理的文本
   * @param tag 标签名称
   * @returns 提取的内容数组，如果没有找到则返回空数组
   */
  const extractTagContent = (text: string, tag: string): string[] => {
    try {
      // 创建正则表达式匹配<tag>内容</tag>模式
      const regex = new RegExp(`<${tag}>(.*?)</${tag}>`, 'gs');
      const matches = [];
      let match;

      // 查找所有匹配项
      while ((match = regex.exec(text)) !== null) {
        matches.push(match[1]); // 添加捕获组中的内容
      }

      console.log(`[extractTagContent] 从文本中提取了 ${matches.length} 个 <${tag}> 标签内容`);
      return matches;
    } catch (error) {
      console.error(`[extractTagContent] 提取标签内容出错:`, error);
      return [];
    }
  };
  const handleFeedbackMessage = (chunk: ExtendedDeepCognitionResponse, aiMessageId: string) => {
    console.log(`[handleFeedbackMessage] 收到反馈消息: ${JSON.stringify(chunk)}`);
    // 进行添加反馈消息，并渲染
    addUserQueryItem({
      label: '用户问题',
      icon: new URL('@/assets/search.png', import.meta.url).href,
      content: chunk.content,
      round: chunk.round
    });
    // if (chunk.round === currentFeedbackRound.value) {
    currentFeedbackRound.value = chunk.round + 1;
    // }
  }

  // 处理来自Browser的消息
  const handleBrowserMessage = (chunk: ExtendedDeepCognitionResponse) => {
    if (chunk.stage === "browsing" && chunk.content_type === "observation") {
      // 处理浏览结果
      handleBrowserObservation(chunk);
    }
  };

  // 处理请求错误
  const handleRequestError = (error: any, aiMessageId: string) => {
    // 首先尝试处理token budget错误
    if (handleTokenBudgetError(error)) {
      console.log('[Deep Conversation] Token budget错误已处理');
      return;
    }

    // 处理其他类型的错误
    chatError.value = error.message || '请求失败';
    console.error('[Deep Conversation] 请求失败:', error);

    updateChatMessage(aiMessageId, "抱歉，我遇到了一些问题，无法回答您的问题。");

    // isProcessing.value = false;
  };


  // 处理错误
  const handleError = (error: any, message: string) => {
    isProcessing.value = false;
    chatError.value = '处理失败';
    console.error(`[Deep Conversation] ${message}:`, error);
  };

  // ========== 处理各类响应消息 ==========

  // 处理Research思考阶段消息
  const handleResearchThinking = (chunk: ExtendedDeepCognitionResponse) => {
    // console.log("[Deep Conversation] 收到Research消息 action:", JSON.stringify(chunk));
    if (!chunk.content) return;
    // console.log("[Deep Conversation] 收到Research消息 action:", chunk);
    // 获取思考内容
    let content = "";

    if (typeof chunk.content === 'string') {
      // 流式思考内容
      content = chunk.content;
    }

    // 获取轮次信息
    const round = typeof chunk.round === 'number' ? chunk.round : 0;

    // 查找是否已有相同轮次的思考项
    const thoughtItem = interfaceItems.find(item =>
      item.type === 'thought' &&
      item.stage === 'select_action' &&
      item.round === round
    ) as ThoughtItem | undefined;

    if (thoughtItem) {
      // 如果已存在思考项，则追加内容
      const paragraphs = [...thoughtItem.content];

      // 如果是字符串内容，追加到最后一段
      if (paragraphs.length > 0 && typeof chunk.content === 'string') {
        paragraphs[paragraphs.length - 1] += content;
      } else {
        paragraphs.push(content);
      }

      // 更新思考项
      updateInterfaceItem(thoughtItem.id, {
        content: paragraphs,
        completionInfo: ''
      });
    } else {
      // 如果不存在思考项，则创建新的
      addThoughtItem({
        label: '思考行动',
        icon: new URL('@/assets/brain.png', import.meta.url).href,
        completionInfo: '',
        content: [content],
        isExpanded: true,
        stage: 'select_action',
        round: round
      });
    }
  };
  // 处理Research行动消息
  const handleResearchAction = (chunk: ExtendedDeepCognitionResponse) => {
    if (!chunk.content) return;
    // 获取轮次信息
    const round = typeof chunk.round === 'number' ? chunk.round : 0;
    let content = '';
    if (typeof chunk.content === 'string') {
      content = chunk.content;
    }

    const thoughtItem = interfaceItems.find(item =>
      item.type === 'thought' &&
      item.stage === 'select_action' &&
      item.round === round
    ) as ThoughtItem | undefined;

    if (thoughtItem) {
      let thought_content = thoughtItem.acting ? thoughtItem.acting : '';

      if (thought_content && typeof chunk.content === 'string') {
        thought_content += content;
      } else {
        thought_content = content;
      }

      updateInterfaceItem(thoughtItem.id,
        {
          acting: thought_content,
          completionInfo: ''
        }
      )
    } else {
      addThoughtItem({
        label: '思考行动',
        icon: new URL('@/assets/brain.png', import.meta.url).href,
        completionInfo: '',
        content: [],
        isExpanded: true,
        stage: 'select_action',
        round: round,
        acting: content
      });
    }
  };
  // 处理Web搜索行动消息
  const handleWebSearchAction = (chunk: ExtendedDeepCognitionResponse) => {
    if (!chunk.content) return;
    console.log(`[handleWebSearchAction] 收到Web搜索行动消息: ${JSON.stringify(chunk)}`);
    // 获取内容
    let content = "";
    let searchQueries: string[] = [];

    if (typeof chunk.content === 'string') {
      // 流式思考内容
      content = chunk.content;
    } else if (Array.isArray(chunk.content)) {
      // 搜索查询列表
      searchQueries = chunk.content.filter(q => typeof q === 'string');
      content = `${searchQueries.join(', ')}`;
    }

    // 获取轮次信息
    const round = typeof chunk.round === 'number' ? chunk.round : 0;
    console.log(`[handleWebSearchAction] 轮次信息: ${round}`);
    // 查找是否已有相同轮次的思考项
    const observationItem = interfaceItems.find(item =>
      item.type === 'observation' &&
      item.stage === 'search' &&
      item.round === round
    ) as ObservationItem | undefined;

    if (observationItem) {
      // 如果已存在观察项，则追加内容
      console.log(`[handleWebSearchAction] 已存在观察项，追加内容: ${content}`);
      const paragraphs = content;

      // 更新思考项
      updateInterfaceItem(observationItem.id, {
        content: {
          searchQuery: paragraphs
        },
        completionInfo: searchQueries.length > 0 ? `搜索 ${searchQueries.length} 个查询` : ''
      });
    } else {
      // 如果不存在观察项，则创建新的
      console.log(`[handleWebSearchAction] 不存在观察项，创建新的观察项: ${content}`);
      addObservationItem({
        label: '搜索查询中',
        icon: new URL('@/assets/search.png', import.meta.url).href,
        searchQuery: content,
        stage: 'search',
        round: round
      });
    }
  };
  const handleClarificationAction = (chunk: ExtendedDeepCognitionResponse) => {
    if (!chunk.content) return;

    const content = chunk.content;
    const round = chunk.round;

    const observationItem = interfaceItems.find(item =>
      item.type === 'observation' &&
      item.stage === 'clarify' &&
      item.round === round
    ) as ObservationItem | undefined;

    if (observationItem) {
      // 如果已存在澄清项，则追加内容
      const paragraphs = [...observationItem.content.searchQuery];

      // 如果是字符串内容，追加到最后一段
      if (paragraphs.length > 0 && typeof chunk.content === 'string') {
        paragraphs[paragraphs.length - 1] += content;
      } else {
        paragraphs.push(content);
      }
    }
    else {
      addObservationItem({
        label: '澄清问题中',
        icon: new URL('@/assets/search.png', import.meta.url).href,
        searchQuery: content,
        stage: 'clarify',
        round: round
      });
    }
  }
  const handleBrowserAction = (chunk: ExtendedDeepCognitionResponse) => {
    if (!chunk.content) return;

    // 获取轮次信息
    const round = typeof chunk.round === 'number' ? chunk.round : 0;

    // 从content中提取单个结果
    const result = chunk.content;
    const searchQueries = result.webpages;

    // 查找是否已有相同轮次的思考项
    const observationItem = interfaceItems.find(item =>
      item.type === 'observation' &&
      item.stage === 'browse' &&
      item.round === round
    ) as ObservationItem | undefined;

    if (observationItem) {
      // 如果已存在观察项，则追加内容
      const paragraphs = [...observationItem.content.searchQuery];

      // 更新思考项
      updateInterfaceItem(observationItem.id, {
        content: paragraphs,
        completionInfo: searchQueries.length > 0 ? `搜索 ${searchQueries.length} 个查询` : ''
      });
    } else {
      // 如果不存在观察项，则创建新的
      addObservationItem({
        label: '搜索查询中',
        icon: new URL('@/assets/search.png', import.meta.url).href,
        searchQuery: searchQueries,
        stage: 'search',
        round: round
      });
    }
  }

  // 处理Web搜索观察消息
  const handleWebSearchObservation = (chunk: ExtendedDeepCognitionResponse) => {
    if (!chunk.content) return;

    // 获取轮次信息
    const round = typeof chunk.round === 'number' ? chunk.round : 0;

    // 从content中提取单个结果
    const result = chunk.content;
    if (typeof result !== 'object' || !result.url) {
      console.error('[Deep Conversation] 搜索结果格式不正确:', result);
      return;
    }

    // 创建预览项
    const preview: PreviewItem = {
      title: result.title || '[未知标题]',
      url: result.url || '#',
      snippet: result.snippet || '无摘要信息',
      useful_information: result.useful_information || '无摘要信息',
      summary: result.summary || '无摘要信息',
      liked: result.liked || false
    };

    // 查找是否已有相同轮次的行动项
    const actionItem = interfaceItems.find(item =>
      item.type === 'action' &&
      item.stage === 'web_search' &&
      item.round === round
    ) as ActionItem | undefined;

    // 处理行动项
    if (actionItem) {
      // 将新的预览添加到现有预览列表
      const updatedPreviews = [...actionItem.content.previews, preview];

      // 更新已有行动项
      updateInterfaceItem(actionItem.id, {
        content: {
          browsing: ``,
          previews: updatedPreviews
        }
      });
    } else {
      // 创建新的行动项
      addActionItem({
        label: '浏览网页中',
        icon: new URL('@/assets/see.png', import.meta.url).href,
        browsing: ``,
        previews: [preview],
        stage: 'web_search',
        round: round
      });
    }
  };

  const handleBrowseObservation = (chunk: ExtendedDeepCognitionResponse) => {
    if (!chunk.content || typeof chunk.content !== 'object') return;
    // console.log("[handleBrowseObservation] 收到浏览网页消息:", JSON.stringify(chunk));
    const content = chunk.content;

    const round = chunk.round;

    const actionItem = interfaceItems.find(item =>
      item.type === 'action' &&
      item.stage === 'web_search' &&
      item.round === round
    ) as ActionItem | undefined;
    // console.log("[handleBrowseObservation] 当前的url:", chunk.url);
    const previewItem: PreviewItem = actionItem?.content.previews.find(preview =>
      preview.url === chunk.url
    ) as PreviewItem | undefined;
    // console.log("[handleBrowseObservation] 找到预览项:", JSON.stringify(previewItem));
    if (previewItem && actionItem) {
      // 更新预览项内容
      if (Array.isArray(content.useful_information)) {
        previewItem.useful_information = content.useful_information.join('\n') || previewItem.useful_information;
      }
      if (typeof content.summary === 'string') {
        previewItem.summary = content.summary || previewItem.summary;
      }
      // console.log("[handleBrowseObservation] 更新预览项:", JSON.stringify(previewItem));

      // 从原预览列表中移除当前项
      const filteredPreviews = actionItem.content.previews.filter(preview =>
        preview.url !== chunk.url
      );

      // 将更新后的预览项放到列表最前面
      const updatedPreviews = [previewItem, ...filteredPreviews];

      updateInterfaceItem(actionItem.id, {
        content: {
          ...actionItem.content,
          previews: updatedPreviews
        }
      });
    }
  }

  // 处理浏览器观察消息
  const handleBrowserObservation = (chunk: ExtendedDeepCognitionResponse) => {
    if (!chunk.content || typeof chunk.content !== 'object') return;

    const content = chunk.content;

    // 获取网页信息
    const webpage = content.webpage || {};
    const isScrapeSuccess = content.is_scrape_success === true;
    const isWebpageEmpty = content.is_webpage_empty === true;
    const summary = content.summary || '';

    // 获取轮次信息
    const round = typeof chunk.round === 'number' ? chunk.round : 0;

    // 查找URL匹配的预览项并更新状态
    interfaceItems.forEach(item => {
      if (item.type === 'action' && item.round === round) {
        const actionItem = item as ActionItem;

        // 遍历所有预览项，查找匹配URL的项
        const updatedPreviews = actionItem.content.previews.map(preview => {
          if (preview.url === webpage.url) {
            // 更新匹配项的状态，保留liked状态
            return {
              ...preview,
              title: isScrapeSuccess ? (webpage.title || preview.title) : `[无法访问] ${preview.title}`,
              summary: isScrapeSuccess ? (summary || preview.summary) : '无法获取网页内容'
            };
          }
          return preview;
        });

        if (JSON.stringify(updatedPreviews) !== JSON.stringify(actionItem.content.previews)) {
          // 如果有更新，则更新行动项
          updateInterfaceItem(actionItem.id, {
            content: {
              ...actionItem.content,
              previews: updatedPreviews
            }
          });
        }
      }
    });
  };

  // ========== 清理方法 ==========
  const clearAllItems = () => {
    // 保留一个编辑器项
    const editorItem = findLatestEditorItem();
    const editorContent = editorItem ? editorItem.content : '';

    // 清空所有项
    canvasItems.length = 0;
    canvasItems.splice(0, canvasItems.length);
    interfaceItems.length = 0;
    interfaceItems.splice(0, interfaceItems.length);

    // 清空草稿历史和差异
    draftHistory.length = 0;
    diffParts.value = [];
    currentDraftRound.value = 0;

    // 清除conversationId
    localStorage.removeItem('current_conversation_id');
    currentConversationId.value = null;

    // 清空uploadFiles
    uploadedFiles.value = []

    // 重置源文本
    currentSourceText.value = '';

    //重置进行中状态
    isProcessing.value = false;
    // 重置编辑器标志
    hasEditor.value = false;
    // 重置认知搜索状态
    isCognitionEnabled.value = true;
    isSearchEnabled.value = true;

    // 重置请求计数器,已经不依赖requestCounter
    requestCounter.value = 0;

    // 清除任何待执行的更新计时器
    if (updateDebounceTimer !== null) {
      clearTimeout(updateDebounceTimer);
      updateDebounceTimer = null;
      pendingEditorId = null;
      pendingEditorContent = null;
    }

    // 添加一个新的编辑器项
    // addEditorItem(editorContent || '# 新的对话已开始');
    // prepareAIResponseUI();

    // 重置状态
    currentDraftId.value = null;

    console.log('[Deep Conversation] 已清空所有项目');
  };

  // 恢复历史对话展示功能
  const restoreConversationHistory = async (conversationId: string): Promise<boolean> => {
    try {
      isProcessing.value = true;
      chatError.value = '';

      // 清空Canvas和Interface项目，准备重新加载
      canvasItems.splice(0, canvasItems.length);
      interfaceItems.splice(0, interfaceItems.length);

      // 重置请求计数器和轮次
      requestCounter.value = 0;

      // 记录当前会话ID
      currentConversationId.value = conversationId;

      console.log(`[Deep Conversation] 开始恢复对话历史, 会话ID: ${conversationId}`);

      // 获取研究状态
      const statusData = await researchApi.getResearchStatus(conversationId);

      if (!statusData || statusData.status === "error") {
        console.error('[Deep Conversation] 获取研究状态失败:', statusData);
        chatError.value = '获取研究状态失败';
        isProcessing.value = false;
        return false;
      }
      if (statusData.status === "running") {
        isProcessing.value = true;
      } else {
        isProcessing.value = false;
      }

      // 获取用户偏好
      for (const key in statusData.user_preferences) {
        updateUserPreferences({
          [key]: statusData.user_preferences[key]
        });
      }
      console.log('[Deep Conversation UserPreference] 用户偏好:', statusData.user_preferences);

      console.log('[Deep Conversation] 获取到研究状态:', statusData);
      // if (statusData.enable_cognition_search) {
      isCognitionEnabled.value = statusData.enable_cognition_search;
      console.log('[Deep Conversation] 恢复认知搜索状态:', isCognitionEnabled.value);
      // }else{
      // isCognitionEnabled.value = false;
      // }

      // 恢复搜索状态
      isSearchEnabled.value = statusData.enable_search !== undefined ? statusData.enable_search : true;
      console.log('[Deep Conversation] 恢复搜索状态:', isSearchEnabled.value);

      // 添加一个用户查询项，显示原始问题
      addUserQueryItem({
        label: `用户问题`,
        icon: new URL('@/assets/search.png', import.meta.url).href,
        content: statusData.question || "未知问题",
        round: 0
      });
      currentFeedbackRound.value = 1;

      // 获取上下文列表
      try {
        const contexts = await researchApi.getContexts(conversationId);
        if (contexts && contexts.contexts) {
          // 将上下文转换为文件描述
          const fileDescriptions = contexts.contexts.map(ctx => ({
            name: ctx.context_id || '未知文件',
            type: ctx.metadata?.file_type || 'application/octet-stream',
            size: ctx.metadata?.file_size || 0,
            lastModified: ctx.metadata?.last_modified || Date.now(),
            isRemote: true,
            contextId: ctx.context_id
          }));

          // 添加到 uploadedFiles
          uploadedFiles.value = [...fileDescriptions];
        }
      } catch (error) {
        console.error('[Deep Conversation] 获取上下文列表失败:', error);
      }

      // 研究尚未完成，添加临时消息
      const aiMessageId = prepareAIResponseUI();

      // 连接WebSocket继续接收更新
      connectResearchWebSocket(conversationId, aiMessageId);
      researchApi.getPendingQuestions(conversationId).then((res) => {
        console.log('[Deep Conversation] 收到pending_questions:', res);
        const clarifychunk = {
          type: "clarification",
          questions: res.pending_questions.map((question: any) => {
            return {
              id: question.id,
              content: {
                question_content: question.question_content,
                question_options: question.question_options
              }
            }
          })
        }
        handleClarificationMessage(clarifychunk, aiMessageId);
      });

      // 触发自定义事件，通知界面需要重新初始化监听器
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('interface-history-restored', {
          detail: { conversationId }
        }));
      }

      console.log('[Deep Conversation] 对话历史恢复完成');
      // isProcessing.value = false;
      return true;
    } catch (error) {
      console.error('[Deep Conversation] 恢复对话历史出错:', error);
      chatError.value = `恢复对话历史出错: ${error instanceof Error ? error.message : String(error)}`;
      isProcessing.value = false;
      return false;
    }
  };
  const handleBatchUpdates = (chunks: any[], aiMessageId: string) => {
    // console.log("[Deep Conversation] 收到batch_updates数据块:", JSON.stringify(chunks));
    for (const chunk of chunks) {
      switch (chunk.type) {
        case "update":
          handleResponseChunk(chunk.content, aiMessageId);
          // console.log("[Deep Conversation] 收到update数据块:", chunk.content);
          break;
        case "clarification":
          // 处理澄清问题
          handleClarificationMessage(chunk.content, aiMessageId);
          break;
        case "system":
          handleSystemMessage(chunk.content, aiMessageId);
          break;
        default:
          break;
      }
    }
  };
  // 处理响应数据块
  const handleResponseChunk = (chunk: ExtendedDeepCognitionResponse, aiMessageId: string) => {
    // console.log("[Deep Conversation] 收到update数据块:", JSON.stringify(chunk));

    // 根据不同的agent和阶段处理响应
    if (chunk.agent === "Orchestrator") {
      handleOrchestratorMessage(chunk, aiMessageId);
    } else if (chunk.agent === "Researcher") {
      handleResearchMessage(chunk, aiMessageId);
    } else if (chunk.agent === "Browser") {
      handleBrowserMessage(chunk);
    } else if (chunk.agent === "Feedback") {
      handleFeedbackMessage(chunk, aiMessageId);
    }

    // 处理错误
    if (chunk.error) {
      chatError.value = chunk.error;
      console.error('[Deep Conversation] 流处理错误:', chunk.error);
      updateChatMessage(aiMessageId, "抱歉，处理过程中出现错误: " + chunk.error);
    }
  };

  // 处理来自Orchestrator的消息
  const handleOrchestratorMessage = (chunk: ExtendedDeepCognitionResponse, aiMessageId: string) => {
    // 处理澄清阶段或回答行动
    if (chunk.stage === "clarification" && chunk.content_type === "action" && chunk.action === "answer") {
      const content = chunk.content || "";
      // console.log("[Deep Conversation] Orchestrator澄清/回答:", content);

      // 获取当前AI消息内容
      const aiMessage = canvasItems.find(item => item.id === aiMessageId && item.type === 'chat') as ChatMessageItem | undefined;
      if (aiMessage && aiMessage.type === 'chat') {
        // 将新内容追加到现有内容
        const updatedContent = aiMessage.content + content;

        // 更新Canvas中的AI消息
        updateChatMessage(aiMessageId, updatedContent);
      } else {
        // 如果找不到消息（不太可能发生），直接更新
        updateChatMessage(aiMessageId, content);
      }
    }
  };

  // 处理来自Research的消息
  const handleResearchMessage = (chunk: ExtendedDeepCognitionResponse, aiMessageId: string) => {
    // 根据不同阶段处理
    // console.log("[Deep Conversation] 收到Research消息:", JSON.stringify(chunk));
    if (chunk.stage === "select_action") {
      if (chunk.content_type === "action" && chunk.action === "think") {
        // 处理思考阶段
        handleResearchThinking(chunk);
      } else if (chunk.content_type === "action" && chunk.action === "acting") {
        // 处理行动阶段
        handleResearchAction(chunk);
      } else if (chunk.content_type === "action" && chunk.action === "search") {
        // 处理搜索阶段
        handleWebSearchAction(chunk);
      } else if (chunk.content_type === "action" && chunk.action === "clarify") {
        // 处理澄清阶段
        handleClarificationAction(chunk);
      } else if (chunk.content_type === "action" && chunk.action === "browse") {
        // 处理浏览阶段
        handleBrowserAction(chunk);
      } else if (chunk.content_type === "action" && chunk.action === "edit") {
      }
    }
    else if (chunk.stage === "web_search") {
      if (chunk.content_type === "observation") {
        // 处理搜索结果观察
        handleWebSearchObservation(chunk);
      }
    }
    else if (chunk.stage === "browse") {
      if (chunk.content_type === "observation") {
        handleBrowseObservation(chunk);
      }
    }
    else if (chunk.stage === "edit_report") {
      if (chunk.content_type === "action" && chunk.action === "edit_report") {
        handleEditReport(chunk, aiMessageId, true);  // 修改为true，因为每次都是完整的draft
      }
    }
  };

  const handleNoAnimatedDiffDisplay = (editorId: string, oldContent: string, newContent: string) => {
    // 直接更新编辑器内容，不计算差异，不显示动画过程和光标
    updateEditorContent(editorId, newContent);
  }
  // region 处理差异动画展示
  const handleAnimatedDiffDisplay = (editorId: string, oldContent: string, newContent: string) => {
    try {
      // 如果当前有动画在播放，先取消它
      if (animationState.playing) {
        // 直接显示当前动画的最终内容
        const currentEditor = canvasItems.find(item => item.id === editorId && item.type === 'editor');
        if (currentEditor && currentEditor.type === 'editor') {
          // 移除可能存在的AI光标标记
          const finalContent = currentEditor.content.replace(/\|AI\|/g, '');
          updateEditorContent(editorId, finalContent);
        }
        // 重置动画状态
        animationState.playing = false;
        animationState.currentStep = 0;
        animationState.totalSteps = 0;
      }

      // 计算差异
      const dmp = new diff_match_patch();
      dmp.Diff_Timeout = 2.0; // 设置为2秒超时

      const diffs = dmp.diff_main(oldContent, newContent);
      dmp.diff_cleanupSemantic(diffs);

      // 更新差异部分（用于显示统计信息）
      diffParts.value = diffs.map(([type, text]) => ({
        text,
        type,
        status: 'complete'
      }));

      // 生成动画序列
      const animationSequence = generateEditSequence(diffs);

      // 设置动画状态
      animationState.currentStep = 0;
      animationState.totalSteps = animationSequence.length;
      animationState.playing = true;

      // 开始逐步执行动画序列
      playEditSequence(editorId, oldContent, animationSequence);
    } catch (error) {
      console.error('[Deep Conversation] 差异计算出错:', error);
      // 出错时直接更新完整内容并添加AI光标标记
      updateEditorContent(editorId, newContent + "|AI|");
    }
  };

  // 新增：根据差异生成编辑序列
  const generateEditSequence = (diffs: Array<[number, string]>): Array<{ operation: string, text: string, length: number }> => {
    const sequence: Array<{ operation: string, text: string, length: number }> = [];

    let currentPosition = 0;

    diffs.forEach(([type, text]) => {
      switch (type) {
        case DIFF_EQUAL:
          // 保持不变的文本，直接移动光标位置
          sequence.push({
            operation: 'move',
            text: text,
            length: text.length
          });
          currentPosition += text.length;
          break;

        case DIFF_DELETE:
          // 删除操作
          // 对于删除操作，考虑批量删除以提高速度
          if (text.length <= 3) { // 增加批量删除的阈值
            // 短文本直接删除
            sequence.push({
              operation: 'delete',
              text: text,
              length: text.length
            });
          } else {
            // 较长文本分批删除，每批最多8个字符
            const batchSize = 8; // 增加批量大小
            for (let i = 0; i < text.length; i += batchSize) {
              const batch = text.substring(i, Math.min(i + batchSize, text.length));
              sequence.push({
                operation: 'delete',
                text: batch,
                length: batch.length
              });
            }
          }
          break;

        case DIFF_INSERT:
          // 插入操作
          // 对于较长的插入，也分批处理
          if (text.length <= 4) { // 增加批量插入的阈值
            // 短文本直接插入
            sequence.push({
              operation: 'insert',
              text: text,
              length: text.length
            });
            currentPosition += text.length;
          } else {
            // 较长文本分批插入，每批最多5个字符
            const batchSize = 5; // 增加批量大小
            for (let i = 0; i < text.length; i += batchSize) {
              const batch = text.substring(i, Math.min(i + batchSize, text.length));
              sequence.push({
                operation: 'insert',
                text: batch,
                length: batch.length
              });
              currentPosition += batch.length;
            }
          }
          break;
      }
    });

    return sequence;
  };

  // 新增：播放编辑序列动画
  const playEditSequence = (editorId: string, initialContent: string, sequence: Array<{ operation: string, text: string, length: number }>) => {
    let currentContent = initialContent;
    let cursorPosition = 0;
    let stepIndex = 0;

    // 基本步骤间隔时间（毫秒）
    const baseStepInterval = 5; // 加快基本速度

    // 播放下一步动画
    const playNextStep = () => {
      // 检查动画是否被取消
      if (!animationState.playing) {
        return;
      }

      if (stepIndex >= sequence.length) {
        // 动画完成，移除光标标记
        animationState.playing = false;

        // 更新最终内容（不带光标）
        updateEditorContent(editorId, currentContent);
        return;
      }

      const step = sequence[stepIndex];
      animationState.currentStep = stepIndex + 1;

      // 根据操作类型执行动作
      switch (step.operation) {
        case 'move':
          // 移动光标位置
          cursorPosition += step.length;
          break;

        case 'delete':
          // 删除当前光标位置的字符
          currentContent = currentContent.substring(0, cursorPosition) +
            currentContent.substring(cursorPosition + step.length);
          break;

        case 'insert':
          // 在当前光标位置插入字符
          currentContent = currentContent.substring(0, cursorPosition) +
            step.text +
            currentContent.substring(cursorPosition);
          cursorPosition += step.length;
          break;
      }

      // 计算间隔时间（根据操作类型可以有不同的间隔）
      let interval = baseStepInterval;
      if (step.operation === 'move') {
        // 对于移动，使用较快速度
        interval = 3; // 加快移动速度

        // 如果移动长度超过20个字符，可以直接跳过显示中间过程
        if (step.length > 20) { // 增加跳过阈值
          // 多次移动可以合并执行，跳过绘制中间过程
          while (stepIndex + 1 < sequence.length && sequence[stepIndex + 1].operation === 'move') {
            stepIndex++;
            cursorPosition += sequence[stepIndex].length;
          }
        }
      } else if (step.operation === 'delete') {
        // 删除操作略快
        interval = 3; // 加快删除速度
      } else if (step.operation === 'insert') {
        // 插入操作略慢
        interval = 6; // 加快插入速度
      }

      // 显示带光标的内容
      const contentWithCursor =
        currentContent.substring(0, cursorPosition) +
        "|AI|" +
        currentContent.substring(cursorPosition);

      updateEditorContent(editorId, contentWithCursor);

      // 继续下一步
      stepIndex++;
      setTimeout(playNextStep, interval);
    };

    // 开始播放动画
    setTimeout(playNextStep, 100); // 减少开始延迟
  };
  // endregion
  // 恢复分享的会话历史
  const restoreSharedConversationHistory = async (conversationId: string): Promise<boolean> => {
    try {
      isProcessing.value = true;
      chatError.value = '';

      // 清空Canvas和Interface项目，准备重新加载
      canvasItems.splice(0, canvasItems.length);
      interfaceItems.splice(0, interfaceItems.length);

      // 重置请求计数器和轮次
      requestCounter.value = 0;

      // 记录当前会话ID
      currentConversationId.value = conversationId;

      console.log(`[Deep Conversation] 开始恢复分享的对话历史, 会话ID: ${conversationId}`);

      // 获取分享信息
      const shareInfo = await researchApi.getSharedResearch(conversationId);

      if (!shareInfo) {
        console.error('[Deep Conversation] 获取分享信息失败');
        chatError.value = '获取分享信息失败';
        isProcessing.value = false;
        return false;
      }

      console.log('[Deep Conversation] 获取到分享信息:', shareInfo);

      // 添加一个用户查询项，显示原始问题
      addUserQueryItem({
        label: `用户问题`,
        icon: new URL('@/assets/search.png', import.meta.url).href,
        content: shareInfo.question || "未知问题",
        round: 0
      });

      currentFeedbackRound.value = 1;

      // 准备AI响应UI
      const aiMessageId = prepareAIResponseUI();

      // 连接WebSocket继续接收更新
      connectShareWebSocket(conversationId, aiMessageId);

      // 触发自定义事件，通知界面需要重新初始化监听器
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('interface-history-restored', {
          detail: { conversationId }
        }));
      }

      console.log('[Deep Conversation] 分享的对话历史恢复完成');
      return true;
    } catch (error) {
      console.error('[Deep Conversation] 恢复分享的对话历史出错:', error);
      chatError.value = `恢复分享的对话历史出错: ${error instanceof Error ? error.message : String(error)}`;
      isProcessing.value = false;
      return false;
    }
  };

  // 连接分享WebSocket
  const connectShareWebSocket = (sessionId: string, aiMessageId: string) => {
    // 确保不会创建重复的WebSocket连接
    researchApi.disconnectWebSocket(sessionId);

    console.log(`[Deep Conversation] 开始连接分享WebSocket, 会话ID: ${sessionId}, aiMessageId: ${aiMessageId}`);

    // 创建WebSocket连接
    const socket = researchApi.connectShareWebSocket(sessionId, {
      onOpen: (event) => {
        console.log(`[Deep Conversation] 分享WebSocket连接已打开, 会话ID: ${sessionId}, 准备接收消息`);
      },
      onMessage: (message) => {
        handleWebSocketMessage(message, aiMessageId);
      },
      onError: (error) => {
        console.error(`[Deep Conversation] 分享WebSocket错误: ${sessionId}`, error);
        handleRequestError(error, aiMessageId);
      },
      onClose: () => {
        console.log(`[Deep Conversation] 分享WebSocket连接已关闭，会话ID: ${sessionId}`);
      }
    });

    console.log(`[Deep Conversation] 分享WebSocket连接已创建，会话ID: ${sessionId}, 等待连接建立`);
  };

  // 添加新版本到编辑器项
  const addEditorVersion = (editorId: string, content: string, round?: number, draft_id?: string): boolean => {
    const found = canvasItems.find(item => item.id === editorId);
    if (found && found.type === 'editor') {
      const editorItem = found as CanvasEditorItem;

      // 创建新版本
      const newVersion = {
        version: editorItem.versions.length,
        content: content,
        timestamp: Date.now(),
        round: round,
        draft_id: draft_id
      };

      // 添加到版本列表
      editorItem.versions.push(newVersion);

      // 自动切换到最新版本
      editorItem.currentVersion = editorItem.versions.length - 1;
      editorItem.content = content;

      console.log(`[Deep Conversation] 添加新版本 ${newVersion.version}，当前版本：${editorItem.currentVersion}`);
      return true;
    }
    return false;
  };

  // 切换编辑器版本
  const switchEditorVersion = (editorId: string, versionIndex: number): boolean => {
    const found = canvasItems.find(item => item.id === editorId);
    if (found && found.type === 'editor') {
      const editorItem = found as CanvasEditorItem;

      if (versionIndex >= 0 && versionIndex < editorItem.versions.length) {
        editorItem.currentVersion = versionIndex;
        editorItem.content = editorItem.versions[versionIndex].content;

        console.log(`[Deep Conversation] 切换到版本 ${versionIndex}`);
        return true;
      }
    }
    return false;
  };

  // 获取编辑器版本信息
  const getEditorVersionInfo = (editorId: string) => {
    const found = canvasItems.find(item => item.id === editorId);
    if (found && found.type === 'editor') {
      const editorItem = found as CanvasEditorItem;
      return {
        totalVersions: editorItem.versions.length,
        currentVersion: editorItem.currentVersion,
        versions: editorItem.versions,
        isLatestVersion: editorItem.currentVersion === editorItem.versions.length - 1
      };
    }
    return null;
  };

  // 检查是否是最新版本（用于决定是否显示动画）
  const isLatestEditorVersion = (editorId: string): boolean => {
    const versionInfo = getEditorVersionInfo(editorId);
    return versionInfo ? versionInfo.isLatestVersion : true;
  };

  // 处理内容点赞/取消点赞
  const toggleContentLike = async (url: string, round?: number): Promise<boolean> => {
    try {
      if (!currentConversationId.value) {
        console.error('[Deep Conversation] 没有当前会话ID，无法提交内容偏好');
        return false;
      }

      // 查找对应的预览项
      let targetPreview: PreviewItem | undefined;
      let targetActionItem: ActionItem | undefined;

      // 在所有action项中查找包含该URL的预览项
      for (const item of interfaceItems) {
        if (item.type === 'action' && (!round || item.round === round)) {
          const actionItem = item as ActionItem;
          const preview = actionItem.content.previews.find(p => p.url === url);
          if (preview) {
            targetPreview = preview;
            targetActionItem = actionItem;
            break;
          }
        }
      }

      if (!targetPreview) {
        console.error('[Deep Conversation] 未找到对应的预览项:', url);
        return false;
      }

      // 切换点赞状态
      const newLikedState = !targetPreview.liked;

      // 立即更新UI状态
      targetPreview.liked = newLikedState;

      try {
        // 调用API提交偏好
        const response = await researchApi.submitContentPreference({
          conversation_id: currentConversationId.value,
          url: url,
          title: targetPreview.title,
          liked: newLikedState,
          timestamp: new Date().toISOString()
        });

        console.log(`[Deep Conversation] 成功提交内容偏好: ${url} -> ${newLikedState ? 'liked' : 'unliked'}`);
        return true;
      } catch (error) {
        console.error('[Deep Conversation] 提交内容偏好时出错:', error);
        // 回滚UI状态
        targetPreview.liked = !newLikedState;
        return false;
      }
    } catch (error) {
      console.error('[Deep Conversation] 处理内容点赞时出错:', error);
      return false;
    }
  };

  // 导出所有需要的接口和函数
  return {
    isProcessing,
    chatError,
    currentDraftId,
    currentConversationId,
    currentDraftRound,
    draftHistory,
    diffParts,
    animationState,
    canvasItems,
    interfaceItems,
    uploadedFiles,
    addUploadedFile,
    removeUploadedFile,
    clearUploadedFiles,
    addChatMessage,
    updateChatMessage,
    updateEditorContent,
    addEditorItem,
    findLatestEditorItem,
    addEditorVersion,
    switchEditorVersion,
    getEditorVersionInfo,
    isLatestEditorVersion,
    addThoughtItem,
    addObservationItem,
    addActionItem,
    addUserQueryItem,
    addModelActionItem,
    updateInterfaceItem,
    toggleThoughtExpanded,
    handleUserInput,
    handleEditRequest,
    clearAllItems,
    restoreConversationHistory,
    addClarificationItem,
    updateClarificationItem,
    removeClarificationItem,
    restoreSharedConversationHistory,
    isSyncing, // 导出同步状态
    showReportFeedback,
    currentSessionId,
    closeReportFeedback,
    loadingStates,
    isCognitionEnabled,
    updateResearchStatus,
    // Token Budget 相关
    tokenBudgetState,
    closeTokenBudgetDialog,
    refreshPage,
    resetTokenBudgetState,
    isSearchEnabled,
    updateSearchStatus,
    toggleContentLike,
    userPreferences,
    updateUserPreferences
  };
}