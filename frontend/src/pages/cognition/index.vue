<template>
  <div class="cognition-page">
    <!-- 顶部导航栏 -->
    <div class="nav-bar">
      <div class="logo-container" @click="$router.push('/draft')">
        <span class="q-logo">Q</span>
        <span class="logo-text">认知平台</span>
      </div>
      
      <div class="nav-tools">
        <!-- 搜索框 -->
        <div class="search-box">
          <v-text-field
            v-model="searchQuery"
            placeholder="搜索认知内容..."
            variant="outlined"
            density="compact"
            hide-details
            prepend-inner-icon="mdi-magnify"
            clearable
            @keyup.enter="handleSearch"
            @click:clear="handleSearchClear"
          />
        </div>
        
        <!-- 创建认知按钮 -->
        <v-btn
          color="primary"
          variant="elevated"
          @click="showCreateDialog = true"
          class="create-btn"
        >
          <v-icon>mdi-plus</v-icon>
          创建认知
        </v-btn>
        
        <!-- 合成认知按钮 -->
        <v-btn
          variant="outlined"
          @click="toggleSynthesisMode"
          class="synthesis-btn"
          :color="synthesisMode ? 'warning' : 'secondary'"
        >
          <v-icon>{{ synthesisMode ? 'mdi-close-circle-outline' : 'mdi-creation' }}</v-icon>
          {{ synthesisMode ? '取消合成' : '合成认知' }}
        </v-btn>
        
        <!-- 我的认知库按钮 -->
        <v-btn
          color="secondary"
          variant="outlined"
          @click="$router.push('/cognition/library')"
          class="library-btn"
        >
          <v-icon>mdi-heart</v-icon>
          我的认知库
        </v-btn>
        
        <!-- 语言选择 -->
        <v-select
          v-model="selectedLanguage"
          :items="languageOptions"
          item-title="label"
          item-value="value"
          variant="outlined"
          density="compact"
          hide-details
          class="language-selector"
          @update:model-value="handleLanguageChange"
        >
          <template v-slot:prepend-inner>
            <v-icon>mdi-translate</v-icon>
          </template>
        </v-select>
        
        <user-status-button />
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- Synthesis Selection Bar -->
      <div v-if="synthesisMode" class="synthesis-selection-bar">
        <div class="synthesis-header">
          <h3>已选择 {{ selectedCognitionsForSynthesis.length }} 项用于合成</h3>
          <p>请选择2-5项以开始合成</p>
        </div>
        <div class="selected-items-container">
          <transition-group name="list-fade">
            <v-chip
              v-for="cognition in selectedCognitionsForSynthesis"
              :key="cognition.id"
              class="selected-item"
              closable
              @click:close="toggleCognitionSelection(cognition)"
              color="primary"
              variant="elevated"
              label
            >
              {{ (cognition.abstract_zh || cognition.question_zh || '无标题').slice(0, 20) }}...
            </v-chip>
          </transition-group>
        </div>
        <div class="synthesis-actions">
          <v-btn
            color="primary"
            variant="elevated"
            :disabled="selectedCognitionsForSynthesis.length < 2 || selectedCognitionsForSynthesis.length > 5"
            :loading="synthesizing"
            @click="handleStartSynthesis"
          >
            <v-icon left>mdi-auto-fix</v-icon>
            生成认知
          </v-btn>
          <v-btn
            variant="text"
            @click="toggleSynthesisMode"
          >
            取消
          </v-btn>
        </div>
      </div>

      <!-- 筛选排序栏 -->
      <div class="filter-bar">
        <div class="filter-section">
          <!-- 搜索范围选择器 -->
          <div class="search-scope-section" ref="searchScopeSectionRef">
            <div class="scope-btn" @click="showSearchScopeSelector = !showSearchScopeSelector">
              <v-icon size="18">{{ searchScope === 'all' ? 'mdi-earth' : 'mdi-heart' }}</v-icon>
              <span>{{ searchScope === 'all' ? '全部' : '收藏' }}</span>
              <v-icon size="16" :class="{ 'rotate': showSearchScopeSelector }">mdi-chevron-down</v-icon>
            </div>
            <div v-if="showSearchScopeSelector" class="scope-selector-dropdown">
              <div v-for="scope in searchScopeOptions" :key="scope.value" class="scope-option" @click="handleSearchScopeChange(scope.value)">
                <v-icon size="16">{{ scope.icon }}</v-icon>
                <span>{{ scope.label }}</span>
              </div>
            </div>
          </div>
          
          <!-- <v-icon color="white" class="filter-icon">mdi-filter-variant</v-icon>
          <span class="filter-label">排序方式：</span> -->
          
          <!-- 排序选择器 -->
          <div class="sort-filter-section" ref="sortFilterSectionRef">
            <div 
              class="sort-btn"
              :class="{ 'active': showSortSelector }"
              @click="showSortSelector = !showSortSelector"
            >
              <div class="sort-btn-content">
                <v-icon 
                  size="18" 
                  :class="{ 'rotate': showSortSelector }"
                  class="sort-icon"
                >
                  mdi-sort
                </v-icon>
                <span class="sort-text">{{ getCurrentSortLabel }}</span>
                <v-icon 
                  size="16" 
                  :class="{ 'rotate': showSortSelector }"
                  class="dropdown-icon"
                >
                  mdi-chevron-down
                </v-icon>
              </div>
            </div>
            
            <!-- 排序选择器下拉 -->
            <div v-if="showSortSelector" class="sort-selector-dropdown">
              <div 
                v-for="option in sortOptions"
                :key="option.value"
                class="sort-option"
                :class="{ 'selected': sortBy === option.value }"
                @click="handleSortSelect(option.value)"
              >
                <v-icon 
                  size="16" 
                  :color="sortBy === option.value ? 'primary' : 'grey'"
                  class="sort-option-icon"
                >
                  {{ sortBy === option.value ? 'mdi-check-circle' : 'mdi-circle-outline' }}
                </v-icon>
                <span class="sort-option-text">{{ option.label }}</span>
              </div>
            </div>
          </div>
          
          <!-- 时间筛选器 -->
          <div class="time-filter-section" ref="timeFilterSectionRef">
            <div class="time-btn" @click="showTimeFilterSelector = !showTimeFilterSelector">
              <v-icon size="18">mdi-clock-outline</v-icon>
              <span>{{ currentTimeFilterLabel }}</span>
              <v-icon size="16" :class="{ 'rotate': showTimeFilterSelector }">mdi-chevron-down</v-icon>
            </div>
            <div v-if="showTimeFilterSelector" class="time-selector-dropdown">
              <div v-for="filter in timeFilterOptions" :key="filter.value" class="time-option" @click="handleTimeFilterChange(filter.value)">
                <v-icon size="16">{{ filter.icon }}</v-icon>
                <span>{{ filter.label }}</span>
              </div>
            </div>
          </div>
          
          <!-- Source筛选器 -->
          <div class="source-filter-section" ref="sourceFilterSectionRef">
            <div class="source-btn" @click="showSourceFilterSelector = !showSourceFilterSelector">
              <v-icon size="18">mdi-source-branch</v-icon>
              <span>{{ currentSourceFilterLabel }}</span>
              <v-icon size="16" :class="{ 'rotate': showSourceFilterSelector }">mdi-chevron-down</v-icon>
            </div>
            <div v-if="showSourceFilterSelector" class="source-selector-dropdown">
              <div v-for="filter in sourceFilterOptions" :key="filter.value" class="source-option" @click="handleSourceFilterChange(filter.value)">
                <v-icon size="16">{{ filter.icon }}</v-icon>
                <span>{{ filter.label }}</span>
              </div>
            </div>
          </div>
          
          <!-- Topic筛选 -->
          <div class="topic-filter-section" ref="topicFilterSectionRef">
            <div 
              class="browse-btn"
              :class="{ 'active': showTopicSelector }"
              @click="showTopicSelector = !showTopicSelector"
            >
              <div class="browse-btn-content">
                <v-icon 
                  size="18" 
                  :class="{ 'rotate': showTopicSelector }"
                  class="browse-icon"
                >
                  mdi-tune-variant
                </v-icon>
                <span class="browse-text">Browse Topics</span>
                <div class="selected-count" v-if="selectedTopics.length > 0">
                  {{ selectedTopics.length }}
                </div>
              </div>
            </div>
            
            <!-- Topic选择器 -->
            <div v-if="showTopicSelector" class="topic-selector">
              <div class="topic-selector-header">
                <div class="header-title">
                  <v-icon color="primary">mdi-tag-multiple</v-icon>
                  <span>话题分类选择</span>
                </div>
                <div class="header-actions">
                  <v-btn
                    size="small"
                    variant="text"
                    color="grey"
                    @click="clearTopicSelection"
                    class="clear-btn"
                  >
                    <v-icon size="16">mdi-close-circle</v-icon>
                    清空
                  </v-btn>
                  <v-btn
                    icon
                    size="small"
                    variant="text"
                    @click="showTopicSelector = false"
                    class="close-btn"
                  >
                    <v-icon>mdi-close</v-icon>
                  </v-btn>
                </div>
              </div>
              
              <div class="topic-categories">
                <div 
                  v-for="category in topicHierarchy"
                  :key="category.primary_topic"
                  class="category-section"
                >
                  <!-- 一级分类 -->
                  <div 
                    class="primary-category"
                    :style="getPrimaryCategoryStyle(category.primary_topic)"
                  >
                    <div class="category-header">
                      <div class="category-left" @click="toggleCategory(category.primary_topic)">
                        <v-icon 
                          size="16" 
                          :class="{ 'rotate': expandedCategories.has(category.primary_topic) }"
                          class="expand-icon"
                        >
                          mdi-chevron-right
                        </v-icon>
                        <span class="category-name">{{ formatCategoryName(category.primary_topic) }}</span>
                      </div>
                      <div class="category-right">
                        <div class="category-badge">
                          {{ getSelectedCountInCategory(category) }}
                        </div>
                        <v-btn
                          size="x-small"
                          variant="text"
                          :color="isAllTopicsSelectedInCategory(category) ? 'white' : 'primary'"
                          @click.stop="toggleSelectAllInCategory(category)"
                          class="select-all-btn"
                          :title="isAllTopicsSelectedInCategory(category) ? '取消全选' : '全选'"
                        >
                          <v-icon size="14">
                            {{ isAllTopicsSelectedInCategory(category) ? 'mdi-checkbox-marked' : 'mdi-checkbox-blank-outline' }}
                          </v-icon>
                        </v-btn>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 二级分类 -->
                  <div 
                    v-if="expandedCategories.has(category.primary_topic)"
                    class="secondary-topics"
                  >
                    <div 
                      v-for="topic in category.related_topic"
                      :key="topic"
                      class="topic-option"
                      :class="{ 'selected': selectedTopics.includes(topic) }"
                      :style="getSecondaryCategoryStyle(category.primary_topic, selectedTopics.includes(topic))"
                      @click="toggleTopic(topic)"
                    >
                      <v-icon 
                        size="14" 
                        :color="selectedTopics.includes(topic) ? 'white' : 'primary'"
                        class="topic-icon"
                      >
                        {{ selectedTopics.includes(topic) ? 'mdi-check-circle' : 'mdi-circle-outline' }}
                      </v-icon>
                      <span class="topic-name">{{ formatTopicName(topic) }}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="topic-actions">
                <div class="selected-summary">
                  已选择 <strong>{{ selectedTopics.length }}</strong> 个话题
                </div>
                <div class="action-buttons">
                  <v-btn
                    size="small"
                    variant="outlined"
                    @click="clearTopicSelection"
                    class="action-btn"
                  >
                    <v-icon size="16">mdi-refresh</v-icon>
                    重置
                  </v-btn>
                  <v-btn
                    icon
                    size="small"
                    variant="text"
                    @click="showTopicSelector = false"
                    class="close-action-btn"
                  >
                    <v-icon size="18">mdi-check</v-icon>
                  </v-btn>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 已选择的topic标签显示 -->
        <div v-if="selectedTopics.length > 0" class="selected-topics">
          <div
            v-for="(topic, index) in selectedTopics.slice(0, 4)"
            :key="topic"
            class="selected-topic-tag"
            @click="removeTopic(topic)"
          >
            {{ topic }}
            <v-icon size="14" class="remove-icon">mdi-close</v-icon>
          </div>
          <!-- 省略显示 -->
          <div 
            v-if="selectedTopics.length > 4" 
            class="selected-topic-more"
            @click="showTopicSelector = true"
            :title="`还有 ${selectedTopics.length - 4} 个已选择的话题，点击查看更多`"
          >
            +{{ selectedTopics.length - 4 }}
          </div>
        </div>
        
        <div class="results-info">
          <v-btn
            color="primary"
            variant="elevated"
            size="small"
            @click="showTrendDialog = true"
            class="trend-btn"
          >
            <v-icon size="16" class="mr-1">mdi-trending-up</v-icon>
            趋势
          </v-btn>
          
          <v-btn
            color="error"
            variant="elevated"
            size="small"
            @click="showHotRankingDialog = true"
            class="hot-btn"
          >
            <v-icon size="16" class="mr-1">mdi-fire</v-icon>
            热榜
            <v-chip 
              size="x-small" 
              color="white" 
              text-color="error"
              class="live-chip-small"
            >
              LIVE
            </v-chip>
          </v-btn>
        </div>
      </div>
      

      
      <div class="cognitions-masonry" v-if="cognitions.length > 0">
        <masonry
          :cols="{ default: 5, 1800: 4, 1400: 3, 1024: 2, 768: 1 }"
          :gutter="{ default: '16px' }"
        >
          <div v-for="cognition in cognitions" :key="cognition.id" class="cognition-item">
            <CognitionCard
              :cognition="cognition"
              :language="selectedLanguage"
              :is-in-synthesis-mode="synthesisMode"
              :is-selected="isCognitionSelected(cognition.id)"
              @click="handleCardClick(cognition)"
              @vote="handleVote"
              @edit="handleEdit"
              @delete="handleDelete"
              @topic-click="handleTopicClick"
              @read-status-changed="handleReadStatusChanged"
            />
          </div>
        </masonry>
      </div>
      
      <!-- 空状态 -->
      <div v-else-if="!loading" class="empty-state">
        <v-icon size="64" color="grey-lighten-1">mdi-brain</v-icon>
        <h3>暂无认知内容</h3>
        <p>成为第一个分享认知的人吧！</p>
        <v-btn
          color="primary"
          variant="elevated"
          @click="showCreateDialog = true"
        >
          创建认知
        </v-btn>
      </div>
      
      <!-- 加载触发器 -->
      <div ref="loaderRef" class="loader-sentinel">
        <div v-if="loadingMore" class="loading-more">
          <v-progress-circular indeterminate size="32" color="primary" />
          <span>加载中...</span>
        </div>
      </div>
    </div>

    <!-- 创建认知对话框 -->
    <CreateCognitionDialog
      v-model="showCreateDialog"
      @created="handleCognitionCreated"
    />

    <!-- 认知详情对话框 -->
    <CognitionDetailDialog
      v-model="showDetailDialog"
      :cognition="selectedCognition"
      :language="selectedLanguage"
      @vote="handleVote"
      @comment="handleComment"
      @close="handleCloseDetail"
      @recommendation-click="handleRecommendationClick"
    />

    <!-- 编辑认知对话框 -->
    <EditCognitionDialog
      v-model="showEditDialog"
      :cognition="editingCognition"
      @updated="handleCognitionUpdated"
      @deleted="handleCognitionDeleted"
      :source-filter="sourceFilter"
    />

    <!-- 趋势统计对话框 -->
    <TrendDialog
      v-model="showTrendDialog"
      :time-filter="timeFilter"
      :selected-topics="selectedTopics"
      :search-scope="searchScope"
      :source-filter="sourceFilter"
    />

    <!-- 合成结果对话框 -->
    <SynthesisResultDialog
      v-model="showSynthesisResultDialog"
      :cognition="synthesizedCognition"
      :language="selectedLanguage"
      @close="showSynthesisResultDialog = false"
      @saved="handleSynthesisSaved"
    />

    <!-- 热榜弹窗 -->
    <HotRankingDialog
      v-model="showHotRankingDialog"
      @topic-click="handleHotTopicClick"
      @cognition-click="handleHotCognitionClick"
      @show-trend-dialog="showTrendDialog = true"
    />

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <v-progress-circular indeterminate size="64" color="primary" />
    </div>

    <!-- 点击外部关闭topic-selector的蒙层 -->
    <div 
      v-if="showTopicSelector" 
      class="topic-selector-overlay"
      @click="showTopicSelector = false"
    ></div>


  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, nextTick, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import CognitionCard from '@/components/cognition/CognitionCard.vue'
import CognitionDetailDialog from '@/components/cognition/CognitionDetailDialog.vue'
import CreateCognitionDialog from '@/components/cognition/CreateCognitionDialog.vue'
import EditCognitionDialog from '@/components/cognition/EditCognitionDialog.vue'
import TrendDialog from '@/components/cognition/TrendDialog.vue'
import UserStatusButton from '@/components/UserStatusButton.vue'
import SynthesisResultDialog from '@/components/cognition/SynthesisResultDialog.vue'
import HotRankingDialog from '@/components/cognition/HotRankingDialog.vue'
import { useCognitionStore } from '@/stores/cognition'
import { useAuthStore } from '@/stores/authStore'

const router = useRouter()
const cognitionStore = useCognitionStore()
const authStore = useAuthStore()

// 响应式数据
const cognitions = ref([])
const loading = ref(false)
const loadingMore = ref(false)
const hasMore = ref(true)
const searchQuery = ref('')
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const showEditDialog = ref(false)
const showTrendDialog = ref(false)
const showHotRankingDialog = ref(false)
const selectedCognition = ref(null)
const editingCognition = ref(null)
const currentPage = ref(1)
const initialPageSize = 15 // 首次加载数量
const subsequentPageSize = 30 // 后续加载数量
const totalPages = ref(0)
const totalCount = ref(0)
const jumpPage = ref(1)
const selectedLanguage = ref('zh')
const languageOptions = ref([
  { label: '简体中文', value: 'zh' },
  { label: 'English', value: 'en' }
])
const sortBy = ref('latest')
const sortOptions = ref([
  { label: '最新', value: 'latest' },
  { label: '点赞最多', value: 'most_liked' }
])
const selectedTopics = ref([])
const availableTopics = ref([])
const showTopicSelector = ref(false)
const topicHierarchy = ref([])
const topicCategories = ref({})
const expandedCategories = ref(new Set())
const showSortSelector = ref(false)
const sortFilterSectionRef = ref(null)
const topicFilterSectionRef = ref(null)
const loaderRef = ref(null)
const searchScope = ref('all')
const showSearchScopeSelector = ref(false)
const searchScopeOptions = ref([
  { label: '在全部认知里搜索', value: 'all', icon: 'mdi-earth' },
  { label: '在已收藏里搜索', value: 'favorites', icon: 'mdi-heart' }
])
const timeFilter = ref('all')
const showTimeFilterSelector = ref(false)
const timeFilterOptions = ref([
  { label: '不限时间', value: 'all', icon: 'mdi-infinity' },
  { label: '最近一天', value: 'day', icon: 'mdi-calendar-today' },
  { label: '最近一周', value: 'week', icon: 'mdi-calendar-week' },
  { label: '最近一月', value: 'month', icon: 'mdi-calendar-month' }
])
const sourceFilter = ref('all')
const searchScopeSectionRef = ref(null)
const timeFilterSectionRef = ref(null)
const sourceFilterSectionRef = ref(null)
const showSourceFilterSelector = ref(false)
const sourceFilterOptions = ref([
  { label: '所有来源', value: 'all', icon: 'mdi-source-branch' },
  { label: 'Hugging Face Paper', value: 'Hugging Face Paper', icon: 'mdi-face-recognition' },
  { label: 'arXiv', value: 'arXiv', icon: 'mdi-file-document-outline' },
  { label: 'Sam Altman', value: 'Sam Altman', icon: 'mdi-account-circle' },
  { label: 'Andrej Karpathy', value: 'Andrej Karpathy', icon: 'mdi-account-circle' },
  { label: 'Elon Musk', value: 'Elon Musk', icon: 'mdi-account-circle' },
  { label: 'Reddit', value: 'Reddit', icon: 'mdi-reddit' }
])
let observer = null

// 合成模式相关状态
const synthesisMode = ref(false)
const selectedCognitionsForSynthesis = ref([])
const synthesizing = ref(false)
const showSynthesisResultDialog = ref(false)
const synthesizedCognition = ref(null)

// 防抖定时器
let debounceTimer = null

// 计算属性
const isLoggedIn = computed(() => authStore.isAuthenticated)
const isMobile = computed(() => {
  return window.innerWidth <= 768
})

const getCurrentSortLabel = computed(() => {
  const option = sortOptions.value.find(o => o.value === sortBy.value)
  return option ? option.label : '最新'
})

const currentTimeFilterLabel = computed(() => {
  return timeFilterOptions.value.find(o => o.value === timeFilter.value)?.label || '不限时间'
})

const currentSourceFilterLabel = computed(() => {
  const option = sourceFilterOptions.value.find(o => o.value === sourceFilter.value)?.label || '所有来源'
  return option
})

// 防抖加载函数
const debouncedLoadCognitions = () => {
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
  debounceTimer = setTimeout(async () => {
    if (!loading.value) {
      await loadCognitions()
    }
  }, 300) // 增加防抖时间到 300ms
}

// 监听页面变化
watch(currentPage, async (newPage, oldPage) => {
  // 避免初始化时的重复调用
  if (oldPage !== undefined) {
    debouncedLoadCognitions()
  }
})

// 监听搜索查询变化
watch(searchQuery, (newQuery) => {
  if (newQuery === '') {
    handleSearchClear()
  }
})

// 监听语言变化
watch(selectedLanguage, async (newLanguage, oldLanguage) => {
  // 避免初始化时的重复调用
  if (oldLanguage !== undefined) {
    currentPage.value = 1
    debouncedLoadCognitions()
  }
})

// 监听排序变化
watch(sortBy, async (newSort, oldSort) => {
  // 避免初始化时的重复调用
  if (oldSort !== undefined) {
    currentPage.value = 1
    debouncedLoadCognitions()
  }
})

// 监听selectedTopics变化
watch(selectedTopics, async (newTopics, oldTopics) => {
  // 避免初始化时的重复调用
  if (oldTopics !== undefined) {
    currentPage.value = 1
    debouncedLoadCognitions()
  }
}, { deep: true })

// 监听sourceFilter变化
watch(sourceFilter, async (newSource, oldSource) => {
  // 避免初始化时的重复调用
  if (oldSource !== undefined) {
    currentPage.value = 1
    debouncedLoadCognitions()
  }
})

// 生命周期
onMounted(async () => {
  await loadAvailableTopics()
  await loadCognitions(true) // 初始加载
  document.addEventListener('click', handleClickOutside, true)
  
  // 设置 Intersection Observer
  observer = new IntersectionObserver(
    (entries) => {
      const entry = entries[0]
      if (entry.isIntersecting && hasMore.value) {
        loadMoreCognitions()
      }
    },
    { threshold: 0.1 }
  )
  if (loaderRef.value) {
    observer.observe(loaderRef.value)
  }
})

// 组件卸载时清理
onUnmounted(() => {
  if (debounceTimer) {
    clearTimeout(debounceTimer)
    debounceTimer = null
  }
  document.removeEventListener('click', handleClickOutside, true)
  if (observer) {
    observer.disconnect()
  }
})

// 方法
// 节流处理滚动事件
const throttledHandleScroll = () => {
  if (scrollThrottleTimer) return
  
  scrollThrottleTimer = setTimeout(() => {
    handleScroll()
    scrollThrottleTimer = null
  }, 200) // 节流到 200ms
}

const handleScroll = () => {
  // 严格控制触发时机：只有接近真正的底部才触发（50px以内）
  const threshold = 50
  const scrollPosition = window.innerHeight + window.scrollY
  const documentHeight = document.body.offsetHeight
  
  if (
    scrollPosition >= documentHeight - threshold && 
    !loading.value && 
    !loadingMore.value && 
    hasMore.value
  ) {
    loadMoreCognitions()
  }
}

const handleClickOutside = (event) => {
  if (showSortSelector.value && sortFilterSectionRef.value && !sortFilterSectionRef.value.contains(event.target)) {
    showSortSelector.value = false
  }
  if (showTopicSelector.value && topicFilterSectionRef.value && !topicFilterSectionRef.value.contains(event.target)) {
    showTopicSelector.value = false
  }
  if (showTimeFilterSelector.value && timeFilterSectionRef.value && !timeFilterSectionRef.value.contains(event.target)) {
    showTimeFilterSelector.value = false
  }
  if (showSearchScopeSelector.value && searchScopeSectionRef.value && !searchScopeSectionRef.value.contains(event.target)) {
    showSearchScopeSelector.value = false
  }
  if (showSourceFilterSelector.value && sourceFilterSectionRef.value && !sourceFilterSectionRef.value.contains(event.target)) {
    showSourceFilterSelector.value = false
  }
}

// 重新加载认知 (搜索、筛选、排序时调用)
async function reloadCognitions() {
  currentPage.value = 1
  hasMore.value = true
  cognitions.value = []
  await loadCognitions(true)
}

async function loadMoreCognitions() {
  if (!hasMore.value || loading.value || loadingMore.value) return
  currentPage.value++
  await loadCognitions(false)
}


// 添加处理推荐点击的函数
async function handleRecommendationClick(recommendationId) {
  if (!recommendationId) return
  
  // 显示加载状态
  loading.value = true
  
  try {
    // 获取推荐的认知详情
    const recommendedCognition = await cognitionStore.getCognition(recommendationId, selectedLanguage.value)
    
    if (recommendedCognition) {
      // 更新当前选中的认知
      selectedCognition.value = recommendedCognition
      
      // 等待DOM更新后滚动对话框到顶部
      await nextTick(() => {
        // 查找对话框内容元素
        const detailContent = document.querySelector('.cognition-detail .detail-content')
        if (detailContent) {
          detailContent.scrollTop = 0
        }
      })

      // 可选：更新 URL 以支持分享和浏览器历史记录
      // 如果您的应用使用 URL 参数来跟踪当前认知，可以添加如下代码
      router.replace({ 
        path: route.path,
        query: { 
          ...route.query, 
          id: recommendationId 
        }
      })
    }
  } catch (error) {
    console.error('获取推荐认知失败:', error)
  } finally {
    loading.value = false
  }
}

async function loadAvailableTopics() {
  try {
    // 使用新的认知主题结构
    const newTopicStructure = {
      hierarchy: [
        {
          primary_topic: "Architecture",
          related_topic: [
            "sparse attention",
            "linear attention", 
            "hybrid attention",
            "long context",
            "mixture-of-experts",
            "auto-regressive multimodal",
            "diffusion-based multimodal",
            "transfusion-based multimodal"
          ]
        },
        {
          primary_topic: "LLMOps",
          related_topic: [
            "Human Annotation",
            "Data Filtering",
            "Data Synthesis",
            "Cognition Engineering",
            "Human-AI Collaboration",
            "Pretraining Recipe",
            "Scaling Law",
            "continue pretraining",
            "long-context training",
            "supervised fine-tuning",
            "reinforcement learning",
            "self-iterative learning",
            "Prompt Engineering",
            "Context Engineering",
            "Benchmark",
            "Evaluation Metric"
          ]
        },
        {
          primary_topic: "Reasoning",
          related_topic: [
            "long thought training",
            "reinforcement learning scaling",
            "efficient reasoning"
          ]
        },
        {
          primary_topic: "Agent",
          related_topic: [
            "agent environment",
            "agent reward",
            "tool use",
            "multi-agent",
            "human-agent interaction",
            "memory management",
            "agentic RL",
            "agentic SFT"
          ]
        },
        {
          primary_topic: "System Efficiency",
          related_topic: [
            "distributed training",
            "RL training infra",
            "quantization and pruning",
            "knowledge distillation",
            "speculative-decoding",
            "kv-cache management"
          ]
        },
        {
          primary_topic: "RL Scaling",
          related_topic: [
            "code start",
            "RL data selection",
            "reward model",
            "entropy control"
          ]
        },
        {
          primary_topic: "Safety & Security",
          related_topic: [
            "safety alignment",
            "Privacy",
            "Explanation",
            "Emerging Risks"
          ]
        }
      ],
      categories: {
        "Architecture": {
          type: "primary",
          color: "#FF6B6B",
          background: "rgba(255, 107, 107, 0.1)"
        },
        "LLMOps": {
          type: "primary", 
          color: "#4ECDC4",
          background: "rgba(78, 205, 196, 0.1)"
        },
        "Reasoning": {
          type: "primary",
          color: "#45B7D1", 
          background: "rgba(69, 183, 209, 0.1)"
        },
        "Agent": {
          type: "primary",
          color: "#96CEB4",
          background: "rgba(150, 206, 180, 0.1)"
        },
        "System Efficiency": {
          type: "primary",
          color: "#FFEAA7",
          background: "rgba(255, 234, 167, 0.1)"
        },
        "RL Scaling": {
          type: "primary",
          color: "#DDA0DD",
          background: "rgba(221, 160, 221, 0.1)"
        },
        "Safety & Security": {
          type: "primary",
          color: "#FD79A8",
          background: "rgba(253, 121, 168, 0.1)"
        }
      },
      available_list: []
    }
    
    // 构建可用话题列表
    newTopicStructure.available_list = newTopicStructure.hierarchy.flatMap(category => [
      category.primary_topic,
      ...category.related_topic
    ])
    
    // 设置到响应式变量
    topicHierarchy.value = newTopicStructure.hierarchy
    topicCategories.value = newTopicStructure.categories
    availableTopics.value = newTopicStructure.available_list
    
    // 默认收起所有一级分类
    expandedCategories.value = new Set()
    
    console.log('新的认知主题结构已加载:', newTopicStructure)
    
  } catch (error) {
    console.error('加载可用topics失败:', error)
    // 使用默认值
    topicHierarchy.value = []
    topicCategories.value = {}
    availableTopics.value = []
    expandedCategories.value = new Set()
  }
}

async function loadCognitions(isInitialLoad = false) {
  if (isInitialLoad) {
    loading.value = true
  } else {
    if (loadingMore.value || loading.value) return // 防止重复加载
    loadingMore.value = true
  }

  const pageSize = isInitialLoad ? initialPageSize : subsequentPageSize
  const isReload = currentPage.value === 1

  try {
    const params = {
      skip: (currentPage.value - 1) * pageSize,
      limit: pageSize,
      search: searchQuery.value || undefined,
      language: selectedLanguage.value,
      sort: sortBy.value,
      time_filter: timeFilter.value,
      search_scope: searchScope.value,
      source_filter: sourceFilter.value
    }
    
    // 添加topics筛选
    if (selectedTopics.value.length > 0) {
      params.topics = selectedTopics.value
    }

    const response = await cognitionStore.getCognitions(params)

    if (response && response.items) {
      if (response.items.length < pageSize) {
        hasMore.value = false
      }
      
      if (isReload) {
        cognitions.value = response.items
      } else {
        cognitions.value = [...cognitions.value, ...response.items]
      }
      
      totalCount.value = response.total
      totalPages.value = response.pages
    } else {
      hasMore.value = false
      if (isReload) {
        cognitions.value = []
      }
    }
    
  } catch (error) {
    console.error('加载认知失败:', error)
    if (!isReload) {
      currentPage.value--
    }
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

async function handleSearch() {
  await reloadCognitions()
}

async function handleSearchClear() {
  searchQuery.value = ''
  await reloadCognitions()
}

function openCognitionDetail(cognition) {
  selectedCognition.value = cognition
  showDetailDialog.value = true
}

function handleCardClick(cognition) {
  if (synthesisMode.value) {
    toggleCognitionSelection(cognition)
  } else {
    openCognitionDetail(cognition)
  }
}

async function handleVote(cognitionId, voteType) {
  try {
    // 先更新本地状态，提供即时反馈
    const cognition = cognitions.value.find(c => c.id === cognitionId)
    if (cognition) {
      // 保存原始投票状态，以防API调用失败需要回滚
      const originalUserVote = cognition.user_vote
      const originalLikes = cognition.likes || 0
      const originalNeutral = cognition.neutral || 0
      const originalDislikes = cognition.dislikes || 0
      
      // 实现正确的单选投票逻辑
      let newVoteType = null
      if (originalUserVote === voteType) {
        // 如果点击的是当前已选中的投票类型，则取消投票
        newVoteType = null
      } else {
        // 否则切换到新的投票类型
        newVoteType = voteType
      }
      
      // 立即更新UI状态
      cognition.user_vote = newVoteType
      
      // 更新投票计数（乐观更新）
      // 首先移除原有投票的计数
      if (originalUserVote) {
        if (originalUserVote === 'like') cognition.likes = Math.max(0, originalLikes - 1)
        else if (originalUserVote === 'neutral') cognition.neutral = Math.max(0, originalNeutral - 1)
        else if (originalUserVote === 'dislike') cognition.dislikes = Math.max(0, originalDislikes - 1)
      }
      
      // 然后添加新投票的计数（如果有的话）
      if (newVoteType === 'like') cognition.likes = (cognition.likes || 0) + 1
      else if (newVoteType === 'neutral') cognition.neutral = (cognition.neutral || 0) + 1
      else if (newVoteType === 'dislike') cognition.dislikes = (cognition.dislikes || 0) + 1
      
      try {
        // 调用API - 传递正确的投票类型
        const apiVoteType = newVoteType === null ? 'cancel' : newVoteType
        await cognitionStore.voteCognition(cognitionId, apiVoteType)
        
        // API成功后，重新获取准确的数据
        const updatedCognition = await cognitionStore.getCognition(cognitionId)
        
        // 保存当前的已读状态，避免被覆盖
        const currentReadStatus = cognition.read_status
        
        Object.assign(cognition, updatedCognition)
        
        // 恢复已读状态
        if (currentReadStatus) {
          cognition.read_status = currentReadStatus
        }
        
        // 如果详情对话框打开着，也要更新
        if (selectedCognition.value && selectedCognition.value.id === cognitionId) {
          selectedCognition.value = { ...updatedCognition }
          // 同样恢复详情对话框中的已读状态
          if (currentReadStatus) {
            selectedCognition.value.read_status = currentReadStatus
          }
        }
      } catch (error) {
        // API调用失败，回滚本地状态
        cognition.user_vote = originalUserVote
        cognition.likes = originalLikes
        cognition.neutral = originalNeutral
        cognition.dislikes = originalDislikes
        throw error
      }
    } else {
      // 如果本地找不到认知，直接调用API
      await cognitionStore.voteCognition(cognitionId, voteType)
    }
  } catch (error) {
    console.error('投票失败:', error)
    // 可以在这里添加错误提示
  }
}

async function handleComment(cognitionId, content, parentId = null, replyToUsername = null) {
  try {
    await cognitionStore.addComment(cognitionId, content, parentId, replyToUsername)
    // 重新获取更新后的认知数据
    const updatedCognition = await cognitionStore.getCognition(cognitionId)
    
    // 更新本地认知数据
    const cognition = cognitions.value.find(c => c.id === cognitionId)
    if (cognition) {
      // 保存当前的已读状态，避免被覆盖
      const currentReadStatus = cognition.read_status
      
      Object.assign(cognition, updatedCognition)
      
      // 恢复已读状态
      if (currentReadStatus) {
        cognition.read_status = currentReadStatus
      }
    }
    
    // 更新详情对话框中的数据
    if (selectedCognition.value && selectedCognition.value.id === cognitionId) {
      // 保存详情对话框中的已读状态
      const currentReadStatus = selectedCognition.value.read_status
      
      selectedCognition.value = { ...updatedCognition }
      
      // 恢复已读状态
      if (currentReadStatus) {
        selectedCognition.value.read_status = currentReadStatus
      }
    }
  } catch (error) {
    console.error('添加评论失败:', error)
  }
}

// 处理已读状态变化
function handleReadStatusChanged(data) {
  try {
    const { cognitionId, readStatus } = data
    
    // 更新本地认知数据的已读状态
    const cognition = cognitions.value.find(c => c.id === cognitionId)
    if (cognition) {
      cognition.read_status = readStatus
    }
    
    // 如果详情对话框打开着，也要更新
    if (selectedCognition.value && selectedCognition.value.id === cognitionId) {
      selectedCognition.value.read_status = readStatus
    }
    
    console.log('已读状态已更新:', cognitionId, readStatus)
  } catch (error) {
    console.error('更新已读状态失败:', error)
  }
}

async function handleCognitionCreated(newCognition) {
  // 回到第一页并重新加载
  currentPage.value = 1
  await loadCognitions()
}

async function handlePageChange(newPage) {
  currentPage.value = newPage
  await loadCognitions()
}

async function handleJumpToPage() {
  if (jumpPage.value >= 1 && jumpPage.value <= totalPages.value) {
    currentPage.value = jumpPage.value
    await loadCognitions()
  }
}

async function handleEdit(cognition) {
  showEditDialog.value = false; // 先关闭，确保重新渲染
  await nextTick();
  editingCognition.value = { ...cognition }; // 拷贝新对象，避免引用问题
  showEditDialog.value = true;
}

async function handleDelete(cognitionId) {
  try {
    await cognitionStore.deleteCognition(cognitionId)
    // 从本地列表中移除已删除的认知
    const index = cognitions.value.findIndex(c => c.id === cognitionId)
    if (index > -1) {
      cognitions.value.splice(index, 1)
    }
    // 重新加载以获取准确的分页信息
    await loadCognitions()
  } catch (error) {
    console.error('删除认知失败:', error)
    // 这里可以添加错误提示
  }
}

async function handleCognitionUpdated(updatedCognition) {
  // 更新本地列表中的认知
  const index = cognitions.value.findIndex(c => c.id === updatedCognition.id)
  if (index > -1) {
    cognitions.value[index] = updatedCognition
  }
  // 如果详情对话框打开且是同一个认知，也更新详情
  if (selectedCognition.value && selectedCognition.value.id === updatedCognition.id) {
    selectedCognition.value = updatedCognition
  }
}

async function handleCognitionDeleted(cognitionId) {
  // 从本地列表中移除已删除的认知
  const index = cognitions.value.findIndex(c => c.id === cognitionId)
  if (index > -1) {
    cognitions.value.splice(index, 1)
  }
  // 如果删除的是当前查看的认知，关闭详情对话框
  if (selectedCognition.value && selectedCognition.value.id === cognitionId) {
    showDetailDialog.value = false
    selectedCognition.value = null
  }
  // 重新加载以获取准确的分页信息
  await loadCognitions()
}

function getVisiblePages() {
  const current = currentPage.value
  const total = totalPages.value
  const delta = 2 // 当前页前后显示的页数
  
  const range = []
  const rangeWithDots = []
  
  for (let i = Math.max(2, current - delta); i <= Math.min(total - 1, current + delta); i++) {
    range.push(i)
  }
  
  if (current - delta > 2) {
    rangeWithDots.push(1, '...')
  } else {
    rangeWithDots.push(1)
  }
  
  rangeWithDots.push(...range)
  
  if (current + delta < total - 1) {
    rangeWithDots.push('...', total)
  } else if (total > 1) {
    rangeWithDots.push(total)
  }
  
  return rangeWithDots.filter((v, i, a) => a.indexOf(v) === i)
}

async function handlePageJump() {
  const targetPage = parseInt(jumpPage.value)
  if (targetPage >= 1 && targetPage <= totalPages.value && targetPage !== currentPage.value) {
    currentPage.value = targetPage
    await loadCognitions()
  }
  jumpPage.value = currentPage.value
}

function handleCloseDetail() {
  selectedCognition.value = null
  showDetailDialog.value = false
}

function getTopicChipStyle(value) {
  const colors = topicColors[value] || {
    color: '#667eea',
    background: 'rgba(102, 126, 234, 0.1)'
  }
  
  return {
    color: colors.color,
    backgroundColor: colors.background
  }
}

async function handleTopicsChange(newTopics) {
  selectedTopics.value = newTopics
  currentPage.value = 1
  await loadCognitions()
}

async function handleTopicClick(topic) {
  if (!selectedTopics.value.includes(topic)) {
    selectedTopics.value.push(topic)
  }
}

async function handleLanguageChange() {
  currentPage.value = 1
  await loadCognitions()
}

async function handleSortSelect(value) {
  sortBy.value = value
  showSortSelector.value = false
  currentPage.value = 1
  await loadCognitions()
}

async function toggleTopic(topic) {
  if (selectedTopics.value.includes(topic)) {
    selectedTopics.value = selectedTopics.value.filter(t => t !== topic)
  } else {
    selectedTopics.value.push(topic)
  }
}

async function clearTopicSelection() {
  selectedTopics.value = []
  showTopicSelector.value = false
}

async function removeTopic(topic) {
  selectedTopics.value = selectedTopics.value.filter(t => t !== topic)
}

function getPrimaryCategoryStyle(category) {
  const categoryInfo = topicCategories.value[category]
  if (categoryInfo && categoryInfo.type === 'primary') {
    return {
      color: categoryInfo.color,
      backgroundColor: categoryInfo.background,
      borderColor: categoryInfo.color
    }
  }
  
  // 默认样式
  return {
    color: '#667eea',
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    borderColor: '#667eea'
  }
}

function getSecondaryCategoryStyle(parentCategory, isSelected) {
  const categoryInfo = topicCategories.value[parentCategory]
  if (categoryInfo && categoryInfo.type === 'primary') {
    const color = categoryInfo.color
    const lightColor = categoryInfo.background
    
    return {
      color: isSelected ? 'white' : color,
      backgroundColor: isSelected ? color : lightColor,
      borderColor: isSelected ? color : 'transparent'
    }
  }
  
  // 默认样式
  return {
    color: isSelected ? 'white' : '#667eea',
    backgroundColor: isSelected ? '#667eea' : 'rgba(102, 126, 234, 0.1)',
    borderColor: isSelected ? '#667eea' : 'transparent'
  }
}

function formatCategoryName(category) {
  return category.replace(/-/g, ' ')
}

function formatTopicName(topic) {
  return topic.replace(/-/g, ' ').replace(/\(/g, ' (')
}

function getSelectedCountInCategory(category) {
  return category.related_topic.filter(topic => selectedTopics.value.includes(topic)).length
}

function toggleCategory(category) {
  if (expandedCategories.value.has(category)) {
    expandedCategories.value.delete(category)
  } else {
    expandedCategories.value.add(category)
  }
}

function handleSearchScopeChange(scope) {
  searchScope.value = scope
  showSearchScopeSelector.value = false
  reloadCognitions()
}

function handleTimeFilterChange(filter) {
  timeFilter.value = filter
  showTimeFilterSelector.value = false
  reloadCognitions()
}

function handleSourceFilterChange(filter) {
  sourceFilter.value = filter
  showSourceFilterSelector.value = false
  reloadCognitions()
}

function isAllTopicsSelectedInCategory(category) {
  const categoryTopics = category.related_topic
  return categoryTopics.length > 0 && categoryTopics.every(topic => selectedTopics.value.includes(topic))
}

function toggleSelectAllInCategory(category) {
  const categoryTopics = category.related_topic
  if (isAllTopicsSelectedInCategory(category)) {
    // 取消全选：移除该分类下的所有topics
    selectedTopics.value = selectedTopics.value.filter(topic => !categoryTopics.includes(topic))
  } else {
    // 全选：添加该分类下的所有topics（去重）
    const newTopics = categoryTopics.filter(topic => !selectedTopics.value.includes(topic))
    selectedTopics.value = [...selectedTopics.value, ...newTopics]
  }
}

// --- 合成模式相关方法 ---

function toggleSynthesisMode() {
  synthesisMode.value = !synthesisMode.value
  if (!synthesisMode.value) {
    selectedCognitionsForSynthesis.value = []
  }
}

function toggleCognitionSelection(cognition) {
  const index = selectedCognitionsForSynthesis.value.findIndex(c => c.id === cognition.id)
  if (index > -1) {
    selectedCognitionsForSynthesis.value.splice(index, 1)
  } else {
    if (selectedCognitionsForSynthesis.value.length >= 5) {
      // 此处可以添加一个toast提示用户
      console.warn("最多选择5项进行合成")
      return
    }
    selectedCognitionsForSynthesis.value.push(cognition)
  }
}

function isCognitionSelected(cognitionId) {
  return selectedCognitionsForSynthesis.value.some(c => c.id === cognitionId)
}

async function handleStartSynthesis() {
  if (selectedCognitionsForSynthesis.value.length < 2 || selectedCognitionsForSynthesis.value.length > 5) {
    return
  }
  synthesizing.value = true
  try {
    const ids = selectedCognitionsForSynthesis.value.map(c => c.id)
    
    const newCognitionData = await cognitionStore.synthesizeCognitions(ids)

    synthesizedCognition.value = newCognitionData

    showSynthesisResultDialog.value = true
    
    toggleSynthesisMode()
  } catch (error) {
    console.error('合成认知失败:', error)
    // 这里可以添加错误提示
  } finally {
    synthesizing.value = false
  }
}

async function handleSynthesisSaved(newCognition) {
  showSynthesisResultDialog.value = false
  // 刷新列表以显示新保存的认知
  await reloadCognitions()
}

// 处理热榜topic点击
function handleHotTopicClick(topic) {
  console.log('热榜topic点击:', topic)
  
  // 清空当前筛选，添加选中的topic
  selectedTopics.value = [topic]
  
  // 重新加载认知列表
  loadCognitions(true)
  
  // 可选：滚动到认知列表顶部
  setTimeout(() => {
    const mainContent = document.querySelector('.main-content')
    if (mainContent) {
      mainContent.scrollTop = 200 // 滚动到热榜下方
    }
  }, 100)
}

// 处理热榜认知点击
function handleHotCognitionClick(cognition) {
  console.log('热榜认知点击:', cognition)
  
  // 设置选中的认知并显示详情
  selectedCognition.value = cognition
  showDetailDialog.value = true
}
</script>

<style scoped>
.cognition-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

.nav-bar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.logo-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 12px;
}

.q-logo {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 20px;
}

.logo-text {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
}

.nav-tools {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-box {
  width: 320px;
}

.create-btn {
  gap: 8px;
}

.synthesis-btn {
  gap: 8px;
}

.library-btn {
  gap: 8px;
}

.language-selector {
  width: 140px;
}

.language-selector :deep(.v-select__selection-text) {
  font-size: 12px !important;
}

.language-selector :deep(.v-field__input) {
  font-size: 12px !important;
}

.language-selector :deep(.v-list-item-title) {
  font-size: 12px !important;
}

.main-content {
  flex: 1;
  padding: 24px;
  max-width: 1800px;
  margin: 0 auto;
  width: 100%;
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 10;
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-icon {
  opacity: 0.8;
}

.filter-label {
  font-size: 14px;
  font-weight: 600;
  color: white;
  white-space: nowrap;
}

.filter-section > div {
  position: relative;
}

.scope-btn, .time-btn, .sort-btn, .browse-btn, .source-btn {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-weight: 600;
  font-size: 14px;
  white-space: nowrap;
}

.scope-btn:hover, .time-btn:hover, .sort-btn:hover, .browse-btn:hover, .source-btn:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.scope-btn.active, .time-btn.active, .sort-btn.active, .browse-btn.active, .source-btn.active {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.15));
  border-color: rgba(255, 255, 255, 0.6);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.scope-btn .v-icon:last-child, .time-btn .v-icon:last-child, .sort-btn .v-icon:last-child, .source-btn .v-icon:last-child {
  transition: transform 0.3s ease;
}

.scope-btn .v-icon:last-child.rotate, .time-btn .v-icon:last-child.rotate, .sort-btn .v-icon:last-child.rotate, .source-btn .v-icon:last-child.rotate {
  transform: rotate(180deg);
}

.scope-selector-dropdown, .time-selector-dropdown, .sort-selector-dropdown, .source-selector-dropdown, .topic-selector {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 2000;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  margin-top: 8px;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
}

.scope-selector-dropdown, .time-selector-dropdown, .sort-selector-dropdown, .source-selector-dropdown {
  width: 160px;
}

.scope-option, .time-option, .sort-option, .source-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
}

.scope-option:hover, .time-option:hover, .sort-option:hover, .source-option:hover {
  background: rgba(102, 126, 234, 0.1);
}

.scope-option.selected, .time-option.selected, .sort-option.selected, .source-option.selected {
  background: rgba(102, 126, 234, 0.15);
  font-weight: 600;
  color: #667eea;
}

.search-scope-section {
  position: relative;
}

.sort-filter-section {
  position: relative;
}

.time-filter-section {
  position: relative;
}

.topic-filter-section {
  position: relative;
}

.browse-btn-content {
  display: flex;
  align-items: center;
  gap: 10px;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.browse-icon {
  transition: transform 0.3s ease;
}

.browse-icon.rotate {
  transform: rotate(180deg);
}

.browse-text {
  white-space: nowrap;
}

.selected-count {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 700;
  min-width: 20px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.topic-selector {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 2000;
  width: 400px;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  margin-top: 8px;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.topic-selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.clear-btn, .close-btn {
  color: rgba(255, 255, 255, 0.8) !important;
  border-radius: 8px;
}

.clear-btn:hover, .close-btn:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: white !important;
}

.topic-categories {
  max-height: 400px;
  overflow-y: auto;
  padding: 12px;
}

.category-section {
  margin-bottom: 8px;
}

.primary-category {
  border-radius: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  margin-bottom: 8px;
}

.primary-category:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.category-left {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  flex: 1;
}

.category-right {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.select-all-btn {
  min-width: auto !important;
  padding: 4px !important;
  border-radius: 6px !important;
  opacity: 0.8;
  transition: all 0.2s ease !important;
}

.select-all-btn:hover {
  opacity: 1 !important;
  background: rgba(255, 255, 255, 0.1) !important;
  transform: scale(1.1);
}

.expand-icon {
  transition: transform 0.2s ease;
}

.expand-icon.rotate {
  transform: rotate(90deg);
}

.category-name {
  font-size: 14px;
  font-weight: 600;
  flex: 1;
}

.category-badge {
  background: rgba(255, 255, 255, 0.9);
  color: inherit;
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 11px;
  font-weight: 700;
  min-width: 20px;
  text-align: center;
}

.secondary-topics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 8px;
  padding: 8px 16px 16px;
  margin-left: 16px;
  border-left: 2px solid rgba(102, 126, 234, 0.2);
  background: rgba(247, 249, 252, 0.5);
  border-radius: 8px;
}

.topic-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid transparent;
}

.topic-option:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.topic-option.selected {
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.topic-icon {
  flex-shrink: 0;
}

.topic-name {
  flex: 1;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.topic-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(247, 249, 252, 0.8);
}

.selected-summary {
  font-size: 14px;
  color: #667eea;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn {
  gap: 6px;
  text-transform: none;
  font-weight: 500;
  border-radius: 8px;
}

.close-action-btn {
  border-radius: 8px;
  color: rgba(102, 126, 234, 0.8) !important;
}

.close-action-btn:hover {
  background: rgba(102, 126, 234, 0.1) !important;
  color: #667eea !important;
}

.selected-topics {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.selected-topic-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  color: white;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.selected-topic-tag:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
}

.selected-topic-tag .remove-icon {
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.selected-topic-tag:hover .remove-icon {
  opacity: 1;
}

.selected-topic-more {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 6px;
  color: white;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 32px;
  text-align: center;
}

.selected-topic-more:hover {
  background: rgba(255, 255, 255, 0.35);
  border-color: rgba(255, 255, 255, 0.6);
  transform: scale(1.05);
}

.results-info {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
  white-space: nowrap;
}

.cognitions-masonry {
  width: 100%;
}

.cognition-item {
  margin-bottom: 16px; /* 控制卡片垂直间距 */
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  min-height: 300px;
}

.empty-state h3 {
  margin: 16px 0 8px;
  font-size: 1.5rem;
  font-weight: 600;
}

.empty-state p {
  margin-bottom: 24px;
  font-size: 1rem;
  opacity: 0.7;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loader-sentinel {
  height: 50px;
  width: 100%;
}

.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  gap: 12px;
  color: white;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-bar {
    padding: 12px 16px;
    flex-direction: column;
    gap: 12px;
  }
  
  .nav-tools {
    width: 100%;
    justify-content: space-between;
  }
  
  .search-box {
    width: 200px;
  }
  
  .main-content {
    padding: 16px;
  }
  
  .cognitions-masonry {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .filter-bar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    padding: 12px 16px;
  }
  
  .filter-section {
    justify-content: center;
  }
  
  .results-info {
    justify-content: center;
    text-align: center;
  }
  
  .loading-more {
    order: 3;
    font-size: 12px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .cognitions-masonry {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1025px) and (max-width: 1400px) {
  .cognitions-masonry {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1401px) and (max-width: 1800px) {
  .cognitions-masonry {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1801px) {
  .cognitions-masonry {
    grid-template-columns: repeat(5, 1fr);
  }
}

.topics-selector {
  width: 220px;
}

.topics-selector :deep(.v-field) {
  background: rgba(255, 255, 255, 0.15) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  border-radius: 12px !important;
  backdrop-filter: blur(10px);
}

.topics-selector :deep(.v-field__input) {
  color: white !important;
  font-size: 13px !important;
  font-weight: 500 !important;
}

.topics-selector :deep(.v-select__selection-text) {
  font-size: 13px !important;
  color: white !important;
  font-weight: 500 !important;
}

.topics-selector :deep(.v-field__append-inner .v-icon) {
  color: rgba(255, 255, 255, 0.7) !important;
}

.topics-selector :deep(.v-field__outline) {
  --v-field-border-opacity: 0.3;
}

.topics-selector:hover :deep(.v-field) {
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
}

.topic-chip {
  font-size: 11px !important;
  height: 22px !important;
  margin: 2px 4px 2px 0 !important;
  border-radius: 12px !important;
  font-weight: 600 !important;
}

/* 趋势按钮样式 */
.trend-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
  transition: all 0.3s ease !important;
}

.trend-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
}

.trend-btn .v-icon {
  color: white !important;
}

.trend-btn .v-btn__content {
  color: white !important;
  font-weight: 600;
}

/* 热榜按钮样式 */
.hot-btn {
  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%) !important;
  border: none !important;
  box-shadow: 0 4px 15px rgba(255, 71, 87, 0.3) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
}

.hot-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(255, 71, 87, 0.4) !important;
}

.hot-btn .v-icon {
  color: white !important;
}

.hot-btn .v-btn__content {
  color: white !important;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
}

.live-chip-small {
  margin-left: 4px !important;
  height: 16px !important;
  font-size: 9px !important;
  font-weight: 700 !important;
  animation: liveBlink 2s infinite;
}

@keyframes liveBlink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.synthesis-selection-bar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 16px 24px;
  border-radius: 16px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  border: 1px solid rgba(0,0,0,0.05);
  animation: slideDown 0.4s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.synthesis-header {
  margin-bottom: 16px;
}

.synthesis-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.synthesis-header p {
  font-size: 14px;
  color: #7f8c8d;
  margin: 4px 0 0;
}

.selected-items-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 20px;
  min-height: 40px;
}

.selected-item {
  transition: all 0.3s ease;
}

.synthesis-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.list-fade-enter-active,
.list-fade-leave-active {
  transition: all 0.3s ease;
}
.list-fade-enter-from,
.list-fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}


</style> 