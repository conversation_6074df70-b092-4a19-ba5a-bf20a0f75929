<template>
  <div class="collection-detail-page">
    <!-- 顶部导航栏 -->
    <div class="nav-bar">
      <div class="nav-left">
        <div class="breadcrumb">
          <v-btn variant="text" size="small" @click="$router.push('/cognition/library')">
            <v-icon>mdi-arrow-left</v-icon>
            返回认知库
          </v-btn>
        </div>
        
        <div class="collection-header" v-if="collectionInfo">
          <div class="collection-icon">
            <v-icon size="32" :color="collectionInfo.is_default ? '#ff4757' : '#667eea'">
              {{ collectionInfo.is_default ? 'mdi-heart' : 'mdi-folder' }}
            </v-icon>
          </div>
          <div class="collection-info">
            <h1 class="collection-name">{{ collectionInfo.name }}</h1>
            <p class="collection-description" v-if="collectionInfo.description">
              {{ collectionInfo.description }}
            </p>
            <p class="collection-meta">
              共 <span class="count-number">{{ totalCount }}</span> 个认知
            </p>
          </div>
        </div>
      </div>
      
      <div class="nav-tools">
        <user-status-button />
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <div v-if="cognitions.length > 0">
        <masonry :cols="{ default: 5, 1800: 4, 1400: 3, 1024: 2, 768: 1 }" :gutter="{ default: '16px' }">
          <div v-for="cognition in cognitions" :key="cognition.id" class="cognition-item">
            <CognitionCard
              :cognition="cognition"
              :language="selectedLanguage"
              @click="openCognitionDetail(cognition)"
              @vote="handleVote"
              @edit="handleEdit"
              @delete="handleDelete"
              @topic-click="handleTopicClick"
            />
          </div>
        </masonry>
      </div>
      
      <div v-else-if="!loading" class="empty-state">
        <h3>这个认知库还是空的</h3>
        <p>快去发现并收藏更多认知吧！</p>
        <v-btn color="primary" @click="$router.push('/cognition')">前往发现</v-btn>
      </div>

      <div ref="loaderRef" class="loader-sentinel">
        <div v-if="loadingMore" class="loading-more">
          <v-progress-circular indeterminate size="32" color="primary" />
          <span>加载中...</span>
        </div>
      </div>
    </div>

    <!-- 认知详情对话框 -->
    <CognitionDetailDialog
      v-model="showDetailDialog"
      :cognition="selectedCognition"
      @vote="handleVote"
      @comment="handleComment"
      @close="selectedCognition = null"
      @recommendation-click="handleRecommendationClick"
    />

    <!-- 编辑认知对话框 -->
    <EditCognitionDialog
      v-model="showEditDialog"
      :cognition="editingCognition"
      @updated="handleCognitionUpdated"
      @deleted="handleDelete"
    />

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <v-progress-circular indeterminate size="64" color="primary" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import CognitionCard from '@/components/cognition/CognitionCard.vue'
import CognitionDetailDialog from '@/components/cognition/CognitionDetailDialog.vue'
import EditCognitionDialog from '@/components/cognition/EditCognitionDialog.vue'
import UserStatusButton from '@/components/UserStatusButton.vue'
import { useCognitionStore } from '@/stores/cognition'

const route = useRoute()
const router = useRouter()
const cognitionStore = useCognitionStore()

const collectionId = computed(() => route.params.id)
const cognitions = ref([])
const collectionInfo = ref(null)
const loading = ref(false)
const loadingMore = ref(false)
const hasMore = ref(true)
const showDetailDialog = ref(false)
const selectedCognition = ref(null)
const currentPage = ref(1)
const pageSize = 30
const showEditDialog = ref(false)
const editingCognition = ref(null)
const selectedLanguage = ref('zh')
const loaderRef = ref(null)

let observer = null

onMounted(() => {
  if (collectionId.value) {
    loadInitialCognitions()
    setupIntersectionObserver()
  }
})

onUnmounted(() => {
  if (observer) observer.disconnect()
})

async function loadInitialCognitions() {
  currentPage.value = 1
  hasMore.value = true
  cognitions.value = []
  await loadCognitions()
}

async function loadCognitions() {
  if (currentPage.value === 1) loading.value = true
  else loadingMore.value = true

  try {
    const response = await cognitionStore.getCollectionCognitions(collectionId.value, {
      skip: (currentPage.value - 1) * pageSize,
      limit: pageSize
    })
    
    if (response && response.items) {
      if (response.items.length < pageSize) hasMore.value = false
      cognitions.value.push(...response.items)
      totalCount.value = response.total
      if (currentPage.value === 1) {
        collectionInfo.value = response.collection
      }
      currentPage.value++
    } else {
      hasMore.value = false
    }
  } catch (error) {
    console.error('加载收藏夹认知失败:', error)
    hasMore.value = false
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

function setupIntersectionObserver() {
  observer = new IntersectionObserver(
    (entries) => {
      if (entries[0].isIntersecting && hasMore.value && !loading.value && !loadingMore.value) {
        loadCognitions()
      }
    },
    { threshold: 0.1 }
  )
  if (loaderRef.value) observer.observe(loaderRef.value)
}

function openCognitionDetail(cognition) {
  selectedCognition.value = cognition
  showDetailDialog.value = true
}

// 添加处理推荐点击的函数
async function handleRecommendationClick(recommendationId) {
  if (!recommendationId) return
  
  // 显示加载状态
  loading.value = true
  
  try {
    // 获取推荐的认知详情
    const recommendedCognition = await cognitionStore.getCognition(recommendationId)
    
    if (recommendedCognition) {
      // 更新当前选中的认知
      selectedCognition.value = recommendedCognition
      
      // 等待DOM更新后滚动对话框到顶部
      await nextTick(() => {
        // 查找对话框内容元素
        const detailContent = document.querySelector('.cognition-detail .detail-content')
        if (detailContent) {
          detailContent.scrollTop = 0
        }
      })

      // 检查这个推荐的认知是否在当前收藏夹中
      const isInCollection = cognitions.value.some(c => c.id === recommendationId)
      
      // 如果不在当前收藏夹中，通知用户
      if (!isInCollection) {
        console.log('提示：此推荐认知不在当前收藏夹中')
        // 这里可以添加一个提示，例如使用 toast 或其他通知组件
      }
      
      // // 可选：更新 URL 参数
      // router.replace({
      //   path: route.path,
      //   query: {
      //     ...route.query,
      //     cognition_id: recommendationId
      //   }
      // })
    }
  } catch (error) {
    console.error('获取推荐认知失败:', error)
  } finally {
    loading.value = false
  }
}

async function handleVote(cognitionId, voteType) {
  try {
    const updatedCognition = await cognitionStore.voteCognition(cognitionId, voteType)
    const index = cognitions.value.findIndex(c => c.id === cognitionId)
    if (index > -1) {
      cognitions.value[index] = { ...cognitions.value[index], ...updatedCognition }
    }
    if (selectedCognition.value && selectedCognition.value.id === cognitionId) {
      selectedCognition.value = { ...selectedCognition.value, ...updatedCognition }
    }
  } catch (error) {
    console.error('投票失败:', error)
  }
}

async function handleComment(cognitionId, content) {
  try {
    const updatedCognition = await cognitionStore.addComment(cognitionId, content)
    const index = cognitions.value.findIndex(c => c.id === cognitionId)
    if (index > -1) {
      Object.assign(cognitions.value[index], updatedCognition)
    }
    if (selectedCognition.value && selectedCognition.value.id === cognitionId) {
      selectedCognition.value = updatedCognition
    }
  } catch (error) {
    console.error('添加评论失败:', error)
  }
}

function handleEdit(cognition) {
  editingCognition.value = { ...cognition }
  showEditDialog.value = true
}

function handleDelete(cognitionId) {
  cognitions.value = cognitions.value.filter(c => c.id !== cognitionId)
  if (selectedCognition.value && selectedCognition.value.id === cognitionId) {
    showDetailDialog.value = false
  }
}

function handleCognitionUpdated(updatedCognition) {
  const index = cognitions.value.findIndex(c => c.id === updatedCognition.id)
  if (index > -1) {
    cognitions.value[index] = updatedCognition
  }
  if (selectedCognition.value && selectedCognition.value.id === updatedCognition.id) {
    selectedCognition.value = updatedCognition
  }
}

function handleTopicClick(topic) {
  router.push('/cognition')
}
</script>

<style scoped>
.collection-detail-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

.nav-bar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 24px;
  flex: 1;
}

.breadcrumb {
  color: #7f8c8d;
}

.collection-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.collection-icon {
  flex-shrink: 0;
}

.collection-info {
  flex: 1;
}

.collection-name {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 4px 0;
}

.collection-description {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0 0 8px 0;
}

.collection-meta {
  font-size: 14px;
  color: #95a5a6;
  margin: 0;
}

.count-number {
  font-weight: 600;
  color: #667eea;
}

.nav-tools {
  display: flex;
  align-items: center;
  gap: 16px;
}

.main-content {
  flex: 1;
  padding: 24px;
  max-width: 1800px;
  margin: 0 auto;
  width: 100%;
}

.cognition-item {
  margin-bottom: 16px;
}

.empty-state {
  text-align: center;
  padding: 80px 20px;
  color: white;
}

.empty-state h3 {
  margin: 16px 0 8px;
  font-size: 24px;
  color: white;
}

.empty-state p {
  margin-bottom: 24px;
  opacity: 0.8;
  font-size: 16px;
}

.loader-sentinel {
  height: 50px;
  width: 100%;
}

.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  gap: 12px;
  color: white;
  font-size: 16px;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-bar {
    padding: 12px 16px;
    flex-direction: column;
    gap: 12px;
  }
  
  .nav-left {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .collection-header {
    width: 100%;
  }
  
  .nav-tools {
    width: 100%;
    justify-content: flex-end;
  }
  
  .main-content {
    padding: 16px;
  }
  
  .cognition-item {
    margin-bottom: 16px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .cognition-item {
    margin-bottom: 16px;
  }
}

@media (min-width: 1025px) and (max-width: 1400px) {
  .cognition-item {
    margin-bottom: 16px;
  }
}

@media (min-width: 1401px) and (max-width: 1800px) {
  .cognition-item {
    margin-bottom: 16px;
  }
}

@media (min-width: 1801px) {
  .cognition-item {
    margin-bottom: 16px;
  }
}
</style> 