<template>
  <div class="library-page">
    <!-- 顶部导航栏 -->
    <div class="nav-bar">
      <div class="nav-left">
        <div class="logo-container" @click="$router.push('/cognition')">
          <span class="q-logo">Q</span>
          <span class="logo-text">我的认知库</span>
        </div>
      </div>
      <div class="nav-tools">
        <user-status-button />
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <div class="collections-grid" v-if="collections.length > 0">
        <div
          v-for="collection in collections"
          :key="collection.id"
          class="collection-card"
          @click="openCollection(collection)"
        >
          <div class="card-header">
            <div class="collection-icon">
              <v-icon size="32" :color="collection.is_default ? '#ff4757' : '#667eea'">
                {{ collection.is_default ? 'mdi-heart' : 'mdi-folder' }}
              </v-icon>
            </div>
            <div class="collection-info">
              <h3 class="collection-name">{{ collection.name }}</h3>
              <p class="collection-count">{{ collection.cognition_count }} 个认知</p>
            </div>
          </div>
          <div class="card-description">
            <p v-if="collection.description">{{ collection.description }}</p>
            <p v-else class="no-description">暂无描述</p>
          </div>
          <div class="card-footer">
            <span class="update-time">{{ formatTime(collection.updated_at) }}</span>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else-if="!loading" class="empty-state">
        <v-icon size="64" color="grey-lighten-1">mdi-heart-outline</v-icon>
        <h3>暂无认知库</h3>
        <p>您还没有创建任何认知库</p>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <v-progress-circular indeterminate size="64" color="primary" />
    </div>
  </div>
</template>

<script setup>
import UserStatusButton from '@/components/UserStatusButton.vue'
import { useCognitionStore } from '@/stores/cognition'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const cognitionStore = useCognitionStore()

// 响应式数据
const collections = ref([])
const loading = ref(false)
let removeListener = null

// 生命周期
onMounted(() => {
  loadCollections()

  // 添加收藏状态监听器，当收藏状态变化时重新加载收藏夹列表
  removeListener = cognitionStore.addFavoriteStatusListener(() => {
    // 延迟一点重新加载，确保后端数据已更新
    setTimeout(() => {
      loadCollections()
    }, 500)
  })
})

onUnmounted(() => {
  if (removeListener) {
    removeListener()
  }
})

// 方法
async function loadCollections() {
  try {
    loading.value = true
    const response = await cognitionStore.getUserCollections()
    collections.value = response || []
  } catch (error) {
    console.error('加载认知库失败:', error)
  } finally {
    loading.value = false
  }
}

function openCollection(collection) {
  router.push(`/cognition/library/${collection.id}`)
}

function formatTime(dateString) {
  try {
    if (!dateString) return '未知时间'
    const date = new Date(dateString)
    return formatDistanceToNow(date, { addSuffix: true, locale: zhCN })
  } catch (error) {
    return '未知时间'
  }
}
</script>

<style scoped>
.library-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

.nav-bar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.logo-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 12px;
}

.q-logo {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 20px;
}

.logo-text {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
}

.nav-tools {
  display: flex;
  align-items: center;
  gap: 16px;
}

.main-content {
  flex: 1;
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.collections-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.collection-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  min-height: 200px;
}

.collection-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
}

.collection-icon {
  flex-shrink: 0;
}

.collection-info {
  flex: 1;
}

.collection-name {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 4px 0;
}

.collection-count {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
}

.card-description {
  color: #5a6c7d;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
  flex: 1;
  min-height: 40px;
  display: flex;
  align-items: flex-start;
}

.card-description p {
  margin: 0;
}

.no-description {
  color: #bdc3c7;
  font-style: italic;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  margin-top: auto;
}

.update-time {
  font-size: 12px;
  color: #95a5a6;
}

.empty-state {
  text-align: center;
  padding: 80px 20px;
  color: white;
}

.empty-state h3 {
  margin: 16px 0 8px;
  font-size: 24px;
  color: white;
}

.empty-state p {
  margin-bottom: 24px;
  opacity: 0.8;
  font-size: 16px;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
</style> 