<template>
  <div class="auth-container">
    <div class="auth-card">
      <div class="logo-container">
        <span class="logo-text">Deep Cognition</span>
      </div>
      <h1>欢迎登录</h1>
      <div v-if="authStore.error" class="error-message">
        {{ authStore.error }}
      </div>

      <form @submit.prevent="handleLogin">
        <div class="form-group">
          <div class="input-with-icon">
            <i class="input-icon">✉️</i>
            <input
              type="username"
              id="username"
              v-model="username"
              required
              placeholder="输入邮箱"
              class="full-width-input"
            />
          </div>
        </div>

        <div class="form-group">
          <div class="input-with-icon">
            <i class="input-icon">🔒</i>
            <input
              type="password"
              id="password"
              v-model="password"
              required
              placeholder="输入密码"
              class="full-width-input"
            />
          </div>
        </div>

        <button type="submit" class="submit-button" :disabled="authStore.loading">
          {{ authStore.loading ? "登录中..." : "登录" }}
        </button>
      </form>

      <div class="auth-links">
        <p>还没有账号？ <router-link to="/register">注册</router-link></p>
      </div>
      
      <!-- <div class="notice-message">
        <v-alert
          color="info"
          variant="tonal"
          border="start"
          density="comfortable"
          class="mt-4"
        >
          <template v-slot:prepend>
            <v-icon
              color="info"
              icon="mdi-information"
              size="small"
            ></v-icon>
          </template>
          <div class="text-body-2">
            <strong>功能变更通知：</strong>
            <p class="mb-0">非常抱歉，由于系统功能变更，原先使用此链接的用户需要重新注册。感谢您的理解与支持。</p>
          </div>
        </v-alert>
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useAuthStore } from "@/stores/authStore";
import { eventApi, EVENT_TYPES } from "@/api/event";
import { AnalyticsService } from '@/utils/analytics';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const username = ref("");
const password = ref("");

const handleLogin = async () => {
  try {
    await authStore.login(username.value, password.value);
    // 登录成功后，检查token是否存在
    if (localStorage.getItem('auth_token')) {
      // 记录登录事件
      try {
        await eventApi.trackEvent(EVENT_TYPES.LOGIN, {
          username: username.value
        });
        AnalyticsService.trackEvent('用户认证', 'Login', '登录');
      } catch (e) {
        console.error("记录登录事件失败:", e);
      }
      
      // 如果有重定向参数，则跳转到该路径
      const redirectPath = route.query.redirect as string || "/draft";
      console.log("authToken", localStorage.getItem('auth_token'));
      router.push(redirectPath);
    }
  } catch (error) {
    console.error("登录失败:", error);
  }
};
</script>

<style scoped>
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f0ebff;
  background-image: linear-gradient(315deg, #e8e6f6 0%, #f0ebff 74%);
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.auth-card {
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 6px 20px rgba(124, 77, 255, 0.15);
  padding: 2.5rem;
  width: 100%;
  max-width: 440px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.auth-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(124, 77, 255, 0.2);
}

.logo-container {
  text-align: center;
  margin-bottom: 1.5rem;
}

.logo-text {
  color: #654C8C;
  font-family: 'Montserrat', sans-serif, 'Arial';
  font-weight: 700;
  letter-spacing: 0.08em;
  text-transform: uppercase;
  font-size: 1.5rem;
  position: relative;
  background: linear-gradient(to right, #654C8C, #7c4dff);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 1px rgba(124, 77, 255, 0.1);
}

h1 {
  margin-bottom: 1.5rem;
  text-align: center;
  color: #654C8C;
  font-size: 1.6rem;
  font-weight: 600;
}

.form-group {
  margin-bottom: 1.2rem;
}

.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 12px;
  font-style: normal;
  color: #654C8C;
  z-index: 1;
}

.full-width-input {
  width: 100%;
  padding: 0.9rem 0.75rem 0.9rem 2.5rem;
  border: 1px solid #e0d9f5;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: #f9f7ff;
  color: #333;
}

.full-width-input:focus {
  outline: none;
  border-color: #7c4dff;
  box-shadow: 0 0 0 3px rgba(124, 77, 255, 0.1);
  background-color: white;
}

.full-width-input::placeholder {
  color: #a99ec9;
}

.submit-button {
  width: 100%;
  padding: 0.9rem;
  background-color: #7c4dff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  margin-top: 1.5rem;
  transition: background-color 0.3s ease, transform 0.1s ease;
}

.submit-button:hover {
  background-color: #654C8C;
  transform: translateY(-2px);
}

.submit-button:active {
  transform: translateY(0);
}

.submit-button:disabled {
  background-color: #c5baed;
  cursor: not-allowed;
  transform: none;
}

.error-message {
  background-color: #ffe9e9;
  color: #d93025;
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
  border-left: 4px solid #d93025;
}

.auth-links {
  margin-top: 2rem;
  text-align: center;
  color: #654C8C;
}

.auth-links a {
  color: #7c4dff;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.2s ease;
}

.auth-links a:hover {
  color: #654C8C;
  text-decoration: underline;
}

.notice-message {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.notice-message :deep(.v-alert) {
  font-size: 0.9rem;
  line-height: 1.5;
}
</style>
