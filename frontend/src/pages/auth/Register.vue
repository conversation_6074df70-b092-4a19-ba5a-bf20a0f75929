<template>
  <div class="auth-container">
    <div class="auth-card">
      <h1>创建账户</h1>

      <div v-if="authStore.error" class="error-message">
        {{ authStore.error }}
      </div>

      <form @submit.prevent="handleRegister">
        <div class="form-group">
          <input
            type="text"
            id="name"
            v-model="name"
            required
            placeholder="输入昵称"
            class="full-width-input"
          />
        </div>

        <div class="form-group">
          <input
            type="email"
            id="username"
            v-model="username"
            required
            placeholder="输入邮箱"
            class="full-width-input"
          />
        </div>

        <!-- <div class="form-group">
          <input
            type="email"
            id="email"
            v-model="email"
            required
            placeholder="输入邮箱"
            class="full-width-input"
          />
        </div> -->

        <div class="form-group">
          <input
            type="password"
            id="password"
            v-model="password"
            required
            placeholder="输入密码"
            minlength="8"
            class="full-width-input"
          />
        </div>

        <div class="form-group">
          <input
            type="password"
            id="confirmPassword"
            v-model="confirmPassword"
            required
            placeholder="确认密码"
            class="full-width-input"
          />
        </div>

        <div class="form-group">
          <input
            type="text"
            id="inviteCode"
            v-model="inviteCode"
            required
            placeholder="输入邀请码"
            class="full-width-input"
            :class="{ 'input-error': inviteCodeError }"
          />
          <div v-if="inviteCodeError" class="error-hint">
            {{ inviteCodeError }}
          </div>
        </div>

        <button
          type="submit"
          class="submit-button"
          :disabled="authStore.loading || password !== confirmPassword"
        >
          {{ authStore.loading ? "创建账户..." : "创建账户" }}
        </button>

        <div
          v-if="password !== confirmPassword && confirmPassword"
          class="password-mismatch"
        >
          密码不匹配
        </div>
      </form>

      <div class="auth-links">
        <p>已经有账户？ <router-link to="/login">登录</router-link></p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRouter } from "vue-router";
import { useAuthStore } from "@/stores/authStore";
import { eventApi, EVENT_TYPES } from "@/api/event"; // 导入事件API
import { AnalyticsService } from '@/utils/analytics';

const router = useRouter();
const authStore = useAuthStore();
const name = ref("");
const username = ref("");
const email = ref("");
const password = ref("");
const confirmPassword = ref("");
const inviteCode = ref("");
const inviteCodeError = ref("");

const handleRegister = async () => {
  try {
    // 重置错误信息
    inviteCodeError.value = "";
    
    // 检查密码是否匹配
    if (password.value !== confirmPassword.value) {
      return;
    }
    
    // 验证邀请码
    if (!inviteCode.value || inviteCode.value.length !== 8) {
      inviteCodeError.value = "请输入有效的8位邀请码";
      return;
    }
    
    email.value = username.value;
    const success = await authStore.register(
      name.value, 
      username.value,
      email.value, 
      password.value,
      inviteCode.value
    );
    
    if (success) {
      // 记录注册事件（匿名记录）
      try {
        await eventApi.trackEvent(EVENT_TYPES.REGISTER, {
          username: username.value
        }, true); // 匿名记录，不需要用户ID
        AnalyticsService.trackEvent('用户注册', 'Register', '注册');
      } catch (e) {
        console.error("记录注册事件失败:", e);
      }
    }
    
    router.push("/login");
  } catch (error: any) {
    console.error("注册失败:", error);
    
    // 处理邀请码错误
    if (error.response && error.response.data && error.response.data.detail) {
      const errorMsg = error.response.data.detail;
      if (errorMsg.includes('邀请码')) {
        inviteCodeError.value = errorMsg;
      }
    }
  }
};
</script>

<style scoped>
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.auth-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  width: 100%;
  max-width: 400px;
}

h1 {
  margin-bottom: 1.5rem;
  text-align: center;
  color: #333;
}

.form-group {
  margin-bottom: 1rem;
}

.full-width-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.full-width-input:focus {
  outline: none;
  border-color: #4a9eff;
  box-shadow: 0 0 0 2px rgba(74, 158, 255, 0.2);
}

.input-error {
  border-color: #c62828;
}

.error-hint {
  color: #c62828;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.submit-button {
  width: 100%;
  padding: 0.75rem;
  background-color: #4a9eff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  margin-top: 1rem;
}

.submit-button:hover {
  background-color: #3a8eef;
}

.submit-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.password-mismatch {
  color: #c62828;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.auth-links {
  margin-top: 1.5rem;
  text-align: center;
}

.auth-links a {
  color: #4a9eff;
  text-decoration: none;
}

.auth-links a:hover {
  text-decoration: underline;
}
</style>
