<template>
  <div class="test-page">
    <v-container>
      <v-row>
        <v-col cols="12">
          <h1>热榜功能测试页面</h1>
          <p>这个页面用于测试新的热榜功能，包括排名变化、热度变化和新兴话题标识。</p>
        </v-col>
      </v-row>
      
      <v-row>
        <v-col cols="12" md="8">
          <HotRankingCard 
            @topic-click="handleTopicClick"
            @cognition-click="handleCognitionClick"
            @show-trend-dialog="showTrendDialog = true"
          />
        </v-col>
        
        <v-col cols="12" md="4">
          <HotRankingTest />
        </v-col>
      </v-row>
      
      <v-row>
        <v-col cols="12">
          <v-card>
            <v-card-title>功能说明</v-card-title>
            <v-card-text>
              <h3>新增功能：</h3>
              <ul>
                <li><strong>排名变化指示器</strong>：显示话题排名的上升、下降或新出现状态</li>
                <li><strong>热度变化百分比</strong>：显示相对于上一时间段的热度变化</li>
                <li><strong>新兴话题标识</strong>：用"NEW"标签标识新出现的话题</li>
                <li><strong>增强的提示信息</strong>：悬停显示详细的排名和热度变化信息</li>
              </ul>
              
              <h3>图标说明：</h3>
              <ul>
                <li>🔺 绿色箭头：排名上升</li>
                <li>🔻 红色箭头：排名下降</li>
                <li>⭐ 黄色星星：新出现的话题</li>
                <li>📈 绿色趋势：热度增长</li>
                <li>📉 红色趋势：热度下降</li>
              </ul>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
    
    <!-- 趋势分析弹窗 -->
    <TrendDialog 
      v-model="showTrendDialog"
      :time-filter="'day'"
      :selected-topics="[]"
      :search-scope="'all'"
      :source-filter="'all'"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import HotRankingCard from '@/components/cognition/HotRankingCard.vue'
import HotRankingTest from '@/components/cognition/HotRankingTest.vue'
import TrendDialog from '@/components/cognition/TrendDialog.vue'

const showTrendDialog = ref(false)

const handleTopicClick = (topic) => {
  console.log('点击话题:', topic)
  // 这里可以跳转到话题相关页面
}

const handleCognitionClick = (cognition) => {
  console.log('点击认知:', cognition)
  // 这里可以打开认知详情
}
</script>

<style scoped>
.test-page {
  padding: 20px 0;
}

h1 {
  color: #1e293b;
  margin-bottom: 16px;
}

h3 {
  color: #374151;
  margin: 16px 0 8px 0;
}

ul {
  margin-left: 20px;
}

li {
  margin-bottom: 4px;
  line-height: 1.5;
}
</style>
