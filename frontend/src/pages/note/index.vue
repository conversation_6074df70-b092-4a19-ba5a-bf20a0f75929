<template>
  <div class="note-page">
    <!-- 使用侧边栏组件 -->
    <CanvasSidebar 
      :nodes="treeNodes" 
      :selected-node-id="currentNote.id"
      @node-selected="handleNodeSelected"
      @update:nodes="updateTreeNodes"
      @toggle="handleSidebarToggle"
      @add-child="handleAddChild"
      @delete-node="handleDeleteNode"
      @rename-node="handleRenameNode"
      @move-node="handleMoveNode"
    />

    <!-- 主内容区域 -->
    <div class="note-content">
      <div class="note-editor-container">
        <!-- 编辑器标题栏 -->
        <div class="note-editor-header">
          <div class="note-title-section">
            <div class="title-and-meta">
              <h2 class="note-title">{{ currentNote.title || '未命名笔记' }}</h2>
              <!-- 创建者信息 -->
              <div v-if="showCreatorInfo" class="creator-info">
                <v-icon size="14" color="grey-darken-1" class="creator-icon">mdi-account-circle</v-icon>
                <span class="creator-text">创建者：{{ creatorDisplayName }}</span>
              </div>
              
              <!-- 原始拥有者信息 -->
              <div v-if="currentNote.copied_from_share && currentNote.original_owner_name" class="original-owner-info">
                <v-icon size="14" color="blue-darken-1" class="mr-1">mdi-share-variant</v-icon>
                <span class="text-body-2 text-blue-darken-1">
                  来源：{{ currentNote.original_owner_name }} 的分享文档
                </span>
              </div>
              
              <!-- 最后修改信息 -->
              <div v-if="currentNote.updated_by_name || (currentNote.updated_at && currentNote.updated_at !== currentNote.created_at)" class="last-modified-info">
                <v-icon size="14" color="grey-darken-1" class="mr-1">mdi-clock-edit</v-icon>
                <span class="text-body-2 text-grey-darken-1">
                  最后修改：{{ currentNote.updated_by_name || '未知用户' }} · {{ formatDate(currentNote.updated_at || currentNote.created_at) }}
                </span>
              </div>
            </div>
          </div>
          
          <div class="header-right">
            <div class="save-status" :class="saveStatusClass">
              <v-icon size="16" class="status-icon">{{ saveStatusIcon }}</v-icon>
              <span class="status-text">{{ saveStatusText }}</span>
            </div>
            
            <div class="note-actions">
              <!-- 分享按钮 -->
              <v-btn 
                icon 
                variant="text" 
                color="primary"
                @click="showShareDialog = true"
              >
                <v-icon>mdi-share-variant</v-icon>
                <v-tooltip activator="parent" location="bottom">分享与协作</v-tooltip>
              </v-btn>
              
              <v-btn 
                icon 
                variant="text" 
                color="primary"
                @click="manualSave"
              >
                <v-icon>mdi-content-save</v-icon>
                <v-tooltip activator="parent" location="bottom">手动保存</v-tooltip>
              </v-btn>
              <v-btn 
                icon 
                variant="text" 
                color="primary"
                @click="exportNote"
              >
                <v-icon>mdi-export</v-icon>
                <v-tooltip activator="parent" location="bottom">导出</v-tooltip>
              </v-btn>
            </div>
          </div>
        </div>

        <!-- 使用现有的编辑器组件，改为v-model绑定 -->
        <NoteMarkdownEditor
          ref="markdownEditorRef"
          :key="editorKey"
          :initial-content="currentNote.content"
          :editable="true"
          :ai-editing-enabled="false"
          @update:content="handleContentUpdate"
        />
      </div>
    </div>

    <!-- 删除确认弹框 -->
    <v-dialog v-model="showDeleteDialog" max-width="500px">
      <v-card>
        <v-card-title class="text-h5">
          {{ isOwner ? '删除笔记' : '退出协作' }}
        </v-card-title>
        <v-card-text>
          <template v-if="isOwner">
            <p class="text-body-1">确定要删除此笔记吗？此操作不可恢复。</p>
            <p class="text-body-2 text-red-darken-1 mt-2">注意：删除后将同时删除所有子笔记。</p>
          </template>
          <template v-else>
            <p class="text-body-1">确定要退出此协作笔记吗？</p>
            <p class="text-body-2 text-orange-darken-1 mt-2">
              退出后，您将无法再编辑此笔记，但笔记本身不会被删除。
            </p>
            <div v-if="nodeToDeleteDocument" class="mt-3 pa-3 bg-blue-lighten-5 rounded">
              <div class="text-body-2 text-blue-darken-1">
                <v-icon size="16" class="mr-1">mdi-information</v-icon>
                <strong>笔记信息：</strong>
              </div>
              <div class="text-body-2 mt-1">
                标题：{{ nodeToDeleteDocument.title }}
              </div>
              <div class="text-body-2">
                拥有者：{{ nodeToDeleteDocument.creator_name || nodeToDeleteDocument.creator_email || '未知用户' }}
              </div>
            </div>
          </template>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="grey-darken-1"
            variant="text"
            @click="showDeleteDialog = false"
          >
            取消
          </v-btn>
          <v-btn
            :color="isOwner ? 'error' : 'orange'"
            variant="text"
            @click="confirmDeleteNote"
          >
            {{ isOwner ? '删除' : '退出协作' }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-snackbar v-model="showSaveSnackbar" color="success" timeout="2000">
      笔记保存成功！
    </v-snackbar>
    
    <v-snackbar v-model="showAutoSaveHint" color="info" timeout="3000">
      系统已开启自动保存功能，无需手动保存
    </v-snackbar>

    <!-- 分享与协作对话框 -->
    <ShareDialog
      v-model="showShareDialog"
      :document-id="currentNote.id || ''"
      :document-title="currentNote.title || ''"
      :owner-id="documentOwnerId"
      :current-user-id="currentUserId"
      :is-folder="isCurrentNoteFolder"
      @share-updated="onShareUpdated"
    />
  </div>
</template>

<script>
import { ref, reactive, onMounted, onBeforeUnmount, watch, computed } from 'vue';
import CanvasSidebar from '@/components/canvas/CanvasSidebar.vue';
import NoteMarkdownEditor from '@/components/NoteMarkdownEditor.vue';
import { documentsApi } from '@/api/documents';
import ShareDialog from '@/components/ShareDialog.vue';
import { useAuthStore } from '@/stores/authStore';

export default {
  name: 'NotePage',
  components: {
    CanvasSidebar,
    NoteMarkdownEditor,
    ShareDialog
  },
  setup() {
    // 获取认证状态
    const authStore = useAuthStore();
    
    // 编辑器引用
    const markdownEditorRef = ref(null);
    
    // 树形结构数据
    const treeNodes = ref([]);
    
    // 当前笔记数据
    const currentNote = reactive({
      id: null,
      title: '',
      content: '# 新建笔记\n\n开始编写您的笔记内容...',
      lastSaved: null,
      owner_id: null,
      creator_id: null,
      creator_name: null,
      creator_email: null
    });
    
    // 原始内容（用于检测变更）
    const originalContent = ref('');
    
    // 是否有未保存的更改
    const hasChanges = ref(false);
    


    // 删除笔记相关
    const showDeleteDialog = ref(false);
    const nodeToDelete = ref(null);
    const nodeToDeleteDocument = ref(null);
    const isOwner = ref(false);
    
    const showSaveSnackbar = ref(false);
    const showAutoSaveHint = ref(false);
    
    const editorKey = ref(0); // 新增：用于强制刷新编辑器
    
    // 自动保存相关
    const autoSaveTimer = ref(null);
    const saveStatus = ref('saved'); // 'saved', 'saving', 'unsaved'
    const lastAutoSaveTime = ref(null);
    
    // 分享相关
    const showShareDialog = ref(false);
    
    // 格式化日期显示
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }

    // 计算属性，确保返回有效的字符串值
    const currentUserId = computed(() => {
      return String(authStore.user?.id || '');
    });
    
    const documentOwnerId = computed(() => {
      return currentNote.owner_id || currentNote.creator_id || currentUserId.value || '';
    });
    
    // 是否显示创建者信息（当前用户不是文档所有者时显示）
    const showCreatorInfo = computed(() => {
      return currentNote.id && 
             currentNote.creator_id && 
             currentNote.creator_id !== currentUserId.value &&
             (currentNote.creator_name || currentNote.creator_email)
    });
    
    // 创建者显示名称
    const creatorDisplayName = computed(() => {
      if (currentNote.creator_name) {
        return currentNote.creator_name
      } else if (currentNote.creator_email) {
        return currentNote.creator_email
      }
      return '未知用户'
    });
    
    // 检查当前笔记是否为文件夹（有子文档）
    const isCurrentNoteFolder = computed(() => {
      if (!currentNote.id) return false
      return treeNodes.value.some(node => node.parentId === currentNote.id)
    });
    
    // 处理内容更新
    const handleContentUpdate = (content) => {
      currentNote.content = content;
      hasChanges.value = true;
      saveStatus.value = 'unsaved';
    };
    
    // 处理节点选择
    const handleNodeSelected = async (node) => {
      console.log('选中节点:', node);
      
      // 如果有未保存的更改，提示用户
      if (hasChanges.value) {
        if (confirm('您有未保存的更改，是否继续？')) {
          await loadNote(node);
        }
      } else {
        await loadNote(node);
      }
    };
    
    // 加载笔记内容
    const loadNote = async (node) => {
      try {
        // 从API获取笔记内容
        const document = await documentsApi.getDocument(node.id);
        // 兼容后端返回的 _id 或 id 字段，确保 currentNote.id 一定有值
        currentNote.id = document._id || document.id;
        currentNote.title = document.title;
        currentNote.content = document.content || '';
        currentNote.owner_id = document.owner_id;
        currentNote.creator_id = document.creator_id;
        currentNote.creator_name = document.creator_name;
        currentNote.creator_email = document.creator_email;
        
        // 添加协作文档相关字段
        currentNote.copied_from_share = document.copied_from_share;
        currentNote.original_owner_name = document.original_owner_name;
        currentNote.updated_by = document.updated_by;
        currentNote.updated_by_name = document.updated_by_name;
        currentNote.updated_at = document.updated_at;
        currentNote.created_at = document.created_at;
        
        currentNote.lastSaved = new Date(document.updated_at);
        originalContent.value = currentNote.content;
        hasChanges.value = false;
        saveStatus.value = 'saved'; // 重置保存状态
        lastAutoSaveTime.value = new Date(document.updated_at); // 设置最后保存时间
        editorKey.value++; // 新增：每次切换笔记强制刷新编辑器
      } catch (error) {
        alert('加载笔记失败，请重试');
      }
    };
    
    // 获取文档树
    const fetchDocumentTree = async (preserveExpandState = false) => {
      try {
        // 保存当前的展开状态
        const currentExpandedState = {};
        if (preserveExpandState && treeNodes.value.length > 0) {
          treeNodes.value.forEach(node => {
            if (node.expanded) {
              currentExpandedState[node.id] = true;
            }
          });
        }
        
        const tree = await documentsApi.getDocumentTree();
        // 兼容后端返回的id或_id，确保树节点id正确
        treeNodes.value = tree.map(node => {
          const nodeId = node.id || node._id;
          return {
            id: nodeId,
            name: node.name || node.title,
            parentId: node.parent_id || null,
            expanded: preserveExpandState ? (currentExpandedState[nodeId] || false) : false
          };
        });
        
        // 如果不是保持状态模式，从本地存储恢复展开状态
        if (!preserveExpandState) {
          restoreExpandState();
        }
      } catch (error) {
        console.error('获取文档树失败:', error);
      }
    };
    
    // 保存展开状态到本地存储
    const saveExpandState = () => {
      const expandedNodes = {};
      treeNodes.value.forEach(node => {
        if (node.expanded) {
          expandedNodes[node.id] = true;
        }
      });
      localStorage.setItem('note-tree-expanded-state', JSON.stringify(expandedNodes));
    };
    
    // 从本地存储恢复展开状态
    const restoreExpandState = () => {
      try {
        const savedState = localStorage.getItem('note-tree-expanded-state');
        if (savedState) {
          const expandedNodes = JSON.parse(savedState);
          treeNodes.value = treeNodes.value.map(node => ({
            ...node,
            expanded: expandedNodes[node.id] || false
          }));
        }
      } catch (error) {
        console.warn('恢复展开状态失败:', error);
      }
    };
    
    // 设置节点展开状态
    const setNodeExpanded = (nodeId, expanded) => {
      treeNodes.value = treeNodes.value.map(node => {
        if (node.id === nodeId) {
          return { ...node, expanded };
        }
        return node;
      });
      saveExpandState(); // 立即保存状态
    };
    
    // 处理添加子节点
    const handleAddChild = async (data) => {
      try {
        // 调用API创建新文档
        const newDocument = await documentsApi.createDocument({
          title: data.name,
          parent_id: data.parentNode?.id, // 如果是根节点，这里会是undefined
          content: '' // 新建笔记内容为空
        });

        // 重新获取文档树，但保持当前展开状态
        await fetchDocumentTree(true);
        
        // 确保新建笔记的父节点展开（如果有父节点）
        if (data.parentNode) {
          setNodeExpanded(data.parentNode.id, true);
        }

        // 直接切换到新建的笔记
        currentNote.id = newDocument._id || newDocument.id;
        currentNote.title = newDocument.title;
        currentNote.content = newDocument.content || '';
        currentNote.owner_id = newDocument.owner_id;
        currentNote.creator_id = newDocument.creator_id;
        currentNote.creator_name = newDocument.creator_name;
        currentNote.creator_email = newDocument.creator_email;
        currentNote.lastSaved = new Date(newDocument.updated_at);
        originalContent.value = currentNote.content;
        hasChanges.value = false;
        saveStatus.value = 'saved';
        lastAutoSaveTime.value = new Date(newDocument.updated_at);
        editorKey.value++;
      } catch (error) {
        console.error('创建笔记失败:', error);
        alert('创建笔记失败，请重试');
      }
    };
    
    // 更新树节点
    const updateTreeNodes = (newNodes) => {
      treeNodes.value = newNodes;
      saveExpandState(); // 每次更新都保存展开状态
      // 不再立即同步到服务器，后端同步由 handleMoveNode 控制
    };
    
    // 处理侧边栏切换
    const handleSidebarToggle = (isOpen) => {
      console.log('侧边栏状态:', isOpen ? '打开' : '关闭');
    };
    
    // 保存笔记（通用方法）
    const saveNote = async (isAutoSave = false) => {
      if (!currentNote.id || !hasChanges.value) {
        return;
      }
      
      try {
        saveStatus.value = 'saving';
        const updatedDocument = await documentsApi.updateDocument(currentNote.id, {
          title: currentNote.title,
          content: currentNote.content
        });
        
        // 更新修改者信息
        currentNote.updated_by = updatedDocument.updated_by;
        currentNote.updated_by_name = updatedDocument.updated_by_name;
        currentNote.updated_at = updatedDocument.updated_at;
        
        currentNote.lastSaved = new Date();
        originalContent.value = currentNote.content;
        hasChanges.value = false;
        saveStatus.value = 'saved';
        
        if (isAutoSave) {
          lastAutoSaveTime.value = new Date();
        } else {
          showSaveSnackbar.value = true;
        }
      } catch (error) {
        saveStatus.value = 'unsaved';
        if (!isAutoSave) {
          alert('保存笔记失败，请重试');
        }
      }
    };
    
    // 手动保存
    const manualSave = async () => {
      await saveNote(false);
    };
    
    // 自动保存
    const autoSave = async () => {
      await saveNote(true);
    };
    
    // 导出笔记
    const exportNote = () => {
      // 创建一个Blob对象
      const blob = new Blob([currentNote.content], { type: 'text/markdown' });
      const url = URL.createObjectURL(blob);
      
      // 创建一个下载链接
      const a = document.createElement('a');
      a.href = url;
      a.download = `${currentNote.title || '未命名笔记'}.md`;
      document.body.appendChild(a);
      a.click();
      
      // 清理
      URL.revokeObjectURL(url);
      document.body.removeChild(a);
    };
    
    // 处理删除节点
    const handleDeleteNode = async (node) => {
      try {
        // 获取文档详情以检查权限
        const document = await documentsApi.getDocument(node.id);
        
        nodeToDelete.value = node;
        nodeToDeleteDocument.value = document;
        
        // 检查用户是否是文档拥有者
        isOwner.value = document.owner_id === currentUserId.value;
        
        showDeleteDialog.value = true;
      } catch (error) {
        console.error('获取文档信息失败:', error);
        alert('获取文档信息失败，请重试');
      }
    };
    
    // 确认删除笔记或退出协作
    const confirmDeleteNote = async () => {
      if (!nodeToDelete.value) return;
      
      try {
        if (isOwner.value) {
          // 文档拥有者：删除文档
          await documentsApi.deleteDocument(nodeToDelete.value.id, true);
        } else {
          // 协作者：退出协作
          await documentsApi.leaveCollaboration(nodeToDelete.value.id);
        }
        
        await fetchDocumentTree(true); // 保持当前展开状态
        
        // 如果删除/退出的是当前选中的笔记，清空编辑器
        if (currentNote.id === nodeToDelete.value.id) {
          currentNote.id = null;
          currentNote.title = '';
          currentNote.content = '# 新建笔记\n\n开始编写您的笔记内容...';
          currentNote.lastSaved = null;
          originalContent.value = currentNote.content;
          hasChanges.value = false;
        }
        
        // 关闭对话框
        showDeleteDialog.value = false;
        nodeToDelete.value = null;
        nodeToDeleteDocument.value = null;
        isOwner.value = false;
      } catch (error) {
        console.error(isOwner.value ? '删除笔记失败:' : '退出协作失败:', error);
        
        // 处理特定的错误信息
        if (error.response?.status === 403) {
          alert(isOwner.value ? '没有权限删除此文档' : '没有权限退出协作');
        } else {
          alert(isOwner.value ? '删除笔记失败，请重试' : '退出协作失败，请重试');
        }
      }
    };
    
    // 计算保存状态显示
    const saveStatusClass = computed(() => {
      switch (saveStatus.value) {
        case 'saving':
          return 'saving';
        case 'unsaved':
          return 'unsaved';
        default:
          return 'saved';
      }
    });
    
    const saveStatusIcon = computed(() => {
      switch (saveStatus.value) {
        case 'saving':
          return 'mdi-loading';
        case 'unsaved':
          return 'mdi-circle';
        default:
          return 'mdi-check-circle';
      }
    });
    
    const saveStatusText = computed(() => {
      switch (saveStatus.value) {
        case 'saving':
          return '保存中...';
        case 'unsaved':
          return '未保存';
        default:
          return lastAutoSaveTime.value ? `已保存 ${formatTime(lastAutoSaveTime.value)}` : '已保存';
      }
    });
    
    // 格式化时间显示
    const formatTime = (date) => {
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    };
    
    // 监听内容变化，设置hasChanges和保存状态
    watch(() => currentNote.content, (newContent) => {
      if (currentNote.id) {  // 只有在有选中笔记时才检查变更
        hasChanges.value = originalContent.value !== newContent;
        if (hasChanges.value) {
          saveStatus.value = 'unsaved';
        }
        console.log('内容已变更:', hasChanges.value);
      }
    });
    
    // 处理重命名节点
    const handleRenameNode = async (data) => {
      try {
        // 调用API更新文档标题
        await documentsApi.updateDocument(data.node.id, {
          title: data.newName
        });
        
        // 重新获取文档树，保持当前展开状态
        await fetchDocumentTree(true);
        
        // 如果重命名的是当前选中的笔记，更新标题
        if (currentNote.id === data.node.id) {
          currentNote.title = data.newName;
        }
      } catch (error) {
        console.error('重命名笔记失败:', error);
        alert('重命名笔记失败，请重试');
      }
    };
    
    // 拖拽移动节点后，同步到后端
    const handleMoveNode = async (data) => {
      try {
        const { draggedNodeId, targetNodeId, position, referenceNodeId } = data;
        
        // 调用后端 API 移动节点，支持精确位置控制
        await documentsApi.moveDocument(
          draggedNodeId, 
          targetNodeId, 
          position || "inside",
          referenceNodeId
        );
        
        // 重新获取文档树，保持当前展开状态
        await fetchDocumentTree(true);
      } catch (error) {
        console.error('移动节点失败:', error);
        alert('移动节点失败，请重试');
      }
    };
    
    // 启动自动保存定时器
    const startAutoSave = () => {
      if (autoSaveTimer.value) {
        clearInterval(autoSaveTimer.value);
      }
      autoSaveTimer.value = setInterval(() => {
        if (hasChanges.value && currentNote.id) {
          autoSave();
        }
      }, 10000); // 每10秒检查一次
    };
    
    // 停止自动保存定时器
    const stopAutoSave = () => {
      if (autoSaveTimer.value) {
        clearInterval(autoSaveTimer.value);
        autoSaveTimer.value = null;
      }
    };
    
    // 处理键盘快捷键
    const handleKeyDown = (event) => {
      // Ctrl+S 保存
      if (event.ctrlKey && event.key === 's') {
        event.preventDefault();
        showAutoSaveHint.value = true;
      }
    };
    
    // 页面离开前保存
    const handleBeforeUnload = async (event) => {
      if (hasChanges.value && currentNote.id) {
        // 同步保存
        await saveNote(false);
      }
    };
    
    // 处理分享对话框切换
    const toggleShareDialog = () => {
      showShareDialog.value = !showShareDialog.value;
    };
    
    // 处理分享更新
    const onShareUpdated = () => {
      console.log('分享设置已更新');
    };
    
    onMounted(async () => {
      // 初始化时加载文档树
      await fetchDocumentTree();
      
      // 如果有笔记，自动打开第一条笔记
      if (treeNodes.value.length > 0) {
        // 优先选择根节点（没有父节点的节点）
        const rootNodes = treeNodes.value.filter(node => !node.parentId);
        const firstNote = rootNodes.length > 0 ? rootNodes[0] : treeNodes.value[0];
        
        // 自动加载第一条笔记
        await loadNote(firstNote);
      } else {
        // 如果没有笔记，设置初始内容
        originalContent.value = currentNote.content;
        saveStatus.value = 'saved';
      }
      
      // 启动自动保存
      startAutoSave();
      
      // 添加键盘事件监听
      document.addEventListener('keydown', handleKeyDown);
      
      // 添加页面离开事件监听
      window.addEventListener('beforeunload', handleBeforeUnload);
    });
    
    onBeforeUnmount(() => {
      // 保存展开状态
      saveExpandState();
      
      // 停止自动保存
      stopAutoSave();
      
      // 移除事件监听
      document.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      
      // 页面卸载前最后保存一次
      if (hasChanges.value && currentNote.id) {
        saveNote(false);
      }
    });
    
    return {
      currentNote,
      treeNodes,
      // Ref
      markdownEditorRef,
      editorKey,
      showDeleteDialog,
      nodeToDelete,
      nodeToDeleteDocument,
      isOwner,
      showSaveSnackbar,
      showAutoSaveHint,
      showShareDialog,
      
      // 计算属性
      currentUserId,
      documentOwnerId,
      creatorDisplayName,
      showCreatorInfo,
      isCurrentNoteFolder,
      saveStatusClass,
      saveStatusIcon,
      saveStatusText,
      
      // 方法
      formatDate,
      handleSidebarToggle,
      updateTreeNodes,
      handleNodeSelected,
      handleContentUpdate,
      manualSave,
      exportNote,
      onShareUpdated,
      // 展开状态管理方法
      saveExpandState,
      restoreExpandState,
      setNodeExpanded,
      // 侧边栏操作方法
      handleAddChild,
      handleDeleteNode,
      confirmDeleteNote,
      handleRenameNode,
      handleMoveNode
    };
  }
}
</script>
  
<style scoped>
.note-page {
  display: flex;
  height: 100vh;
  width: 100%;
  background-color: #f8f9fa;
}

.note-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.note-editor-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.note-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #eaeaea;
}

.note-title-section {
  flex: 1;
  min-width: 0;
}

.header-right {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 16px;
  margin-left: 24px;
}

.title-and-meta {
  flex: 1;
}

.note-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #4A3670;
  margin: 0 0 4px 0;
}

.creator-info {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
}

.creator-icon {
  opacity: 0.7;
}

.creator-text {
  font-size: 0.75rem;
  color: #6c757d;
  font-weight: 400;
}

.original-owner-info {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  padding: 4px 8px;
  background-color: #e3f2fd;
  border-radius: 6px;
  border-left: 3px solid #2196f3;
}

.last-modified-info {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  padding: 4px 8px;
  background-color: #f5f5f5;
  border-radius: 6px;
  border-left: 3px solid #9e9e9e;
}

.save-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 14px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  background-color: #f8f9fa;
}

.save-status.saved {
  background-color: #e8f5e8;
  color: #2e7d32;
  border-color: #c8e6c9;
}

.save-status.saving {
  background-color: #fff3e0;
  color: #f57c00;
  border-color: #ffcc02;
}

.save-status.unsaved {
  background-color: #ffebee;
  color: #d32f2f;
  border-color: #ffcdd2;
}

.save-status .status-icon {
  opacity: 0.8;
}

.save-status.saving .status-icon {
  animation: rotate 1s linear infinite;
}

.status-text {
  font-size: 0.8rem;
  white-space: nowrap;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.note-actions {
  display: flex;
  gap: 4px;
}

.note-markdown-editor {
  flex: 1;
  overflow-y: auto;
  padding: 0 48px;
}

/* 编辑器样式覆盖 */
:deep(.ProseMirror) {
  padding: 24px 24px;
  outline: none;
  overflow-y: auto;
  max-height: none;
  min-height: calc(100vh - 200px);
}

:deep(.ProseMirror:focus) {
  outline: none;
}

:deep(.ProseMirror h1) {
  color: #4A3670;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

:deep(.ProseMirror h2) {
  color: #4A3670;
  margin-top: 1.2rem;
  margin-bottom: 0.8rem;
}

:deep(.ProseMirror h3),
:deep(.ProseMirror h4),
:deep(.ProseMirror h5),
:deep(.ProseMirror h6) {
  color: #4A3670;
}

:deep(.ProseMirror p) {
  color: #333333;
  line-height: 1.6;
  margin-bottom: 1rem;
}

:deep(.ProseMirror ul),
:deep(.ProseMirror ol) {
  color: #333333;
  margin-bottom: 1rem;
}

:deep(.ProseMirror blockquote) {
  border-left: 4px solid #4A3670;
  padding-left: 1rem;
  color: #555555;
  font-style: italic;
}

:deep(.ProseMirror pre) {
  background-color: #f5f5f5;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
}

:deep(.ProseMirror code) {
  background-color: #f5f5f5;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: 'SF Mono', Monaco, Menlo, Consolas, monospace;
}

:deep(.ProseMirror table) {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 1rem;
}

:deep(.ProseMirror th),
:deep(.ProseMirror td) {
  border: 1px solid #ddd;
  padding: 8px;
}

:deep(.ProseMirror th) {
  background-color: #f5f5f5;
  font-weight: 600;
}

.sidebar-header {
  padding: 18px 18px 0 18px;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.back-home-btn {
  font-size: 15px;
  font-weight: 500;
  color: #654C8C;
  background: #f6f3fd;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(101, 76, 140, 0.06);
  padding: 6px 18px;
  margin-bottom: 8px;
  transition: background 0.2s, box-shadow 0.2s;
}
.back-home-btn:hover {
  background: #e6e0f7;
  color: #4a3670;
}
</style>