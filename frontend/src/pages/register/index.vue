<template>
  <div class="register-container">
    <div class="register-form">
      <h2>用户注册</h2>
      
      <div class="form-group">
        <label for="username">用户名</label>
        <input 
          type="text" 
          id="username" 
          v-model="formData.username" 
          placeholder="请输入3-50个字符的用户名"
          :class="{ 'error': errors.username }"
        />
        <div class="error-message" v-if="errors.username">{{ errors.username }}</div>
      </div>
      
      <div class="form-group">
        <label for="email">邮箱</label>
        <input 
          type="email" 
          id="email" 
          v-model="formData.email" 
          placeholder="请输入有效的邮箱地址"
          :class="{ 'error': errors.email }"
        />
        <div class="error-message" v-if="errors.email">{{ errors.email }}</div>
      </div>
      
      <div class="form-group">
        <label for="password">密码</label>
        <input 
          type="password" 
          id="password" 
          v-model="formData.password" 
          placeholder="请输入至少6个字符的密码"
          :class="{ 'error': errors.password }"
        />
        <div class="error-message" v-if="errors.password">{{ errors.password }}</div>
      </div>
      
      <div class="form-group">
        <label for="confirmPassword">确认密码</label>
        <input 
          type="password" 
          id="confirmPassword" 
          v-model="formData.confirmPassword" 
          placeholder="请再次输入密码"
          :class="{ 'error': errors.confirmPassword }"
        />
        <div class="error-message" v-if="errors.confirmPassword">{{ errors.confirmPassword }}</div>
      </div>
      
      <div class="form-group">
        <label for="inviteCode">邀请码</label>
        <input 
          type="text" 
          id="inviteCode" 
          v-model="formData.invite_code" 
          placeholder="请输入8位邀请码"
          :class="{ 'error': errors.invite_code }"
        />
        <div class="error-message" v-if="errors.invite_code">{{ errors.invite_code }}</div>
      </div>
      
      <button 
        class="register-btn" 
        @click="handleRegister" 
        :disabled="isLoading"
      >
        <i class="fas fa-spinner fa-spin" v-if="isLoading"></i>
        {{ isLoading ? '注册中...' : '注册' }}
      </button>
      
      <div class="login-link">
        已有账号？<router-link to="/login">立即登录</router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { authService } from '@/api/authService';

const router = useRouter();

// 表单数据
const formData = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  invite_code: ''
});

// 错误信息
const errors = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  invite_code: ''
});

const isLoading = ref(false);

// 表单验证
const validateForm = () => {
  let isValid = true;
  
  // 重置错误信息
  Object.keys(errors).forEach(key => {
    errors[key as keyof typeof errors] = '';
  });
  
  // 验证用户名
  if (!formData.username) {
    errors.username = '请输入用户名';
    isValid = false;
  } else if (formData.username.length < 3 || formData.username.length > 50) {
    errors.username = '用户名长度应在3-50个字符之间';
    isValid = false;
  }
  
  // 验证邮箱
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!formData.email) {
    errors.email = '请输入邮箱地址';
    isValid = false;
  } else if (!emailRegex.test(formData.email)) {
    errors.email = '请输入有效的邮箱地址';
    isValid = false;
  }
  
  // 验证密码
  if (!formData.password) {
    errors.password = '请输入密码';
    isValid = false;
  } else if (formData.password.length < 6) {
    errors.password = '密码长度不能少于6个字符';
    isValid = false;
  }
  
  // 验证确认密码
  if (formData.password !== formData.confirmPassword) {
    errors.confirmPassword = '两次输入的密码不一致';
    isValid = false;
  }
  
  // 验证邀请码
  if (!formData.invite_code) {
    errors.invite_code = '请输入邀请码';
    isValid = false;
  } else if (formData.invite_code.length !== 8) {
    errors.invite_code = '邀请码必须是8位字符';
    isValid = false;
  }
  
  return isValid;
};

// 处理注册
const handleRegister = async () => {
  if (!validateForm()) {
    return;
  }
  
  isLoading.value = true;
  
  try {
    const userData = {
      username: formData.username,
      email: formData.email,
      password: formData.password,
      invite_code: formData.invite_code
    };
    
    await authService.register(userData);
    
    // 注册成功，跳转到登录页
    alert('注册成功，请登录');
    router.push('/login');
  } catch (error: any) {
    console.error('注册失败:', error);
    
    // 处理后端返回的错误信息
    if (error.response && error.response.data) {
      const errorMsg = error.response.data.detail;
      
      if (errorMsg.includes('用户名已存在')) {
        errors.username = errorMsg;
      } else if (errorMsg.includes('邮箱已被注册')) {
        errors.email = errorMsg;
      } else if (errorMsg.includes('邀请码')) {
        errors.invite_code = errorMsg;
      } else {
        alert(`注册失败: ${errorMsg}`);
      }
    } else {
      alert('注册失败，请稍后再试');
    }
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background-color: #f5f5f5;
}

.register-form {
  width: 100%;
  max-width: 450px;
  padding: 30px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.register-form h2 {
  margin-bottom: 20px;
  text-align: center;
  color: #333;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-group input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-group input:focus {
  outline: none;
  border-color: #3699ff;
  box-shadow: 0 0 0 2px rgba(54, 153, 255, 0.2);
}

.form-group input.error {
  border-color: #f56c6c;
}

.error-message {
  margin-top: 5px;
  color: #f56c6c;
  font-size: 12px;
}

.register-btn {
  width: 100%;
  padding: 12px;
  background-color: #3699ff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}

.register-btn:hover {
  background-color: #2984e0;
}

.register-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.register-btn i {
  margin-right: 8px;
}

.login-link {
  margin-top: 16px;
  text-align: center;
  font-size: 14px;
}

.login-link a {
  color: #3699ff;
  text-decoration: none;
}

.login-link a:hover {
  text-decoration: underline;
}
</style> 