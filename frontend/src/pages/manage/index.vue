<template>
  <div class="manage-container">
    <AdminSidebar @menu-change="handleMenuChange" />
    
    <div class="content-container">
      <div class="content-header">
        <h1>{{ currentMenuTitle }}</h1>
      </div>
      
      <div class="content-body">
        <!-- 根据当前选中的菜单显示不同的内容 -->
        <InviteCodeManager v-if="currentMenu === 'invite-codes'" />
        <TokenUsageManager v-else-if="currentMenu === 'token-usage'" />
        <TokenSessionsManager v-else-if="currentMenu === 'token-sessions'" />
        <UserTokenManager v-else-if="currentMenu === 'user-token-management'" />
        <div v-else-if="currentMenu === 'users'" class="placeholder">
          <h3>用户管理</h3>
          <p>用户管理功能待开发</p>
        </div>
        <div v-else-if="currentMenu === 'settings'" class="settings-container">
          <v-card class="mb-4">
            <v-card-title>环境配置</v-card-title>
            <v-card-text>
              <v-switch
                v-model="isProduct"
                :label="isProduct ? '生产环境' : '开发环境'"
                color="primary"
                @change="confirmEnvironmentChange"
                :loading="environmentStore.loading"
              ></v-switch>
              <v-alert
                v-if="environmentStore.error"
                type="error"
                class="mt-2"
              >
                {{ environmentStore.error }}
              </v-alert>
            </v-card-text>
          </v-card>
        </div>
      </div>
    </div>

    <!-- 确认对话框 -->
    <v-dialog v-model="showConfirmDialog" max-width="400">
      <v-card>
        <v-card-title>确认切换环境</v-card-title>
        <v-card-text>
          您确定要切换到{{ isProduct ? '生产' : '开发' }}环境吗？此操作可能会影响系统的行为。
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="grey"
            variant="text"
            @click="cancelEnvironmentChange"
          >
            取消
          </v-btn>
          <v-btn
            color="primary"
            variant="text"
            @click="handleEnvironmentChange"
            :loading="environmentStore.loading"
          >
            确认
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import AdminSidebar from '@/components/AdminSidebar.vue';
import InviteCodeManager from '@/components/InviteCodeManager.vue';
import TokenUsageManager from '@/components/TokenUsageManager.vue';
import TokenSessionsManager from '@/components/TokenSessionsManager.vue';
import UserTokenManager from '@/components/UserTokenManager.vue';
import { useEnvironmentStore } from '@/stores/environmentStore';

// 当前选中的菜单
const currentMenu = ref('invite-codes');

// 环境配置
const environmentStore = useEnvironmentStore();
const isProduct = computed({
  get: () => environmentStore.environment === 'product',
  set: (value) => environmentStore.environment = value ? 'product' : 'dev'
});

// 确认对话框
const showConfirmDialog = ref(false);
const pendingEnvironmentChange = ref(false);

// 确认环境切换
function confirmEnvironmentChange() {
  showConfirmDialog.value = true;
  pendingEnvironmentChange.value = true;
}

// 取消环境切换
function cancelEnvironmentChange() {
  showConfirmDialog.value = false;
  pendingEnvironmentChange.value = false;
  // 恢复开关状态
  isProduct.value = !isProduct.value;
}

// 处理环境切换
async function handleEnvironmentChange() {
  try {
    await environmentStore.updateEnvironment(isProduct.value ? 'product' : 'dev');
    showConfirmDialog.value = false;
    pendingEnvironmentChange.value = false;
  } catch (error) {
    console.error('环境切换失败:', error);
    // 恢复开关状态
    isProduct.value = !isProduct.value;
  }
}

// 菜单标题映射
const menuTitles = {
  'users': '用户管理',
  'invite-codes': '邀请码管理',
  'token-usage': 'Token用量统计',
  'token-sessions': '会话Token详情',
  'user-token-management': '用户Token管理',
  'settings': '系统设置'
};

// 计算当前菜单标题
const currentMenuTitle = computed(() => {
  return menuTitles[currentMenu.value as keyof typeof menuTitles] || '管理控制台';
});

// 处理菜单切换
const handleMenuChange = (menuKey: string) => {
  currentMenu.value = menuKey;
};

// 监听菜单切换，切到settings时自动刷新环境配置
watch(currentMenu, (newVal) => {
  if (newVal === 'settings') {
    environmentStore.fetchEnvironment();
  }
});

// 初始化
onMounted(async () => {
  if (currentMenu.value === 'settings') {
    await environmentStore.fetchEnvironment();
  }
});
</script>

<style scoped>
.manage-container {
  display: flex;
  height: 100vh;
  width: 100%;
}

.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #f5f5f5;
}

.content-header {
  padding: 20px;
  background-color: #ffffff;
  border-bottom: 1px solid #e5e5e5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.content-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.content-body {
  flex: 1;
  overflow: auto;
  padding: 20px;
}

.placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.placeholder h3 {
  font-size: 24px;
  color: #333;
  margin-bottom: 16px;
}

.placeholder p {
  color: #6c757d;
  font-size: 16px;
}

.settings-container {
  max-width: 800px;
  margin: 0 auto;
}
</style>