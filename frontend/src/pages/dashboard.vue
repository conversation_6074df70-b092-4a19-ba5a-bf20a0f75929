<template>
  <div class="dashboard">
    <div class="card">
      <h2 class="card-title">数据统计看板</h2>
      
      <div class="filters">
        <div class="time-range">
          <label>时间范围：</label>
          <select v-model="timeRange" @change="fetchData">
            <option value="hourly">按小时 (过去24小时)</option>
            <option value="daily">按天 (过去7天)</option>
          </select>
        </div>
        
        <div class="event-filters">
          <label>事件类型：</label>
          <div class="event-checkboxes">
            <label v-for="(name, type) in eventTypeLabels" :key="type" class="event-checkbox">
              <input type="checkbox" v-model="selectedEventTypes" :value="type" @change="updateChart"/>
              {{ name }}
            </label>
          </div>
        </div>
      </div>
      
      <div class="chart-container">
        <div 
          ref="chartRef" 
          style="width: 100%; height: 400px; position: relative;"
        ></div>
      </div>

      <div v-if="loading" class="loading-overlay">
        加载中...
      </div>
      
      <div v-if="!loading && !hasData" class="no-data-overlay">
        暂无数据
      </div>

      <div v-if="debugMode" class="debug-info">
        <h4>原始数据:</h4>
        <pre>{{ JSON.stringify(statsData, null, 2) }}</pre>
        <h4>图表状态:</h4>
        <p>图表引用: {{ chartRef ? '已获取' : '未获取' }}</p>
        <p>图表实例: {{ chart ? '已初始化' : '未初始化' }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, nextTick } from 'vue';
import { onBeforeUnmount } from 'vue';
import * as echarts from 'echarts/core';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  ToolboxComponent,
} from 'echarts/components';
import { LineChart } from 'echarts/charts';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import { eventApi, EVENT_TYPES } from '@/api/event';
import type { PeriodStats } from '@/api/event';

// 确保所有必要的ECharts组件已注册
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  ToolboxComponent,
  LineChart,
  CanvasRenderer,
  UniversalTransition
]);

// 调试模式开关
const debugMode = ref(true); // 设置为true以显示调试信息

// 中文事件类型标签
const eventTypeLabels = {
  [EVENT_TYPES.LOGIN]: '用户登录',
  [EVENT_TYPES.REGISTER]: '用户注册',
  [EVENT_TYPES.CHAT_SUBMIT]: '提交对话',
  [EVENT_TYPES.CHAT_CREATE]: '创建对话',
  [EVENT_TYPES.CHAT_SHARE]: '分享对话',
  [EVENT_TYPES.CHAT_VIEW_SHARED]: '查看分享',
  [EVENT_TYPES.BROWSE_PUBLIC]: '浏览广场'
};

const chartRef = ref<HTMLElement | null>(null);
const chart = ref<echarts.ECharts | null>(null);
const timeRange = ref<'hourly' | 'daily'>('hourly');
const loading = ref(true);
const statsData = ref<PeriodStats>({});
const selectedEventTypes = ref(Object.keys(eventTypeLabels)); // 默认全选
const hasData = computed(() => {
  // 检查是否有任何数据点
  if (!statsData.value || typeof statsData.value !== 'object') return false;
  
  // 检查stats对象是否有数据
  return Object.keys(statsData.value).length > 0;
});

// 监听时间范围变化
watch(timeRange, () => {
  fetchData();
});

// 格式化日期为后端所需格式（YYYYMMDD）
const formatDateForBackend = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}${month}${day}`;
};

// 获取数据
const fetchData = async () => {
  loading.value = true;
  
  try {
    // 计算时间范围
    const endDate = new Date();
    let startDate = new Date();
    
    if (timeRange.value === 'hourly') {
      // 过去24小时
      startDate.setHours(startDate.getHours() - 24);
    } else {
      // 过去7天
      startDate.setDate(startDate.getDate() - 7);
    }
    
    // 为所有时间格式只使用YYYYMMDD格式
    const startDateStr = formatDateForBackend(startDate);
    const endDateStr = formatDateForBackend(endDate);
    
    // 根据选择的时间范围确定时间粒度
    const by = timeRange.value === 'hourly' ? 'hour' : 'day';
    
    // 调用API获取数据
    const response = await eventApi.getPeriodStats(startDateStr, endDateStr, by);
    console.log('API返回数据:', response);
    
    // 直接使用返回的stats对象
    if (response && response.stats) {
      statsData.value = response.stats;
      console.log('更新statsData:', statsData.value);
    } else {
      console.error('API返回数据格式不正确:', response);
      statsData.value = {};
    }
    
    // 延迟更新图表，确保DOM已完全更新
    await nextTick();
    
    // 确保数据加载完后再次尝试初始化图表
    ensureChartInitialized();
    
    // 然后更新图表
    updateChart();
  } catch (error) {
    console.error('获取统计数据失败:', error);
    statsData.value = {};
  } finally {
    loading.value = false;
  }
};

// 初始化图表
const initChart = () => {
  console.log('开始初始化图表');
  console.log('chartRef:', chartRef.value);
  
  // 确保DOM元素存在
  if (!chartRef.value) {
    console.error('图表DOM元素未找到，无法初始化图表');
    return;
  }
  
  // 如果已经有图表实例，先销毁它
  if (chart.value) {
    chart.value.dispose();
  }
  
  // 创建新的图表实例
  try {
    chart.value = echarts.init(chartRef.value);
    console.log('图表初始化完成:', chart.value);
    
    // 添加窗口大小变化的监听器
    window.addEventListener('resize', () => {
      chart.value?.resize();
    });
  } catch (error) {
    console.error('图表初始化失败:', error);
    chart.value = null;
  }
};

// 确保图表已初始化
const ensureChartInitialized = async () => {
  console.log('确保图表初始化');
  
  // 等待下一个DOM更新周期
  await nextTick();
  
  if (!chart.value) {
    console.log('图表未初始化，尝试初始化');
    initChart();
  }
  
  if (!chart.value) {
    console.error('图表初始化失败');
    return false;
  }
  
  return true;
};

// 更新图表数据
const updateChart = async () => {
  console.log('开始更新图表');
  
  // 确保图表已初始化
  if (!(await ensureChartInitialized())) {
    console.error('图表未初始化，无法更新数据');
    return;
  }
  
  // 确保有数据
  if (!hasData.value) {
    console.log('没有数据可显示');
    return;
  }
  
  // 获取时间点列表（排序）
  const timePoints = Object.keys(statsData.value).sort();
  
  // 准备图表数据并计算最大值
  let maxValue = 0;
  const series = selectedEventTypes.value.map(eventType => {
    const data = timePoints.map(timePoint => {
      const value = statsData.value[timePoint]?.[eventType] || 0;
      maxValue = Math.max(maxValue, value);
      return value;
    });
    
    return {
      name: eventTypeLabels[eventType] || eventType,
      type: 'line',
      smooth: true,
      data: data,
      // 添加标签配置
      label: {
        show: true,
        position: 'top',
        fontSize: 12,
        color: '#666',
        formatter: (params: any) => {
          return params.value > 0 ? params.value : '';
        }
      },
      // 添加symbol配置，使数据点更明显
      symbol: 'circle',
      symbolSize: 8,
      // 添加emphasis配置，鼠标悬停效果
      emphasis: {
        scale: true,
        focus: 'series'
      }
    };
  });
  
  // 计算Y轴最大值（留出一定空间）
  const yAxisMax = Math.ceil(maxValue * 1.2); // 最大值的1.2倍，向上取整
  
  // 设置图表选项
  const option = {
    title: {
      text: timeRange.value === 'hourly' ? '24小时事件统计' : '7天事件统计',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: selectedEventTypes.value.map(type => eventTypeLabels[type] || type),
      bottom: 0,
      padding: [5, 5],
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '60px',
      top: '60px', // 增加顶部空间以显示数据标签
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: timePoints.map(formatTimePoint),
      axisLabel: {
        formatter: function(value) {
          return value;
        },
        interval: timePoints.length > 12 ? 'auto' : 0,
        rotate: timePoints.length > 12 ? 45 : 0 // 当数据点较多时旋转标签
      }
    },
    yAxis: {
      type: 'value',
      name: '事件数量',
      nameLocation: 'end',
      nameGap: 15,
      nameTextStyle: {
        padding: [0, 0, 0, 5]
      },
      min: 0,
      max: yAxisMax,
      minInterval: 1, // 最小间隔为1，确保y轴是整数
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed'
        }
      }
    },
    series: series
  };
  
  // 应用图表配置
  chart.value.setOption(option);
  console.log('图表更新完成');
};

// 处理每个时间点的格式，转换为东八区并格式化显示
const formatTimePoint = (timePoint: string) => {
  try {
    if (timeRange.value === 'hourly') {
      // 处理小时格式 (例如 "2025031701")
      if (timePoint.length >= 10) {
        const year = parseInt(timePoint.slice(0, 4));
        const month = parseInt(timePoint.slice(4, 6)) - 1; // JavaScript月份从0开始
        const day = parseInt(timePoint.slice(6, 8));
        const hour = parseInt(timePoint.slice(8, 10));
        
        // 创建UTC日期
        const date = new Date(Date.UTC(year, month, day, hour));
        
        // 转换为东八区（+8小时）
        const localDate = new Date(date.getTime() + 8 * 60 * 60 * 1000);
        
        // 格式化为 "MM-DD HH:00"
        const formattedMonth = String(localDate.getMonth() + 1).padStart(2, '0');
        const formattedDay = String(localDate.getDate()).padStart(2, '0');
        const formattedHour = String(localDate.getHours()).padStart(2, '0');
        
        return `${formattedMonth}-${formattedDay} ${formattedHour}:00`;
      }
    } else {
      // 处理日期格式 (例如 "20250317")
      if (timePoint.length >= 8) {
        const year = parseInt(timePoint.slice(0, 4));
        const month = parseInt(timePoint.slice(4, 6));
        const day = parseInt(timePoint.slice(6, 8));
        
        return `${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      }
    }
    
    // 如果无法解析，返回原始值
    return timePoint;
  } catch (e) {
    console.error('格式化时间点失败:', e, timePoint);
    return timePoint;
  }
};

// 在组件销毁时清理图表实例
onBeforeUnmount(() => {
  if (chart.value) {
    chart.value.dispose();
    chart.value = null;
  }
  
  // 移除可能的事件监听器
  window.removeEventListener('resize', () => {
    chart.value?.resize();
  });
});

// 组件挂载时的处理
onMounted(async () => {
  console.log('组件挂载开始');
  
  try {
    // 等待DOM更新
    await nextTick();
    
    // 初始化图表
    initChart();
    
    // 获取数据
    await fetchData();
    
    console.log('组件挂载完成');
  } catch (error) {
    console.error('组件挂载过程出错:', error);
  }
});
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.card-title {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 24px;
  color: #333;
}

.filters {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.time-range {
  display: flex;
  align-items: center;
}

.time-range label {
  margin-right: 10px;
  font-weight: 500;
  color: #555;
}

.time-range select {
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.event-filters {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.event-filters label {
  font-weight: 500;
  color: #555;
}

.event-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.event-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.event-checkbox input {
  margin-right: 5px;
}

.chart-container {
  margin-top: 20px;
  width: 100%;
  height: 400px;
  position: relative;
  border: 1px solid #eee;
  border-radius: 4px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  color: #666;
}

.no-data-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  color: #666;
}

.debug-info {
  margin: 20px 0;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  overflow: auto;
  max-height: 300px;
  font-family: monospace;
  font-size: 12px;
}

.debug-info h4 {
  margin: 5px 0;
  font-size: 14px;
}

.debug-info p {
  margin: 5px 0;
}
</style>