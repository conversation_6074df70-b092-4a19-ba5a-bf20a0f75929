<template>
  <div class="folder-share-page">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-content">
        <v-progress-circular
          indeterminate
          color="primary"
          size="56"
          width="4"
        />
        <p class="loading-text">正在加载分享文件夹...</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="error-content">
        <div class="error-icon-wrapper">
          <v-icon size="64" color="error" class="error-icon">
            mdi-alert-circle-outline
          </v-icon>
        </div>
        <h1 class="error-title">访问失败</h1>
        <p class="error-message">{{ error }}</p>
        
        <div class="error-actions">
          <v-btn
            color="primary"
            variant="elevated"
            size="large"
            @click="retry"
            class="action-btn"
          >
            <v-icon start>mdi-refresh</v-icon>
            重新尝试
          </v-btn>
          
          <v-btn
            color="grey-darken-1"
            variant="outlined"
            size="large"
            @click="goHome"
            class="action-btn"
          >
            <v-icon start>mdi-home</v-icon>
            返回首页
          </v-btn>
        </div>
      </div>
    </div>

    <!-- 文件夹内容 -->
    <div v-else-if="folderInfo" class="folder-content">
      <!-- 顶部导航栏 -->
      <div class="top-nav">
        <div class="nav-left">
          <v-btn
            variant="outlined"
            color="primary"
            @click="goHome"
            class="nav-btn"
          >
            <v-icon start>mdi-notebook</v-icon>
            我的笔记
          </v-btn>
        </div>
        
        <div class="nav-center">
          <div class="nav-brand">
            <v-icon size="28" color="primary" class="mr-2">mdi-brain</v-icon>
            <span class="brand-text">Deep Cognition</span>
          </div>
        </div>
        
        <div class="nav-right">
          <v-chip
            size="small"
            color="success"
            variant="flat"
            class="share-badge"
          >
            <v-icon start size="14">mdi-folder-multiple</v-icon>
            共享文件夹
          </v-chip>
        </div>
      </div>

      <!-- 文件夹标题区域 -->
      <div class="folder-header">
        <div class="folder-title-section">
          <h1 class="folder-title">
            <v-icon size="32" color="primary" class="mr-3">mdi-folder-open</v-icon>
            {{ folderInfo.title }}
          </h1>
          <div class="folder-subtitle">
            <v-icon size="16" color="grey-darken-1" class="mr-1">mdi-account-circle</v-icon>
            <span>来自 {{ folderInfo.creator_name || folderInfo.creator_email || '未知用户' }} 的分享文件夹</span>
          </div>
          
          <!-- 权限和操作区域 -->
          <div class="folder-actions">
            <v-chip
              :color="getPermissionColor(sharePermissions)"
              variant="flat"
              size="small"
              class="permission-chip"
            >
              <v-icon start size="14">{{ getPermissionIcon(sharePermissions) }}</v-icon>
              {{ getPermissionText(sharePermissions) }}
            </v-chip>
            
            <!-- 加入笔记按钮 - 仅对可编辑文件夹且已登录用户显示 -->
            <v-btn
              v-if="canEdit && isAuthenticated && !isFolderInUserNotes"
              variant="elevated"
              color="primary"
              size="small"
              @click="showAddToNotesDialog = true"
              class="add-to-notes-btn"
            >
              <v-icon start size="16">mdi-bookmark-plus</v-icon>
              加入我的笔记
            </v-btn>
            
            <!-- 已在笔记中的提示 -->
            <v-btn
              v-else-if="canEdit && isAuthenticated && isFolderInUserNotes"
              variant="outlined"
              color="success"
              size="small"
              disabled
              class="add-to-notes-btn"
            >
              <v-icon start size="16">mdi-check</v-icon>
              已在笔记中
            </v-btn>
          </div>
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="main-content">
        <!-- 左侧文档树 -->
        <div class="sidebar" :class="{ 'collapsed': !sidebarOpen }">
          <div class="sidebar-header">
            <h3 class="sidebar-title">文档导航</h3>
            <v-btn
              icon
              variant="text"
              size="small"
              @click="toggleSidebar"
              class="sidebar-toggle"
            >
              <v-icon>{{ sidebarOpen ? 'mdi-chevron-left' : 'mdi-chevron-right' }}</v-icon>
            </v-btn>
          </div>
          
          <div v-if="sidebarOpen" class="sidebar-content">
            <div class="document-tree">
              <div v-if="documentTree.length === 0" class="empty-state">
                <v-icon size="48" color="grey-lighten-1" class="mb-2">mdi-folder-outline</v-icon>
                <p class="empty-text">暂无文档</p>
              </div>
              <folder-tree-node
                v-for="node in documentTree"
                :key="node.id"
                :node="node"
                :selected-id="selectedDocumentId"
                @node-selected="handleNodeSelected"
              />
            </div>
          </div>
        </div>

        <!-- 右侧文档内容 -->
        <div class="document-content" :class="{ 'full-width': !sidebarOpen }">
          <!-- 默认欢迎页面 -->
          <div v-if="!selectedDocument" class="welcome-page">
            <div class="welcome-content">
              <div class="welcome-icon">
                <v-icon size="96" color="primary" class="mb-4">mdi-folder-open-outline</v-icon>
              </div>
              <h2 class="welcome-title">欢迎访问分享文件夹</h2>
              <p class="welcome-desc">请从左侧选择一个文档开始阅读</p>
              
              <div class="folder-stats">
                <div class="stat-item">
                  <v-icon color="primary" class="mr-2">mdi-file-document</v-icon>
                  <span>{{ documentCount }} 个文档</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 文档显示区域 -->
          <div v-else class="document-display">
            <!-- 文档标题栏 -->
            <div class="document-header">
              <div class="document-title-section">
                <h2 class="document-title">{{ selectedDocument.title }}</h2>
                <div class="document-meta">
                  <span class="meta-item">
                    <v-icon size="14" class="mr-1">mdi-clock-outline</v-icon>
                    {{ formatDate(selectedDocument.updated_at || selectedDocument.created_at) }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 文档内容 -->
            <div class="document-body">
              <NoteMarkdownEditor
                :key="selectedDocument.id"
                :initial-content="selectedDocument.content || ''"
                :editable="false"
                :ai-editing-enabled="false"
                class="readonly-editor"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加入我的笔记对话框 -->
    <v-dialog v-model="showAddToNotesDialog" max-width="500" persistent="false">
      <v-card class="add-to-notes-dialog">
        <v-card-title class="dialog-title">
          <v-icon start color="primary">mdi-bookmark-plus</v-icon>
          加入我的笔记
        </v-card-title>
        
        <v-card-text class="dialog-content">
          <p class="dialog-description">
            将此文件夹及其所有子文档保存到您的笔记中，您将可以在个人笔记页面继续编辑这些文档。
          </p>
          
          <!-- 模式选择 -->
          <div class="mode-selection">
            <h4 class="mode-title">请选择保存模式：</h4>
            <v-radio-group 
              v-model="selectedMode" 
              class="mode-radio-group"
              hide-details
            >
              <v-radio 
                value="collaborative" 
                class="mode-radio"
              >
                <template v-slot:label>
                  <div class="mode-option">
                    <div class="mode-header">
                      <v-icon color="success" class="mr-2">mdi-account-multiple</v-icon>
                      <span class="mode-name">协作模式</span>
                      <v-chip color="success" size="x-small" variant="flat" class="ml-2">推荐</v-chip>
                    </div>
                    <div class="mode-description">
                      与原作者实时协作，您的修改会同步到原文件夹，原作者的修改您也能看到
                    </div>
                  </div>
                </template>
              </v-radio>
              
              <v-radio 
                value="copy" 
                class="mode-radio"
              >
                <template v-slot:label>
                  <div class="mode-option">
                    <div class="mode-header">
                      <v-icon color="info" class="mr-2">mdi-content-copy</v-icon>
                      <span class="mode-name">复制模式</span>
                    </div>
                    <div class="mode-description">
                      创建独立副本，您的修改不会影响原文件夹，适合需要大幅修改的情况
                    </div>
                  </div>
                </template>
              </v-radio>
            </v-radio-group>
          </div>
          
          <div class="folder-preview">
            <div class="preview-header">
              <v-icon color="primary" class="preview-icon">mdi-folder-multiple</v-icon>
              <div class="preview-info">
                <div class="preview-title">{{ folderInfo.title }}</div>
                <div class="preview-meta">
                  来自：{{ folderInfo.creator_name || folderInfo.creator_email || '未知用户' }}
                </div>
                <div class="preview-stats">
                  <v-icon size="14" color="grey-darken-1" class="mr-1">mdi-file-document</v-icon>
                  {{ documentCount }} 个文档
                </div>
              </div>
            </div>
            
            <div class="preview-features">
              <div class="feature-item">
                <v-icon size="16" color="success" class="mr-2">
                  {{ selectedMode === 'collaborative' ? 'mdi-account-multiple' : 'mdi-file-tree' }}
                </v-icon>
                <span>{{ selectedMode === 'collaborative' ? '实时协作' : '保持结构' }}</span>
              </div>
              <div class="feature-item">
                <v-icon size="16" color="success" class="mr-2">
                  {{ selectedMode === 'collaborative' ? 'mdi-sync' : 'mdi-shield-check' }}
                </v-icon>
                <span>{{ selectedMode === 'collaborative' ? '同步修改' : '独立修改' }}</span>
              </div>
              <div class="feature-item">
                <v-icon size="16" color="success" class="mr-2">mdi-history</v-icon>
                <span>修改记录</span>
              </div>
            </div>
            
            <v-alert 
              :type="selectedMode === 'collaborative' ? 'success' : 'info'" 
              variant="tonal" 
              density="compact" 
              class="mt-3"
            >
              <v-icon start size="16">mdi-information</v-icon>
              {{ selectedMode === 'collaborative' 
                ? '您将成为文件夹的协作者，修改会实时同步' 
                : '文件夹将被复制到您的笔记中，您的修改不会影响原始文件夹' 
              }}
            </v-alert>
          </div>
        </v-card-text>
        
        <v-card-actions class="dialog-actions">
          <v-spacer></v-spacer>
          <v-btn
            variant="text"
            @click="showAddToNotesDialog = false"
            :disabled="addToNotesLoading"
            class="cancel-btn"
          >
            取消
          </v-btn>
          <v-btn
            color="primary"
            variant="elevated"
            @click="addFolderToMyNotes"
            :loading="addToNotesLoading"
            class="save-btn"
          >
            <v-icon start>mdi-bookmark-plus</v-icon>
            加入笔记
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 提示消息 -->
    <v-snackbar v-model="showMessage" :color="messageType" timeout="3000" location="top">
      {{ message }}
    </v-snackbar>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { documentsApi } from '@/api/documents'
import NoteMarkdownEditor from '@/components/NoteMarkdownEditor.vue'
import FolderTreeNode from '@/components/FolderTreeNode.vue'
import { useAuthStore } from '@/stores/authStore'

export default {
  name: 'SharedFolderPage',
  components: {
    NoteMarkdownEditor,
    FolderTreeNode
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const authStore = useAuthStore()
    
    // 状态
    const loading = ref(true)
    const error = ref(null)
    const folderInfo = ref(null)
    const documentTree = ref([])
    const flatDocumentList = ref([])  // 添加扁平文档列表
    const selectedDocument = ref(null)
    const selectedDocumentId = ref(null)
    const sidebarOpen = ref(true)
    const sharePermissions = ref(['read'])
    
    // 加入笔记相关状态
    const showAddToNotesDialog = ref(false)
    const addToNotesLoading = ref(false)
    const isFolderInUserNotes = ref(false)
    const selectedMode = ref('collaborative')  // 默认选择协作模式
    
    // 消息提示
    const showMessage = ref(false)
    const message = ref('')
    const messageType = ref('info')

    // 获取分享token
    const shareToken = computed(() => route.params.token)

    // 认证状态
    const isAuthenticated = computed(() => authStore.isAuthenticated)

    // 权限计算
    const canEdit = computed(() => {
      return sharePermissions.value.includes('write') || sharePermissions.value.includes('admin')
    })

    // 构建树形结构的方法
    const buildTree = (flatList) => {
      console.log('开始构建树形结构, flatList:', flatList)
      
      if (!Array.isArray(flatList) || flatList.length === 0) {
        console.log('flatList为空或不是数组')
        return []
      }

      // 创建一个映射对象
      const nodeMap = {}
      const tree = []

      // 先创建所有节点的映射
      flatList.forEach(item => {
        nodeMap[item.id] = {
          ...item,
          children: []
        }
      })

      // 构建树形结构
      flatList.forEach(item => {
        if (item.parent_id && nodeMap[item.parent_id]) {
          // 有父节点，添加到父节点的children中
          nodeMap[item.parent_id].children.push(nodeMap[item.id])
        } else {
          // 没有父节点或父节点不存在，作为根节点
          tree.push(nodeMap[item.id])
        }
      })

      console.log('树形结构构建完成:', tree)
      return tree
    }

    // 文档统计
    const documentCount = computed(() => {
      return flatDocumentList.value.length
    })

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }

    // 加载分享文件夹
    const loadSharedFolder = async () => {
      try {
        loading.value = true
        error.value = null

        console.log('开始加载分享文件夹, token:', shareToken.value)

        // 获取文件夹信息
        const folderData = await documentsApi.getSharedDocument(shareToken.value)
        console.log('获取到文件夹信息:', folderData)
        
        if (!folderData.is_folder_share) {
          // 不是文件夹分享，重定向到普通分享页面
          console.log('不是文件夹分享，重定向到普通分享页面')
          router.replace(`/share/${shareToken.value}`)
          return
        }
        
        folderInfo.value = folderData
        sharePermissions.value = folderData.share_permissions || ['read']

        // 获取文档树（扁平列表）
        console.log('开始获取文档树...')
        const flatData = await documentsApi.getSharedFolderTree(shareToken.value)
        console.log('获取到扁平文档数据:', flatData)
        
        // 保存扁平列表
        flatDocumentList.value = flatData
        
        // 构建树形结构
        const treeData = buildTree(flatData)
        documentTree.value = treeData
        console.log('设置文档树完成, documentTree.value:', documentTree.value)

        // 检查用户是否已经有这个文件夹
        if (isAuthenticated.value) {
          await checkIfFolderInUserNotes()
        }

      } catch (err) {
        console.error('加载分享文件夹失败:', err)
        if (err.response?.status === 403) {
          error.value = '分享链接已过期或被撤销'
        } else if (err.response?.status === 404) {
          error.value = '分享的文件夹不存在'
        } else {
          error.value = '加载失败，请检查网络连接'
        }
      } finally {
        loading.value = false
      }
    }

    // 处理节点选择
    const handleNodeSelected = async (nodeId) => {
      try {
        selectedDocumentId.value = nodeId
        const document = await documentsApi.getDocumentInSharedFolder(shareToken.value, nodeId)
        selectedDocument.value = document
      } catch (err) {
        console.error('加载文档失败:', err)
        showToast('加载文档失败', 'error')
      }
    }

    // 切换侧边栏
    const toggleSidebar = () => {
      sidebarOpen.value = !sidebarOpen.value
    }

    // 显示提示
    const showToast = (msg, type = 'info') => {
      message.value = msg
      messageType.value = type
      showMessage.value = true
    }

    // 重试
    const retry = () => {
      loadSharedFolder()
    }

    // 返回首页
    const goHome = () => {
      router.push('/')
    }

    // 权限相关方法
    const getPermissionText = (permissions) => {
      if (permissions.includes('admin')) return '管理员'
      if (permissions.includes('write')) return '可编辑'
      if (permissions.includes('comment')) return '可评论'
      return '只读'
    }

    const getPermissionColor = (permissions) => {
      if (permissions.includes('admin')) return 'purple'
      if (permissions.includes('write')) return 'green'
      if (permissions.includes('comment')) return 'orange'
      return 'grey'
    }

    const getPermissionIcon = (permissions) => {
      if (permissions.includes('admin')) return 'mdi-shield-crown'
      if (permissions.includes('write')) return 'mdi-pencil'
      if (permissions.includes('comment')) return 'mdi-comment'
      return 'mdi-eye'
    }

    // 检查文件夹是否已在用户笔记中
    const checkIfFolderInUserNotes = async () => {
      try {
        if (!folderInfo.value || !isAuthenticated.value) {
          isFolderInUserNotes.value = false
          return
        }
        
        // 获取用户的文档树
        const userDocuments = await documentsApi.getDocumentTree()
        
        // 检查当前文件夹是否已经在用户的笔记中
        const currentFolderId = folderInfo.value.id || folderInfo.value._id
        const isInNotes = userDocuments.some(doc => {
          const docId = doc.id || doc._id
          return docId === currentFolderId || 
                 (doc.original_document_id && doc.original_document_id === currentFolderId)
        })
        
        isFolderInUserNotes.value = isInNotes
      } catch (error) {
        console.error('检查文件夹是否在用户笔记中失败:', error)
        isFolderInUserNotes.value = false
      }
    }

    // 加入我的笔记
    const addFolderToMyNotes = async () => {
      if (!isAuthenticated.value) {
        showToast('请先登录后再保存文件夹', 'warning')
        return
      }
      
      addToNotesLoading.value = true
      
      try {
        let result
        
        // 根据选择的模式调用不同的API
        if (selectedMode.value === 'collaborative') {
          // 协作模式
          result = await documentsApi.addSharedFolderToNotesCollaborative(shareToken.value)
          showToast('文件夹已成功加入协作！您现在可以与原作者实时协作编辑。', 'success')
        } else {
          // 复制模式
          result = await documentsApi.addSharedFolderToNotes(shareToken.value)
          showToast('文件夹已成功复制到您的笔记中！', 'success')
        }
        
        showAddToNotesDialog.value = false
        isFolderInUserNotes.value = true
        
        // 跳转到笔记页面
        setTimeout(() => {
          if (selectedMode.value === 'collaborative') {
            // 协作模式跳转到原文件夹
            router.push(`/note?id=${folderInfo.value.id}`)
          } else {
            // 复制模式跳转到新文件夹
            router.push(`/note?id=${result.id}`)
          }
        }, 1500)
        
      } catch (error) {
        console.error('保存文件夹失败:', error)
        if (error.response?.status === 403) {
          showToast('分享链接已过期或被撤销', 'error')
        } else if (error.response?.status === 401) {
          showToast('请先登录后再保存文件夹', 'warning')
        } else if (error.response?.status === 409) {
          showToast('此文件夹已在您的笔记中', 'info')
        } else {
          showToast('保存文件夹失败，请重试', 'error')
        }
      } finally {
        addToNotesLoading.value = false
      }
    }

    // 生命周期
    onMounted(() => {
      if (!shareToken.value) {
        error.value = '无效的分享链接'
        loading.value = false
        return
      }
      
      loadSharedFolder()
    })

    return {
      loading,
      error,
      folderInfo,
      documentTree,
      flatDocumentList,
      selectedDocument,
      selectedDocumentId,
      sidebarOpen,
      showMessage,
      message,
      messageType,
      documentCount,
      sharePermissions,
      showAddToNotesDialog,
      addToNotesLoading,
      isFolderInUserNotes,
      selectedMode,
      canEdit,
      isAuthenticated,
      formatDate,
      handleNodeSelected,
      toggleSidebar,
      retry,
      goHome,
      getPermissionText,
      getPermissionColor,
      getPermissionIcon,
      addFolderToMyNotes
    }
  }
}
</script>

<style scoped>
.folder-share-page {
  min-height: 100vh;
  background: #f8fafc;
}

/* 加载和错误状态样式 */
.loading-container, .error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.loading-content, .error-content {
  text-align: center;
  color: white;
}

.loading-text, .error-title, .error-message {
  color: white;
}

.error-title {
  font-size: 2rem;
  font-weight: 600;
  margin: 16px 0 8px;
}

.error-message {
  font-size: 1.1rem;
  margin-bottom: 24px;
  opacity: 0.9;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.action-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  font-weight: 500;
}

/* 顶部导航栏 */
.top-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-left, .nav-right {
  flex: 1;
}

.nav-right {
  display: flex;
  justify-content: flex-end;
}

.nav-center {
  flex: 2;
  display: flex;
  justify-content: center;
}

.nav-brand {
  display: flex;
  align-items: center;
}

.brand-text {
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  letter-spacing: -0.5px;
}

.nav-btn {
  border-radius: 8px;
  text-transform: none;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  height: 36px;
  padding: 0 16px;
}

.nav-btn:hover {
  background: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.share-badge {
  background: rgba(76, 175, 80, 0.2);
  color: white;
  font-weight: 500;
  height: 28px;
}

/* 文件夹标题区域 */
.folder-header {
  background: white;
  padding: 24px;
  margin: 16px 24px 0;
  border-radius: 12px;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.folder-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.folder-subtitle {
  display: flex;
  align-items: center;
  color: #6b7280;
  font-size: 0.9rem;
}

.folder-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 16px;
}

.permission-chip {
  height: 28px;
  font-weight: 500;
}

.add-to-notes-btn {
  border-radius: 8px;
  text-transform: none;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(103, 126, 234, 0.2);
}

/* 主内容区域 */
.main-content {
  display: flex;
  height: calc(100vh - 200px);
  margin: 16px 24px 24px;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

/* 侧边栏 */
.sidebar {
  width: 320px;
  border-right: 1px solid #e5e7eb;
  background: #fafbfc;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  min-height: 100%;
}

.sidebar.collapsed {
  width: 48px;
  overflow: hidden;
}

.sidebar-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: white;
  flex-shrink: 0;
}

.sidebar-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.sidebar-toggle {
  color: #6b7280;
  flex-shrink: 0;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.document-tree {
  min-height: 100px;
  /* 树形结构样式在FolderTreeNode组件中定义 */
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-text {
  color: #9ca3af;
  font-size: 0.9rem;
  margin: 0;
}

/* 文档内容区域 */
.document-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s ease;
}

.document-content.full-width {
  /* 侧边栏收起时的样式 */
}

/* 欢迎页面 */
.welcome-page {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9fafb;
}

.welcome-content {
  text-align: center;
  max-width: 400px;
}

.welcome-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.welcome-desc {
  color: #6b7280;
  font-size: 1rem;
  margin-bottom: 24px;
}

.folder-stats {
  display: flex;
  justify-content: center;
  gap: 24px;
}

.stat-item {
  display: flex;
  align-items: center;
  color: #6b7280;
  font-size: 0.9rem;
}

/* 文档显示区域 */
.document-display {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.document-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: white;
}

.document-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 6px;
}

.document-meta {
  display: flex;
  align-items: center;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  color: #6b7280;
  font-size: 0.875rem;
}

.document-body {
  flex: 1;
  overflow: auto;
  padding: 24px;
  background: #ffffff;
}

/* 只读编辑器样式 */
.readonly-editor {
  /* 确保表格样式正确 */
}

:deep(.ProseMirror) {
  padding: 20px;
  min-height: 400px;
  line-height: 1.6;
  color: #374151;
  font-size: 15px;
  outline: none;
  border: none;
}

:deep(.ProseMirror h1) {
  font-size: 1.75rem;
  font-weight: 600;
  margin: 24px 0 12px 0;
  color: #1f2937;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 6px;
}

:deep(.ProseMirror h2) {
  font-size: 1.375rem;
  font-weight: 600;
  margin: 20px 0 8px 0;
  color: #1f2937;
}

:deep(.ProseMirror h3) {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 16px 0 6px 0;
  color: #1f2937;
}

:deep(.ProseMirror p) {
  margin: 0 0 12px 0;
}

:deep(.ProseMirror ul), :deep(.ProseMirror ol) {
  padding-left: 20px;
  margin: 12px 0;
}

:deep(.ProseMirror li) {
  margin: 3px 0;
}

:deep(.ProseMirror blockquote) {
  border-left: 3px solid #6366f1;
  padding-left: 12px;
  margin: 12px 0;
  color: #6b7280;
  font-style: italic;
}

:deep(.ProseMirror code) {
  background-color: #f1f5f9;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'SF Mono', Monaco, Menlo, Consolas, monospace;
  font-size: 0.85em;
  color: #e11d48;
}

:deep(.ProseMirror pre) {
  background-color: #f1f5f9;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 12px 0;
  border: 1px solid #e2e8f0;
}

:deep(.ProseMirror pre code) {
  background: none;
  padding: 0;
  color: #374151;
}

/* 表格样式 */
:deep(.ProseMirror table) {
  border-collapse: collapse;
  margin: 15px 0;
  width: 100%;
  max-width: 100%;
  background-color: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

:deep(.ProseMirror th),
:deep(.ProseMirror td) {
  border: 1px solid #e5e7eb;
  padding: 8px 12px;
  text-align: left;
  vertical-align: top;
}

:deep(.ProseMirror th) {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
}

:deep(.ProseMirror tr:nth-child(even)) {
  background-color: #f9fafb;
}

:deep(.ProseMirror tr:hover) {
  background-color: #f3f4f6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
    height: auto;
    min-height: calc(100vh - 200px);
  }
  
  .sidebar {
    width: 100%;
    height: auto;
    min-height: 300px;
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .sidebar.collapsed {
    width: 100%;
    height: 60px;
    min-height: 60px;
  }
  
  .sidebar-content {
    max-height: 300px;
  }
  
  .document-content {
    min-height: 500px;
  }
}

/* 对话框样式 */
.add-to-notes-dialog {
  border-radius: 16px;
  overflow: hidden;
}

.dialog-title {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  padding: 20px 24px 16px 24px;
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
}

.dialog-content {
  padding: 24px;
}

.dialog-description {
  font-size: 0.9rem;
  color: #6b7280;
  margin-bottom: 20px;
  line-height: 1.5;
}

/* 模式选择样式 */
.mode-selection {
  margin-bottom: 24px;
  padding: 16px;
  background: #f1f5f9;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.mode-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
}

.mode-radio-group {
  margin: 0;
}

.mode-radio {
  margin-bottom: 12px;
}

.mode-option {
  width: 100%;
  padding: 12px 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.mode-option:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.mode-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.mode-name {
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.mode-description {
  font-size: 0.8rem;
  color: #6b7280;
  line-height: 1.4;
  margin-left: 28px;
}

/* 选中的模式样式 */
:deep(.v-selection-control--selected .mode-option) {
  border-color: #10b981;
  background: #f0fdf4;
}

:deep(.v-selection-control--selected .mode-name) {
  color: #059669;
}

.folder-preview {
  background: #f8fafc;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #e5e7eb;
}

.preview-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.preview-icon {
  margin-right: 12px;
  font-size: 32px;
}

.preview-info {
  flex: 1;
}

.preview-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

.preview-meta {
  font-size: 0.8rem;
  color: #6b7280;
  margin-bottom: 4px;
}

.preview-stats {
  font-size: 0.8rem;
  color: #6b7280;
  display: flex;
  align-items: center;
}

.preview-features {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.feature-item {
  display: flex;
  align-items: center;
  font-size: 0.8rem;
  color: #374151;
}

.dialog-actions {
  padding: 16px 24px;
  background-color: #fafbfc;
}

.cancel-btn, .save-btn {
  font-weight: 500;
  text-transform: none;
}

.save-btn {
  box-shadow: 0 2px 8px rgba(103, 126, 234, 0.2);
}
</style> 