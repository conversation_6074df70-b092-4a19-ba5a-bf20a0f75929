<template>
  <div class="share-page" :class="{ 'show-document': document }">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-content">
        <v-progress-circular
          indeterminate
          color="primary"
          size="56"
          width="4"
        />
        <p class="loading-text">正在加载分享文档...</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="error-content">
        <div class="error-icon-wrapper">
          <v-icon size="64" color="error" class="error-icon">
            mdi-alert-circle-outline
          </v-icon>
        </div>
        <h1 class="error-title">访问失败</h1>
        <p class="error-message">{{ error }}</p>
        
        <div class="error-actions">
          <v-btn
            color="primary"
            variant="elevated"
            size="large"
            @click="retry"
            class="action-btn"
          >
            <v-icon start>mdi-refresh</v-icon>
            重新尝试
          </v-btn>
          
          <v-btn
            color="grey-darken-1"
            variant="outlined"
            size="large"
            @click="goHome"
            class="action-btn"
          >
            <v-icon start>mdi-home</v-icon>
            返回首页
          </v-btn>
        </div>
      </div>
    </div>

    <!-- 文档内容 -->
    <div v-else-if="document" class="document-container">
      <!-- 顶部导航栏 -->
      <div class="top-nav">
        <div class="nav-left">
          <v-btn
            variant="outlined"
            color="primary"
            @click="goHome"
            class="nav-btn"
          >
            <v-icon start>mdi-notebook</v-icon>
            我的笔记
          </v-btn>
        </div>
        
        <div class="nav-center">
          <div class="nav-brand">
            <v-icon size="28" color="primary" class="mr-2">mdi-brain</v-icon>
            <span class="brand-text">Deep Cognition</span>
          </div>
        </div>
        
        <div class="nav-right">
          <v-chip
            size="small"
            color="primary"
            variant="flat"
            class="share-badge"
          >
            <v-icon start size="14">mdi-share-variant</v-icon>
            共享文档
          </v-chip>
        </div>
      </div>

      <!-- 文档标题区域 -->
      <div class="document-header">
        <div class="document-title-section">
          <h1 class="document-title">{{ document.title }}</h1>
          <div class="document-subtitle">
            <v-icon size="16" color="grey-darken-1" class="mr-1">mdi-account-circle</v-icon>
            <span>来自 {{ document.creator_name || document.creator_email || '未知用户' }} 的分享</span>
          </div>
        </div>
      </div>

      <!-- 文档信息卡片 -->
      <div class="document-info-card">
        <div class="info-left">
          <div class="owner-info">
            <v-avatar size="40" color="primary" class="mr-3">
              <span class="text-white text-h6">{{ (document.creator_name || document.creator_email || '未知')[0].toUpperCase() }}</span>
            </v-avatar>
            <div>
              <div class="owner-name">{{ document.creator_name || document.creator_email || '未知用户' }}</div>
              <div class="owner-label">文档拥有者</div>
            </div>
          </div>
          
          <div class="document-meta">
            <v-chip 
              size="small" 
              :color="getPermissionColor(sharePermissions)"
              variant="flat"
              class="permission-chip"
            >
              <v-icon start size="14">{{ getPermissionIcon(sharePermissions) }}</v-icon>
              {{ getPermissionText(sharePermissions) }}
            </v-chip>
            
            <div class="meta-item">
              <v-icon size="16" color="grey-darken-1" class="mr-1">mdi-calendar</v-icon>
              <span class="text-body-2">创建于 {{ formatDate(document.created_at) }}</span>
            </div>
            
            <div v-if="document.updated_by_name && document.updated_at !== document.created_at" class="meta-item">
              <v-icon size="16" color="grey-darken-1" class="mr-1">mdi-clock-edit</v-icon>
              <span class="text-body-2">
                最后修改：{{ document.updated_by_name }} · {{ formatDate(document.updated_at) }}
              </span>
            </div>
          </div>
        </div>
        
        <div class="info-right">
          <div class="document-status-and-actions">
            <div v-if="canEdit" class="collaboration-status">
              <v-icon size="20" color="success" class="mr-2">mdi-account-multiple</v-icon>
              <span class="status-text">协作编辑模式</span>
            </div>
            <div v-else class="collaboration-status readonly">
              <v-icon size="20" color="warning" class="mr-2">mdi-eye</v-icon>
              <span class="status-text">只读模式</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 协作状态栏 -->
      <div v-if="canEdit" class="collaboration-bar">
        <div class="save-status" :class="saveStatus">
          <v-icon size="16" class="status-icon">{{ saveStatusIcon }}</v-icon>
          <span class="status-text">{{ saveStatusText }}</span>
        </div>
        
        <div class="collaboration-hint">
          <v-icon size="14" color="info" class="mr-1">mdi-information</v-icon>
          <span class="hint-text">您的修改将自动保存</span>
        </div>
      </div>

      <!-- 协作编辑提示卡片 - 仅对可编辑文档显示 -->
      <div v-if="canEdit && isAuthenticated && !isDocumentInUserNotes" class="collaboration-invite-card">
        <div class="invite-content">
          <div class="invite-icon">
            <div class="icon-wrapper">
              <v-icon size="40" color="white">mdi-account-plus</v-icon>
            </div>
          </div>
          <div class="invite-text">
            <h3 class="invite-title">🚀 加入协作编辑</h3>
            <p class="invite-description">
              将此文档添加到您的笔记中，与文档拥有者实时协作编辑，共同创作优质内容
            </p>
            <div class="invite-features">
              <div class="feature-tag">
                <v-icon size="14" color="success" class="mr-1">mdi-check</v-icon>
                实时同步
              </div>
              <div class="feature-tag">
                <v-icon size="14" color="success" class="mr-1">mdi-check</v-icon>
                版本记录
              </div>
              <div class="feature-tag">
                <v-icon size="14" color="success" class="mr-1">mdi-check</v-icon>
                协作编辑
              </div>
            </div>
          </div>
          <div class="invite-action">
            <v-btn
              variant="elevated"
              color="primary"
              size="large"
              @click="showCopyDialog = true"
              class="invite-btn"
            >
              <v-icon start>mdi-bookmark-plus</v-icon>
              立即加入协作
            </v-btn>
          </div>
        </div>
      </div>

      <!-- 文档编辑器/查看器 -->
      <div class="document-content">
        <div v-if="canEdit" class="editor-container">
          <NoteMarkdownEditor
            :initial-content="document.content"
            :editable="true"
            @update:content="handleContentUpdate"
            class="markdown-editor"
          />
        </div>
        
        <div v-else class="viewer-container">
          <div class="markdown-content" v-html="renderedContent"></div>
        </div>
      </div>

      <!-- 保存到我的笔记对话框 -->
      <v-dialog v-model="showCopyDialog" max-width="500" persistent="false">
        <v-card class="copy-dialog">
          <v-card-title class="dialog-title">
            <v-icon start color="primary">mdi-content-copy</v-icon>
            保存到我的笔记
          </v-card-title>
          
          <v-card-text class="dialog-content">
            <p class="dialog-description">
              将此文档保存到您的笔记中，您将可以在个人笔记页面继续编辑此文档。
            </p>
            
            <div class="document-preview">
              <div class="preview-header">
                <v-icon color="primary" class="preview-icon">mdi-file-document</v-icon>
                <div class="preview-info">
                  <div class="preview-title">{{ document.title }}</div>
                  <div class="preview-meta">
                    来自：{{ document.creator_name || document.creator_email || '未知用户' }}
                  </div>
                </div>
              </div>
              
              <div class="preview-features">
                <div class="feature-item">
                  <v-icon size="16" color="success" class="mr-2">mdi-account-multiple</v-icon>
                  <span>协作编辑</span>
                </div>
                <div class="feature-item">
                  <v-icon size="16" color="success" class="mr-2">mdi-history</v-icon>
                  <span>修改记录</span>
                </div>
              </div>
            </div>
          </v-card-text>
          
          <v-card-actions class="dialog-actions">
            <v-spacer></v-spacer>
            <v-btn
              variant="text"
              @click="showCopyDialog = false"
              :disabled="copyLoading"
              class="cancel-btn"
            >
              取消
            </v-btn>
            <v-btn
              color="primary"
              variant="elevated"
              @click="saveToMyNotes"
              :loading="copyLoading"
              class="save-btn"
            >
              <v-icon start>mdi-content-copy</v-icon>
              保存到笔记
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </div>

    <!-- 提示消息 -->
    <v-snackbar v-model="showMessage" :color="messageType" timeout="3000" location="top">
      {{ message }}
    </v-snackbar>
  </div>
</template>

<script>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { documentsApi } from '@/api/documents'
import NoteMarkdownEditor from '@/components/NoteMarkdownEditor.vue'
import { marked } from 'marked'
import { useAuthStore } from '@/stores/authStore'

export default {
  name: 'SharedNotePage',
  components: {
    NoteMarkdownEditor
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const authStore = useAuthStore()
    
    // 状态
    const loading = ref(true)
    const error = ref(null)
    const document = ref(null)
    const sharePermissions = ref(['read'])
    const hasChanges = ref(false)
    const saveStatus = ref('saved')
    
    // 保存到我的笔记相关状态
    const showCopyDialog = ref(false)
    const copyLoading = ref(false)
    const isDocumentInUserNotes = ref(false)
    
    // 消息提示
    const showMessage = ref(false)
    const message = ref('')
    const messageType = ref('info')

    // 获取分享token
    const shareToken = computed(() => route.params.token)

    // 认证状态
    const isAuthenticated = computed(() => authStore.isAuthenticated)

    // 权限计算
    const canEdit = computed(() => {
      return sharePermissions.value.includes('write') || sharePermissions.value.includes('admin')
    })
    
    const canWrite = computed(() => {
      return sharePermissions.value.includes('write') || sharePermissions.value.includes('admin')
    })

    // 渲染的Markdown内容
    const renderedContent = computed(() => {
      if (!document.value?.content) return ''
      return marked(document.value.content)
    })

    // 保存状态显示
    const saveStatusIcon = computed(() => {
      switch (saveStatus.value) {
        case 'saving':
          return 'mdi-loading';
        case 'unsaved':
          return 'mdi-circle';
        default:
          return 'mdi-check-circle';
      }
    });
    
    const saveStatusText = computed(() => {
      switch (saveStatus.value) {
        case 'saving':
          return '保存中...';
        case 'unsaved':
          return '未保存';
        default:
          return '已保存';
      }
    });

    // 方法
    const loadSharedDocument = async () => {
      try {
        loading.value = true
        error.value = null

        const sharedDoc = await documentsApi.getSharedDocument(shareToken.value)
        
        // 检查是否为文件夹分享，如果是则重定向到文件夹分享页面
        if (sharedDoc.is_folder_share) {
          router.replace(`/share/folder/${shareToken.value}`)
          return
        }
        
        document.value = sharedDoc
        sharePermissions.value = sharedDoc.share_permissions || ['read']
        
        // 检查用户是否已经有这个文档
        if (isAuthenticated.value) {
          await checkIfDocumentInUserNotes()
        }
        
      } catch (err) {
        console.error('加载分享文档失败:', err)
        if (err.response?.status === 403) {
          error.value = '分享链接已过期或被撤销'
        } else if (err.response?.status === 404) {
          error.value = '分享的文档不存在'
        } else {
          error.value = '加载失败，请检查网络连接'
        }
      } finally {
        loading.value = false
      }
    }

    const retry = () => {
      loadSharedDocument()
    }

    const goHome = () => {
      router.push('/note')
    }

    const enableEdit = () => {
      if (!canEdit.value) return
      showToast('进入编辑模式', 'info')
    }

    const printDocument = () => {
      window.print()
    }

    const downloadDocument = () => {
      if (!document.value) return
      
      const content = document.value.content
      const blob = new Blob([content], { type: 'text/markdown' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${document.value.title}.md`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      
      showToast('文档下载成功', 'success')
    }

    const getPermissionText = (permissions) => {
      if (permissions.includes('admin')) return '管理员'
      if (permissions.includes('write')) return '可编辑'
      if (permissions.includes('comment')) return '可评论'
      return '只读'
    }

    const getPermissionColor = (permissions) => {
      if (permissions.includes('admin')) return 'purple'
      if (permissions.includes('write')) return 'green'
      if (permissions.includes('comment')) return 'orange'
      return 'grey'
    }

    const getPermissionIcon = (permissions) => {
      if (permissions.includes('admin')) return 'mdi-shield-crown'
      if (permissions.includes('write')) return 'mdi-pencil'
      if (permissions.includes('comment')) return 'mdi-comment'
      return 'mdi-eye'
    }

    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }

    const showToast = (msg, type = 'info') => {
      message.value = msg
      messageType.value = type
      showMessage.value = true
    }

    // 自动保存相关
    const autoSaveTimer = ref(null);
    
    // 处理内容更新
    const handleContentUpdate = (content) => {
      if (document.value) {
        document.value.content = content;
        hasChanges.value = true;
        saveStatus.value = 'unsaved';
        
        // 启动自动保存
        scheduleAutoSave();
      }
    };
    
    // 调度自动保存
    const scheduleAutoSave = () => {
      // 清除之前的定时器
      if (autoSaveTimer.value) {
        clearTimeout(autoSaveTimer.value);
      }
      
      // 设置3秒后自动保存
      autoSaveTimer.value = setTimeout(() => {
        if (hasChanges.value && document.value && canWrite.value) {
          autoSave();
        }
      }, 3000);
    };
    
    // 执行自动保存
    const autoSave = async () => {
      if (!document.value || !hasChanges.value || !canWrite.value) return;
      
      try {
        saveStatus.value = 'saving';
        
        // 使用分享token更新文档
        const updatedDoc = await documentsApi.updateSharedDocument(shareToken.value, {
          content: document.value.content
        });
        
        // 只更新修改者信息，不重新加载整个文档
        if (updatedDoc.updated_by_name) {
          document.value.updated_by = updatedDoc.updated_by;
          document.value.updated_by_name = updatedDoc.updated_by_name;
          document.value.updated_at = updatedDoc.updated_at;
        }
        
        hasChanges.value = false;
        saveStatus.value = 'saved';
        showToast('自动保存成功', 'success');
      } catch (error) {
        console.error('自动保存失败:', error);
        saveStatus.value = 'unsaved';
        
        if (error.response?.status === 403) {
          showToast('分享链接已过期或权限不足', 'error');
        } else {
          showToast('自动保存失败', 'error');
        }
      }
    };

    // 错误页面相关方法
    const contactSupport = () => {
      // 这里可以跳转到支持页面或打开邮件客户端
      showToast('请发送邮件至 <EMAIL> 获取帮助', 'info')
    }

    const viewHelp = () => {
      // 这里可以跳转到帮助文档
      window.open('/help', '_blank')
    }

    // 保存到我的笔记
    const saveToMyNotes = async () => {
      if (!isAuthenticated.value) {
        showToast('请先登录后再保存文档', 'warning')
        return
      }
      
      copyLoading.value = true
      
      try {
        // 调用API将分享文档添加到用户笔记中（协作模式）
        const result = await documentsApi.addSharedDocumentToNotes(shareToken.value)
        showToast('文档已成功添加到您的笔记中！', 'success')
        showCopyDialog.value = false
        
        // 可选：跳转到笔记页面
        setTimeout(() => {
          router.push(`/note?id=${result.id}`)
        }, 1500)
        
      } catch (error) {
        console.error('保存文档失败:', error)
        if (error.response?.status === 403) {
          showToast('分享链接已过期或被撤销', 'error')
        } else if (error.response?.status === 401) {
          showToast('请先登录后再保存文档', 'warning')
        } else if (error.response?.status === 409) {
          showToast('此文档已在您的笔记中', 'info')
        } else {
          showToast('保存文档失败，请重试', 'error')
        }
      } finally {
        copyLoading.value = false
      }
    }

    // 检查文档是否已在用户笔记中
    const checkIfDocumentInUserNotes = async () => {
      try {
        if (!document.value || !isAuthenticated.value) {
          isDocumentInUserNotes.value = false
          return
        }
        
        // 获取用户的文档树
        const userDocuments = await documentsApi.getDocumentTree()
        
        // 检查当前文档是否已经在用户的笔记中
        // 可以通过文档ID或者原始文档ID来检查
        const currentDocId = document.value.id || document.value._id
        const isInNotes = userDocuments.some(doc => {
          const docId = doc.id || doc._id
          return docId === currentDocId || 
                 (doc.original_document_id && doc.original_document_id === currentDocId)
        })
        
        isDocumentInUserNotes.value = isInNotes
      } catch (error) {
        console.error('检查文档是否在用户笔记中失败:', error)
        isDocumentInUserNotes.value = false
      }
    }

    // 生命周期
    onMounted(() => {
      if (!shareToken.value) {
        error.value = '无效的分享链接'
        loading.value = false
        return
      }
      
      loadSharedDocument()
    })

    // 页面卸载前保存
    onBeforeUnmount(() => {
      // 清理自动保存定时器
      if (autoSaveTimer.value) {
        clearTimeout(autoSaveTimer.value)
      }
      
      // 如果有未保存的更改，立即保存
      if (hasChanges.value && document.value && canWrite.value) {
        autoSave()
      }
    })

    return {
      loading,
      error,
      document,
      sharePermissions,
      hasChanges,
      saveStatus,
      showMessage,
      message,
      messageType,
      isAuthenticated,
      canEdit,
      canWrite,
      renderedContent,
      saveStatusIcon,
      saveStatusText,
      handleContentUpdate,
      retry,
      goHome,
      enableEdit,
      printDocument,
      downloadDocument,
      getPermissionText,
      getPermissionColor,
      getPermissionIcon,
      formatDate,
      contactSupport,
      viewHelp,
      showCopyDialog,
      copyLoading,
      saveToMyNotes,
      isDocumentInUserNotes
    }
  }
}
</script>

<style scoped>
.share-page {
  min-height: 100vh;
  background: #f8fafc;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-text {
  margin-top: 24px;
  font-size: 1.125rem;
  font-weight: 500;
  opacity: 0.9;
}

/* 错误状态样式 */
.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.error-content {
  background: white;
  border-radius: 16px;
  padding: 48px 32px;
  max-width: 500px;
  width: 100%;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.error-icon-wrapper {
  margin-bottom: 24px;
}

.error-icon {
  color: #ef4444 !important;
}

.error-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12px;
}

.error-message {
  color: #6b7280;
  margin-bottom: 32px;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-btn {
  min-width: 120px;
  border-radius: 8px;
  text-transform: none;
  font-weight: 500;
}

/* 文档容器样式 */
.document-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  background: #f8fafc;
  min-height: 100vh;
}

/* 顶部导航栏 */
.top-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-left, .nav-right {
  flex: 1;
}

.nav-right {
  display: flex;
  justify-content: flex-end;
}

.nav-center {
  flex: 2;
  display: flex;
  justify-content: center;
}

.nav-brand {
  display: flex;
  align-items: center;
}

.brand-text {
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  letter-spacing: -0.5px;
}

.nav-btn {
  border-radius: 8px;
  text-transform: none;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  height: 36px;
  padding: 0 16px;
}

.nav-btn:hover {
  background: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.share-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: 500;
  height: 28px;
}

/* 文档标题区域 */
.document-header {
  background: white;
  padding: 20px 24px;
  margin: 16px 24px 12px;
  border-radius: 12px;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.document-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 6px;
  line-height: 1.2;
}

.document-subtitle {
  display: flex;
  align-items: center;
  color: #6b7280;
  font-size: 0.9rem;
}

/* 文档信息卡片 */
.document-info-card {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px 24px;
  margin: 0 24px 12px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.05);
}

.info-left {
  flex: 1;
}

.owner-info {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.owner-name {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.2;
}

.owner-label {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 2px;
  font-weight: 500;
}

.document-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

.permission-chip {
  font-weight: 600;
  border-radius: 6px;
  height: 24px;
}

.meta-item {
  display: flex;
  align-items: center;
  color: #6b7280;
  font-size: 0.8rem;
  font-weight: 500;
}

.info-right {
  flex-shrink: 0;
}

.document-status-and-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.collaboration-status {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 8px;
  color: white;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
}

.collaboration-status.readonly {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.2);
}

.status-text {
  font-size: 0.8rem;
  font-weight: 600;
}

/* 协作状态栏 */
.collaboration-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  margin: 0 24px 12px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.save-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 600;
  transition: all 0.2s ease;
}

.save-status.saved {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 1px 4px rgba(16, 185, 129, 0.3);
}

.save-status.saving {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  box-shadow: 0 1px 4px rgba(245, 158, 11, 0.3);
}

.save-status.unsaved {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 1px 4px rgba(239, 68, 68, 0.3);
}

.save-status .status-icon {
  opacity: 0.9;
}

.save-status.saving .status-icon {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.collaboration-hint {
  display: flex;
  align-items: center;
  gap: 4px;
}

.hint-text {
  font-size: 0.8rem;
  color: #64748b;
  font-weight: 500;
}

/* 文档内容区域 */
.document-content {
  margin: 0 24px 20px;
}

.editor-container, .viewer-container {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.markdown-editor {
  min-height: 400px;
}

.markdown-content {
  padding: 20px;
  line-height: 1.6;
  color: #374151;
  font-size: 15px;
}

/* 保存对话框样式 */
.copy-dialog {
  border-radius: 12px;
  overflow: hidden;
}

.dialog-title {
  background: #f8fafc;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
}

.dialog-content {
  padding: 20px;
}

.dialog-description {
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 20px;
  font-size: 0.9rem;
}

.document-preview {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
}

.preview-icon {
  font-size: 20px;
}

.preview-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 3px;
}

.preview-meta {
  font-size: 0.8rem;
  color: #6b7280;
}

.preview-features {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.8rem;
  color: #059669;
  font-weight: 500;
}

.dialog-actions {
  padding: 12px 20px;
  background: #f8fafc;
  border-top: 1px solid #e5e7eb;
}

.cancel-btn {
  color: #6b7280;
  text-transform: none;
  font-weight: 500;
}

/* Markdown编辑器样式 */
:deep(.ProseMirror) {
  padding: 20px;
  min-height: 400px;
  line-height: 1.6;
  color: #374151;
  font-size: 15px;
  outline: none;
  border: none;
}

:deep(.ProseMirror h1) {
  font-size: 1.75rem;
  font-weight: 600;
  margin: 24px 0 12px 0;
  color: #1f2937;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 6px;
}

:deep(.ProseMirror h2) {
  font-size: 1.375rem;
  font-weight: 600;
  margin: 20px 0 8px 0;
  color: #1f2937;
}

:deep(.ProseMirror h3) {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 16px 0 6px 0;
  color: #1f2937;
}

:deep(.ProseMirror p) {
  margin: 0 0 12px 0;
}

:deep(.ProseMirror ul), :deep(.ProseMirror ol) {
  padding-left: 20px;
  margin: 12px 0;
}

:deep(.ProseMirror li) {
  margin: 3px 0;
}

:deep(.ProseMirror blockquote) {
  border-left: 3px solid #6366f1;
  padding-left: 12px;
  margin: 12px 0;
  color: #6b7280;
  font-style: italic;
}

:deep(.ProseMirror code) {
  background-color: #f1f5f9;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'SF Mono', Monaco, Menlo, Consolas, monospace;
  font-size: 0.85em;
  color: #e11d48;
}

:deep(.ProseMirror pre) {
  background-color: #f1f5f9;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 12px 0;
  border: 1px solid #e2e8f0;
}

:deep(.ProseMirror pre code) {
  background: none;
  padding: 0;
  color: #374151;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .document-container {
    margin: 0;
  }
  
  .top-nav {
    padding: 8px 12px;
    flex-direction: column;
    gap: 8px;
  }
  
  .nav-left, .nav-center, .nav-right {
    flex: none;
    width: 100%;
  }
  
  .nav-center {
    text-align: left;
  }
  
  .brand-text {
    font-size: 1.1rem;
  }
  
  .document-header {
    margin: 12px;
    padding: 16px;
  }
  
  .document-title {
    font-size: 1.25rem;
  }
  
  .document-info-card {
    margin: 0 12px 8px;
    padding: 16px;
    flex-direction: column;
    gap: 12px;
  }
  
  .document-status-and-actions {
    align-items: flex-start;
    width: 100%;
  }
  
  .collaboration-bar {
    margin: 0 12px 8px;
    padding: 8px 12px;
    flex-direction: column;
    gap: 6px;
    align-items: flex-start;
  }
  
  .collaboration-invite-card {
    margin: 0 12px 12px;
    padding: 16px;
  }
  
  .invite-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .invite-btn {
    width: 100%;
  }
  
  .icon-wrapper {
    width: 50px;
    height: 50px;
  }
  
  .invite-title {
    font-size: 1.1rem;
  }
  
  .invite-description {
    font-size: 0.85rem;
  }
  
  .document-content {
    margin: 0 12px 12px;
  }
  
  :deep(.ProseMirror) {
    padding: 16px;
    font-size: 14px;
  }
  
  .preview-features {
    flex-direction: column;
    gap: 6px;
  }
  
  .dialog-content {
    padding: 16px;
  }
  
  .document-preview {
    padding: 12px;
  }
}

/* 协作提示卡片 */
.collaboration-invite-card {
  margin: 0 24px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);
  position: relative;
  overflow: hidden;
}

.collaboration-invite-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  pointer-events: none;
}

.invite-content {
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  z-index: 1;
}

.invite-icon {
  flex-shrink: 0;
}

.icon-wrapper {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.invite-text {
  flex: 1;
  color: white;
}

.invite-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 6px;
  color: white;
}

.invite-description {
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 12px;
  opacity: 0.9;
}

.invite-features {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.feature-tag {
  display: flex;
  align-items: center;
  gap: 3px;
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  color: white;
}

.invite-action {
  flex-shrink: 0;
}

.invite-btn {
  background: white;
  color: #667eea;
  font-weight: 600;
  border-radius: 12px;
  padding: 0 20px;
  height: 44px;
  font-size: 0.9rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

.invite-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}
</style> 