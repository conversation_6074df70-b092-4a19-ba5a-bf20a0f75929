<template>
  <div class="draft-page">
    <!-- 添加顶部导航栏 -->
    <div class="nav-bar">
      <div class="logo-container" @click="createNewChat">
        <span class="q-logo" @click="createNewChat">Q</span>
        <span class="logo-text">Deep Cognition</span>
      </div>
    
      <!-- 添加右侧工具栏 -->
      <div class="nav-tools">
        <!-- 在分享模式下隐藏新建对话按钮 -->
        <v-btn
          v-if="!isShareMode"
          icon
          variant="text"
          color="#654C8C"
          size="small"
          @click="createNewChat"
          class="tool-btn"
          title="新建对话"
        >
          <v-icon>mdi-plus</v-icon>
        </v-btn>
        
        <!-- 在分享模式下隐藏历史记录按钮 -->
        <HistoryDialog 
          v-if="!isShareMode"
          :current-conversation-id="currentConversationId"
          @select-history="handleSelectHistory"
        />
        
        <!-- 在分享模式下隐藏设置按钮 -->
        <SettingsDialog
          v-if="!isShareMode"
          @update-settings="handleUpdateSettings"
        />
        
        <!-- 在分享模式下隐藏分享按钮 -->
        <v-btn
          v-if="!isShareMode && !isEmptyPage"
          icon
          variant="text"
          color="#654C8C"
          size="small"
          @click="showShareDialog = true"
          class="tool-btn"
          title="分享对话"
        >
          <v-icon>mdi-share-variant</v-icon>
        </v-btn>
        
        <user-status-button />
      </div>
    </div>
    
    <!-- 加载骨架 -->
    <div v-if="isInitializing" class="draft-layout">
      <div class="canvas-wrapper" :style="{ width: '70%' }">
        <v-skeleton-loader
          class="mx-auto border"
          type="article, actions"
        ></v-skeleton-loader>
      </div>
      <div class="resizer"><div class="resizer-handle"></div></div>
      <div class="interface-wrapper" :style="{ width: '30%' }">
        <v-skeleton-loader
          class="mx-auto border"
          type="list-item-avatar-three-line, list-item-three-line, card"
        ></v-skeleton-loader>
      </div>
    </div>
    
    <div v-else class="draft-layout">
      <!-- 左侧 Canvas -->
      <div class="canvas-wrapper" :style="{ width: leftWidth + '%' }">

        <CanvasComponent 
          ref="canvasRef" 
          :canvasItems="canvasItems"
          :editorVersionsInfo="editorVersionsInfo"
          @updateEditorContent="updateEditorContent"
          @pause="handlePauseStream"
          @edit-command="handleEditCommand"
          @edit-report="handleEditReport"
          @select-history="handleSelectHistory"
          @switchVersion="handleSwitchVersion"
        />

        <div v-if="currentConversationId && !isShareMode" class="feedback-button-container">
          <v-btn
            color="primary"
            variant="outlined"
            size="small"
            @click="openReportFeedback"
            class="feedback-button"
          >
            <v-icon left>mdi-comment-quote</v-icon>
            提供反馈
          </v-btn>
        </div>
      </div>
      
      <!-- 可拖动的分隔线 -->
      <div 
        class="resizer" 
        @mousedown="startResize" 
        @touchstart="startResize"
      >
        <div class="resizer-handle"></div>
      </div>
      
      <!-- 右侧界面 -->
      <div class="interface-wrapper" :style="{ width: (100 - leftWidth) + '%' }">
        <InterfaceComponent 
          :interfaceItems="interfaceItems"
          :isProcessing="isProcessing"
          :chatError="chatError"
          :isShareMode="isShareMode"
          :uploadedFiles="uploadedFiles"
          :loadingStates="loadingStates"
          :isCognitionEnabled="isCognitionEnabled"
          :preferences="userPreferencesComponent"
          :isSearchEnabled="isSearchEnabled"
          @userInput="handleUserInput"
          @toggleThought="toggleThoughtExpanded"
          @uploadFile="handleFileUpload"
          @removeFile="handleFileRemove"
          @previewFile="handleFilePreview"
          @update:isCognitionEnabled="handleCognitionSwitch"
          @preferenceChange="handlePreferenceChange"
          @update:isSearchEnabled="handleSearchSwitch"
        />
      </div>
    </div>
    
    <!-- 在分享模式下隐藏悬浮球 -->
    <HoverBall 
      v-if="interfaceItems.length > 0 && !isShareMode"
      :is-processing="isProcessing"
      :unsolved-count="popoutConfig.questions.length"
      @pause="handlePauseRequest" 
      @resume="handleResumeRequest"
      @toggle-popout="togglePopout"
    />
    
    <!-- 在分享模式下隐藏弹出框 -->
    <PopoutComponent
      v-if="!isShareMode"
      ref="popoutRef"
      :questions="popoutConfig.questions"
      :input-label="popoutConfig.inputLabel"
      :input-placeholder="popoutConfig.inputPlaceholder"
      :submit-text="popoutConfig.submitText"
      :min-height="popoutConfig.minHeight"
      :max-height="popoutConfig.maxHeight"
      :max-width="popoutConfig.maxWidth"
      v-model:visible="popoutConfig.visible"
      @submit="handlePopoutSubmit"
      @close="handlePopoutClose"
    />
    
    <!-- 分享对话框 -->
    <v-dialog v-model="showShareDialog" max-width="500">
      <v-card>
        <v-card-title>分享对话</v-card-title>
        <v-card-text>
          <p class="text-subtitle-1 mb-2">请手动复制以下链接分享给他人：</p>
          <v-text-field
            :model-value="shareLink"
            readonly
            variant="outlined"
            class="share-link-field"
          />
          <v-alert
            type="info"
            variant="tonal"
            class="mt-2"
          >
            由于浏览器安全限制，请手动选择并复制链接。
          </v-alert>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="primary" @click="showShareDialog = false">关闭</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 添加登录提示对话框 -->
    <v-dialog v-model="showLoginDialog" max-width="400">
      <v-card>
        <v-card-title>需要登录</v-card-title>
        <v-card-text>
          <p class="text-subtitle-1">您需要登录才能使用对话功能。</p>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="primary" @click="navigateToLogin">去登录</v-btn>
          <v-btn color="grey" @click="showLoginDialog = false">取消</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <ReportFeedback 
      v-if="showReportFeedback" 
      :session-id="currentConversationId"
      @close="closeReportFeedback"
      @submitted="handleFeedbackSubmitted"
    />

    <!-- 添加预览上下文对话框 -->
    <preview-context
      v-model="showPreviewContext"
      :context-id="previewContext.contextId"
      :conversation-id="previewContext.conversationId"
      :title="previewContext.title"
      :file-type="previewContext.fileType"
      :content-url="previewContext.contentUrl"
      :content="previewContext.content"
      @close="handlePreviewContextClose"
    />

    <!-- Token Budget 不足对话框 -->
    <TokenBudgetDialog
      v-model:visible="tokenBudgetState.isDialogVisible"
      :remaining-budget="tokenBudgetState.errorInfo?.remainingBudget || 0"
      :required-budget="tokenBudgetState.errorInfo?.requiredBudget || 0"
      :error-message="tokenBudgetState.errorInfo?.message || ''"
      @close="closeTokenBudgetDialog"
      @refresh="refreshPage"
    />
  </div>
</template>

<script lang="ts">
import { ref, provide, onMounted, onUnmounted, reactive, watch, computed, defineAsyncComponent } from 'vue';
import CanvasComponent from '@/components/canvas/index.vue';
import InterfaceComponent from '@/components/interface/index.vue';
import { useDeepConversation } from '@/composables/useDeepConversation';
import { ClarificationItem } from '@/composables/useDeepConversation';
import { useRoute, useRouter } from "vue-router";
import { chatApi } from '@/api/chat';
import UserStatusButton from '@/components/UserStatusButton.vue';
import type { ChatHistory } from '@/components/HistoryDialog.vue';
import { useAuthStore } from '@/stores/authStore';
import { onBeforeRouteUpdate } from 'vue-router';
import { researchApi } from '@/api/chat';
import { v4 as uuidv4 } from 'uuid';
import { type ExtendedFile } from '@/composables/useDeepConversation';
import { use } from 'marked';
// 定义通信事件类型
type CanvasAction = 'replace' | 'highlight' | 'insert' | 'delete' | 'message';

interface CanvasEvent {
  action: CanvasAction;
  payload: any;
}

// 定义弹出框提交事件类型
interface PopoutSubmitEvent {
  input: string;
  selectedOptions: string[];
}

export default {
  name: 'DraftPage',
  components: {
    CanvasComponent,
    InterfaceComponent,
    UserStatusButton,
    // 将非核心组件改为异步加载，实现按需导入
    HistoryDialog: defineAsyncComponent(() => import('@/components/HistoryDialog.vue')),
    PopoutComponent: defineAsyncComponent(() => import('@/components/popout/index.vue')),
    HoverBall: defineAsyncComponent(() => import('@/components/hoverball/index.vue')),
    SettingsDialog: defineAsyncComponent(() => import('@/components/settings/index.vue')),
    ReportFeedback: defineAsyncComponent(() => import('@/components/ReportFeedback.vue')),
    PreviewContext: defineAsyncComponent(() => import('@/components/previewContext.vue')),
    TokenBudgetDialog: defineAsyncComponent(() => import('@/components/TokenBudgetDialog.vue'))
  },
  setup() {
    const canvasRef = ref(null);
    const popoutRef = ref(null);
    const router = useRouter();
    const route = useRoute();
    const authStore = useAuthStore();
    const isInitializing = ref(false);
    
    // 调用深度对话组合式API
    const {
      isProcessing,
      chatError,
      currentDraftId,
      userPreferences,
      updateUserPreferences,
      currentConversationId,
      canvasItems,
      interfaceItems,
      addChatMessage,
      updateChatMessage,
      updateEditorContent,
      addEditorItem,
      findLatestEditorItem,
      addThoughtItem,
      addObservationItem,
      addActionItem,
      updateInterfaceItem,
      toggleThoughtExpanded,
      handleUserInput,
      handleEditRequest,
      clearAllItems,
      restoreConversationHistory,
      restoreSharedConversationHistory,
      updateClarificationItem,
      removeClarificationItem,
      addUploadedFile,
      removeUploadedFile,
      uploadedFiles,
      loadingStates,
      isCognitionEnabled,
      updateResearchStatus,
      switchEditorVersion,
      getEditorVersionInfo,
      // Token Budget 相关
      tokenBudgetState,
      closeTokenBudgetDialog,
      refreshPage,
      resetTokenBudgetState,
      isSearchEnabled,
      updateSearchStatus,
      // 内容偏好相关
      toggleContentLike
    } = useDeepConversation();

    
    // 计算所有编辑器的版本信息
    const editorVersionsInfo = computed(() => {
      const versionsInfo = {};
      canvasItems.forEach(item => {
        if (item.type === 'editor') {
          versionsInfo[item.id] = getEditorVersionInfo(item.id);
        }
      });
      return versionsInfo;
    });
    
    // 弹出框配置
    const popoutConfig = reactive({
      questions: [] as {
        id: string;
        content: string;
        options: string[];
        activeQuestionIndex: number;
        totalQuestions: number;
      }[],
      inputLabel: '回复',
      inputPlaceholder: '您还有什么需要补充的...',
      submitText: '提交',
      minHeight: 'auto',
      maxHeight: 'auto',
      maxWidth: '600px',
      visible: false
    });
    
    // 用户偏好设置 - 使用computed让它能响应userPreferences的变化
    const userPreferencesComponent = computed(() => [
      {
        key: 'professional',
        label: '专业性',
        description: '增强回答的专业深度和技术准确性',
        value: userPreferences.professional,
        color: '#654C8C'
      },
      {
        key: 'critical',
        label: '批判性',
        description: '提供更多质疑和多角度分析',
        value: userPreferences.critical,
        color: '#7B68EE'
      },
      {
        key: 'comparison',
        label: '表格对比',
        description: '优先使用表格形式进行对比分析',
        value: userPreferences.comparison,
        color: '#9370DB'
      },
      {
        key: 'organization',
        label: '组织性',
        description: '生成文稿的组织结构',
        value: userPreferences.organization,
        color: '#9370DB'
      },
      {
        key: 'cutting_edge',
        label: '前沿性',
        description: '优先使用前沿技术进行分析',
        value: userPreferences.cutting_edge,
        color: '#9370DB'
      },
      {
        key: 'coverage',
        label: '覆盖面',
        description: '优先使用覆盖面广的分析',
        value: userPreferences.coverage,
        color: '#9370DB'
      },
      {
        key: 'depth',
        label: '深度',
        description: '优先使用深度分析',
        value: userPreferences.depth,
        color: '#9370DB'
      }
    ]);
    
    // 处理偏好设置变化
    const handlePreferenceChange = ({ key, value }) => {
      console.log(`[Draft] 接收到偏好设置变化: ${key} = ${value}`);
      
      // 创建新的偏好设置对象
      const newPreferences = { [key]: value };
      
      // 调用useDeepConversation的updateUserPreferences方法更新全局状态
      updateUserPreferences(newPreferences);
      
      console.log(`[Draft] 已更新全局偏好设置: ${key} = ${value}`);
    };
    
    // 切换弹出框显示状态
    const togglePopout = () => {
      popoutConfig.visible = !popoutConfig.visible;
    };
    
    // 修改弹出框提交处理函数
    const handlePopoutSubmit = async (data: PopoutSubmitEvent, questionId: string) => {
      console.log('接收到弹出框提交:', data, '问题ID:', questionId);
      
      // 从问题ID中提取原始的澄清项ID（格式为：clarificationId_questionIndex）
      const idParts = questionId.split('_');
      if (idParts.length < 2) {
        console.warn('无效的问题ID格式:', questionId);
        return;
      }
      
      const clarificationId = idParts[0];
      const questionIndex = parseInt(idParts[1], 10);
      
      // 构建回答消息
      let message = '';
      if (data.selectedOptions && data.selectedOptions.length > 0) {
        message = data.selectedOptions.join('；');
      }
      if (data.input.trim()) {
        if (message) {
          message += `\n补充说明：${data.input}`;
        } else {
          message = data.input;
        }
      }
      // 用户提供了回答
      if (message) {
        try {
          // 创建符合研究代理期望格式的回答
          const formattedAnswers = [{
            id: questionIndex,
            answer: message
          }];
          console.log('formattedAnswers', formattedAnswers);
          // 回退到使用旧接口
          await researchApi.submitClarificationAnswer(currentConversationId.value, formattedAnswers);
          console.log(`[Draft] 使用旧接口提交澄清问题回答成功`);
          
          // 标记整个澄清项为已回答
          updateClarificationItem(clarificationId, {
            isAnswered: true
          });
          
          // 从弹出框中移除部分问题
          popoutConfig.questions = popoutConfig.questions.filter(q => {
            const parts = q.id.split('_');
            return !(parts.length > 1 && parts[0] === clarificationId && parts[1] === questionIndex.toString());
          });
          
          // 检查是否所有问题都已回答完毕
          if (popoutConfig.questions.length === 0) {
            console.log('[Draft] 所有问题已回答完毕，自动继续对话');
            // 隐藏弹出框
            popoutConfig.visible = false;
            // 延迟一小段时间后自动触发继续功能
            setTimeout(() => {
              handleResumeRequest();
            }, 500); // 500毫秒延迟，给用户一个视觉反馈的时间
          }
        } catch (error) {
          console.error(`[Draft] 提交澄清问题回答出错:`, error);
        }
      }
    };
    
    // 左侧区域宽度百分比
    const leftWidth = ref(70);
    // 是否正在调整大小
    const isResizing = ref(false);
    // 初始鼠标位置
    const initialMouseX = ref(0);
    // 初始宽度
    const initialLeftWidth = ref(0);
    
    // 开始调整大小
    const startResize = (event: MouseEvent | TouchEvent) => {
      isResizing.value = true;
      
      // 获取初始鼠标/触摸位置
      if ('touches' in event) {
        initialMouseX.value = event.touches[0].clientX;
      } else {
        initialMouseX.value = event.clientX;
      }
      
      // 保存当前宽度
      initialLeftWidth.value = leftWidth.value;
      
      // 添加移动和结束调整事件监听
      document.addEventListener('mousemove', handleResize);
      document.addEventListener('mouseup', stopResize);
      document.addEventListener('touchmove', handleResize);
      document.addEventListener('touchend', stopResize);
      
      // 添加no-select类以防止文本选择
      document.body.classList.add('no-select');
    };
    
    // 处理调整大小
    const handleResize = (event: MouseEvent | TouchEvent) => {
      if (!isResizing.value) return;
      
      const clientX = 'touches' in event ? event.touches[0].clientX : event.clientX;
      const containerWidth = document.querySelector('.draft-layout')?.clientWidth || 1000;
      const deltaX = clientX - initialMouseX.value;
      const deltaPercent = (deltaX / containerWidth) * 100;
      
      // 计算新宽度
      let newLeftWidth = initialLeftWidth.value + deltaPercent;
      
      // 限制宽度在合理范围内
      newLeftWidth = Math.max(30, Math.min(80, newLeftWidth));
      
      // 更新宽度
      leftWidth.value = newLeftWidth;
    };
    
    // 停止调整大小
    const stopResize = () => {
      isResizing.value = false;
      
      // 移除事件监听
      document.removeEventListener('mousemove', handleResize);
      document.removeEventListener('mouseup', stopResize);
      document.removeEventListener('touchmove', handleResize);
      document.removeEventListener('touchend', stopResize);
      
      // 移除no-select类
      document.body.classList.remove('no-select');
      
      // 存储当前宽度到localStorage
      localStorage.setItem('draft_left_width', leftWidth.value.toString());
    };
    
    // 创建一个响应式的通信状态对象
    const canvasState = reactive({
      // 当前编辑器内容
      content: '',
      // 编辑器就绪状态
      ready: false,
      // 最后一次操作结果
      lastActionResult: null,
      // 最后一次错误
      lastError: null,
      // 动画状态
      animationInProgress: false,
      animationProgress: 0
    });
    
    // 处理选择历史记录
    const handleSelectHistory = (history: ChatHistory) => {
      console.log('选择历史记录:', history);
      
      // 如果不是当前对话，清空当前画布内容
      if (history.conversation_id !== currentConversationId.value) {
        
        const conversationId = history.id;
        if (!authStore.isAuthenticated) {
          authStore.verifyTokenValidity();
        }
        // 从localStorage恢复左侧区域宽度设置
        if (authStore.isAuthenticated) {
          clearAllItems();
          localStorage.setItem('current_conversation_id', conversationId);
          isShareMode.value = false;
          // 清空弹出框配置
          popoutConfig.questions = [];
          popoutConfig.visible = false;
          // 清空已保存的用户输入和选择
          if (popoutRef.value && popoutRef.value.clearInputsAndSelections) {
            popoutRef.value.clearInputsAndSelections();
          }
          
          try {
            console.log(`[Draft] 尝试恢复会话历史, ID: ${conversationId}`);
            const restored = restoreConversationHistory(conversationId);
            if (restored) {
              console.log(`[Draft] 成功恢复会话历史, ID: ${conversationId}`);
              
              // 添加额外的延迟再次触发接口重新绑定事件，确保DOM完全更新后执行
              setTimeout(() => {
                window.dispatchEvent(new CustomEvent('interface-reinitialize', { 
                  detail: { conversationId }
                }));
              }, 1000);
            } else {
              console.warn(`[Draft] 无法恢复会话历史, ID: ${conversationId}`);
            }
          } catch (error) {
            console.error(`[Draft] 恢复会话历史出错:`, error);
          }
          const savedWidth = localStorage.getItem('draft_left_width');
          if (savedWidth) {
            leftWidth.value = parseFloat(savedWidth);
          }
        }
      }
    };
    
    // 处理悬浮球暂停请求
    const handlePauseRequest = () => {
      console.log('悬浮球触发暂停请求');
      handlePauseStream();
      isProcessing.value = false;
    };
    
    // 处理悬浮球继续请求
    const handleResumeRequest = () => {
      console.log('悬浮球触发继续请求');
      
      if (!currentConversationId.value) {
        console.warn('[Draft] 无法继续会话，conversationId为空');
        return;
      }
      
      try {
        // 使用researchApi的resumeResearch方法继续会话
        researchApi.resumeResearch(currentConversationId.value)
          .then(response => {
            console.log(`[Draft] 成功继续会话 ID: ${currentConversationId.value}`, response);
            // isProcessing.value = true; // 这里不需要设置，因为后端一旦启动，status会自然改变，而暂停是得不到后端反馈的（有时）
          })
          .catch(error => {
            console.error(`[Draft] 继续会话出错:`, error);
          });
      } catch (error) {
        console.error(`[Draft] 调用继续API出错:`, error);
      }
    };

    
    // 添加分享模式状态
    const isShareMode = ref(false);
    
    // 添加空界面状态
    const isEmptyPage = computed(() => {
      return !route.query.conversation_id;
    });
    
    // 在组件挂载时检查URL参数
    onMounted(async () => {
      isInitializing.value = true;
      // 输出调试信息
      console.log('[Draft] 组件挂载时的userPreferences:', userPreferences);
      console.log('[Draft] 组件挂载时的userPreferencesComponent:', userPreferencesComponent.value);
      
      // 从URL参数获取会话ID和分享模式
      const conversationId = route.query.conversation_id as string;
      const shareMode = route.query.share === 'true';
      
      console.log('[Draft] 挂载时检查URL参数:', { conversationId, shareMode });
      
      // 设置分享模式
      isShareMode.value = shareMode;
      
      // 如果URL中有会话ID，并且与当前会话不同，则切换到该会话
      if (conversationId && conversationId !== currentConversationId.value) {
        // 检查用户是否已登录
        if (!authStore.isAuthenticated) {
          await authStore.verifyTokenValidity();
        }
        
        try {
          // 根据是否是分享模式选择不同的恢复方法
          if (shareMode) {
            console.log('[Draft] 检测到分享模式，使用分享恢复方法');
            const restored = await restoreSharedConversationHistory(conversationId);
            if (restored) {
              console.log('[Draft] 成功恢复分享的会话历史');
            } else {
              console.warn('[Draft] 无法恢复分享的会话历史');
            }
          } else {
            console.log('[Draft] 检测到普通模式，并行加载数据');

            // 定义两个并行的异步任务
            const statusPromise = researchApi.getResearchStatus(conversationId);
            
            const historyPromise = (async () => {
              if (authStore.isAuthenticated) {
                // 执行所有在恢复历史之前的同步设置
                clearAllItems();
                localStorage.setItem('current_conversation_id', conversationId);
                isShareMode.value = false;
                popoutConfig.questions = [];
                popoutConfig.visible = false;
                if (popoutRef.value && popoutRef.value.clearInputsAndSelections) {
                  popoutRef.value.clearInputsAndSelections();
                }
                // 返回恢复历史的Promise
                return restoreConversationHistory(conversationId);
              }
              return Promise.resolve(null); // 如果未登录，返回一个已解决的Promise
            })();

            // 使用 Promise.all 等待两个任务并行完成
            const [status, restored] = await Promise.all([statusPromise, historyPromise]);

            // 处理状态结果
            if (status.status === "paused") {
              isProcessing.value = false;
              console.log('[Draft] 检测到会话暂停:', status);
            } else {
              isProcessing.value = true;
              console.log('[Draft] 检测到会话继续:', status);
            }

            // 处理历史恢复结果
            if (authStore.isAuthenticated) {
              if (restored) {
                console.log(`[Draft] 成功恢复会话历史, ID: ${conversationId}`);
              } else {
                console.warn(`[Draft] 无法恢复会话历史, ID: ${conversationId}`);
              }
            }
          }
        } catch (error) {
          console.error(`[Draft] 获取会话数据出错:`, error);
        }
      } else {
        isProcessing.value = false;
      }

      isInitializing.value = false;
      
      // 从localStorage恢复左侧区域宽度设置
      const savedWidth = localStorage.getItem('draft_left_width');
      if (savedWidth) {
        leftWidth.value = parseFloat(savedWidth);
      }
      
      // 监听通用Canvas事件
      window.addEventListener('canvas-event', handleCanvasEvent as unknown as EventListener);
      
      // 为了向后兼容，保留原有的canvas-replace事件监听
      window.addEventListener('canvas-replace', ((event: CustomEvent) => {
        const { tagString } = event.detail;
        canvasMethods.replaceByTags(tagString);
      }) as unknown as EventListener);
      
      // 监听canvas-ready事件
      window.addEventListener('canvas-ready', handleCanvasReady as unknown as EventListener);
      
      // 监听澄清问题事件
      window.addEventListener('handle-clarification', ((event: CustomEvent) => {
        if (event.detail && event.detail.item) {
          processClarificationItem(event.detail.item);
        }
      }) as unknown as EventListener);
      
      // // 检查localStorage中是否有initialQuery，而不是URL参数
      // const initialQuery = localStorage.getItem('dair_initial_query');
      // if (initialQuery) {
      //   // 一旦组件挂载完成，自动提交初始查询
      //   setTimeout(() => {
      //     handleUserInput(initialQuery);
      //     // 使用后立即清除，避免页面刷新后重复使用
      //     localStorage.removeItem('dair_initial_query');
      //   }, 500); // 添加短暂延迟以确保组件完全加载
      // }
    });
    
    // // 添加路由更新守卫，处理URL参数变化但页面不重新加载的情况
    // onBeforeRouteUpdate(async (to, from) => {
    //   const newConversationId = to.query.conversation_id as string;
    //   const oldConversationId = from.query.conversation_id as string;
      
    //   // 只有当对话ID变化时才处理
    //   if (newConversationId && newConversationId !== currentConversationId.value) {
    //     console.log(`[Draft] 路由参数变化，新对话ID: ${newConversationId}`);
        
    //     // 检查用户是否已登录
    //     if (!authStore.isAuthenticated) {
    //       await authStore.verifyTokenValidity();
    //     }
        
    //     if (authStore.isAuthenticated) {
    //       localStorage.setItem('current_conversation_id', newConversationId);
    //       clearAllItems();
    //       currentConversationId.value = newConversationId;
    //       // 尝试从API恢复对话历史
    //       try {
    //         console.log(`[Draft] 尝试恢复会话历史, ID: ${newConversationId}`);
    //         const restored = await restoreConversationHistory(newConversationId);
    //         if (restored) {
    //           console.log(`[Draft] 成功恢复会话历史, ID: ${newConversationId}`);
    //         } else {
    //           console.warn(`[Draft] 无法恢复会话历史, ID: ${newConversationId}`);
    //         }
    //       } catch (error) {
    //         console.error(`[Draft] 恢复会话历史出错:`, error);
    //       }
    //     }
    //   }
    // });
    
    // 定义通信方法集合
    const canvasMethods = {
      // 替换文本
      replace: (searchText: string, replaceText: string) => {
        if (!canvasRef.value || !canvasRef.value.canvasEditor) {
          canvasState.lastError = '编辑器未就绪';
          return false;
        }
        
        try {
          const result = canvasRef.value.canvasEditor.replaceAllTextInMarkdown(searchText, replaceText);
          canvasState.lastActionResult = result;
          return result;
        } catch (error) {
          console.error('替换文本失败:', error);
          canvasState.lastError = '替换文本失败';
          return false;
        }
      },
      
      // 按标签替换
      replaceByTags: (tagString: string) => {
        if (!canvasRef.value || !canvasRef.value.handleTagReplacement) {
          canvasState.lastError = '编辑器未就绪';
          return false;
        }
        
        try {
          const result = canvasRef.value.handleTagReplacement(tagString);
          canvasState.lastActionResult = result;
          return result;
        } catch (error) {
          console.error('标签替换失败:', error);
          canvasState.lastError = '标签替换失败';
          return false;
        }
      },
      
      // 按标签替换（带动画效果）
      replaceByTagsWithAnimation: async (tagString: string, options = {}) => {
        if (!canvasRef.value || !canvasRef.value.handleAnimatedTagReplacement) {
          canvasState.lastError = '编辑器未就绪';
          return false;
        }
        
        try {
          canvasState.animationInProgress = true;
          const result = await canvasRef.value.handleAnimatedTagReplacement(tagString, options);
          canvasState.lastActionResult = result;
          canvasState.animationInProgress = false;
          return result;
        } catch (error) {
          console.error('动画替换失败:', error);
          canvasState.lastError = '动画替换失败';
          canvasState.animationInProgress = false;
          return false;
        }
      },
      
      // 获取当前内容
      getContent: () => {
        if (!canvasRef.value || !canvasRef.value.canvasEditor) {
          return canvasState.content;
        }
        
        try {
          return canvasRef.value.canvasEditor.getContent();
        } catch (error) {
          console.error('获取内容失败:', error);
          return canvasState.content;
        }
      },
      
      // 设置内容
      setContent: (content: string) => {
        if (!canvasRef.value || !canvasRef.value.canvasEditor) {
          canvasState.lastError = '编辑器未就绪';
          return false;
        }
        
        try {
          const result = canvasRef.value.canvasEditor.setContent(content);
          if (result) {
            canvasState.content = content;
          }
          return result;
        } catch (error) {
          console.error('设置内容失败:', error);
          canvasState.lastError = '设置内容失败';
          return false;
        }
      },
      
      // 更新编辑器就绪状态
      updateReady: (ready: boolean) => {
        canvasState.ready = ready;
      },
      
      // 检查是否正在执行动画
      isAnimating: () => {
        return canvasState.animationInProgress;
      },
      
      // 添加用户消息 - 暴露来自useDeepConversation的方法
      addUserMessage: (content: string) => {
        return addChatMessage(content, true);
      },
      
      // 添加助手消息 - 暴露来自useDeepConversation的方法
      addAssistantMessage: (content: string) => {
        return addChatMessage(content, false);
      },
      
      // 更新助手消息 - 暴露来自useDeepConversation的方法
      updateAssistantMessage: (messageId: string, content: string) => {
        return updateChatMessage(messageId, content);
      },
      
      // 处理暂停事件 
      handlePause: () => {
        console.log('Canvas暂停操作被触发 - 由canvasMethods提供的方法');
        // 不再在这里调用API，而是通过事件传递到DraftPage组件
      }
    };
    
    // 提供Canvas通信接口给子组件
    provide('canvasInterface', {
      state: canvasState,
      methods: canvasMethods,
      ref: canvasRef
    });
    
    // 提供内容偏好功能给子组件
    provide('toggleContentLike', toggleContentLike);
    
    // 通用事件处理函数
    const handleCanvasEvent = (event: CustomEvent) => {
      const { action, payload } = event.detail as CanvasEvent;
      
      switch (action) {
        case 'replace':
          if (payload.tagString) {
            if (payload.useAnimation && canvasMethods.replaceByTagsWithAnimation) {
              canvasMethods.replaceByTagsWithAnimation(
                payload.tagString, 
                payload.animationOptions
              );
            } else {
              canvasMethods.replaceByTags(payload.tagString);
            }
          } else if (payload.search && payload.replace) {
            canvasMethods.replace(payload.search, payload.replace);
          }
          break;
        
        case 'message':
          if (payload.content) {
            if (payload.isUser) {
              canvasMethods.addUserMessage(payload.content);
            } else {
              canvasMethods.addAssistantMessage(payload.content);
            }
          }
          break;
          
        // 可以扩展更多操作类型
        default:
          console.warn('未知的Canvas操作:', action);
      }
    };
    
    // 从canvas/index.vue接收的内容更新事件
    const handleContentUpdate = (content: string) => {
      console.log(`[DEBUG] Draft页面: 接收到编辑器内容更新，长度: ${content.length}`);
      console.log(`[DEBUG] Draft页面: 内容预览: ${content.substring(0, 30)}...`);
      canvasState.content = content;
    };
    
    // 处理来自Canvas的暂停事件
    const handlePauseStream = () => {
      console.log('DraftPage: 接收到暂停请求');
      
      if (!currentConversationId.value) {
        console.warn('DraftPage: 无法暂停会话，conversationId为空');
        return;
      }
      
      try {
        // 使用researchApi的pauseResearch方法暂停会话
        researchApi.pauseResearch(currentConversationId.value)
          .then(response => {
            console.log(`[Draft] 成功暂停会话 ID: ${currentConversationId.value}`, response);
          })
          .catch(error => {
            console.error(`[Draft] 暂停会话出错:`, error);
          });
      } catch (error) {
        console.error(`[Draft] 调用暂停API出错:`, error);
      }
    };
    
    // 监听编辑器就绪事件
    const handleCanvasReady = () => {
      canvasState.ready = true;
      // 获取初始内容
      if (canvasRef.value && canvasRef.value.canvasEditor) {
        canvasState.content = canvasMethods.getContent();
      }
    };
    
    // 处理编辑命令
    const handleEditCommand = (formattedMessage: string) => {
      console.log('DraftPage: 接收到编辑命令，转发到编辑请求处理器');
      // 统一使用编辑请求处理器
      handleEditRequest(formattedMessage);
    };
    
    // 处理编辑完整报告
    const handleEditReport = (reportContent: string) => {
      console.log('DraftPage: 接收到编辑完整报告请求');
      
      // 构造带有编辑指示的消息
      const formattedMessage = `以下是我修改后的研究报告内容：\n\n${reportContent}`;
      
      // 使用统一的编辑请求处理器
      handleEditRequest(formattedMessage);
    };
    
    // 处理版本切换
    const handleSwitchVersion = (editorId: string, versionIndex: number) => {
      console.log('DraftPage: 处理版本切换', editorId, versionIndex);
      // 调用useDeepConversation的switchEditorVersion方法
      switchEditorVersion(editorId, versionIndex);
    };
    
    // 创建新对话
    const createNewChat = () => {
      // 清除当前所有内容
      clearAllItems();
      isShareMode.value = false;
      // 清空弹出框配置
      popoutConfig.questions = [];
      popoutConfig.visible = false;
      // 清空已保存的用户输入和选择
      if (popoutRef.value && popoutRef.value.clearInputsAndSelections) {
        popoutRef.value.clearInputsAndSelections();
      }
      
      // 清空弹出框配置
      popoutConfig.questions = [];
      popoutConfig.visible = false;
      // 清空已保存的用户输入和选择
      if (popoutRef.value && popoutRef.value.clearInputsAndSelections) {
        popoutRef.value.clearInputsAndSelections();
      }
      
      // 跳转到没有conversation_id的draft页面
      router.push({ path: '/draft' }).then(() => {
        // 跳转完成后刷新页面
        // window.location.reload();
      });
    };

    // 添加登录提示对话框状态
    const showLoginDialog = ref(false);
    
    // 重写用户输入处理逻辑，支持新建对话流程
    const handleWrappedUserInput = async (text: string) => {
      // 检查用户是否已登录
      if (!authStore.isAuthenticated) {
        showLoginDialog.value = true;
        return;
      }
      
      await handleUserInput(text);
    };
    
    // 导航到登录页面
    const navigateToLogin = () => {
      router.push('/login');
    };
    
    // 添加一个处理Clarification项目的函数，用于websocket回调或其他地方调用
    const processClarificationItem = (item: any) => {
      console.log(`[Draft] 处理澄清问题项目:`, item);
      
      // 确保是有效的澄清问题项目
      if (!item || item.type !== 'clarification' || !Array.isArray(item.questions) || item.isAnswered) {
        console.warn('[Draft] 无效的澄清问题项目或已回答:', item);
        return;
      }
      
      // 更新弹出框中显示的问题
      updatePopoutQuestion(item as ClarificationItem);
      
      // 如果弹出框是最小化状态，自动展开
      if (popoutRef.value && popoutRef.value.isMinimized) {
        popoutRef.value.toggleMinimized();
      }
    };
    
    // 更新弹出框中的问题
    const updatePopoutQuestion = (clarificationItem: ClarificationItem) => {
      if (clarificationItem.type !== 'clarification') return;
      
      // 获取当前活动问题索引
      const activeIndex = clarificationItem.activeQuestionIndex || 0;
      
      // 移除所有现有问题，因为每轮澄清问题ID都是从0开始
      popoutConfig.questions = [];
      
      // 添加澄清项目中的所有问题
      clarificationItem.questions.forEach((question, index) => {
        const newQuestion = {
          id: `${clarificationItem.id}_${index}`,
          content: question.content,
          options: question.options || [],
          activeQuestionIndex: index,
          totalQuestions: clarificationItem.questions.length
        };
        console.log(`[Draft] 添加澄清问题:`, newQuestion);
        popoutConfig.questions.push(newQuestion);
      });
      
      // 如果弹出框是最小化状态，自动展开
      if (popoutRef.value && popoutRef.value.isMinimized) {
        popoutRef.value.toggleMinimized();
      }
    };
    
    // 处理设置更新
    const handleUpdateSettings = (event: any) => {
      console.log('接收到设置更新:', event);
      const preferences = {
        action_selection_model: {
          name: event.settings.thinkingModel,
          temperature: 0.6,
          max_tokens: 20000
        },
        report_editing_model: {
          name: event.settings.draftingModel,
          temperature: 0.6,
          max_tokens: 20000
        }
      }
      console.log('发送设置更新:', preferences);
      researchApi.setModelsPreferences(preferences);
    };
    
    // 添加 handlePopoutClose 函数
    const handlePopoutClose = () => {
      console.log('弹出框关闭');
      popoutConfig.visible = false;
    };
    
    // 分享相关状态
    const showShareDialog = ref(false);
    
    // 计算分享链接
    const shareLink = computed(() => {
      if (!currentConversationId.value) return '';
      const baseUrl = window.location.origin;
      return `${baseUrl}/draft?conversation_id=${currentConversationId.value}&share=true`;
    });
    
    // 处理文件上传
    const handleFileUpload = (file: File) => {
      addUploadedFile(file);
    };

    // 处理文件删除
    const handleFileRemove = (index: number) => {
      removeUploadedFile(index);
    };

    const showReportFeedback = ref(false);
    // 打开反馈界面
    const openReportFeedback = async () => {
    showReportFeedback.value = true;
    console.log('反馈组件应该显示了');
    console.log('当前会话ID:', currentConversationId.value);
};
    // 关闭反馈界面
    const closeReportFeedback = () => {
      showReportFeedback.value = false;
    };
    // 处理反馈提交
    const handleFeedbackSubmitted = (feedback) => {
      console.log('用户提交了反馈:', feedback);
      // 可以在这里添加提交成功的提示
    };
    // 处理认知搜索开关
    const handleCognitionSwitch = (value: boolean) => {
      console.log('[Deep Conversation] 认知搜索开关:', value);
      updateResearchStatus(value);
    };
    
    // 处理搜索开关
    const handleSearchSwitch = (value: boolean) => {
      console.log('[Deep Conversation] 搜索开关:', value);
      updateSearchStatus(value);
    };
    
    // 预览上下文相关状态
    const showPreviewContext = ref(false);
    const previewContext = reactive({
      contextId: '',
      conversationId: '',
      title: '',
      fileType: '',
      contentUrl: '',
      content: ''
    });

    // 处理文件预览
    const handleFilePreview = async (file: ExtendedFile) => {
      try {
        if ('isRemote' in file && file.isRemote) {
          // 如果是远程文件（FileDescription），需要请求后端
          if (!file.contextId) {
            console.error('远程文件缺少 contextId');
            return;
          }
          
          const response = await researchApi.requestContext(file.contextId, currentConversationId.value);
          if (response.status === 200 && response.data instanceof Blob) {
            // 从响应头中获取文件类型
            const contentType = response.headers['content-type'] || file.type || 'application/octet-stream';
            const blob = new Blob([response.data], { type: contentType });
            const fileUrl = URL.createObjectURL(blob);
            
            // 如果是纯文本或markdown文件，读取其内容
            let content = '';
            if (contentType.includes('plain') || contentType.includes('markdown')) {
              content = await blob.text();
            }
            
            Object.assign(previewContext, {
              contextId: file.contextId,
              conversationId: currentConversationId.value,
              title: file.name,
              fileType: contentType,
              contentUrl: fileUrl,
              content: content
            });
            
            // 显示预览对话框
            showPreviewContext.value = true;
          } else {
            console.error('预览文件失败: 无效的响应数据');
          }
        } else {
          // 如果是本地文件（File），直接创建预览URL
          const fileUrl = URL.createObjectURL(file as File);
          
          // 如果是纯文本或markdown文件，读取其内容
          let content = '';
          if ((file as File).type.includes('plain') || (file as File).type.includes('markdown')) {
            content = await (file as File).text();
          }
          
          Object.assign(previewContext, {
            contextId: '',
            conversationId: currentConversationId.value,
            title: (file as File).name,
            fileType: (file as File).type,
            contentUrl: fileUrl,
            content: content
          });
          
          // 显示预览对话框
          showPreviewContext.value = true;
        }
      } catch (error) {
        console.error('预览文件失败:', error);
      }
    };

    // 处理预览上下文关闭
    const handlePreviewContextClose = () => {
      showPreviewContext.value = false;
      // 清空预览数据
      Object.assign(previewContext, {
        contextId: '',
        conversationId: '',
        title: '',
        fileType: '',
        contentUrl: '',
        content: ''
      });
    };
    
    onUnmounted(() => {
      // 移除事件监听器
      window.removeEventListener('canvas-event', handleCanvasEvent as unknown as EventListener);
      window.removeEventListener('canvas-replace', (() => {}) as unknown as EventListener);
      window.removeEventListener('canvas-ready', handleCanvasReady as unknown as EventListener);
      window.removeEventListener('handle-clarification', (() => {}) as unknown as EventListener);
    });
    
    return {
      // 画布和界面
      canvasRef,
      popoutRef,
      canvasItems,
      interfaceItems,
      handleUserInput: handleWrappedUserInput,
      updateEditorContent,
      toggleThoughtExpanded,
      // 其他方法
      chatError,
      isProcessing,
      handleContentUpdate,
      handleCanvasReady,
      handlePauseStream,
      handleEditCommand,
      handleEditReport,
      currentConversationId,
      handleSelectHistory,
      // 拖动调整相关
      leftWidth,
      startResize,
      handleResize,
      stopResize,
      createNewChat,
      // 弹出框相关
      popoutConfig,
      handlePopoutSubmit,
      handlePopoutClose,
      
      // 悬浮球相关
      handlePauseRequest,
      handleResumeRequest,
      togglePopout,
      
      // 设置相关
      handleUpdateSettings,
      isShareMode,
      restoreSharedConversationHistory,
      showShareDialog,
      shareLink,
      isEmptyPage,
      showLoginDialog,
      navigateToLogin,
      handleFileUpload,
      handleFileRemove,
      uploadedFiles,
      loadingStates,
      showReportFeedback,
      openReportFeedback,
      closeReportFeedback,
      handleFeedbackSubmitted,
      isCognitionEnabled,
      handleCognitionSwitch,
      showPreviewContext,
      previewContext,
      handlePreviewContextClose,
      handleFilePreview,
      handleSwitchVersion,
      editorVersionsInfo,
      // Token Budget 相关
      tokenBudgetState,
      closeTokenBudgetDialog,
      refreshPage,
      resetTokenBudgetState,
      // 偏好
      userPreferences,
      userPreferencesComponent,
      handlePreferenceChange,
      // 搜索相关
      isSearchEnabled,
      updateSearchStatus,
      handleSearchSwitch,
      isInitializing
    };
  }
}
</script>

<style scoped>
.draft-page {
  height: 100vh; /* 使用视口高度 */
  width: 100vw;
  overflow: visible; /* 防止出现滚动条 */
  position: fixed; /* 固定位置 */
  top: 0;
  left: 0;
  background-color: #e8e6f6; /* 修改背景色 */
  font-size: 0.9rem; /* 全局缩小字体 */
  display: flex;
  flex-direction: column; /* 纵向排列导航栏和内容 */
}

/* 导航栏样式 */
.nav-bar {
  width: 100%;
  height: 48px;
  background-color: #e8e6f6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  border-bottom: 1px solid rgba(101, 76, 140, 0.1);
  z-index: 100;
}

.logo-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.logo-container:hover {
  opacity: 0.8;
}

.q-logo {
  width: 36px;
  height: 36px;
  background-color: rgba(101, 76, 140, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #654C8C;
  font-size: 24px;
  font-weight: bold;
  margin-right: 12px;
}

.logo-text {
  color: #654C8C;
  font-size: 20px;
  font-weight: 500;
}

.nav-description {
  font-size: 14px;
  color: #654C8C;
  font-style: italic;
  opacity: 0.85;
  margin-right: 16px; /* 添加右边距，将文字往里挪 */
}

.nav-tools {
  display: flex;
  align-items: center;
  margin-left: auto; /* 将工具栏推到右侧 */
}

.nav-tools > * {
  margin-left: 8px; /* 工具栏内部组件间距 */
}

.draft-layout {
  display: flex;
  flex: 1; /* 让内容区域填充剩余空间 */
  width: 100%;
  overflow: hidden; /* 修改这里，防止整体出现滚动条 */
  position: relative; /* 添加相对定位以支持拖动 */
}

.canvas-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  overflow-y: auto; /* 添加垂直滚动条 */
  transition: width 0.1s ease; /* 添加平滑过渡效果 */
  
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(101, 76, 140, 0.2) transparent;
}

/* 针对Webkit浏览器的滚动条样式 */
.canvas-wrapper::-webkit-scrollbar {
  width: 4px;
}

.canvas-wrapper::-webkit-scrollbar-track {
  background: transparent;
  margin: 4px 0;
}

.canvas-wrapper::-webkit-scrollbar-thumb {
  background-color: rgba(101, 76, 140, 0.2);
  border-radius: 2px;
}

.canvas-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: rgba(101, 76, 140, 0.4);
}

.resizer {
  width: 14px;
  background-color: transparent;
  height: 100%;
  position: relative;
  cursor: col-resize;
  z-index: 10;
  user-select: none;
  display: flex;
  justify-content: center;
  align-items: center;
}

.resizer-handle {
  width: 3px;
  height: 40%;
  background-color: rgba(101, 76, 140, 0); /* 初始状态透明 */
  border-radius: 2px;
  transition: background-color 0.3s, width 0.2s;
}

.resizer:hover .resizer-handle {
  width: 5px;
  background-color: rgba(101, 76, 140, 0.3);
}

.resizer:active .resizer-handle {
  width: 6px;
  background-color: rgba(101, 76, 140, 0.5);
}

.interface-wrapper {
  flex-shrink: 0; /* 防止右侧栏被压缩 */
  height: calc(100vh - 48px); /* 减去导航栏高度 */
  overflow-y: auto; /* 添加垂直滚动条 */
  transition: width 0.1s ease; /* 添加平滑过渡效果 */
  
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(101, 76, 140, 0.2) transparent;
}

/* 针对Webkit浏览器的滚动条样式 */
.interface-wrapper::-webkit-scrollbar {
  width: 4px;
}

.interface-wrapper::-webkit-scrollbar-track {
  background: transparent;
  margin: 4px 0;
}

.interface-wrapper::-webkit-scrollbar-thumb {
  background-color: rgba(101, 76, 140, 0.2);
  border-radius: 2px;
}

.interface-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: rgba(101, 76, 140, 0.4);
}

/* 确保 CanvasComponent 填充整个空间 */
:deep(.v-container) {
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 8px; /* 减小容器内边距 */
}

/* 防止拖动时选中文本 */
.no-select {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  cursor: col-resize !important;
}

/* 针对小屏幕的媒体查询 */
@media (max-width: 1300px) {
  .draft-page {
    font-size: 0.85rem; /* 更小的字体 */
  }
  
  .canvas-wrapper {
    width: 65%; /* 调整比例 */
    padding: 0 12px; /* 更小的内边距 */
  }
  
  .interface-wrapper {
    width: 35%;
  }
}

/* 针对非常小的屏幕 */
@media (max-width: 1100px) {
  .canvas-wrapper {
    width: 60%;
    padding: 0 8px;
  }
  
  .interface-wrapper {
    width: 40%;
  }
  
  :deep(.v-container) {
    padding: 6px;
  }
}

/* 工具按钮统一样式 */
.tool-btn {
  margin: 0 4px;
  opacity: 0.85;
  transition: opacity 0.2s ease;
}

.tool-btn:hover {
  opacity: 1;
}

.canvas-container {
  flex: 1;
  min-height: 0; /* 重要：防止 flex 子项溢出 */
  overflow: auto; /* 如果内容过多，允许滚动 */
}

.feedback-button-container {
  padding: 6px;
  background-color: #f7f6fb;
  border-top: 1px solid rgba(217, 214, 239, 0.5);
  display: flex;
  justify-content: left;
}

.feedback-button {
  width: 100%;
  max-width: 200px;
}
</style>

<!-- 添加全局样式 -->
<style>
/* 设置文本选中时的样式 */
::selection {
  background-color: #7c4dff !important; /* 紫色背景 */
  color: white !important; /* 白色文字 */
}

/* 兼容 Firefox */
::-moz-selection {
  background-color: #7c4dff !important; /* 紫色背景 */
  color: white !important; /* 白色文字 */
}

/* 设置编辑器中选中文本的样式 */
.ProseMirror ::selection {
  background-color: #7c4dff !important;
  color: white !important;
}

.ProseMirror ::-moz-selection {
  background-color: #7c4dff !important;
  color: white !important;
}

/* 设置编辑器的光标颜色 */
.ProseMirror .cursor {
  border-left-color: #7c4dff !important;
}

/* 全局UI大小调整 */
.v-btn {
  font-size: 0.85rem;
}

.v-card-title {
  font-size: 1rem !important;
}

.v-card-subtitle {
  font-size: 0.85rem !important;
}

.v-card-text {
  font-size: 0.85rem !important;
}

.v-text-field input, .v-textarea textarea {
  font-size: 0.85rem !important;
}

.v-label {
  font-size: 0.85rem !important;
}

/* 减小图标大小 */
.v-icon--size-default {
  font-size: 1.25rem !important;
}

/* 针对小屏幕的媒体查询 */
@media (max-width: 1300px) {
  .v-btn {
    font-size: 0.8rem;
  }
  
  .v-card-title {
    font-size: 0.95rem !important;
  }
  
  .v-card-subtitle {
    font-size: 0.8rem !important;
  }
  
  .v-card-text {
    font-size: 0.8rem !important;
  }
  
  .v-text-field input, .v-textarea textarea {
    font-size: 0.8rem !important;
  }
  
  .v-label {
    font-size: 0.8rem !important;
  }
  
  .v-icon--size-default {
    font-size: 1.15rem !important;
  }
  .feedback-button-container {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 100;
  }

  .feedback-button {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}
</style>