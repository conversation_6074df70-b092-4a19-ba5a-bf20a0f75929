<template>
  <div class="ppt-review-page">
    <v-container class="pa-6">
      <v-row>
        <v-col cols="12">
          <h1 class="text-h4 mb-4">PPT评审Agent</h1>
          <p class="text-subtitle-1 mb-6">专业VC投资人视角的PPT评审服务</p>
        </v-col>
      </v-row>

      <!-- 文件上传区域 -->
      <v-row v-if="!currentSession">
        <v-col cols="12" md="8">
          <v-card elevation="2" class="pa-6">
            <v-card-title>上传PPT文件</v-card-title>
            <v-card-text>
              <v-file-input
                v-model="selectedFile"
                label="选择PPT文件"
                accept=".ppt,.pptx"
                prepend-icon="mdi-file-powerpoint"
                show-size
                @update:model-value="onFileSelected"
              ></v-file-input>

              <v-row class="mt-4">
                <v-col cols="12" md="6">
                  <v-select
                    v-model="reviewOptions.language"
                    label="评审语言"
                    :items="languageOptions"
                    dense
                  ></v-select>
                </v-col>
                <v-col cols="12" md="6">
                  <v-select
                    v-model="reviewOptions.review_depth"
                    label="评审深度"
                    :items="depthOptions"
                    dense
                  ></v-select>
                </v-col>
              </v-row>

              <v-textarea
                v-model="reviewOptions.additional_context"
                label="额外上下文信息（可选）"
                rows="3"
                no-resize
              ></v-textarea>

              <v-btn
                :disabled="!selectedFile || isUploading"
                :loading="isUploading"
                color="primary"
                large
                @click="startReview"
              >
                <v-icon left>mdi-rocket-launch</v-icon>
                开始评审
              </v-btn>
            </v-card-text>
          </v-card>
        </v-col>

        <v-col cols="12" md="4">
          <v-card elevation="1" class="pa-4">
            <v-card-title class="text-h6">API测试</v-card-title>
            <v-card-text>
              <v-btn
                @click="testAuth"
                color="info"
                class="mb-2"
                block
                small
              >
                测试认证状态
              </v-btn>
              <v-btn
                @click="testTemplates"
                color="success"
                class="mb-2"
                block
                small
              >
                测试模板API
              </v-btn>
              <v-btn
                @click="testHistory"
                color="warning"
                block
                small
              >
                测试历史API
              </v-btn>
              <v-btn
                @click="createTestFile"
                color="orange"
                class="mt-2"
                block
                small
              >
                创建测试文件
              </v-btn>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

      <!-- 评审进度区域 -->
      <v-row v-if="currentSession">
        <v-col cols="12">
          <v-card elevation="2" class="pa-6">
            <v-card-title class="d-flex align-center">
              <v-icon 
                :icon="currentSession.status === 'completed' ? 'mdi-check-circle' : 
                      currentSession.status === 'failed' ? 'mdi-alert-circle' : 
                      'mdi-loading mdi-spin'" 
                :color="currentSession.status === 'completed' ? 'success' : 
                       currentSession.status === 'failed' ? 'error' : 'primary'"
                class="mr-2"
              ></v-icon>
              评审进度
              <v-chip 
                :color="currentSession.status === 'completed' ? 'success' : 
                       currentSession.status === 'failed' ? 'error' : 
                       currentSession.status === 'uploading' ? 'info' : 'warning'"
                size="small"
                class="ml-3"
              >
                {{ 
                  currentSession.status === 'completed' ? '已完成' :
                  currentSession.status === 'failed' ? '失败' :
                  currentSession.status === 'uploading' ? '上传中' : '处理中'
                }}
              </v-chip>
            </v-card-title>
            <v-card-text>
              <div class="mb-4">
                <strong>会话ID:</strong> 
                <span class="font-mono">{{ currentSession.session_id }}</span>
                <v-btn 
                  icon="mdi-content-copy" 
                  size="x-small" 
                  variant="text"
                  @click="copyToClipboard(currentSession.session_id)"
                  class="ml-1"
                ></v-btn>
              </div>
              
              <div class="mb-4">
                <strong>当前阶段:</strong> 
                <span class="text-primary">{{ currentSession.current_stage }}</span>
              </div>
              
              <v-progress-linear
                :model-value="currentSession.progress"
                height="25"
                :color="currentSession.status === 'completed' ? 'success' : 
                       currentSession.status === 'failed' ? 'error' : 'primary'"
                class="mb-4"
                rounded
              >
                <template v-slot:default="{ value }">
                  <strong class="text-white">{{ Math.ceil(value) }}%</strong>
                </template>
              </v-progress-linear>

              <div class="mb-4 p-3 rounded" :class="currentSession.status === 'failed' ? 'bg-error-lighten-5' : 'bg-info-lighten-5'">
                <strong>状态消息:</strong> {{ currentSession.message }}
              </div>

              <div class="d-flex flex-wrap gap-2">
                <v-btn
                  @click="refreshStatus"
                  color="info"
                  :disabled="isRefreshing"
                  :loading="isRefreshing"
                  prepend-icon="mdi-refresh"
                >
                  刷新状态
                </v-btn>

                <v-btn
                  v-if="currentSession.status === 'completed'"
                  @click="getReport"
                  color="success"
                  prepend-icon="mdi-file-document"
                >
                  获取报告
                </v-btn>

                <v-btn
                  @click="resetSession"
                  color="secondary"
                  prepend-icon="mdi-restart"
                >
                  重新开始
                </v-btn>
                
                <v-btn
                  v-if="currentSession.status === 'failed'"
                  @click="resetSession"
                  color="error"
                  variant="outlined"
                  prepend-icon="mdi-alert-circle"
                >
                  清除失败状态
                </v-btn>
              </div>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

      <!-- 评审报告区域 -->
      <v-row v-if="reviewReport">
        <v-col cols="12">
          <v-card elevation="2" class="pa-6">
            <v-card-title class="text-h4 text-center mb-6">
              <v-icon left>mdi-file-document-outline</v-icon>
              PPT评审报告
            </v-card-title>
            
            <!-- 报告基本信息 -->
            <div v-if="reviewReport.session_id" class="mb-6">
              <v-chip color="primary" variant="outlined" class="mr-2">
                会话ID: {{ reviewReport.session_id }}
              </v-chip>
              <v-chip color="secondary" variant="outlined" v-if="reviewReport.report_date">
                报告时间: {{ new Date(reviewReport.report_date).toLocaleString() }}
              </v-chip>
            </div>

            <!-- 总体印象 -->
            <v-card v-if="reviewReport.overall_impression" class="mb-4" variant="outlined">
              <v-card-title class="bg-blue-50">
                <v-icon left color="blue">mdi-eye</v-icon>
                总体印象
              </v-card-title>
              <v-card-text class="text-h6">
                {{ reviewReport.overall_impression }}
              </v-card-text>
            </v-card>

            <!-- 主要优势 -->
            <v-card v-if="reviewReport.top_strengths && reviewReport.top_strengths.length > 0" class="mb-4" variant="outlined">
              <v-card-title class="bg-green-50">
                <v-icon left color="green">mdi-thumb-up</v-icon>
                主要优势
              </v-card-title>
              <v-card-text>
                <v-list>
                  <v-list-item v-for="(strength, index) in reviewReport.top_strengths" :key="index">
                    <template v-slot:prepend>
                      <v-icon color="green">mdi-check-circle</v-icon>
                    </template>
                    <v-list-item-title>{{ strength }}</v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-card-text>
            </v-card>

            <!-- 主要关注点 -->
            <v-card v-if="reviewReport.top_concerns && reviewReport.top_concerns.length > 0" class="mb-4" variant="outlined">
              <v-card-title class="bg-orange-50">
                <v-icon left color="orange">mdi-alert</v-icon>
                主要关注点
              </v-card-title>
              <v-card-text>
                <v-list>
                  <v-list-item v-for="(concern, index) in reviewReport.top_concerns" :key="index">
                    <template v-slot:prepend>
                      <v-icon color="orange">mdi-alert-circle</v-icon>
                    </template>
                    <v-list-item-title>{{ concern }}</v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-card-text>
            </v-card>

            <!-- 关键问题 -->
            <v-card v-if="reviewReport.key_questions && reviewReport.key_questions.length > 0" class="mb-4" variant="outlined">
              <v-card-title class="bg-purple-50">
                <v-icon left color="purple">mdi-help-circle</v-icon>
                投资人关键问题
              </v-card-title>
              <v-card-text>
                <v-list>
                  <v-list-item v-for="(question, index) in reviewReport.key_questions" :key="index">
                    <template v-slot:prepend>
                      <v-icon color="purple">mdi-help</v-icon>
                    </template>
                    <v-list-item-title>{{ question }}</v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-card-text>
            </v-card>

            <!-- 逐页评审 -->
            <v-card v-if="reviewReport.page_reviews && reviewReport.page_reviews.length > 0" class="mb-4" variant="outlined">
              <v-card-title class="bg-indigo-50">
                <v-icon left color="indigo">mdi-book-open-page-variant</v-icon>
                逐页评审详情
              </v-card-title>
              <v-card-text>
                <v-expansion-panels variant="accordion">
                  <v-expansion-panel v-for="(page, index) in reviewReport.page_reviews" :key="index">
                    <v-expansion-panel-title>
                      <div class="d-flex align-center">
                        <v-chip size="small" color="indigo" class="mr-3">
                          第{{ page.slide_number }}页
                        </v-chip>
                        <span class="font-weight-medium">{{ page.title || '无标题' }}</span>
                      </div>
                    </v-expansion-panel-title>
                    <v-expansion-panel-text>
                      <div class="pa-3">
                        <div v-if="page.analysis" class="mb-3">
                          <strong>分析内容：</strong>
                          <p class="mt-2">{{ page.analysis }}</p>
                        </div>
                        <div v-if="page.strengths && page.strengths.length > 0" class="mb-3">
                          <strong class="text-green">优点：</strong>
                          <ul class="mt-1">
                            <li v-for="strength in page.strengths" :key="strength">{{ strength }}</li>
                          </ul>
                        </div>
                        <div v-if="page.weaknesses && page.weaknesses.length > 0" class="mb-3">
                          <strong class="text-orange">需要改进：</strong>
                          <ul class="mt-1">
                            <li v-for="weakness in page.weaknesses" :key="weakness">{{ weakness }}</li>
                          </ul>
                        </div>
                        <div v-if="page.questions && page.questions.length > 0">
                          <strong class="text-purple">相关问题：</strong>
                          <ul class="mt-1">
                            <li v-for="question in page.questions" :key="question">{{ question }}</li>
                          </ul>
                        </div>
                      </div>
                    </v-expansion-panel-text>
                  </v-expansion-panel>
                </v-expansion-panels>
              </v-card-text>
            </v-card>

            <!-- 建议 -->
            <v-card v-if="reviewReport.recommendations" class="mb-4" variant="outlined">
              <v-card-title class="bg-teal-50">
                <v-icon left color="teal">mdi-lightbulb</v-icon>
                改进建议
              </v-card-title>
              <v-card-text>
                <div v-if="Array.isArray(reviewReport.recommendations)">
                  <v-list>
                    <v-list-item v-for="(rec, index) in reviewReport.recommendations" :key="index">
                      <template v-slot:prepend>
                        <v-icon color="teal">mdi-lightbulb-outline</v-icon>
                      </template>
                      <v-list-item-title>{{ rec }}</v-list-item-title>
                    </v-list-item>
                  </v-list>
                </div>
                <div v-else-if="typeof reviewReport.recommendations === 'object'">
                  <v-row>
                    <v-col v-for="(recList, category) in reviewReport.recommendations" :key="category" cols="12" md="4">
                      <v-card variant="outlined" class="pa-3">
                        <v-card-title class="text-subtitle-1">{{ String(category).replace('_', ' ').toUpperCase() }}</v-card-title>
                        <v-list v-if="Array.isArray(recList)" density="compact">
                          <v-list-item v-for="rec in recList" :key="rec" class="px-0">
                            <template v-slot:prepend>
                              <v-icon size="small" color="teal">mdi-circle-small</v-icon>
                            </template>
                            <v-list-item-title class="text-body-2">{{ rec }}</v-list-item-title>
                          </v-list-item>
                        </v-list>
                      </v-card>
                    </v-col>
                  </v-row>
                </div>
              </v-card-text>
            </v-card>

            <!-- 详细分析报告 -->
            <v-card v-if="reviewReport.final_report_text" class="mb-4" variant="outlined">
              <v-card-title class="bg-grey-50">
                <v-icon left>mdi-text-long</v-icon>
                详细分析报告
              </v-card-title>
              <v-card-text>
                <div style="white-space: pre-wrap; line-height: 1.6;">{{ reviewReport.final_report_text }}</div>
              </v-card-text>
            </v-card>

            <!-- 操作按钮 -->
            <div class="text-center mt-6">
              <v-btn @click="exportReport" color="primary" variant="outlined" class="mr-3">
                <v-icon left>mdi-download</v-icon>
                导出报告
              </v-btn>
              <v-btn @click="copyToClipboard(JSON.stringify(reviewReport, null, 2))" color="secondary" variant="outlined" class="mr-3">
                <v-icon left>mdi-content-copy</v-icon>
                复制原始数据
              </v-btn>
              <v-btn @click="resetSession" color="info" variant="outlined">
                <v-icon left>mdi-refresh</v-icon>
                开始新评审
              </v-btn>
            </div>

            <!-- 原始数据展示（可折叠） -->
            <v-expansion-panels class="mt-6" variant="accordion">
              <v-expansion-panel>
                <v-expansion-panel-title>
                  <v-icon left>mdi-code-json</v-icon>
                  查看原始JSON数据
                </v-expansion-panel-title>
                <v-expansion-panel-text>
                  <pre style="white-space: pre-wrap; font-family: monospace; font-size: 12px; background-color: #f5f5f5; padding: 16px; border-radius: 4px;">{{ JSON.stringify(reviewReport, null, 2) }}</pre>
                </v-expansion-panel-text>
              </v-expansion-panel>
            </v-expansion-panels>
          </v-card>
        </v-col>
      </v-row>

      <!-- 调试信息 -->
      <v-row v-if="debugInfo">
        <v-col cols="12">
          <v-card elevation="1" class="pa-4">
            <v-card-title class="text-h6">调试信息</v-card-title>
            <v-card-text>
              <pre style="white-space: pre-wrap; font-size: 12px;">{{ debugInfo }}</pre>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { 
  startPPTReview,
  getPPTReviewStatus,
  getPPTReviewReport,
  getPPTTemplates,
  getPPTReviewHistory,
  type PPTReviewRequest 
} from '@/api/ppt'

// 响应式数据
const selectedFile = ref<File | null>(null)
const isUploading = ref(false)
const isRefreshing = ref(false)
const currentSession = ref<any>(null)
const reviewReport = ref<any>(null)
const debugInfo = ref<string>('')

// 评审选项
const reviewOptions = ref<PPTReviewRequest>({
  language: 'zh',
  review_depth: 'standard',
  additional_context: ''
})

// 选项数据
const languageOptions = [
  { title: '中文', value: 'zh' },
  { title: 'English', value: 'en' }
]

const depthOptions = [
  { title: '基础评审', value: 'basic' },
  { title: '标准评审', value: 'standard' },
  { title: '深度评审', value: 'comprehensive' }
]

// 文件选择处理
function onFileSelected(files: File[] | File | null) {
  addDebugInfo(`文件选择事件触发: ${typeof files}, ${Array.isArray(files) ? files.length : 'not array'}`)
  
  let file: File | null = null
  
  // 处理不同的返回类型
  if (Array.isArray(files) && files.length > 0) {
    // 如果是数组，取第一个文件
    file = files[0]
    addDebugInfo(`从数组中获取文件: ${file?.name}`)
  } else if (files && typeof files === 'object' && files.constructor === File) {
    // 如果是单个 File 对象
    file = files as File
    addDebugInfo(`直接获取文件对象: ${file?.name}`)
  } else if (files && typeof files === 'object' && 'name' in files) {
    // 如果是包含 File 属性的对象
    file = files as File
    addDebugInfo(`获取包含name属性的对象: ${file?.name}`)
  }
  
  if (file && file.name && file.size > 0) {
    selectedFile.value = file
    console.log('Selected file:', file.name, file.size, 'bytes', file.type)
    addDebugInfo(`✅ 文件选择成功: ${file.name} (${file.size} bytes, ${file.type})`)
  } else {
    selectedFile.value = null
    addDebugInfo(`❌ 文件选择失败: file=${!!file}, name=${file?.name}, size=${file?.size}`)
  }
}

// 添加调试信息
function addDebugInfo(info: string) {
  const timestamp = new Date().toLocaleTimeString()
  debugInfo.value += `[${timestamp}] ${info}\n`
}

// 开始评审
async function startReview() {
  addDebugInfo('🚀 startReview 函数被调用')
  
  if (!selectedFile.value) {
    addDebugInfo('❌ 错误: 未选择文件')
    alert('请先选择PPT文件！')
    return
  }

  const file = selectedFile.value
  if (!file.name) {
    addDebugInfo('❌ 错误: 文件对象无效')
    alert('文件对象无效，请重新选择文件！')
    return
  }

  isUploading.value = true
  addDebugInfo(`📤 开始上传和评审文件: ${file.name} (${file.size} bytes)`)

  try {
    // 添加上传状态提示
    currentSession.value = {
      session_id: '上传中...',
      status: 'uploading',
      progress: 0,
      current_stage: 'uploading',
      message: '正在上传文件，请稍候...'
    }

    const response = await startPPTReview(file, reviewOptions.value)
    
    addDebugInfo(`✅ 评审启动成功: ${JSON.stringify(response)}`)
    currentSession.value = {
      session_id: response.session_id,
      status: response.status,
      progress: 10,
      current_stage: 'initializing',
      message: response.message || '评审任务已启动'
    }

    // 立即开始轮询状态
    addDebugInfo('🔄 开始轮询评审状态...')
    startStatusPolling()
    
  } catch (error: any) {
    // 清除上传状态
    currentSession.value = null
    
    // 专门处理402错误（token预算不足）
    if (error?.response?.status === 402) {
      const errorMsg = error?.response?.data?.detail || 'Token使用额度已用尽，请联系管理员进行充值'
      addDebugInfo(`💰 Token预算不足: ${errorMsg}`)
      alert(`⚠️ Token预算不足\n\n${errorMsg}\n\n请联系管理员进行充值后再试。`)
    } else {
      // 显示详细错误信息
      const errorDetails = error?.response?.data?.detail || error?.message || '未知错误'
      const statusCode = error?.response?.status || 'unknown'
      
      addDebugInfo(`💥 评审启动失败 [${statusCode}]: ${errorDetails}`)
      console.error('Start review error:', error)
      
      // 用户友好的错误提示
      let userMessage = '❌ 评审启动失败\n\n'
      if (statusCode === 400) {
        userMessage += '文件格式或内容有问题，请检查文件是否为有效的PPT格式。'
      } else if (statusCode === 500) {
        userMessage += '服务器内部错误，请稍后重试或联系技术支持。'
      } else {
        userMessage += `错误信息: ${errorDetails}\n状态码: ${statusCode}`
      }
      
      alert(userMessage)
    }
  } finally {
    isUploading.value = false
  }
}

// 状态轮询
let pollingInterval: ReturnType<typeof setInterval> | null = null

function startStatusPolling() {
  if (pollingInterval) {
    clearInterval(pollingInterval)
  }

  let pollCount = 0
  const maxPolls = 200 // 最多轮询200次，避免无限轮询

  pollingInterval = setInterval(async () => {
    pollCount++
    
    if (!currentSession.value) {
      addDebugInfo('⏹️ 会话不存在，停止轮询')
      if (pollingInterval) {
        clearInterval(pollingInterval)
        pollingInterval = null
      }
      return
    }

    const status = currentSession.value.status
    if (status === 'completed' || status === 'failed') {
      addDebugInfo(`🎯 评审已${status === 'completed' ? '完成' : '失败'}，停止轮询`)
      if (pollingInterval) {
        clearInterval(pollingInterval)
        pollingInterval = null
      }
      return
    }

    if (pollCount > maxPolls) {
      addDebugInfo(`⏰ 轮询超时，停止轮询 (已轮询${pollCount}次)`)
      if (pollingInterval) {
        clearInterval(pollingInterval)
        pollingInterval = null
      }
      return
    }

    // 只在前几次或者每10次输出轮询信息，减少刷屏
    if (pollCount <= 3 || pollCount % 10 === 0) {
      addDebugInfo(`🔄 轮询状态 (第${pollCount}次)...`)
    }
    await refreshStatus(false)
  }, 2000) // 每2秒检查一次
}

// 刷新状态
async function refreshStatus(showLoading = true) {
  if (!currentSession.value) return

  if (showLoading) {
    isRefreshing.value = true
  }

  try {
    const status = await getPPTReviewStatus(currentSession.value.session_id)
    
    // 更新会话状态
    const oldProgress = currentSession.value.progress
    const oldStage = currentSession.value.current_stage
    const oldStatus = currentSession.value.status
    
    currentSession.value = { ...currentSession.value, ...status }
    
    // 只在关键变化时记录日志，减少刷屏
    const stageChanged = status.current_stage !== oldStage
    const statusChanged = status.status !== oldStatus
    const significantProgress = Math.abs(status.progress - oldProgress) >= 10
    
    if (stageChanged || statusChanged || significantProgress) {
      addDebugInfo(`📊 状态更新: ${status.status} - ${status.current_stage} (${status.progress}%)`)
    }
    
    // 如果完成了，显示特别提示
    if (status.status === 'completed' && oldStatus !== 'completed') {
      addDebugInfo(`🎉 评审完成！自动获取报告...`)
      // 自动获取报告
      setTimeout(() => {
        getReport()
      }, 1000) // 延迟1秒获取，确保后端数据已保存
    } else if (status.status === 'failed' && oldStatus !== 'failed') {
      addDebugInfo(`💥 评审失败: ${status.error || status.message}`)
      alert(`评审失败: ${status.error || status.message}`)
    }
    
  } catch (error: any) {
    const errorMsg = error?.response?.data?.detail || error?.message || '未知错误'
    addDebugInfo(`❌ 获取状态失败: ${errorMsg}`)
    console.error('Refresh status error:', error)
    
    // 如果是404错误，说明会话不存在
    if (error?.response?.status === 404) {
      alert('评审会话不存在，可能已过期')
      resetSession()
    }
  } finally {
    if (showLoading) {
      isRefreshing.value = false
    }
  }
}

// 获取报告
async function getReport() {
  if (!currentSession.value) {
    addDebugInfo('❌ 没有活跃的评审会话')
    return
  }

  addDebugInfo('📋 开始获取评审报告...')
  addDebugInfo(`会话ID: ${currentSession.value.session_id}`)
  
  try {
    const report = await getPPTReviewReport(currentSession.value.session_id)
    
    // 输出原始响应到控制台用于调试
    console.log('🔍 PPT评审报告原始响应:', report)
    addDebugInfo(`🔍 原始报告响应类型: ${typeof report}`)
    addDebugInfo(`🔍 原始报告内容长度: ${JSON.stringify(report).length}`)
    
    // 检查报告内容
    if (!report || (typeof report === 'object' && Object.keys(report).length === 0)) {
      addDebugInfo('⚠️ 报告内容为空或无效')
      alert('⚠️ 获取到的报告内容为空，可能评审尚未完全完成或发生了错误。请稍后重试或联系技术支持。')
      return
    }
    
    reviewReport.value = report
    addDebugInfo('✅ 评审报告获取成功')
    
    // 显示成功提示
    alert('✅ 评审报告获取成功！请查看下方报告内容。')
    
  } catch (error: any) {
    const errorMsg = error?.response?.data?.detail || error?.message || '未知错误'
    const statusCode = error?.response?.status
    
    // 输出完整错误信息到控制台
    console.error('🔍 获取报告错误详情:', {
      error,
      response: error?.response,
      status: statusCode,
      data: error?.response?.data
    })
    
    addDebugInfo(`❌ 获取报告失败 [${statusCode}]: ${errorMsg}`)
    
    if (statusCode === 400) {
      alert('评审尚未完成，无法获取报告')
    } else if (statusCode === 404) {
      alert('评审会话不存在，无法获取报告')
    } else if (statusCode === 403) {
      alert('权限不足，无法访问此报告')
    } else {
      alert(`获取报告失败 [${statusCode}]: ${errorMsg}`)
    }
  }
}

// 重置会话
function resetSession() {
  addDebugInfo('🔄 重置会话状态...')
  
  currentSession.value = null
  reviewReport.value = null
  selectedFile.value = null
  
  if (pollingInterval) {
    clearInterval(pollingInterval)
    pollingInterval = null
    addDebugInfo('⏹️ 停止状态轮询')
  }
  
  addDebugInfo('✅ 会话已重置，可以开始新的评审')
}

// 测试函数
async function testAuth() {
  const token = localStorage.getItem('auth_token')
  addDebugInfo(`认证Token: ${token ? '存在' : '不存在'}`)
  if (token) {
    addDebugInfo(`Token长度: ${token.length}`)
    addDebugInfo(`Token预览: ${token.substring(0, 20)}...`)
  }
}

async function testTemplates() {
  try {
    addDebugInfo('测试模板API...')
    const templates = await getPPTTemplates()
    addDebugInfo(`模板API响应: ${JSON.stringify(templates)}`)
  } catch (error) {
    addDebugInfo(`模板API错误: ${error}`)
  }
}

async function testHistory() {
  try {
    addDebugInfo('测试历史API...')
    const history = await getPPTReviewHistory()
    addDebugInfo(`历史API响应: ${JSON.stringify(history)}`)
  } catch (error) {
    addDebugInfo(`历史API错误: ${error}`)
  }
}

// 创建测试文件
function createTestFile() {
  try {
    addDebugInfo('创建测试PPT文件...')
    
    // 创建一个简单的测试文件内容（模拟PPT内容）
    const testContent = `
[PowerPoint Test]
This is a test PowerPoint file for API testing.
Slide 1: Introduction
Slide 2: Problem Statement
Slide 3: Solution Overview
Slide 4: Market Analysis
Slide 5: Team Introduction
    `.trim()
    
    // 创建Blob和File对象
    const blob = new Blob([testContent], { 
      type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation' 
    })
    
    const testFile = new File([blob], 'test-presentation.pptx', {
      type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      lastModified: Date.now()
    })
    
    // 直接设置选中的文件
    selectedFile.value = testFile
    addDebugInfo(`✅ 测试文件创建成功: ${testFile.name} (${testFile.size} bytes, ${testFile.type})`)
    
    console.log('Test file created:', testFile)
  } catch (error) {
    addDebugInfo(`❌ 创建测试文件失败: ${error}`)
    console.error('Create test file error:', error)
  }
}

// 组件挂载时
onMounted(() => {
  addDebugInfo('🚀 PPT评审页面已加载')
  testAuth()
})

// 组件卸载时清理
onUnmounted(() => {
  if (pollingInterval) {
    clearInterval(pollingInterval)
    pollingInterval = null
    addDebugInfo('🧹 组件卸载，清理轮询定时器')
  }
})

// 导出报告
function exportReport() {
  if (!reviewReport.value) {
    alert('没有可导出的报告')
    return
  }

  try {
    // 生成HTML格式的报告
    const htmlContent = generateReportHTML(reviewReport.value)
    
    // 创建Blob并下载
    const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `PPT评审报告_${reviewReport.value.session_id || 'unknown'}_${new Date().toISOString().slice(0, 10)}.html`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    addDebugInfo('📥 报告导出成功')
    alert('✅ 报告已导出为HTML文件')
  } catch (error) {
    addDebugInfo(`❌ 报告导出失败: ${error}`)
    alert('❌ 报告导出失败，请重试')
  }
}

// 生成HTML格式的报告
function generateReportHTML(report: any): string {
  const now = new Date().toLocaleString()
  
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPT评审报告 - ${report.session_id || 'Unknown'}</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; max-width: 1200px; margin: 0 auto; padding: 20px; background-color: #f5f5f5; }
        .report-container { background: white; border-radius: 8px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; border-bottom: 2px solid #1976d2; padding-bottom: 20px; margin-bottom: 30px; }
        .title { color: #1976d2; font-size: 28px; font-weight: bold; margin-bottom: 10px; }
        .subtitle { color: #666; font-size: 14px; }
        .section { margin-bottom: 25px; border: 1px solid #e0e0e0; border-radius: 6px; overflow: hidden; }
        .section-header { background: linear-gradient(135deg, #1976d2, #42a5f5); color: white; padding: 12px 20px; font-weight: bold; font-size: 16px; }
        .section-content { padding: 20px; }
        .highlight-box { background: #f8f9fa; border-left: 4px solid #1976d2; padding: 15px; margin: 15px 0; }
        .strength { color: #2e7d32; }
        .concern { color: #f57c00; }
        .question { color: #7b1fa2; }
        .recommendation { color: #00695c; }
        ul, ol { padding-left: 20px; }
        li { margin-bottom: 8px; }
        .page-review { border: 1px solid #e0e0e0; border-radius: 4px; margin-bottom: 15px; padding: 15px; background: #fafafa; }
        .page-title { font-weight: bold; color: #1976d2; margin-bottom: 10px; }
        .meta-info { color: #666; font-size: 12px; text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; }
    </style>
</head>
<body>
    <div class="report-container">
        <div class="header">
            <div class="title">🎯 PPT评审报告</div>
            <div class="subtitle">投资人视角专业评审 | 会话ID: ${report.session_id || 'N/A'}</div>
            <div class="subtitle">生成时间: ${report.report_date ? new Date(report.report_date).toLocaleString() : now}</div>
        </div>

        ${report.overall_impression ? `
        <div class="section">
            <div class="section-header">👁️ 总体印象</div>
            <div class="section-content">
                <div class="highlight-box">
                    <strong>${report.overall_impression}</strong>
                </div>
            </div>
        </div>
        ` : ''}

        ${report.top_strengths && report.top_strengths.length > 0 ? `
        <div class="section">
            <div class="section-header">👍 主要优势</div>
            <div class="section-content">
                <ul>
                    ${report.top_strengths.map((strength: string) => `<li class="strength">✅ ${strength}</li>`).join('')}
                </ul>
            </div>
        </div>
        ` : ''}

        ${report.top_concerns && report.top_concerns.length > 0 ? `
        <div class="section">
            <div class="section-header">⚠️ 主要关注点</div>
            <div class="section-content">
                <ul>
                    ${report.top_concerns.map((concern: string) => `<li class="concern">🔶 ${concern}</li>`).join('')}
                </ul>
            </div>
        </div>
        ` : ''}

        ${report.key_questions && report.key_questions.length > 0 ? `
        <div class="section">
            <div class="section-header">❓ 投资人关键问题</div>
            <div class="section-content">
                <ol>
                    ${report.key_questions.map((question: string) => `<li class="question">${question}</li>`).join('')}
                </ol>
            </div>
        </div>
        ` : ''}

        ${report.page_reviews && report.page_reviews.length > 0 ? `
        <div class="section">
            <div class="section-header">📑 逐页评审详情</div>
            <div class="section-content">
                ${report.page_reviews.map((page: any) => `
                    <div class="page-review">
                        <div class="page-title">第${page.slide_number}页: ${page.title || '无标题'}</div>
                        ${page.analysis ? `<p><strong>分析:</strong> ${page.analysis}</p>` : ''}
                        ${page.strengths && page.strengths.length > 0 ? `
                            <p><strong class="strength">优点:</strong></p>
                            <ul>${page.strengths.map((s: string) => `<li>${s}</li>`).join('')}</ul>
                        ` : ''}
                        ${page.weaknesses && page.weaknesses.length > 0 ? `
                            <p><strong class="concern">需要改进:</strong></p>
                            <ul>${page.weaknesses.map((w: string) => `<li>${w}</li>`).join('')}</ul>
                        ` : ''}
                        ${page.questions && page.questions.length > 0 ? `
                            <p><strong class="question">相关问题:</strong></p>
                            <ul>${page.questions.map((q: string) => `<li>${q}</li>`).join('')}</ul>
                        ` : ''}
                    </div>
                `).join('')}
            </div>
        </div>
        ` : ''}

        ${report.recommendations ? `
        <div class="section">
            <div class="section-header">💡 改进建议</div>
            <div class="section-content">
                ${Array.isArray(report.recommendations) ? `
                    <ul>
                        ${report.recommendations.map((rec: string) => `<li class="recommendation">💡 ${rec}</li>`).join('')}
                    </ul>
                ` : `
                    ${Object.entries(report.recommendations).map(([category, recs]: [string, any]) => `
                        <h4>${String(category).replace('_', ' ').toUpperCase()}</h4>
                        <ul>
                            ${Array.isArray(recs) ? recs.map((rec: string) => `<li class="recommendation">${rec}</li>`).join('') : ''}
                        </ul>
                    `).join('')}
                `}
            </div>
        </div>
        ` : ''}

        ${report.final_report_text ? `
        <div class="section">
            <div class="section-header">📝 详细分析报告</div>
            <div class="section-content">
                <div style="white-space: pre-wrap; line-height: 1.8;">${report.final_report_text}</div>
            </div>
        </div>
        ` : ''}

        <div class="meta-info">
            <p>📊 本报告由DAIR项目PPT评审Agent生成 | 导出时间: ${now}</p>
            <p>🤖 基于资深VC投资合伙人视角的专业评审标准</p>
        </div>
    </div>
</body>
</html>
  `.trim()
}

// 复制到剪贴板
function copyToClipboard(text: string) {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text).then(() => {
      addDebugInfo(`📋 已复制到剪贴板`)
      alert('✅ 内容已复制到剪贴板')
    }).catch(err => {
      addDebugInfo(`❌ 复制失败: ${err}`)
      alert('❌ 复制失败，请手动选择复制')
    })
  } else {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      addDebugInfo(`📋 已复制到剪贴板`)
      alert('✅ 内容已复制到剪贴板')
    } catch (err) {
      addDebugInfo(`❌ 复制失败: ${err}`)
      alert('❌ 复制失败，请手动选择复制')
    }
    document.body.removeChild(textArea)
  }
}
</script>

<style scoped>
.ppt-review-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px 0;
}

pre {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  max-height: 400px;
  overflow-y: auto;
}
</style> 