import { createApp } from "vue";
import router from "@/router";
// import { setupSentry } from "@/utils/sentry";
import App from "./App.vue";
import pinia from "@/stores";
import { useAuthStore } from "@/stores/authStore";
import { createVuetify } from "vuetify";
import "@mdi/font/css/materialdesignicons.css";
import "vuetify/styles";
import "@/assets/scss/discord-theme.scss";
import masonry from 'vue-next-masonry'

const vuetify = createVuetify();

// 设置Sentry
// setupSentry({
//   app,
//   router,
//   userInfo: {
//     id: "11757",
//     username: "admin",
//   },
// });

function printAsciiLogo() {
  console.log(`
  ██████╗ ███████╗███████╗██████╗  ██████╗ ██████╗  ██████╗ ███╗   ██╗██╗████████╗██╗ ██████╗ ███╗   ██╗
  ██╔══██╗██╔════╝██╔════╝██╔══██╗██╔════╝██╔═══██╗██╔════╝ ████╗  ██║██║╚══██╔══╝██║██╔═══██╗████╗  ██║
  ██║  ██║█████╗  █████╗  ██████╔╝██║     ██║   ██║██║  ███╗██╔██╗ ██║██║   ██║   ██║██║   ██║██╔██╗ ██║
  ██║  ██║██╔══╝  ██╔══╝  ██╔═══╝ ██║     ██║   ██║██║   ██║██║╚██╗██║██║   ██║   ██║██║   ██║██║╚██╗██║
  ██████╔╝███████╗███████╗██║     ╚██████╗╚██████╔╝╚██████╔╝██║ ╚████║██║   ██║   ██║╚██████╔╝██║ ╚████║
  ╚═════╝ ╚══════╝╚══════╝╚═╝      ╚═════╝ ╚═════╝  ╚═════╝ ╚═╝  ╚═══╝╚═╝   ╚═╝   ╚═╝ ╚═════╝ ╚═╝  ╚═══╝
                                                                                       v1.0.0 - DAIR Team
  `);

  // 添加彩色文字
  console.log(
    "%c欢迎使用 DeepCognition AI 对话平台！",
    "color: #4285f4; font-size: 20px; font-weight: bold;"
  );

}

async function bootstrap() {
  // 打印logo
  printAsciiLogo();

  const app = createApp(App);
  app.use(pinia);
  app.use(vuetify);
  app.use(router);
  app.use(masonry);

  // 初始化认证存储
  const authStore = useAuthStore();
  await authStore.init();

  app.mount("#app");
}

bootstrap();
