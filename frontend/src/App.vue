<script setup>
import { RouterView } from "vue-router";
</script>

<template>
  <main class="discord-theme">
    <RouterView />
  </main>
</template>

<style>
.discord-theme {
  font-family: 'Whitney', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  height: 100vh;
  width: 100vw;
  overflow: visible;
  background-color: var(--discord-light);
  color: var(--discord-text);
}

html, body {
  margin: 0;
  padding: 0;
  overflow: visible;
  font-display: swap;
}
</style>
