// 基础消息
export interface BaseMessage {
  id: string;
  role: "user" | "assistant";
  timestamp: number;
}

// 用户消息
export interface UserMessage extends BaseMessage {
  role: "user";
  content: string;
}


// 思考过程
export interface ThinkingProcess {
  content: string;
  duration: number;
}

// AI响应消息
export interface AssistantMessage extends BaseMessage {
  role: "assistant";
  contentChunks: string[]; // 用于存储流式响应的文本片段
  content?: string; // 完整响应内容（当状态为completed时）
  thinking?: ThinkingProcess;
  error?: string; // 当状态为error时的错误信息
  errorDetail?: string; // 错误详细信息
  errorCode?: string; // 错误代码
  hasError?: boolean; // 是否有错误
  completed?: boolean; // 当状态为completed时，表示响应已完成
  progressMessages?: string[]; // 添加这个字段
  progressDetails?: string[]; // 添加这个字段
  dc?: string; // 添加这个字段,
  elapsedTimeSeconds?: number; // 添加这个字段
}

// 用于展示的消息类型
export type ChatMessage = UserMessage | AssistantMessage;


// 模型定义
export interface Model {
  id: string;
  name: string;
}

// 认知相关类型定义
export interface CognitionComment {
  id: string;
  user_id: string;
  username: string;
  content: string;
  created_at: string;
}

export interface CognitionVote {
  user_id: string;
  vote_type: 'like' | 'neutral' | 'dislike';
  created_at: string;
}

export interface FavoriteStatus {
  is_favorited: boolean;
  collections: string[];
}

export interface Cognition {
  id: string;
  abstract_zh?: string;
  abstract_en?: string;
  think_zh?: string;
  think_en?: string;
  question_zh?: string;
  question_en?: string;
  answer_zh?: string;
  answer_en?: string;
  source: string;
  author_id: string;
  author_name: string;
  blogger?: string;
  link?: string;
  content?: string;
  likes: number;
  neutral: number;
  dislikes: number;
  comments: CognitionComment[];
  votes: CognitionVote[];
  created_at: string;
  updated_at: string;
  user_vote?: string;
  favorite_status?: FavoriteStatus;
}

export interface CognitionCreate {
  abstract_zh: string;
  abstract_en?: string;
  think_zh: string;
  think_en?: string;
  question_zh: string;
  question_en?: string;
  answer_zh: string;
  answer_en?: string;
  source: string;
  blogger?: string;
  link?: string;
  content?: string;
}

export interface CognitionCollection {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  is_default: boolean;
  created_at: string;
  updated_at: string;
  cognition_count?: number;
}

export interface PaginatedCognitionResponse {
  items: Cognition[];
  total: number;
  page: number;
  size: number;
  pages: number;
}
