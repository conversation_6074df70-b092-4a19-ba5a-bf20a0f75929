declare module 'vue-next-masonry' {
  import { Plugin } from 'vue'
  
  interface MasonryOptions {
    transition?: string
    itemSelector?: string
    columnWidth?: number | string
    gutter?: number
    percentPosition?: boolean
    originLeft?: boolean
    originTop?: boolean
    containerStyle?: Record<string, any>
  }
  
  const masonry: Plugin & {
    install(app: any, options?: MasonryOptions): void
  }
  
  export default masonry
} 