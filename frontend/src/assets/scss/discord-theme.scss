/* Discord主题变量和全局样式 */

:root {
  /* Discord调色板 */
  --discord-dark: #202225;
  --discord-medium: #2f3136;
  --discord-light: #36393f;
  --discord-lighter: #40444b;
  --discord-hover: #4f545c;
  --discord-selected: #7289da;
  --discord-text: #000000;
  --discord-text-muted: #a3a6aa;
  --discord-interactive: #b9bbbe;
  --discord-interactive-hover: #dcddde;
  --discord-green: #3ba55c;
  --discord-red: #ed4245;
  --discord-yellow: #f0b232;
  --discord-blue: #5865f2;
  --discord-purple: #9b59b6;
  
  /* 布局尺寸 */
  --sidebar-width: 240px;
  --sidebar-collapsed-width: 60px;
  --header-height: 48px;
  --footer-height: 68px;
  --border-radius-md: 8px;
  --border-radius-sm: 4px;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 2px 10px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 4px 20px rgba(0, 0, 0, 0.15);
  
  /* 过渡时间 */
  --transition-fast: 0.15s;
  --transition-normal: 0.25s;
  --transition-slow: 0.4s;
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background-color: transparent;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(79, 84, 92, 0.3);
  border-radius: 10px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(79, 84, 92, 0.5);
}

/* 常用工具类 */
.d-flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.d-flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.d-flex-column {
  display: flex;
  flex-direction: column;
}

.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}

.discord-shadow {
  box-shadow: var(--shadow-md);
}

.discord-card {
  background-color: var(--discord-light);
  border-radius: var(--border-radius-md);
  color: var(--discord-text);
}

.discord-text-overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-normal) ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-down-enter-active,
.slide-down-leave-active {
  transition: transform var(--transition-normal) ease, opacity var(--transition-normal) ease;
}

.slide-down-enter-from,
.slide-down-leave-to {
  transform: translateY(-20px);
  opacity: 0;
} 