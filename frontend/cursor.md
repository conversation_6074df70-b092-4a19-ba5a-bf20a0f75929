# DAIR 前端控制文件

## 主题
现代化管理控制台界面，采用深色侧边栏配色方案

## UI设计
- 使用Vue 3 + TypeScript + Composition API
- 响应式布局，支持移动端适配
- 统一的组件设计语言
- 表格和表单采用现代化交互设计

## 开发中的假设
- 管理员权限通过localStorage中的is_admin标识判断
- Token管理功能仅对管理员开放
- 所有API调用都通过统一的authService进行

## 特殊处理
- 用户Token管理支持实时编辑预算和使用量
- 提供确认对话框防止误操作
- 超限用户在界面中特殊标识显示

## Changelog
- 2024-12-19: **优化数学公式位置映射算法**
  - 发现原有映射函数与textBetween行为不一致，导致公式位置偏移
  - 重写mapTextPositionToDoc函数，使用线性扫描确保与textBetween完全一致
  - 添加映射验证机制，只在位置映射正确时才渲染公式
  - 简化调试输出，提高性能和可读性
  - 解决数学公式覆盖正常内容和余留美元符号的问题
- 2024-12-19: **修复数学公式扩展的文本节点分割问题**
  - 发现ProseMirror将文档文本分割成多个节点，导致跨节点的数学公式无法匹配
  - 重写检测逻辑，使用textBetween获取完整文档文本，避免节点分割问题
  - 实现mapTextPositionToDoc函数，将文本位置准确映射回文档位置
  - 添加详细的调试日志，帮助追踪位置映射过程
  - 改进边界情况处理，确保位置映射的健壮性
  - 现在能正确处理被格式化、换行等因素分割的数学公式
  - 创建测试用例验证复杂场景下的公式渲染
- 2024-12-19: **自定义数学公式扩展替换第三方扩展**
  - 发现 @aarkue/tiptap-math-extension 的关键缺陷：只在输入时渲染，setContent时无法工作
  - 创建自定义 MathRenderer.ts 扩展，支持 $...$ 内联公式和 $$...$$ 块级公式
  - 新扩展能在任何时候检测和渲染数学公式，包括 setContent 调用时
  - 使用装饰器模式渲染公式，自动隐藏原始文本，显示 KaTeX 渲染结果
  - 支持悬停效果、错误处理、渲染失败时显示原始公式
  - 移除了旧扩展相关的调试代码和强制刷新方法
  - 确保外部内容变化时数学公式能够正确显示
- 2024-12-19: **修复FastAPI路由顺序冲突问题**
  - 发现image_proxy端点被/{document_id}路由误匹配导致认证错误
  - 调整router.py中路由定义顺序，将具体路由放在参数化路由之前
  - 彻底解决TOS图片代理的认证问题
  - 确认image_proxy现在能正确返回404而非401错误
- 2024-12-19: **简化TOS图片代理为公开接口**
  - 将image_proxy端点改为公开接口，不需要任何认证
  - 简化convertTOSImageUrl函数，移除token参数处理
  - 彻底解决浏览器直接请求图片时的认证问题
  - 保持URL格式验证确保只能代理TOS存储链接
  - 大幅简化架构，提升性能和可维护性
- 2024-12-19: **修复TOS图片代理认证问题**
  - 修复了浏览器直接请求图片时无法携带Authorization header的问题
  - 在convertTOSImageUrl函数中添加token查询参数支持
  - 后端image_proxy端点现在支持从查询参数和Authorization header两种方式获取token
  - 解决了编辑器中TOS图片无法正常显示的401认证错误
  - 确保图片链接转换后能够正常加载和显示
- 2024-12-19: **新增TOS图片代理功能支持**
  - 在documents.ts API中添加TOS链接识别和转换功能
  - 实现isTOSImageUrl函数检测TOS存储链接
  - 实现convertTOSImageUrl函数将TOS链接转换为后端代理链接
  - 在getDocument和uploadImage中自动处理TOS图片链接
  - 在NoteMarkdownEditor中支持拖拽和粘贴TOS图片自动转换
  - 解决TOS私有存储图片在前端无法直接访问的问题
  - 确保所有图片资源都能正常显示和编辑
- 2024-12-19: **修复图片上传功能使用真实API**
  - 更新frontend/src/api/documents.ts中的uploadImage函数
  - 移除fake URL返回，改为真正调用后端/documents/upload_image接口
  - 使用FormData上传文件，设置正确的Content-Type为multipart/form-data
  - 现在图片上传功能完全对接后端TOS存储服务
- 2024-12-19: 新增用户Token管理功能
  - 创建UserTokenManager.vue组件，提供完整的用户Token管理界面
  - 在authService中添加Token管理相关API接口
  - 在管理页面中集成用户Token管理模块
  - 在AdminSidebar中添加用户Token管理菜单项
  - 实现Token使用情况的可视化展示（进度条、状态标识）
  - 支持批量查看、单独编辑、重置操作
  - 新增批量Token管理功能：多选用户、批量修改预算、批量修改使用量
  - 添加全选/取消全选功能，支持单个用户选择
  - 实现批量操作对话框，提供友好的批量修改界面
  - 在authService中添加batchUpdateUserTokenBudget、batchUpdateUserTokenUsage接口
  - 优化用户界面，选中用户高亮显示，批量操作按钮动态显示
  - **新增Token使用量同步功能**：确保数据一致性
    - 在authService中添加syncUserTokenUsageFromSessions、syncAllUsersTokenUsageFromSessions接口
    - 在UserTokenManager组件中添加单用户同步、批量同步、全量同步功能
    - 提供三种同步方式：单个用户同步按钮、选中用户批量同步、全量同步所有用户
    - 同步功能可以解决session记录与用户表token使用量不一致的问题
    - 添加同步状态指示和错误处理，提供友好的用户反馈
- 2024-12-19: 添加token budget前端处理功能
  - 创建TokenBudgetDialog.vue组件，提供友好的token不足提示界面
  - 创建useTokenBudget.ts composable，处理token budget错误解析和状态管理
  - 在useDeepConversation.ts中集成token budget处理逻辑
  - 在draft页面中添加TokenBudgetDialog组件
  - 支持HTTP 402错误的自动识别、解析和用户友好提示
  - 提供联系管理员充值的引导信息

## 主题与UI
- 主题颜色
    - 主色调: 紫色系
    - 辅助色: 根据功能区分不同模块
- 默认页面
    - 草稿页面 (draft) 作为默认登陆页面
- UI组件库
    - 基于Vuetify 3.x构建
    - 使用Markdown编辑器(基于TipTap)处理富文本

## 开发假设与预设
- 前端使用Vue 3 + TypeScript + Pinia + Vue Router开发
- 使用组合式API (Composition API)进行状态管理
- 页面结构按功能模块划分：auth(认证)、draft(草稿)
- dashboard(数据看板)暂时不使用
- 全局状态通过Pinia进行管理，局部状态使用composables
- API请求统一通过src/api模块管理

