#!/bin/bash

# SSL证书自动获取和管理脚本
# 用于GitHub Actions自动部署

set -e

# 配置参数
DOMAIN="dev.q.opensii.ai"
EMAIL="<EMAIL>"
CERT_DIR="./data/certbot/conf/live/$DOMAIN"
WEBROOT_DIR="./data/certbot/www"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[SSL-AUTO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[SSL-WARN]${NC} $1"
}

error() {
    echo -e "${RED}[SSL-ERROR]${NC} $1"
}

# 检查证书是否存在且有效
check_certificate() {
    if [ ! -f "$CERT_DIR/fullchain.pem" ] || [ ! -f "$CERT_DIR/privkey.pem" ]; then
        log "证书文件不存在，需要获取新证书"
        return 1
    fi
    
    # 检查证书是否在30天内过期
    if openssl x509 -checkend 2592000 -noout -in "$CERT_DIR/fullchain.pem" >/dev/null 2>&1; then
        log "证书有效且未在30天内过期"
        return 0
    else
        warn "证书将在30天内过期，需要更新"
        return 1
    fi
}

# 创建必要的目录
create_directories() {
    log "创建证书目录..."
    mkdir -p data/certbot/conf
    mkdir -p data/certbot/www
    mkdir -p data/certbot/logs
}

# 获取Let's Encrypt证书
get_certificate() {
    log "获取Let's Encrypt证书..."
    
    # 清理可能存在的临时文件和容器
    rm -rf /tmp/nginx-temp.conf
    docker stop nginx-temp >/dev/null 2>&1 || true
    docker rm nginx-temp >/dev/null 2>&1 || true
    
    # 创建临时nginx配置文件
    cat > /tmp/nginx-temp.conf << EOF
server {
    listen 80;
    server_name $DOMAIN;
    
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }
    
    location / {
        return 200 'SSL verification in progress...';
        add_header Content-Type text/plain;
    }
}
EOF
    
    # 启动临时HTTP服务器用于验证
    docker run -d \
        --name nginx-temp \
        -p 80:80 \
        -v "${PWD}/data/certbot/www:/var/www/certbot:ro" \
        -v /tmp/nginx-temp.conf:/etc/nginx/conf.d/default.conf:ro \
        nginx:alpine
    
    # 等待容器启动
    sleep 3
    
    # 获取证书
    docker run --rm \
        --name certbot \
        -v "${PWD}/data/certbot/conf:/etc/letsencrypt" \
        -v "${PWD}/data/certbot/www:/var/www/certbot" \
        -v "${PWD}/data/certbot/logs:/var/log/letsencrypt" \
        certbot/certbot certonly \
        --webroot \
        --webroot-path=/var/www/certbot \
        --email $EMAIL \
        --agree-tos \
        --no-eff-email \
        --force-renewal \
        -d $DOMAIN
    
    # 停止临时服务器
    docker stop nginx-temp >/dev/null 2>&1 || true
    docker rm nginx-temp >/dev/null 2>&1 || true
    rm -rf /tmp/nginx-temp.conf
    
    if [ $? -eq 0 ]; then
        log "证书获取成功！"
        return 0
    else
        error "证书获取失败！"
        return 1
    fi
}

# 验证证书文件
verify_certificate() {
    if [ ! -f "$CERT_DIR/fullchain.pem" ] || [ ! -f "$CERT_DIR/privkey.pem" ]; then
        error "证书文件验证失败"
        return 1
    fi
    
    # 验证证书格式
    if ! openssl x509 -in "$CERT_DIR/fullchain.pem" -text -noout >/dev/null 2>&1; then
        error "证书格式验证失败"
        return 1
    fi
    
    log "证书验证成功"
    return 0
}

# 主函数
main() {
    log "开始SSL证书自动化处理 for $DOMAIN ..."
    
    # 创建目录
    create_directories
    
    # 停止可能存在的nginx容器
    docker stop dair-nginx >/dev/null 2>&1 || true
    
    # 检查现有证书
    if check_certificate; then
        log "现有证书有效，无需更新"
    else
        log "需要获取新证书"
        if ! get_certificate; then
            error "证书获取失败，退出"
            exit 1
        fi
    fi
    
    # 验证最终证书
    if verify_certificate; then
        log "SSL证书准备完成！"
        log "证书路径: $CERT_DIR"
        log "域名: $DOMAIN"
        
        # 显示证书过期时间
        EXPIRY=$(openssl x509 -in "$CERT_DIR/fullchain.pem" -noout -enddate | cut -d= -f2)
        log "证书过期时间: $EXPIRY"
    else
        error "证书验证失败！"
        exit 1
    fi
}

# 执行主函数
main "$@" 