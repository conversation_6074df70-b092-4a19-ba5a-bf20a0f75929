version: "3"

services:
  # 后端服务
  api:
    image: ${ALI_DOCKER_REGISTRY}/deepcognition-build/dc-backend:latest
    container_name: dair-backend
    volumes:
      - downloads:/app/downloads
      - trajectory:/app/backend/dc_agents/trajectory
      - uploads:/app/dair/uploads
      - logs:/app/dair/logs
    environment:
      - MONGO_URI=mongodb://dair:<EMAIL>:3717,mongoreplica98081f54cfd51.mongodb.ap-southeast-1.ivolces.com:3717/?authSource=admin&replicaSet=rs-mongo-replica-98081f54cfd5&retryWrites=true
      - REDIS_URL=redis://redis:6379/0
      - API_HOST=0.0.0.0
      - API_PORT=8000
    depends_on:
      - redis
    networks:
      - main-network

  # 前端构建服务
  frontend-builder:
    image: ${ALI_DOCKER_REGISTRY}/deepcognition-build/dc-frontend:latest
    volumes:
      - ./frontend:/app
      - ./frontend/dist:/app/dist
    command: sh -c "npm install && npm run build"
    networks:
      - main-network

  # Nginx服务
  nginx:
    image: ${ALI_DOCKER_REGISTRY}/deepcognition-build/dc-nginx:latest
    container_name: dair-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./frontend/dist:/usr/share/nginx/html:ro
      - ./data/certbot/conf:/etc/letsencrypt
      - ./data/certbot/www:/var/www/certbot
    depends_on:
      - api
      - frontend-builder
    networks:
      - main-network

  # Redis服务
  redis:
    image: redis:7-alpine
    container_name: dair-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: >
      redis-server 
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
      --appendonly yes
      --appendfsync everysec
    volumes:
      - redis_data:/data
    networks:
      - main-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mongodb_data:
  redis_data:
  downloads:
  trajectory:
  uploads:
  logs:


networks:
  main-network:
    driver: bridge
