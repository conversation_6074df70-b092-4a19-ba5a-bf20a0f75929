<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字体与主题样式预览</title>
    <!-- 引入 Tailwind CSS Play CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入所有 Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Helvetica+Neue:wght@700;900&family=ZCOOL+KuaiLe&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Permanent+Marker&family=ZCOOL+KuaiLe&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Caveat:wght@700;900&family=Long+Cang&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@600;700&family=<PERSON>+<PERSON>+<PERSON>&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@700;900&family=ZCOOL+XiaoWei&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@800;900&family=IBM+Plex+Sans+JP:wght@700;900&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Nunito:wght@700;900&family=Zen+Maru+Gothic:wght@700;900&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@700;900&family=Zen+Old+Mincho:wght@700;900&display=swap" rel="stylesheet">
    <style>
        /* 自定义字体样式 */
        .font-combo-1-en { font-family: 'Playfair Display', sans-serif; }
.font-combo-1-zh { font-family: 'Zen Old Mincho', sans-serif; }
.font-combo-2-en { font-family: 'Montserrat', sans-serif; }
.font-combo-2-zh { font-family: 'IBM Plex Sans JP', sans-serif; }
.font-combo-3-en { font-family: 'Cormorant Garamond', sans-serif; }
.font-combo-3-zh { font-family: 'Ma Shan Zheng', sans-serif; }
.font-combo-4-en { font-family: 'Nunito', sans-serif; }
.font-combo-4-zh { font-family: 'Zen Maru Gothic', sans-serif; }
.font-combo-5-en { font-family: 'Orbitron', sans-serif; }
.font-combo-5-zh { font-family: 'ZCOOL XiaoWei', sans-serif; }
.font-combo-6-en { font-family: 'Caveat', sans-serif; }
.font-combo-6-zh { font-family: 'Long Cang', sans-serif; }
.font-combo-7-en { font-family: 'Helvetica Neue', sans-serif; }
.font-combo-7-zh { font-family: 'ZCOOL KuaiLe', sans-serif; }
.font-combo-8-en { font-family: 'Permanent Marker', sans-serif; }
.font-combo-8-zh { font-family: 'ZCOOL KuaiLe', sans-serif; }

        
        /* 页面基础样式 */
        body { background-color: #111827; color: #f3f4f6; font-family: sans-serif; }
        .preview-card {
            position: relative;
            width: 480px;
            height: 270px;
            border-radius: 1.5rem;
            overflow: hidden;
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 2rem;
            color: white;
        }
        .preview-text {
            text-align: center;
            text-shadow: 0 2px 4px rgba(0,0,0,0.5);
        }
        .preview-title { font-size: 2.25rem; line-height: 2.5rem; font-weight: 900; }
        .preview-subtitle { font-size: 1.125rem; line-height: 1.75rem; margin-top: 0.5rem; opacity: 0.9; }
    </style>
</head>
<body class="p-8">
    <div class="container mx-auto">
        <h1 class="text-4xl font-bold text-white mb-8 border-b border-gray-600 pb-4">字体样式预览</h1>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- 字体预览内容将在这里生成 -->
            
        <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 class="text-xl font-bold mb-4 text-cyan-400">典雅明朝 (modern_sans)</h3>
        
            <div class="text-white text-2xl">
                <p class="font-combo-1-en">The quick brown fox jumps over the lazy dog.</p>
                <p class="font-combo-1-zh mt-2">敏捷的棕色狐狸跳过了懒惰的狗。</p>
            </div>
            </div>
        <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 class="text-xl font-bold mb-4 text-cyan-400">粗体衬线 (elegant_serif)</h3>
        
            <div class="text-white text-2xl">
                <p class="font-combo-2-en">The quick brown fox jumps over the lazy dog.</p>
                <p class="font-combo-2-zh mt-2">敏捷的棕色狐狸跳过了懒惰的狗。</p>
            </div>
            </div>
        <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 class="text-xl font-bold mb-4 text-cyan-400">学术雅正 (academic_kai)</h3>
        
            <div class="text-white text-2xl">
                <p class="font-combo-3-en">The quick brown fox jumps over the lazy dog.</p>
                <p class="font-combo-3-zh mt-2">敏捷的棕色狐狸跳过了懒惰的狗。</p>
            </div>
            </div>
        <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 class="text-xl font-bold mb-4 text-cyan-400">温暖圆润 (friendly_round)</h3>
        
            <div class="text-white text-2xl">
                <p class="font-combo-4-en">The quick brown fox jumps over the lazy dog.</p>
                <p class="font-combo-4-zh mt-2">敏捷的棕色狐狸跳过了懒惰的狗。</p>
            </div>
            </div>
        <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 class="text-xl font-bold mb-4 text-cyan-400">科技未来 (tech_future)</h3>
        
            <div class="text-white text-2xl">
                <p class="font-combo-5-en">The quick brown fox jumps over the lazy dog.</p>
                <p class="font-combo-5-zh mt-2">敏捷的棕色狐狸跳过了懒惰的狗。</p>
            </div>
            </div>
        <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 class="text-xl font-bold mb-4 text-cyan-400">手写书法 (handwritten_warm)</h3>
        
            <div class="text-white text-2xl">
                <p class="font-combo-6-en">The quick brown fox jumps over the lazy dog.</p>
                <p class="font-combo-6-zh mt-2">敏捷的棕色狐狸跳过了懒惰的狗。</p>
            </div>
            </div>
        <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 class="text-xl font-bold mb-4 text-cyan-400">瑞士简约 (minimalist_swiss)</h3>
        
            <div class="text-white text-2xl">
                <p class="font-combo-7-en">The quick brown fox jumps over the lazy dog.</p>
                <p class="font-combo-7-zh mt-2">敏捷的棕色狐狸跳过了懒惰的狗。</p>
            </div>
            </div>
        <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 class="text-xl font-bold mb-4 text-cyan-400">创意表现 (creative_display)</h3>
        
            <div class="text-white text-2xl">
                <p class="font-combo-8-en">The quick brown fox jumps over the lazy dog.</p>
                <p class="font-combo-8-zh mt-2">敏捷的棕色狐狸跳过了懒惰的狗。</p>
            </div>
            </div>
        </div>

        <h1 class="text-4xl font-bold text-white mt-16 mb-8 border-b border-gray-600 pb-4">主题样式预览</h1>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- 主题预览内容将在这里生成 -->
            
        <div>
            <h3 class="text-lg font-semibold mb-2 text-center">极光流彩 (aurora_flow)</h3>
            <div class="preview-card bg-gradient-to-br from-cyan-500 via-pink-600 to-purple-800">
                <div class="absolute w-96 h-96 bg-white/5 rounded-full blur-3xl -top-20 -left-40" ></div>
<div class="absolute w-80 h-80 bg-white/5 rounded-full blur-3xl -bottom-24 -right-24" ></div>
<div class="absolute w-60 h-60 bg-white/5 rounded-full blur-3xl bottom-10 left-10" ></div>

                <div class="preview-text relative z-10">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>
                    <div class="preview-title">Hello World</div>
                    <div class="preview-subtitle">你好，世界</div>
                </div>
            </div>
        </div>
        
        <div>
            <h3 class="text-lg font-semibold mb-2 text-center">赛博朋克 (retro_synthwave)</h3>
            <div class="preview-card bg-gradient-to-br from-purple-600 via-pink-500 to-orange-400">
                <div class="absolute w-full h-1 bg-gradient-to-r from-transparent via-cyan-400/60 to-transparent top-16 blur-sm" ></div>
<div class="absolute w-full h-1 bg-gradient-to-r from-transparent via-pink-400/60 to-transparent top-24 blur-sm" ></div>
<div class="absolute w-full h-1 bg-gradient-to-r from-transparent via-purple-400/60 to-transparent top-32 blur-sm" ></div>
<div class="absolute w-1 h-full bg-gradient-to-b from-transparent via-cyan-400/40 to-transparent left-1/4 blur-sm" ></div>
<div class="absolute w-1 h-full bg-gradient-to-b from-transparent via-pink-400/40 to-transparent right-1/4 blur-sm" ></div>
<div class="absolute w-64 h-64 bg-gradient-radial from-cyan-400/20 to-transparent rounded-full blur-xl top-8 right-8" ></div>
<div class="absolute w-48 h-48 bg-gradient-radial from-pink-400/25 to-transparent rounded-full blur-lg bottom-8 left-8" ></div>

                <div class="preview-text relative z-10">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>
                    <div class="preview-title">Hello World</div>
                    <div class="preview-subtitle">你好，世界</div>
                </div>
            </div>
        </div>
        
        <div>
            <h3 class="text-lg font-semibold mb-2 text-center">熔岩晶洞 (lava_geode)</h3>
            <div class="preview-card bg-gradient-to-br from-yellow-300 via-orange-500 to-red-600">
                <div class="absolute w-80 h-80 bg-white/15 blur-2xl -top-10 -right-20" style="clip-path: polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%)"></div>
<div class="absolute w-72 h-72 bg-white/15 blur-2xl -bottom-16 -left-16" style="clip-path: polygon(20% 0%, 80% 0%, 100% 100%, 0% 100%)"></div>

                <div class="preview-text relative z-10">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>
                    <div class="preview-title">Hello World</div>
                    <div class="preview-subtitle">你好，世界</div>
                </div>
            </div>
        </div>
        
        <div>
            <h3 class="text-lg font-semibold mb-2 text-center">深海秘境 (ocean_deep)</h3>
            <div class="preview-card bg-gradient-to-br from-teal-500 via-blue-600 to-indigo-700">
                <div class="absolute w-full h-20 bg-white/5 blur-3xl top-16 left-0" style="clip-path: polygon(0% 50%, 10% 60%, 20% 55%, 30% 65%, 40% 50%, 50% 60%, 60% 45%, 70% 55%, 80% 40%, 90% 50%, 100% 45%, 100% 100%, 0% 100%)"></div>
<div class="absolute w-full h-24 bg-white/8 blur-2xl top-32 left-0" style="clip-path: polygon(0% 60%, 15% 45%, 25% 55%, 35% 40%, 45% 50%, 55% 35%, 65% 45%, 75% 30%, 85% 40%, 95% 25%, 100% 35%, 100% 100%, 0% 100%)"></div>
<div class="absolute w-full h-16 bg-white/10 blur-xl bottom-20 left-0" style="clip-path: polygon(0% 40%, 12% 55%, 24% 45%, 36% 60%, 48% 40%, 60% 55%, 72% 35%, 84% 50%, 96% 30%, 100% 40%, 100% 100%, 0% 100%)"></div>
<div class="absolute w-full h-12 bg-white/12 blur-lg bottom-8 left-0" style="clip-path: polygon(0% 50%, 8% 35%, 16% 45%, 24% 30%, 32% 40%, 40% 25%, 48% 35%, 56% 20%, 64% 30%, 72% 15%, 80% 25%, 88% 10%, 96% 20%, 100% 15%, 100% 100%, 0% 100%)"></div>
<div class="absolute w-8 h-8 bg-white/20 rounded-full blur-sm top-20 left-16" ></div>
<div class="absolute w-6 h-6 bg-white/15 rounded-full blur-sm top-40 right-20" ></div>
<div class="absolute w-4 h-4 bg-white/20 rounded-full blur-xs bottom-32 left-24" ></div>
<div class="absolute w-5 h-5 bg-white/10 rounded-full blur-sm bottom-48 right-32" ></div>
<div class="absolute w-96 h-96 bg-gradient-radial from-cyan-300/8 to-transparent rounded-full blur-3xl -top-20 -right-20" ></div>
<div class="absolute w-80 h-80 bg-gradient-radial from-teal-300/10 to-transparent rounded-full blur-2xl top-1/4 left-1/4" ></div>

                <div class="preview-text relative z-10">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>
                    <div class="preview-title">Hello World</div>
                    <div class="preview-subtitle">你好，世界</div>
                </div>
            </div>
        </div>
        
        <div>
            <h3 class="text-lg font-semibold mb-2 text-center">液态金属 (liquid_chrome)</h3>
            <div class="preview-card bg-gradient-to-br from-gray-400 via-slate-300 to-zinc-200">
                <div class="absolute w-full h-1 bg-gradient-to-r from-transparent via-white/40 to-transparent top-12 transform rotate-45 blur-[2px]" ></div>
<div class="absolute w-full h-1 bg-gradient-to-r from-transparent via-white/40 to-transparent top-24 transform rotate-45 blur-[2px]" ></div>
<div class="absolute w-full h-1 bg-gradient-to-r from-transparent via-white/40 to-transparent top-36 transform rotate-45 blur-[2px]" ></div>
<div class="absolute w-full h-1 bg-gradient-to-r from-transparent via-white/40 to-transparent top-16 transform -rotate-45 blur-[2px]" ></div>
<div class="absolute w-full h-1 bg-gradient-to-r from-transparent via-white/40 to-transparent top-28 transform -rotate-45 blur-[2px]" ></div>
<div class="absolute w-full h-1 bg-gradient-to-r from-transparent via-white/40 to-transparent top-40 transform -rotate-45 blur-[2px]" ></div>
<div class="absolute w-96 h-96 border-t-2 border-l-2 border-white/20 rounded-tl-full -top-12 -left-12 blur-[1px]" ></div>
<div class="absolute w-80 h-80 border-b-2 border-r-2 border-white/20 rounded-br-full -bottom-12 -right-12 blur-[1px]" ></div>
<div class="absolute w-96 h-48 bg-gradient-to-r from-white/50 via-white/30 to-transparent -top-8 -left-8 transform rotate-12 blur-lg" style="clip-path: polygon(0% 0%, 100% 0%, 85% 100%, 15% 100%)"></div>
<div class="absolute w-64 h-64 bg-gradient-radial from-white/30 via-white/15 to-transparent rounded-full top-1/3 left-1/3 blur-lg" ></div>
<div class="absolute inset-0 bg-gradient-to-br from-black/5 via-white/10 to-black/5 mix-blend-overlay" ></div>
<div class="absolute w-full h-full bg-gradient-to-tr from-transparent via-white/10 to-transparent animate-pulse" ></div>

                <div class="preview-text relative z-10">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>
                    <div class="preview-title">Hello World</div>
                    <div class="preview-subtitle">你好，世界</div>
                </div>
            </div>
        </div>
        
        <div>
            <h3 class="text-lg font-semibold mb-2 text-center">星际漫游 (cosmic_journey)</h3>
            <div class="preview-card bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
                <div class="absolute w-2 h-2 bg-white rounded-full top-16 left-20" ></div>
<div class="absolute w-1 h-1 bg-white/80 rounded-full top-24 right-32" ></div>
<div class="absolute w-3 h-3 bg-white/60 rounded-full bottom-32 left-16" ></div>
<div class="absolute w-1 h-1 bg-white rounded-full bottom-20 right-20" ></div>
<div class="absolute w-96 h-96 bg-gradient-radial from-purple-500/10 to-transparent rounded-full blur-3xl -top-20 -right-20" ></div>
<div class="absolute w-80 h-80 bg-gradient-radial from-blue-400/8 to-transparent rounded-full blur-3xl -bottom-16 -left-16" ></div>

                <div class="preview-text relative z-10">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>
                    <div class="preview-title">Hello World</div>
                    <div class="preview-subtitle">你好，世界</div>
                </div>
            </div>
        </div>
        
        <div>
            <h3 class="text-lg font-semibold mb-2 text-center">森林幻境 (forest_magic)</h3>
            <div class="preview-card bg-gradient-to-br from-green-400 via-emerald-500 to-teal-600">
                <div class="absolute w-[600px] h-[600px] bg-yellow-400/10 blur-3xl -top-32 -right-32 transform rotate-12" style="clip-path: circle(50% at 50% 50%)"></div>
<div class="absolute w-[500px] h-[500px] bg-white/15 blur-3xl -top-24 -left-32 transform rotate-45" style="clip-path: polygon(0% 0%, 100% 0%, 75% 100%, 25% 100%)"></div>
<div class="absolute w-64 h-64 bg-white/12 blur-3xl -top-8 -left-8 transform rotate-12" ></div>
<div class="absolute w-56 h-80 bg-white/8 blur-3xl -bottom-12 -right-4 transform -rotate-45" ></div>
<div class="absolute w-40 h-40 bg-white/15 rounded-full blur-xl top-1/4 right-1/4" ></div>
<div class="absolute w-32 h-48 bg-white/10 blur-2xl bottom-1/3 left-1/4 transform rotate-30" ></div>

                <div class="preview-text relative z-10">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>
                    <div class="preview-title">Hello World</div>
                    <div class="preview-subtitle">你好，世界</div>
                </div>
            </div>
        </div>
        
        <div>
            <h3 class="text-lg font-semibold mb-2 text-center">极地极光 (aurora_dreams)</h3>
            <div class="preview-card bg-gradient-to-br from-indigo-900 via-purple-600 to-green-400">
                <div class="absolute w-full h-16 bg-gradient-to-r from-transparent via-green-300/40 to-transparent top-12 transform -skew-y-3 blur-md" ></div>
<div class="absolute w-full h-12 bg-gradient-to-r from-transparent via-blue-300/50 to-transparent top-24 transform skew-y-2 blur-lg" ></div>
<div class="absolute w-full h-20 bg-gradient-to-r from-transparent via-purple-300/35 to-transparent top-40 transform -skew-y-1 blur-xl" ></div>
<div class="absolute w-full h-8 bg-gradient-to-r from-transparent via-pink-300/45 to-transparent top-64 transform skew-y-3 blur-sm" ></div>
<div class="absolute w-96 h-96 bg-gradient-radial from-green-400/10 to-transparent rounded-full blur-3xl -top-16 -right-16" ></div>
<div class="absolute w-80 h-80 bg-gradient-radial from-purple-400/15 to-transparent rounded-full blur-2xl -bottom-8 -left-8" ></div>

                <div class="preview-text relative z-10">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>
                    <div class="preview-title">Hello World</div>
                    <div class="preview-subtitle">你好，世界</div>
                </div>
            </div>
        </div>
        
        <div>
            <h3 class="text-lg font-semibold mb-2 text-center">冰雪奇缘 (frozen_wonder)</h3>
            <div class="preview-card bg-gradient-to-br from-blue-200 via-cyan-300 to-indigo-400">
                <div class="absolute w-16 h-16 bg-white/20 -top-4 -left-4" style="clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)"></div>
<div class="absolute w-12 h-12 bg-white/15 top-12 right-8" style="clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)"></div>
<div class="absolute w-20 h-20 bg-white/10 bottom-16 left-12" style="clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)"></div>
<div class="absolute w-8 h-8 bg-white/25 bottom-8 right-16" style="clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)"></div>
<div class="absolute w-14 h-14 bg-white/10 top-1/2 right-1/4" style="clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)"></div>
<div class="absolute w-96 h-96 bg-white/5 rounded-full blur-3xl -top-20 -right-20" ></div>

                <div class="preview-text relative z-10">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>
                    <div class="preview-title">Hello World</div>
                    <div class="preview-subtitle">你好，世界</div>
                </div>
            </div>
        </div>
        
        <div>
            <h3 class="text-lg font-semibold mb-2 text-center">霓虹夜色 (neon_night)</h3>
            <div class="preview-card bg-gradient-to-br from-purple-900 via-pink-800 to-indigo-900">
                <div class="absolute w-2 h-80 bg-pink-300/40 blur-sm top-0 left-12 shadow-lg shadow-pink-500/20" ></div>
<div class="absolute w-2 h-64 bg-cyan-300/40 blur-sm top-8 right-20 shadow-lg shadow-cyan-500/20" ></div>
<div class="absolute w-80 h-2 bg-purple-300/40 blur-sm bottom-16 left-0 shadow-lg shadow-purple-500/20" ></div>
<div class="absolute w-64 h-2 bg-yellow-300/40 blur-sm top-24 left-8 shadow-lg shadow-yellow-500/20" ></div>
<div class="absolute w-48 h-48 bg-gradient-radial from-pink-400/10 to-transparent rounded-full blur-2xl top-1/4 right-1/4" ></div>
<div class="absolute w-32 h-32 bg-gradient-radial from-cyan-400/15 to-transparent rounded-full blur-xl bottom-1/3 left-1/3" ></div>
<div class="absolute w-24 h-24 bg-gradient-radial from-purple-400/20 to-transparent rounded-full blur-lg top-1/2 left-1/2" ></div>

                <div class="preview-text relative z-10">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>
                    <div class="preview-title">Hello World</div>
                    <div class="preview-subtitle">你好，世界</div>
                </div>
            </div>
        </div>
        
        <div>
            <h3 class="text-lg font-semibold mb-2 text-center">沙漠风暴 (desert_storm)</h3>
            <div class="preview-card bg-gradient-to-br from-amber-300 via-orange-400 to-red-500">
                <div class="absolute w-[800px] h-[400px] bg-white/15 blur-3xl -bottom-24 -left-32 transform -rotate-3" style="clip-path: polygon(0% 100%, 100% 100%, 100% 30%, 60% 0%, 30% 20%, 0% 60%)"></div>
<div class="absolute w-[400px] h-[300px] bg-white/12 blur-[100px] bottom-48 left-0 transform -rotate-6" style="clip-path: polygon(0% 100%, 100% 100%, 80% 0%, 20% 0%)"></div>
<div class="absolute w-[600px] h-[250px] bg-white/10 blur-lg -bottom-12 right-0 transform rotate-2" style="clip-path: polygon(30% 0%, 100% 100%, 0% 100%, 0% 50%)"></div>
<div class="absolute w-full h-48 bg-gradient-to-r from-white/8 via-white/5 to-transparent blur-2xl bottom-64" ></div>
<div class="absolute w-full h-40 bg-gradient-to-r from-transparent via-white/8 to-transparent blur-md bottom-0 transform -skew-y-2" ></div>

                <div class="preview-text relative z-10">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>
                    <div class="preview-title">Hello World</div>
                    <div class="preview-subtitle">你好，世界</div>
                </div>
            </div>
        </div>
        
        <div>
            <h3 class="text-lg font-semibold mb-2 text-center">樱花飞舞 (cherry_blossom)</h3>
            <div class="preview-card bg-gradient-to-br from-pink-200 via-rose-300 to-pink-400">
                <div class="absolute w-[400px] h-[400px] bg-white/10 rounded-full blur-3xl -top-24 -left-24" ></div>
<div class="absolute w-48 h-48 bg-white/40 rounded-full blur-xl top-8 left-8" ></div>
<div class="absolute w-40 h-40 bg-white/35 rounded-full blur-lg top-24 right-12" ></div>
<div class="absolute w-56 h-56 bg-white/30 rounded-full blur-2xl bottom-16 left-16" ></div>
<div class="absolute w-44 h-44 bg-white/45 rounded-full blur-xl bottom-24 right-8" ></div>
<div class="absolute w-16 h-16 bg-white/50 rounded-full blur-sm top-32 left-32" ></div>
<div class="absolute w-12 h-12 bg-white/55 rounded-full blur-sm bottom-40 right-36" ></div>
<div class="absolute w-14 h-14 bg-white/45 rounded-full blur-sm top-48 right-24" ></div>
<div class="absolute w-10 h-10 bg-white/60 rounded-full blur-sm bottom-32 left-40" ></div>

                <div class="preview-text relative z-10">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>
                    <div class="preview-title">Hello World</div>
                    <div class="preview-subtitle">你好，世界</div>
                </div>
            </div>
        </div>
        
        </div>
    </div>
</body>
</html>