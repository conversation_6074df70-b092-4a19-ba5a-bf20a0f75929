# -*- coding: utf-8 -*-
"""
简化版封面生成器
只生成HTML文件，不依赖Playwright
适合快速测试和预览
"""

import sys
import hashlib
from pathlib import Path
from jinja2 import Environment, FileSystemLoader

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from cover_config import (
    get_font_keys, get_theme_keys, get_font_config, get_theme_config, get_card_icon, DEFAULT_CONFIG
)


class SimpleCoverGenerator:
    """简化封面生成器"""
    
    def __init__(self, template_dir=None, output_dir=None):
        script_dir = Path(__file__).parent
        self.template_dir = Path(template_dir) if template_dir else script_dir
        self.output_dir = Path(output_dir) if output_dir else script_dir / "output"
        self.output_dir.mkdir(exist_ok=True)
        
        # 初始化Jinja2环境
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(self.template_dir)),
            autoescape=True
        )
        
        # 缓存字体和主题列表
        self.font_keys = get_font_keys()
        self.theme_keys = get_theme_keys()
        
        print(f"简化封面生成器初始化完成")
        print(f"模板目录: {self.template_dir}")
        print(f"输出目录: {self.output_dir}")

    def clean_text(self, text):
        """清理文本"""
        if not text:
            return ""
        
        import re
        # 移除emoji
        emoji_pattern = r'[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF\U00002600-\U000026FF\U00002700-\U000027BF]+'
        text = re.sub(emoji_pattern, '', text)
        
        # 移除多余空白
        text = re.sub(r'\s+', ' ', text).strip()
        return text

    def get_deterministic_selection(self, cognition_id):
        """确定性选择字体和主题"""
        hash_input = str(cognition_id).encode('utf-8')
        hash_digest = hashlib.md5(hash_input).hexdigest()
        
        font_seed = int(hash_digest[:8], 16)
        theme_seed = int(hash_digest[8:16], 16)
        
        font_key = self.font_keys[font_seed % len(self.font_keys)]
        theme_key = self.theme_keys[theme_seed % len(self.theme_keys)]
        
        return font_key, theme_key

    def format_text_by_font(self, text, font_config):
        """根据字体配置格式化文本"""
        if not font_config.get("use_mixed_fonts", False):
            return text, font_config.get("container_class", "")
        
        import re
        tokenizer = re.compile(r'[a-zA-Z0-9\._-]+|[\u4e00-\u9fa5]|[，。；、,.!?;]')
        tokens = tokenizer.findall(text)
        
        html_parts = []
        en_class = font_config.get("en_class", "")
        zh_class = font_config.get("zh_class", "")
        
        for token in tokens:
            if re.match(r'[a-zA-Z0-9]', token):
                html_parts.append(f'<span class="{en_class}">{token}</span>')
            elif token in '，。；、,.!?;':
                html_parts.append(f'<span class="{zh_class}">{token}</span>')
            else:
                html_parts.append(f'<span class="{zh_class}">{token}</span>')
        
        formatted_html = ''.join(html_parts)
        return formatted_html, ""

    def prepare_template_data(self, cognition_data):
        """准备模板数据"""
        # 提取基础数据
        cognition_id = cognition_data.get('id', 1)
        card_type = cognition_data.get('tag', DEFAULT_CONFIG['card_type'])
        main_topic = cognition_data.get('primary_topic', DEFAULT_CONFIG['fallback_topic'])
        summary = cognition_data.get('abstract_zh', DEFAULT_CONFIG['fallback_text'])
        author_name = cognition_data.get('source', DEFAULT_CONFIG['fallback_author'])
        author_org = cognition_data.get('author_org', DEFAULT_CONFIG['fallback_org'])
        avatar_url = cognition_data.get('avatar_url')
        
        # 清理文本（但保留None值）
        main_topic = self.clean_text(main_topic) if main_topic else main_topic
        summary = self.clean_text(summary) if summary else summary
        author_name = self.clean_text(author_name) if author_name else author_name
        author_org = self.clean_text(author_org) if author_org else author_org
        
        # 确定性选择字体和主题
        font_key, theme_key = self.get_deterministic_selection(cognition_id)
        font_config = get_font_config(font_key)
        theme_config = get_theme_config(theme_key)
        
        # 格式化摘要文本
        formatted_summary, summary_class = self.format_text_by_font(summary, font_config)
        
        # 确定主题样式类
        topic_class = ""
        if font_config.get("use_mixed_fonts", False):
            topic_class = font_config.get("en_class", "")
        else:
            topic_class = font_config.get("container_class", "")
        
        # 准备模板数据
        template_data = {
            'main_topic': main_topic,
            'formatted_summary': formatted_summary,
            'card_type': card_type,
            'author_name': author_name,
            'author_org': author_org,
            'has_avatar': bool(avatar_url),
            'avatar_url': avatar_url or '',
            'font_link': font_config['link'],
            'container_class': font_config.get('container_class', ''),
            'summary_class': summary_class,
            'topic_class': topic_class,
            'gradient_class': theme_config['gradient_class'],
            'shapes': theme_config['shapes'],
            'card_icon': get_card_icon(card_type),
            'meta': {
                'cognition_id': cognition_id,
                'font_key': font_key,
                'theme_key': theme_key,
                'font_name': font_config['name'],
                'theme_name': theme_config['name']
            }
        }
        
        return template_data

    def generate_html(self, cognition_data, output_filename=None):
        """生成HTML文件"""
        cognition_id = cognition_data.get('id', 'unknown')
        
        if not output_filename:
            output_filename = f"cover_{cognition_id}.html"
        
        output_path = self.output_dir / output_filename
        
        try:
            # 渲染HTML
            template = self.jinja_env.get_template("cover_template.html")
            template_data = self.prepare_template_data(cognition_data)
            html_content = template.render(**template_data)
            
            # 保存HTML文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"HTML生成成功 - ID: {cognition_id}")
            print(f"  字体: {template_data['meta']['font_name']}")
            print(f"  主题: {template_data['meta']['theme_name']}")
            print(f"  文件: {output_path}")
            
            return str(output_path)
            
        except Exception as e:
            print(f"HTML生成失败 - ID: {cognition_id}, Error: {e}")
            raise

    def validate_cognition_data(self, data):
        """验证认知数据"""
        required_fields = ['id']
        
        for field in required_fields:
            if field not in data:
                print(f"数据验证失败: 缺少必需字段 '{field}'")
                return False
        
        return True


def generate_html_cover(cognition_data, output_dir=None, template_dir=None):
    """便利函数：生成单个HTML封面"""
    generator = SimpleCoverGenerator(template_dir=template_dir, output_dir=output_dir)
    
    if not generator.validate_cognition_data(cognition_data):
        raise ValueError("认知数据验证失败")
    
    return generator.generate_html(cognition_data)


def batch_generate_covers(cognitions_list, output_dir=None, template_dir=None):
    """批量生成封面HTML文件"""
    generator = SimpleCoverGenerator(template_dir=template_dir, output_dir=output_dir)
    
    results = []
    errors = []
    
    print(f"开始批量生成 {len(cognitions_list)} 个封面...")
    
    for i, cognition_data in enumerate(cognitions_list, 1):
        try:
            if not generator.validate_cognition_data(cognition_data):
                errors.append(f"ID {cognition_data.get('id', 'unknown')}: 数据验证失败")
                continue
            
            output_path = generator.generate_html(cognition_data)
            results.append(output_path)
            
            print(f"进度: {i}/{len(cognitions_list)}")
            
        except Exception as e:
            error_msg = f"ID {cognition_data.get('id', 'unknown')}: {e}"
            errors.append(error_msg)
            print(f"生成失败: {error_msg}")
    
    print(f"\n批量生成完成!")
    print(f"成功: {len(results)} 个")
    print(f"失败: {len(errors)} 个")
    
    if errors:
        print("\n失败列表:")
        for error in errors:
            print(f"  - {error}")
    
    return results, errors


if __name__ == "__main__":
    # 测试代码
    test_data = {
        'id': 1,
        'tag': '验证',
        'primary_topic': 'Chain of Thought',
        'abstract_zh': '步骤分解让大模型推理更加透明可解释',
        'source': 'Wei et al.',
        'author_org': 'DeepMind',
        'avatar_url': None
    }
    
    result = generate_html_cover(test_data)
    print(f"测试完成，生成HTML: {result}")
