import cover_config

def generate_font_previews():
    """根据 FONT_LIBRARY 生成字体预览的 HTML 和 CSS"""
    html_output = ""
    css_output = ""
    
    for key, font in cover_config.FONT_LIBRARY.items():
        font_name = font["name"]
        container_class = font.get("container_class")
        
        # 提取字体族名称用于CSS
        # 例如: "Poppins:wght@700;900" -> "Poppins"
        font_families = []
        link_parts = font["link"].split('family=')
        for part in link_parts[1:]:
            family_name = part.split(':')[0].split('&')[0].replace('+', ' ')
            font_families.append(f"'{family_name}'")

        # 生成预览卡片
        card_html = f"""
        <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 class="text-xl font-bold mb-4 text-cyan-400">{font_name} ({key})</h3>
        """
        
        if font["use_mixed_fonts"]:
            en_class = font["en_class"]
            zh_class = font["zh_class"]
            # 分别应用中英文字体
            card_html += f"""
            <div class="text-white text-2xl">
                <p class="{en_class}">The quick brown fox jumps over the lazy dog.</p>
                <p class="{zh_class} mt-2">敏捷的棕色狐狸跳过了懒惰的狗。</p>
            </div>
            """
            # 生成 CSS 规则
            css_output += f".{en_class} {{ font-family: {font_families[0]}, sans-serif; }}\n"
            css_output += f".{zh_class} {{ font-family: {font_families[1]}, sans-serif; }}\n"

        else:
            # 统一应用字体
            card_html += f"""
            <div class="text-white text-2xl {container_class}">
                <p>The quick brown fox / 敏捷的棕色狐狸</p>
            </div>
            """
            # 生成 CSS 规则
            css_output += f".{container_class} {{ font-family: {', '.join(font_families)}, sans-serif; }}\n"

        card_html += "</div>"
        html_output += card_html

    return html_output, css_output

def generate_theme_previews():
    """根据 THEME_LIBRARY 生成主题预览的 HTML"""
    html_output = ""
    for key, theme in cover_config.THEME_LIBRARY.items():
        name = theme["name"]
        gradient = theme["gradient_class"]
        
        shapes_html = ""
        for shape in theme["shapes"]:
            style_attr = ""
            if "clip_path" in shape:
                style_attr = f"style=\"clip-path: {shape['clip_path']}\""
            shapes_html += f'<div class="{shape["class"]}" {style_attr}></div>\n'

        card_html = f"""
        <div>
            <h3 class="text-lg font-semibold mb-2 text-center">{name} ({key})</h3>
            <div class="preview-card {gradient}">
                {shapes_html}
                <div class="preview-text relative z-10">
                    {cover_config.CARD_TYPE_ICONS['洞察']}
                    <div class="preview-title">Hello World</div>
                    <div class="preview-subtitle">你好，世界</div>
                </div>
            </div>
        </div>
        """
        html_output += card_html
    return html_output

def main():
    """主函数，生成预览文件"""
    # 读取模板文件
    with open("template.html", "r", encoding="utf-8") as f:
        template_content = f.read()
    
    # 1. 收集所有 Google Fonts 链接
    font_links = [font["link"] for font in cover_config.FONT_LIBRARY.values()]
    # 去重并合并
    unique_links = list(set(font_links))
    google_fonts_links_html = "\n".join([f'<link href="{link}" rel="stylesheet">' for link in unique_links])

    # 2. 生成字体预览内容
    font_previews_html, custom_font_styles_css = generate_font_previews()

    # 3. 生成主题预览内容
    theme_previews_html = generate_theme_previews()

    # 4. 替换模板中的占位符
    final_html = template_content.replace("{{google_fonts_links}}", google_fonts_links_html)
    final_html = final_html.replace("{{custom_font_styles}}", custom_font_styles_css)
    final_html = final_html.replace("{{font_previews}}", font_previews_html)
    final_html = final_html.replace("{{theme_previews}}", theme_previews_html)

    # 5. 写入最终的预览文件
    with open("preview.html", "w", encoding="utf-8") as f:
        f.write(final_html)
    
    print("🎉 预览文件 'preview.html' 已成功生成！请在浏览器中打开它。")

if __name__ == "__main__":
    main()