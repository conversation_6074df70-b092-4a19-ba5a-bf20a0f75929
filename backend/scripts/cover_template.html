<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>认知平台封面 - {{ main_topic }}</title>
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- 基础字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet" />

    <!-- 动态字体 -->
    <link href="{{ font_link }}" rel="stylesheet" />

    <style>
        /* 基础字体和动态字体类 */
        .font-ui {
            font-family: "Poppins", sans-serif;
        }
        .font-combo-1 {
            font-family: "Poppins", "Noto Sans SC", sans-serif;
        }
        .font-combo-2-en {
            font-family: "Lexend", sans-serif;
        }
        .font-combo-2-zh {
            font-family: "Ma <PERSON>", cursive;
        }
        .font-combo-3-en {
            font-family: "Fredoka One", cursive;
        }
        .font-combo-3-zh {
            font-family: "ZCOOL KuaiLe", cursive;
        }
        .font-combo-4 {
            font-family: "Playfair Display", "LXGW WenKai", serif;
        }
        .font-combo-5-en {
            font-family: "Orbitron", monospace;
        }
        .font-combo-5-zh {
            font-family: "Noto Sans SC", sans-serif;
            font-weight: 300;
        }
        .font-combo-6-en {
            font-family: "Dancing Script", cursive;
        }
        .font-combo-6-zh {
            font-family: "Zhi Mang Xing", cursive;
        }
        .font-combo-7 {
            font-family: "Roboto Mono", "Source Han Sans SC", monospace;
        }
        .font-combo-8-en {
            font-family: "Crimson Text", serif;
        }
        .font-combo-8-zh {
            font-family: "Long Cang", cursive;
        }

        /* 无头像布局样式 */
        .no-avatar-layout .avatar-container {
            display: none;
        }
        .no-avatar-layout .content-main {
            grid-template-columns: 1fr;
            text-align: center;
        }
        .no-avatar-layout #summary-container {
            text-align: center;
            font-size: 3.5rem;
            line-height: 1.1;
        }

        /* 主题文本样式优化 */
        #main-topic {
            word-wrap: break-word;
            hyphens: auto;
            overflow-wrap: break-word;
        }

        /* 针对不同字体的主题大小调整 */
        .font-combo-3-en #main-topic,
        .font-combo-3-zh #main-topic {
            font-size: 1.25rem;
        }
        .font-combo-6-en #main-topic,
        .font-combo-6-zh #main-topic {
            font-size: 1.375rem;
        }
        .font-combo-8-en #main-topic,
        .font-combo-8-zh #main-topic {
            font-size: 1.375rem;
        }

        /* 调试信息样式 */
        .debug-info {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-size: 12px;
            font-family: monospace;
            max-width: 300px;
            z-index: 1000;
        }
        .debug-info h4 {
            margin: 0 0 10px 0;
            font-size: 14px;
            font-weight: bold;
            color: #60A5FA;
        }
        .debug-info .debug-item {
            margin: 5px 0;
        }
        .debug-info .debug-label {
            color: #A78BFA;
            font-weight: bold;
        }
    </style>
</head>
<body class="bg-gray-900 flex items-center justify-center min-h-screen p-4 font-ui">
    <div id="poster-container" class="w-[400px] h-[600px] rounded-xl shadow-2xl overflow-hidden relative {{ container_class }} {{ gradient_class }}{% if not has_avatar %} no-avatar-layout{% endif %}">
        
        <!-- 动态背景层 -->
        <div id="dynamic-background-shapes" class="absolute inset-0 z-10">
            {% for shape in shapes %}
            <div class="{{ shape.class }}"{% if shape.clip_path %} style="clip-path: {{ shape.clip_path }}"{% endif %}></div>
            {% endfor %}
        </div>

        <!-- 内容层 -->
        <div class="absolute inset-0 z-20 p-8 flex flex-col text-white">
            <h1 id="main-topic" class="absolute top-8 right-8 text-2xl font-bold drop-shadow-md text-right max-w-[200px] leading-tight {{ topic_class }}">{{ main_topic }}</h1>
            
            <div id="card-type-icon" class="self-start bg-white/20 rounded-full p-3 backdrop-blur-md w-fit mb-6">
                {{ card_icon|safe }}
            </div>
            
            <div id="content-main" class="flex-grow {% if has_avatar %}grid grid-cols-2 gap-4 items-center{% else %}flex items-center justify-center{% endif %} mt-12 content-main">
                {% if has_avatar %}
                <div id="avatar-container" class="flex justify-center items-center avatar-container">
                    <img id="avatar" class="w-32 h-32 object-cover rounded-full border-4 border-white/30 backdrop-blur-sm shadow-lg" src="{{ avatar_url }}" alt="Author Avatar" />
                </div>
                {% endif %}
                
                <p id="summary-container" class="text-4xl font-bold drop-shadow-md leading-tight {{ summary_class }}">{{ formatted_summary|safe }}</p>
            </div>
            
            <div class="flex-shrink-0 mt-auto">
                <div class="flex items-center gap-3 text-sm text-white/80">
                    <div>
                        {% if author_name %}
                        <p id="author-name" class="font-bold">{{ author_name }}</p>
                        {% endif %}
                        {% if author_org %}
                        <p id="author-org" class="opacity-80">{{ author_org }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 调试信息 -->
    {% if meta %}
    <div class="debug-info">
        <h4>🔧 生成配置</h4>
        <div class="debug-item">
            <span class="debug-label">ID:</span> {{ meta.cognition_id }}
        </div>
        <div class="debug-item">
            <span class="debug-label">字体:</span> {{ meta.font_name }}<br>
            <small>({{ meta.font_key }})</small>
        </div>
        <div class="debug-item">
            <span class="debug-label">主题:</span> {{ meta.theme_name }}<br>
            <small>({{ meta.theme_key }})</small>
        </div>
        <div class="debug-item">
            <span class="debug-label">卡片类型:</span> {{ card_type }}
        </div>
        <div class="debug-item">
            <span class="debug-label">头像:</span> {{ "有" if has_avatar else "无" }}
        </div>
    </div>
    {% endif %}
</body>
</html> 