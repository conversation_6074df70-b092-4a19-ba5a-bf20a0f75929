# -*- coding: utf-8 -*-
"""
认知平台封面生成器
基于celebrity.html优化的封面生成系统
"""

import os
import re
import hashlib
import base64
from pathlib import Path
from typing import Dict, Optional, Tuple
from jinja2 import Environment, FileSystemLoader
import asyncio
from playwright.async_api import async_playwright

from cover_config import (
    FONT_LIBRARY, THEME_LIBRARY, CARD_TYPE_ICONS, DEFAULT_CONFIG,
    get_font_keys, get_theme_keys, get_font_config, get_theme_config, get_card_icon
)


class CoverGenerator:
    """封面生成器类"""
    
    def __init__(self, template_dir: str = None, output_dir: str = None):
        """
        初始化封面生成器
        
        Args:
            template_dir: 模板文件目录，默认为当前脚本目录
            output_dir: 输出图片目录，默认为当前目录/output
        """
        # 设置目录路径
        script_dir = Path(__file__).parent
        self.template_dir = Path(template_dir) if template_dir else script_dir
        self.output_dir = Path(output_dir) if output_dir else script_dir / "output"
        self.output_dir.mkdir(exist_ok=True)
        
        # 初始化Jinja2环境
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(self.template_dir)),
            autoescape=True
        )
        
        # 缓存字体和主题列表
        self.font_keys = get_font_keys()
        self.theme_keys = get_theme_keys()
        
        print(f"封面生成器初始化完成")
        print(f"模板目录: {self.template_dir}")
        print(f"输出目录: {self.output_dir}")
        print(f"可用字体: {len(self.font_keys)} 种")
        print(f"可用主题: {len(self.theme_keys)} 种")

    def clean_text(self, text: str) -> str:
        """
        清理文本，移除emoji和特殊字符，参考celebrity.html的处理逻辑
        
        Args:
            text: 原始文本
            
        Returns:
            清理后的文本
        """
        if not text:
            return ""
        
        # 移除emoji（参考celebrity.html的emojiRegex）
        emoji_pattern = r'[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF\U00002600-\U000026FF\U00002700-\U000027BF]+'
        text = re.sub(emoji_pattern, '', text)
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text

    def get_deterministic_selection(self, cognition_id: int) -> Tuple[str, str]:
        """
        基于认知ID进行确定性的字体和主题选择
        
        Args:
            cognition_id: 认知卡片ID
            
        Returns:
            (font_key, theme_key) 选择的字体和主题键
        """
        # 使用认知ID的哈希值作为种子
        hash_input = str(cognition_id).encode('utf-8')
        hash_digest = hashlib.md5(hash_input).hexdigest()
        
        # 将哈希值转换为数字用于索引
        font_seed = int(hash_digest[:8], 16)
        theme_seed = int(hash_digest[8:16], 16)
        
        # 通过取模确定性选择
        font_key = self.font_keys[font_seed % len(self.font_keys)]
        theme_key = self.theme_keys[theme_seed % len(self.theme_keys)]
        
        return font_key, theme_key

    def format_text_by_font(self, text: str, font_config: Dict) -> Tuple[str, str]:
        """
        根据字体配置格式化文本，参考celebrity.html的formatter逻辑
        
        Args:
            text: 要格式化的文本
            font_config: 字体配置字典
            
        Returns:
            (formatted_html, css_class) 格式化后的HTML和CSS类
        """
        if not font_config.get("use_mixed_fonts", False):
            # 统一字体，直接返回
            return text, font_config.get("container_class", "")
        
        # 混合字体处理（中英文分离）
        tokenizer = re.compile(r'[a-zA-Z0-9\._-]+|[\u4e00-\u9fa5]|[，。；、,.!?;]')
        tokens = tokenizer.findall(text)
        
        html_parts = []
        en_class = font_config.get("en_class", "")
        zh_class = font_config.get("zh_class", "")
        
        for token in tokens:
            if re.match(r'[a-zA-Z0-9]', token):
                html_parts.append(f'<span class="{en_class}">{token}</span>')
            elif token in '，。；、,.!?;':
                html_parts.append(f'<span class="{zh_class}">{token}</span><br>')
            else:
                html_parts.append(f'<span class="{zh_class}">{token}</span>')
        
        # 移除末尾的<br>
        formatted_html = ''.join(html_parts)
        if formatted_html.endswith('<br>'):
            formatted_html = formatted_html[:-4]
        
        return formatted_html, ""

    def prepare_template_data(self, cognition_data: Dict) -> Dict:
        """
        准备模板渲染数据
        
        Args:
            cognition_data: 认知卡片数据
            
        Returns:
            模板渲染数据字典
        """
        # 提取基础数据并应用兜底值
        cognition_id = cognition_data.get('id', 1)
        card_type = cognition_data.get('tag', DEFAULT_CONFIG['card_type'])
        main_topic = cognition_data.get('primary_topic', DEFAULT_CONFIG['fallback_topic'])
        summary = cognition_data.get('abstract_zh', DEFAULT_CONFIG['fallback_text'])
        author_name = cognition_data.get('source', DEFAULT_CONFIG['fallback_author'])
        author_org = cognition_data.get('author_org', DEFAULT_CONFIG['fallback_org'])
        avatar_url = cognition_data.get('avatar_url')
        
        # 清理文本（但保留None值）
        main_topic = self.clean_text(main_topic) if main_topic else main_topic
        summary = self.clean_text(summary) if summary else summary
        author_name = self.clean_text(author_name) if author_name else author_name
        author_org = self.clean_text(author_org) if author_org else author_org
        
        # 确定性选择字体和主题
        font_key, theme_key = self.get_deterministic_selection(cognition_id)
        font_config = get_font_config(font_key)
        theme_config = get_theme_config(theme_key)
        
        # 格式化摘要文本
        formatted_summary, summary_class = self.format_text_by_font(summary, font_config)
        
        # 确定主题样式类
        topic_class = ""
        if font_config.get("use_mixed_fonts", False):
            # 混合字体的主题使用英文类
            topic_class = font_config.get("en_class", "")
        else:
            topic_class = font_config.get("container_class", "")
        
        # 准备模板数据
        template_data = {
            # 基础内容
            'main_topic': main_topic,
            'formatted_summary': formatted_summary,
            'card_type': card_type,
            'author_name': author_name,  # 可能为None
            'author_org': author_org,    # 可能为None
            
            # 头像相关
            'has_avatar': bool(avatar_url),
            'avatar_url': avatar_url or '',
            
            # 字体相关
            'font_link': font_config['link'],
            'container_class': font_config.get('container_class', ''),
            'summary_class': summary_class,
            'topic_class': topic_class,
            
            # 主题相关
            'gradient_class': theme_config['gradient_class'],
            'shapes': theme_config['shapes'],
            
            # 图标
            'card_icon': get_card_icon(card_type),
            
            # 元数据（用于调试）
            'meta': {
                'cognition_id': cognition_id,
                'font_key': font_key,
                'theme_key': theme_key,
                'font_name': font_config['name'],
                'theme_name': theme_config['name']
            }
        }
        
        return template_data

    async def render_html(self, cognition_data: Dict, template_name: str = "cover_template.html") -> str:
        """
        渲染HTML模板
        
        Args:
            cognition_data: 认知卡片数据
            template_name: 模板文件名
            
        Returns:
            渲染后的HTML字符串
        """
        try:
            template = self.jinja_env.get_template(template_name)
            template_data = self.prepare_template_data(cognition_data)
            html_content = template.render(**template_data)
            
            print(f"HTML模板渲染成功 - ID: {cognition_data.get('id', 'unknown')}")
            print(f"   字体: {template_data['meta']['font_name']}")
            print(f"   主题: {template_data['meta']['theme_name']}")
            
            return html_content
            
        except Exception as e:
            print(f"HTML模板渲染失败: {e}")
            raise

    async def generate_image(self, cognition_data: Dict, 
                           output_filename: str = None,
                           width: int = 400, height: int = 600) -> str:
        """
        生成封面图片
        
        Args:
            cognition_data: 认知卡片数据
            output_filename: 输出文件名，默认为cognition_{id}.png
            width: 图片宽度，默认400px
            height: 图片高度，默认600px
            
        Returns:
            生成的图片文件路径
        """
        cognition_id = cognition_data.get('id', 'unknown')
        
        if not output_filename:
            output_filename = f"cognition_{cognition_id}.png"
        
        output_path = self.output_dir / output_filename
        
        try:
            # 渲染HTML
            html_content = await self.render_html(cognition_data)
            
            # 使用Playwright截图
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                page = await browser.new_page()
                
                # 设置页面内容
                await page.set_content(html_content, wait_until='networkidle')
                
                # 等待字体加载
                await page.wait_for_timeout(2000)
                
                # 定位封面容器并截图（移除quality参数，PNG不支持）
                poster_element = page.locator('#poster-container')
                await poster_element.screenshot(
                    path=str(output_path),
                    type='png'
                )
                
                await browser.close()
            
            print(f"封面生成成功: {output_path}")
            return str(output_path)
            
        except Exception as e:
            print(f"封面生成失败 - ID: {cognition_id}, Error: {e}")
            raise

    def validate_cognition_data(self, data: Dict) -> bool:
        """
        验证认知数据的完整性
        
        Args:
            data: 认知数据字典
            
        Returns:
            验证是否通过
        """
        required_fields = ['id']  # 只要求ID字段，其他都有兜底
        
        for field in required_fields:
            if field not in data:
                print(f"数据验证失败: 缺少必需字段 '{field}'")
                return False
        
        return True

# 便利函数
async def generate_cover(cognition_data: Dict, 
                        output_dir: str = None,
                        template_dir: str = None) -> str:
    """
    便利函数：生成单个封面
    
    Args:
        cognition_data: 认知卡片数据
        output_dir: 输出目录
        template_dir: 模板目录
        
    Returns:
        生成的图片文件路径
    """
    generator = CoverGenerator(template_dir=template_dir, output_dir=output_dir)
    
    if not generator.validate_cognition_data(cognition_data):
        raise ValueError("认知数据验证失败")
    
    return await generator.generate_image(cognition_data)


if __name__ == "__main__":
    # 测试代码
    test_data = {
        'id': 1,
        'tag': '验证',
        'primary_topic': 'Chain of Thought',
        'abstract_zh': '步骤分解让大模型推理更加透明可解释',
        'source': 'Wei et al.',
        'author_org': 'DeepMind',
        'avatar_url': None
    }
    
    async def test():
        result = await generate_cover(test_data)
        print(f"测试完成，生成图片: {result}")
    
    asyncio.run(test()) 