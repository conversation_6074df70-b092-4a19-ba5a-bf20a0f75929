<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字体与主题样式预览</title>
    <!-- 引入 Tailwind CSS Play CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入所有 Google Fonts -->
    {{google_fonts_links}}
    <style>
        /* 自定义字体样式 */
        {{custom_font_styles}}
        
        /* 页面基础样式 */
        body { background-color: #111827; color: #f3f4f6; font-family: sans-serif; }
        .preview-card {
            position: relative;
            width: 480px;
            height: 270px;
            border-radius: 1.5rem;
            overflow: hidden;
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 2rem;
            color: white;
        }
        .preview-text {
            text-align: center;
            text-shadow: 0 2px 4px rgba(0,0,0,0.5);
        }
        .preview-title { font-size: 2.25rem; line-height: 2.5rem; font-weight: 900; }
        .preview-subtitle { font-size: 1.125rem; line-height: 1.75rem; margin-top: 0.5rem; opacity: 0.9; }
    </style>
</head>
<body class="p-8">
    <div class="container mx-auto">
        <h1 class="text-4xl font-bold text-white mb-8 border-b border-gray-600 pb-4">字体样式预览</h1>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- 字体预览内容将在这里生成 -->
            {{font_previews}}
        </div>

        <h1 class="text-4xl font-bold text-white mt-16 mb-8 border-b border-gray-600 pb-4">主题样式预览</h1>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- 主题预览内容将在这里生成 -->
            {{theme_previews}}
        </div>
    </div>
</body>
</html>