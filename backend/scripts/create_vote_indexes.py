#!/usr/bin/env python3
"""
认知投票数据库索引创建脚本
用于优化投票查询性能
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from motor.motor_asyncio import AsyncIOMotorClient
from backend.config import settings
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def create_vote_indexes():
    """创建投票相关的数据库索引"""
    client = None
    try:
        # 连接MongoDB
        client = AsyncIOMotorClient(settings.MONGO_URI)
        db = client[settings.MONGO_DB_NAME]
        
        logger.info("开始创建投票数据库索引...")
        
        # 投票表索引
        votes_collection = db.cognition_votes
        
        # 1. 用户ID和认知ID的复合唯一索引（防止重复投票）
        try:
            await votes_collection.create_index(
                [("user_id", 1), ("cognition_id", 1)], 
                unique=True, 
                name="user_cognition_unique",
                background=True
            )
            logger.info("✓ 创建用户-认知唯一索引")
        except Exception as e:
            if "already exists" not in str(e):
                logger.warning(f"创建用户-认知唯一索引失败: {str(e)}")
        
        # 2. 认知ID索引（用于查询某个认知的所有投票）
        try:
            await votes_collection.create_index(
                [("cognition_id", 1)], 
                name="cognition_id_index",
                background=True
            )
            logger.info("✓ 创建认知ID索引")
        except Exception as e:
            if "already exists" not in str(e):
                logger.warning(f"创建认知ID索引失败: {str(e)}")
        
        # 3. 用户ID索引（用于查询某个用户的所有投票）
        try:
            await votes_collection.create_index(
                [("user_id", 1)], 
                name="user_id_index",
                background=True
            )
            logger.info("✓ 创建用户ID索引")
        except Exception as e:
            if "already exists" not in str(e):
                logger.warning(f"创建用户ID索引失败: {str(e)}")
        
        # 4. 投票类型索引（用于统计不同类型的投票）
        try:
            await votes_collection.create_index(
                [("vote_type", 1)], 
                name="vote_type_index",
                background=True
            )
            logger.info("✓ 创建投票类型索引")
        except Exception as e:
            if "already exists" not in str(e):
                logger.warning(f"创建投票类型索引失败: {str(e)}")
        
        # 5. 创建时间索引（用于按时间排序）
        try:
            await votes_collection.create_index(
                [("created_at", -1)], 
                name="created_at_desc_index",
                background=True
            )
            logger.info("✓ 创建创建时间索引")
        except Exception as e:
            if "already exists" not in str(e):
                logger.warning(f"创建创建时间索引失败: {str(e)}")
        
        # 6. 复合索引：认知ID + 投票类型（用于统计查询）
        try:
            await votes_collection.create_index(
                [("cognition_id", 1), ("vote_type", 1)], 
                name="cognition_vote_type_index",
                background=True
            )
            logger.info("✓ 创建认知-投票类型复合索引")
        except Exception as e:
            if "already exists" not in str(e):
                logger.warning(f"创建认知-投票类型复合索引失败: {str(e)}")
        
        logger.info("投票数据库索引创建完成！")
        
    except Exception as e:
        logger.error(f"创建投票数据库索引失败: {str(e)}")
        raise
    finally:
        if client:
            client.close()

async def main():
    """主函数"""
    try:
        await create_vote_indexes()
        logger.info("所有索引创建完成")
    except Exception as e:
        logger.error(f"脚本执行失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main()) 