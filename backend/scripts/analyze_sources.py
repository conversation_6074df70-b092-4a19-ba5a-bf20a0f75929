# -*- coding: utf-8 -*-
"""
分析认知数据中的source字段，查看头像匹配情况
"""

import sys
import json
from pathlib import Path
from collections import Counter

# 添加脚本目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from avatar_matcher_base64 import Base64AvatarMatcher

def analyze_sources(json_file_path: str):
    """分析source字段"""
    
    # 加载数据
    with open(json_file_path, 'r', encoding='utf-8') as f:
        cognitions = json.load(f)
    
    print(f"📊 分析 {len(cognitions)} 条认知数据中的source字段")
    
    # 提取所有source字段
    sources = []
    for cognition in cognitions:
        source = cognition.get('source', '')
        if source:
            sources.append(source)
    
    print(f"📋 共有 {len(sources)} 个非空source字段")
    
    # 统计最常见的source
    source_counter = Counter(sources)
    print(f"\n🔝 最常见的source (前20个):")
    for source, count in source_counter.most_common(20):
        print(f"  {count:3d} - {source}")
    
    # 初始化头像匹配器
    matcher = Base64AvatarMatcher()
    
    # 分析匹配情况
    print(f"\n🎭 头像匹配分析:")
    matched_sources = []
    unmatched_sources = []
    
    for source in set(sources):  # 去重分析
        match = matcher.match_avatar(source)
        if match:
            matched_sources.append((source, match))
        else:
            unmatched_sources.append(source)
    
    print(f"✅ 可匹配头像: {len(matched_sources)} 个唯一source")
    print(f"❌ 无法匹配: {len(unmatched_sources)} 个唯一source")
    
    if matched_sources:
        print(f"\n🎯 匹配成功的source:")
        for source, match in matched_sources:
            count = source_counter[source]
            print(f"  📸 {match['standard_name']} <- '{source}' ({count} 次, 置信度: {match['confidence']:.2f})")
    
    # 计算实际影响的认知数量
    total_matched_cognitions = sum(source_counter[source] for source, _ in matched_sources)
    print(f"\n📈 总体统计:")
    print(f"  总认知数: {len(cognitions)}")
    print(f"  有source的认知: {len(sources)}")
    print(f"  能匹配头像的认知: {total_matched_cognitions}")
    print(f"  头像匹配率: {total_matched_cognitions / len(cognitions) * 100:.1f}%")
    
    # 显示一些未匹配的样本
    if unmatched_sources:
        print(f"\n❓ 部分未匹配的source (前15个):")
        for source in sorted(unmatched_sources, key=lambda x: source_counter[x], reverse=True)[:15]:
            count = source_counter[source]
            print(f"  {count:3d} - {source}")


if __name__ == "__main__":
    json_file = "/home/<USER>/cognitions_export.json"
    
    if not Path(json_file).exists():
        print(f"❌ 文件不存在: {json_file}")
        sys.exit(1)
    
    analyze_sources(json_file) 