#!/usr/bin/env python3
"""
MongoDB索引创建脚本

运行此脚本来为DAIR项目创建必要的MongoDB索引，提高查询性能
"""

import asyncio
import logging
from motor.motor_asyncio import AsyncIOMotorClient
from backend.config import settings

logger = logging.getLogger(__name__)

async def create_indexes():
    """创建所有必要的MongoDB索引"""
    try:
        # 建立MongoDB连接
        mongo_uri = settings.MONGO_URI
        if "mongodb://" in mongo_uri and "@" not in mongo_uri:
            mongo_uri = mongo_uri.replace("mongodb://", "mongodb://dair:Dair1234@")
        
        client = AsyncIOMotorClient(
            mongo_uri,
            serverSelectionTimeoutMS=5000,
            connectTimeoutMS=10000,
            socketTimeoutMS=45000,
            maxPoolSize=100
        )
        
        db = client[settings.MONGO_DB_NAME]
        
        # 为research_sessions集合创建索引
        sessions_collection = db["research_sessions"]
        
        print("为research_sessions集合创建索引...")
        
        # session_id唯一索引（最重要）
        try:
            await sessions_collection.create_index("session_id", unique=True)
            print("✓ session_id唯一索引创建成功")
        except Exception as e:
            print(f"✗ session_id索引创建失败: {e}")
        
        # user_id索引
        try:
            await sessions_collection.create_index("user_id")
            print("✓ user_id索引创建成功")
        except Exception as e:
            print(f"✗ user_id索引创建失败: {e}")
        
        # last_activity索引（用于排序）
        try:
            await sessions_collection.create_index("last_activity")
            print("✓ last_activity索引创建成功")
        except Exception as e:
            print(f"✗ last_activity索引创建失败: {e}")
        
        # status索引
        try:
            await sessions_collection.create_index("status")
            print("✓ status索引创建成功")
        except Exception as e:
            print(f"✗ status索引创建失败: {e}")
        
        # token_usage查询复合索引
        try:
            await sessions_collection.create_index([("user_id", 1), ("token_usage", 1)])
            print("✓ user_id + token_usage复合索引创建成功")
        except Exception as e:
            print(f"✗ token_usage复合索引创建失败: {e}")
        
        # 为users集合创建索引
        users_collection = db["users"]
        
        print("\n为users集合创建索引...")
        
        # username唯一索引
        try:
            await users_collection.create_index("username", unique=True)
            print("✓ username唯一索引创建成功")
        except Exception as e:
            print(f"✗ username索引创建失败: {e}")
        
        # email唯一索引
        try:
            await users_collection.create_index("email", unique=True)
            print("✓ email唯一索引创建成功")
        except Exception as e:
            print(f"✗ email索引创建失败: {e}")
        
        # 为documents集合创建索引
        documents_collection = db["documents"]
        
        print("\n为documents集合创建索引...")
        
        # creator_id索引
        try:
            await documents_collection.create_index("creator_id")
            print("✓ creator_id索引创建成功")
        except Exception as e:
            print(f"✗ creator_id索引创建失败: {e}")
        
        # parent_id索引
        try:
            await documents_collection.create_index("parent_id")
            print("✓ parent_id索引创建成功")
        except Exception as e:
            print(f"✗ parent_id索引创建失败: {e}")
        
        # owner_id索引
        try:
            await documents_collection.create_index("owner_id")
            print("✓ owner_id索引创建成功")
        except Exception as e:
            print(f"✗ owner_id索引创建失败: {e}")
        
        print("\n✅ 索引创建完成！")
        
        # 显示索引信息
        print("\n📊 当前索引状态:")
        for collection_name in ["research_sessions", "users", "documents"]:
            collection = db[collection_name]
            indexes = await collection.list_indexes().to_list(length=None)
            print(f"\n{collection_name}集合的索引:")
            for idx in indexes:
                print(f"  - {idx['name']}: {idx.get('key', {})}")
        
        client.close()
        
    except Exception as e:
        print(f"❌ 创建索引失败: {e}")
        logger.error(f"创建索引失败: {e}")

if __name__ == "__main__":
    asyncio.run(create_indexes()) 