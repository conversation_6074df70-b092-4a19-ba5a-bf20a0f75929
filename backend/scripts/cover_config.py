# -*- coding: utf-8 -*-
"""
封面生成器配置文件
"""

# 字体库配置 - 中文差异化版本
FONT_LIBRARY = {
    "modern_sans": {
        "name": "典雅明朝",
        "link": "https://fonts.googleapis.com/css2?family=Playfair+Display:wght@700;900&family=Zen+Old+Mincho:wght@700;900&display=swap",
        "container_class": "",
        "use_mixed_fonts": True,
        "en_class": "font-combo-1-en",
        "zh_class": "font-combo-1-zh",
        "description": "Playfair Display + 禅宗明朝体，典雅传统风格"
    },
    "elegant_serif": {
        "name": "粗体衬线",
        "link": "https://fonts.googleapis.com/css2?family=Montserrat:wght@800;900&family=IBM+Plex+Sans+JP:wght@700;900&display=swap",
        "container_class": "",
        "use_mixed_fonts": True,
        "en_class": "font-combo-2-en", 
        "zh_class": "font-combo-2-zh",
        "description": "Montserrat + IBM Plex Sans JP，现代科技风格"
    },
    "academic_kai": {
        "name": "学术雅正",
        "link": "https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@600;700&family=Ma+Shan+Zheng&display=swap",
        "container_class": "",
        "use_mixed_fonts": True,
        "en_class": "font-combo-3-en",
        "zh_class": "font-combo-3-zh", 
        "description": "Cormorant Garamond + 马善政楷体，学术雅正风格"
    },
    "friendly_round": {
        "name": "温暖圆润",
        "link": "https://fonts.googleapis.com/css2?family=Nunito:wght@700;900&family=Zen+Maru+Gothic:wght@700;900&display=swap",
        "container_class": "",
        "use_mixed_fonts": True,
        "en_class": "font-combo-4-en",
        "zh_class": "font-combo-4-zh",
        "description": "Nunito + 禅宗圆体，温暖圆润风格"
    },
    "tech_future": {
        "name": "科技未来",
        "link": "https://fonts.googleapis.com/css2?family=Orbitron:wght@700;900&family=ZCOOL+XiaoWei&display=swap",
        "container_class": "",
        "use_mixed_fonts": True,
        "en_class": "font-combo-5-en",
        "zh_class": "font-combo-5-zh",
        "description": "Orbitron + 站酷小薇，科技未来风格"
    },
    "handwritten_warm": {
        "name": "手写书法",
        "link": "https://fonts.googleapis.com/css2?family=Caveat:wght@700;900&family=Long+Cang&display=swap",
        "container_class": "",
        "use_mixed_fonts": True,
        "en_class": "font-combo-6-en",
        "zh_class": "font-combo-6-zh",
        "description": "Caveat + 龙藏体，手写书法风格"
    },
    "minimalist_swiss": {
        "name": "瑞士简约",
        "link": "https://fonts.googleapis.com/css2?family=Helvetica+Neue:wght@700;900&family=ZCOOL+KuaiLe&display=swap",
        "container_class": "",
        "use_mixed_fonts": True,
        "en_class": "font-combo-7-en",
        "zh_class": "font-combo-7-zh",
        "description": "Helvetica Neue + 站酷快乐体，瑞士简约风格"
    },
    "creative_display": {
        "name": "创意表现",
        "link": "https://fonts.googleapis.com/css2?family=Permanent+Marker&family=ZCOOL+KuaiLe&display=swap",
        "container_class": "",
        "use_mixed_fonts": True,
        "en_class": "font-combo-8-en",
        "zh_class": "font-combo-8-zh",
        "description": "Permanent Marker + 站酷快乐体，创意表现风格"
    }
}

# 主题库配置
THEME_LIBRARY = {
    "aurora_flow": {
        "name": "极光流彩",
        "gradient_class": "bg-gradient-to-br from-cyan-500 via-pink-600 to-purple-800",
        "shapes": [
            {"class": "absolute w-96 h-96 bg-white/5 rounded-full blur-3xl -top-20 -left-40"},
            {"class": "absolute w-80 h-80 bg-white/5 rounded-full blur-3xl -bottom-24 -right-24"},
            {"class": "absolute w-60 h-60 bg-white/5 rounded-full blur-3xl bottom-10 left-10"}
        ]
    },
    "retro_synthwave": {
        "name": "赛博朋克",
        "gradient_class": "bg-gradient-to-br from-purple-600 via-pink-500 to-orange-400",
        "shapes": [
            {"class": "absolute w-full h-1 bg-gradient-to-r from-transparent via-cyan-400/60 to-transparent top-16 blur-sm"},
            {"class": "absolute w-full h-1 bg-gradient-to-r from-transparent via-pink-400/60 to-transparent top-24 blur-sm"},
            {"class": "absolute w-full h-1 bg-gradient-to-r from-transparent via-purple-400/60 to-transparent top-32 blur-sm"},
            {"class": "absolute w-1 h-full bg-gradient-to-b from-transparent via-cyan-400/40 to-transparent left-1/4 blur-sm"},
            {"class": "absolute w-1 h-full bg-gradient-to-b from-transparent via-pink-400/40 to-transparent right-1/4 blur-sm"},
            {"class": "absolute w-64 h-64 bg-gradient-radial from-cyan-400/20 to-transparent rounded-full blur-xl top-8 right-8"},
            {"class": "absolute w-48 h-48 bg-gradient-radial from-pink-400/25 to-transparent rounded-full blur-lg bottom-8 left-8"}
        ]
    },
    "lava_geode": {
        "name": "熔岩晶洞",
        "gradient_class": "bg-gradient-to-br from-yellow-300 via-orange-500 to-red-600",
        "shapes": [
            {
                "class": "absolute w-80 h-80 bg-white/15 blur-2xl -top-10 -right-20",
                "clip_path": "polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%)"
            },
            {
                "class": "absolute w-72 h-72 bg-white/15 blur-2xl -bottom-16 -left-16", 
                "clip_path": "polygon(20% 0%, 80% 0%, 100% 100%, 0% 100%)"
            }
        ]
    },
    "ocean_deep": {
        "name": "深海秘境",
        "gradient_class": "bg-gradient-to-br from-teal-500 via-blue-600 to-indigo-700",
        "shapes": [
            # 第一层海浪 - 最远层，最大模糊
            {
                "class": "absolute w-full h-20 bg-white/5 blur-3xl top-16 left-0",
                "clip_path": "polygon(0% 50%, 10% 60%, 20% 55%, 30% 65%, 40% 50%, 50% 60%, 60% 45%, 70% 55%, 80% 40%, 90% 50%, 100% 45%, 100% 100%, 0% 100%)"
            },
            # 第二层海浪 - 中间层
            {
                "class": "absolute w-full h-24 bg-white/8 blur-2xl top-32 left-0",
                "clip_path": "polygon(0% 60%, 15% 45%, 25% 55%, 35% 40%, 45% 50%, 55% 35%, 65% 45%, 75% 30%, 85% 40%, 95% 25%, 100% 35%, 100% 100%, 0% 100%)"
            },
            # 第三层海浪 - 较近层
            {
                "class": "absolute w-full h-16 bg-white/10 blur-xl bottom-20 left-0",
                "clip_path": "polygon(0% 40%, 12% 55%, 24% 45%, 36% 60%, 48% 40%, 60% 55%, 72% 35%, 84% 50%, 96% 30%, 100% 40%, 100% 100%, 0% 100%)"
            },
            # 第四层海浪 - 最近层，最清晰
            {
                "class": "absolute w-full h-12 bg-white/12 blur-lg bottom-8 left-0",
                "clip_path": "polygon(0% 50%, 8% 35%, 16% 45%, 24% 30%, 32% 40%, 40% 25%, 48% 35%, 56% 20%, 64% 30%, 72% 15%, 80% 25%, 88% 10%, 96% 20%, 100% 15%, 100% 100%, 0% 100%)"
            },
            # 深海气泡效果 - 大气泡
            {"class": "absolute w-8 h-8 bg-white/20 rounded-full blur-sm top-20 left-16"},
            {"class": "absolute w-6 h-6 bg-white/15 rounded-full blur-sm top-40 right-20"},
            {"class": "absolute w-4 h-4 bg-white/20 rounded-full blur-xs bottom-32 left-24"},
            {"class": "absolute w-5 h-5 bg-white/10 rounded-full blur-sm bottom-48 right-32"},
            # 深海光晕 - 模拟阳光透射
            {"class": "absolute w-96 h-96 bg-gradient-radial from-cyan-300/8 to-transparent rounded-full blur-3xl -top-20 -right-20"},
            {"class": "absolute w-80 h-80 bg-gradient-radial from-teal-300/10 to-transparent rounded-full blur-2xl top-1/4 left-1/4"}
        ]
    },
    "liquid_chrome": {
        "name": "液态金属",
        # 调整渐变色,增加金属质感
        "gradient_class": "bg-gradient-to-br from-gray-400 via-slate-300 to-zinc-200",
        "shapes": [
            # 主要金属纹理线条 - 左上到右下
            {
                "class": "absolute w-full h-1 bg-gradient-to-r from-transparent via-white/40 to-transparent top-12 transform rotate-45 blur-[2px]"
            },
            {
                "class": "absolute w-full h-1 bg-gradient-to-r from-transparent via-white/40 to-transparent top-24 transform rotate-45 blur-[2px]"
            },
            {
                "class": "absolute w-full h-1 bg-gradient-to-r from-transparent via-white/40 to-transparent top-36 transform rotate-45 blur-[2px]"
            },
            # 交叉金属纹理线条 - 右上到左下
            {
                "class": "absolute w-full h-1 bg-gradient-to-r from-transparent via-white/40 to-transparent top-16 transform -rotate-45 blur-[2px]"
            },
            {
                "class": "absolute w-full h-1 bg-gradient-to-r from-transparent via-white/40 to-transparent top-28 transform -rotate-45 blur-[2px]"
            },
            {
                "class": "absolute w-full h-1 bg-gradient-to-r from-transparent via-white/40 to-transparent top-40 transform -rotate-45 blur-[2px]"
            },
            # 弧形装饰线条
            {
                "class": "absolute w-96 h-96 border-t-2 border-l-2 border-white/20 rounded-tl-full -top-12 -left-12 blur-[1px]"
            },
            {
                "class": "absolute w-80 h-80 border-b-2 border-r-2 border-white/20 rounded-br-full -bottom-12 -right-12 blur-[1px]"
            },
            # 主要高光区域
            {
                "class": "absolute w-96 h-48 bg-gradient-to-r from-white/50 via-white/30 to-transparent -top-8 -left-8 transform rotate-12 blur-lg",
                "clip_path": "polygon(0% 0%, 100% 0%, 85% 100%, 15% 100%)"
            },
            # 中心光晕
            {
                "class": "absolute w-64 h-64 bg-gradient-radial from-white/30 via-white/15 to-transparent rounded-full top-1/3 left-1/3 blur-lg"
            },
            # 表面纹理
            {
                "class": "absolute inset-0 bg-gradient-to-br from-black/5 via-white/10 to-black/5 mix-blend-overlay"
            },
            # 动态光泽效果
            {
                "class": "absolute w-full h-full bg-gradient-to-tr from-transparent via-white/10 to-transparent animate-pulse"
            }
        ]
    },
    "cosmic_journey": {
        "name": "星际漫游",
        "gradient_class": "bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900",
        "shapes": [
            {"class": "absolute w-2 h-2 bg-white rounded-full top-16 left-20"},
            {"class": "absolute w-1 h-1 bg-white/80 rounded-full top-24 right-32"},
            {"class": "absolute w-3 h-3 bg-white/60 rounded-full bottom-32 left-16"},
            {"class": "absolute w-1 h-1 bg-white rounded-full bottom-20 right-20"},
            {"class": "absolute w-96 h-96 bg-gradient-radial from-purple-500/10 to-transparent rounded-full blur-3xl -top-20 -right-20"},
            {"class": "absolute w-80 h-80 bg-gradient-radial from-blue-400/8 to-transparent rounded-full blur-3xl -bottom-16 -left-16"}
        ]
    },
    "forest_magic": {
        "name": "森林幻境",
        "gradient_class": "bg-gradient-to-br from-green-400 via-emerald-500 to-teal-600",
        "shapes": [
            # 增加了一个大型光晕,模拟阳光透过树叶的效果
            {
                "class": "absolute w-[600px] h-[600px] bg-yellow-400/10 blur-3xl -top-32 -right-32 transform rotate-12",
                "clip_path": "circle(50% at 50% 50%)"
            },
            # 调整了原有形状的透明度和位置,增加层次感
            {
                "class": "absolute w-[500px] h-[500px] bg-white/15 blur-3xl -top-24 -left-32 transform rotate-45",
                "clip_path": "polygon(0% 0%, 100% 0%, 75% 100%, 25% 100%)"
            },
            # 增加了一个较小的光斑,增强自然感
            {
                "class": "absolute w-64 h-64 bg-white/12 blur-3xl -top-8 -left-8 transform rotate-12"
            },
            # 调整了原有形状的大小和位置
            {"class": "absolute w-56 h-80 bg-white/8 blur-3xl -bottom-12 -right-4 transform -rotate-45"},
            # 增加了一个圆形光斑,丰富层次
            {"class": "absolute w-40 h-40 bg-white/15 rounded-full blur-xl top-1/4 right-1/4"},
            # 增加了一个较小的椭圆形光斑
            {"class": "absolute w-32 h-48 bg-white/10 blur-2xl bottom-1/3 left-1/4 transform rotate-30"}
        ]
    },
    "aurora_dreams": {
        "name": "极地极光",
        "gradient_class": "bg-gradient-to-br from-indigo-900 via-purple-600 to-green-400",
        "shapes": [
            {
                "class": "absolute w-full h-16 bg-gradient-to-r from-transparent via-green-300/40 to-transparent top-12 transform -skew-y-3 blur-md"
            },
            {
                "class": "absolute w-full h-12 bg-gradient-to-r from-transparent via-blue-300/50 to-transparent top-24 transform skew-y-2 blur-lg"
            },
            {
                "class": "absolute w-full h-20 bg-gradient-to-r from-transparent via-purple-300/35 to-transparent top-40 transform -skew-y-1 blur-xl"
            },
            {
                "class": "absolute w-full h-8 bg-gradient-to-r from-transparent via-pink-300/45 to-transparent top-64 transform skew-y-3 blur-sm"
            },
            {"class": "absolute w-96 h-96 bg-gradient-radial from-green-400/10 to-transparent rounded-full blur-3xl -top-16 -right-16"},
            {"class": "absolute w-80 h-80 bg-gradient-radial from-purple-400/15 to-transparent rounded-full blur-2xl -bottom-8 -left-8"}
        ]
    },
    "frozen_wonder": {
        "name": "冰雪奇缘",
        "gradient_class": "bg-gradient-to-br from-blue-200 via-cyan-300 to-indigo-400",
        "shapes": [
            {
                "class": "absolute w-16 h-16 bg-white/20 -top-4 -left-4",
                "clip_path": "polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)"
            },
            {
                "class": "absolute w-12 h-12 bg-white/15 top-12 right-8",
                "clip_path": "polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)"
            },
            {
                "class": "absolute w-20 h-20 bg-white/10 bottom-16 left-12",
                "clip_path": "polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)"
            },
            {
                "class": "absolute w-8 h-8 bg-white/25 bottom-8 right-16",
                "clip_path": "polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)"
            },
            {
                "class": "absolute w-14 h-14 bg-white/10 top-1/2 right-1/4",
                "clip_path": "polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)"
            },
            {"class": "absolute w-96 h-96 bg-white/5 rounded-full blur-3xl -top-20 -right-20"}
        ]
    },
    "neon_night": {
        "name": "霓虹夜色",
        "gradient_class": "bg-gradient-to-br from-purple-900 via-pink-800 to-indigo-900",
        "shapes": [
            {"class": "absolute w-2 h-80 bg-pink-300/40 blur-sm top-0 left-12 shadow-lg shadow-pink-500/20"},
            {"class": "absolute w-2 h-64 bg-cyan-300/40 blur-sm top-8 right-20 shadow-lg shadow-cyan-500/20"},
            {"class": "absolute w-80 h-2 bg-purple-300/40 blur-sm bottom-16 left-0 shadow-lg shadow-purple-500/20"},
            {"class": "absolute w-64 h-2 bg-yellow-300/40 blur-sm top-24 left-8 shadow-lg shadow-yellow-500/20"},
            {"class": "absolute w-48 h-48 bg-gradient-radial from-pink-400/10 to-transparent rounded-full blur-2xl top-1/4 right-1/4"},
            {"class": "absolute w-32 h-32 bg-gradient-radial from-cyan-400/15 to-transparent rounded-full blur-xl bottom-1/3 left-1/3"},
            {"class": "absolute w-24 h-24 bg-gradient-radial from-purple-400/20 to-transparent rounded-full blur-lg top-1/2 left-1/2"}
        ]
    },
    "desert_storm": {
        "name": "沙漠风暴",
        "gradient_class": "bg-gradient-to-br from-amber-300 via-orange-400 to-red-500",
        "shapes": [
            # 主要S形沙丘 - 增加高度并优化形状
            {
                "class": "absolute w-[800px] h-[400px] bg-white/15 blur-3xl -bottom-24 -left-32 transform -rotate-3",
                "clip_path": "polygon(0% 100%, 100% 100%, 100% 30%, 60% 0%, 30% 20%, 0% 60%)"
            },
            # 左侧峰顶额外模糊层 - 增强立体感
            {
                "class": "absolute w-[400px] h-[300px] bg-white/12 blur-[100px] bottom-48 left-0 transform -rotate-6",
                "clip_path": "polygon(0% 100%, 100% 100%, 80% 0%, 20% 0%)"
            },
            # 右侧谷地清晰层 - 更突出层次感
            {
                "class": "absolute w-[600px] h-[250px] bg-white/10 blur-lg -bottom-12 right-0 transform rotate-2",
                "clip_path": "polygon(30% 0%, 100% 100%, 0% 100%, 0% 50%)"
            },
            # 顶部沙尘漂浮效果
            {
                "class": "absolute w-full h-48 bg-gradient-to-r from-white/8 via-white/5 to-transparent blur-2xl bottom-64"
            },
            # 底部沙丘纹理
            {
                "class": "absolute w-full h-40 bg-gradient-to-r from-transparent via-white/8 to-transparent blur-md bottom-0 transform -skew-y-2"
            }
        ]
    },
    "cherry_blossom": {
        "name": "樱花飞舞",
        "gradient_class": "bg-gradient-to-br from-pink-200 via-rose-300 to-pink-400",
        "shapes": [
            # 柔和光晕背景层
            {"class": "absolute w-[400px] h-[400px] bg-white/10 rounded-full blur-3xl -top-24 -left-24"},
            # 大型花瓣群
            {"class": "absolute w-48 h-48 bg-white/40 rounded-full blur-xl top-8 left-8"},
            {"class": "absolute w-40 h-40 bg-white/35 rounded-full blur-lg top-24 right-12"},
            {"class": "absolute w-56 h-56 bg-white/30 rounded-full blur-2xl bottom-16 left-16"},
            {"class": "absolute w-44 h-44 bg-white/45 rounded-full blur-xl bottom-24 right-8"},
            # 小型点缀花瓣
            {"class": "absolute w-16 h-16 bg-white/50 rounded-full blur-sm top-32 left-32"},
            {"class": "absolute w-12 h-12 bg-white/55 rounded-full blur-sm bottom-40 right-36"},
            {"class": "absolute w-14 h-14 bg-white/45 rounded-full blur-sm top-48 right-24"},
            {"class": "absolute w-10 h-10 bg-white/60 rounded-full blur-sm bottom-32 left-40"}
        ]
    }
}

# 卡片类型图标映射
CARD_TYPE_ICONS = {
    "猜想": """<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" /></svg>""",
    "洞察": """<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>""",
    "研究": """<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>""",
    "发现": """<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" /></svg>""",
    "验证": """<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>"""
}

# 默认兜底配置
DEFAULT_CONFIG = {
    "font": "academic_kai",           # 默认字体（现代无衬线）
    "theme": "aurora_flow",          # 默认主题
    "card_type": "猜想",            # 默认卡片类型
    "fallback_text": "认知",    # 兜底文本
    "fallback_topic": None,   # 兜底主题
    "fallback_author": None,        # 兜底作者（None表示不显示）
    "fallback_org": None            # 兜底机构（None表示不显示）
}

# 获取字体列表（用于确定性选择）
def get_font_keys():
    return list(FONT_LIBRARY.keys())

# 获取主题列表（用于确定性选择）  
def get_theme_keys():
    return list(THEME_LIBRARY.keys())

# 获取字体配置
def get_font_config(font_key):
    return FONT_LIBRARY.get(font_key, FONT_LIBRARY[DEFAULT_CONFIG["font"]])

# 获取主题配置
def get_theme_config(theme_key):
    return THEME_LIBRARY.get(theme_key, THEME_LIBRARY[DEFAULT_CONFIG["theme"]])

# 获取图标
def get_card_icon(card_type):
    return CARD_TYPE_ICONS.get(card_type, CARD_TYPE_ICONS[DEFAULT_CONFIG["card_type"]]) 