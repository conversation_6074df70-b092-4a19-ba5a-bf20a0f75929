#!/usr/bin/env python3
"""
为现有认知添加topics的脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cognition.database import CognitionDatabase

async def add_topics_to_cognitions():
    """为现有认知添加topics"""
    client = CognitionDatabase()
    
    # 获取所有认知
    cognitions = await client.get_cognitions_paginated(0, 100)
    
    print(f"找到 {len(cognitions)} 个认知")
    
    # 为每个认知添加topics
    for i, cognition in enumerate(cognitions):
        cognition_id = cognition.get('id')
        current_topics = cognition.get('topics', [])
        
        if not current_topics:  # 如果没有topics，添加一些
            # 根据内容关键词分配topics
            content = (cognition.get('abstract', '') + ' ' + 
                      cognition.get('think', '') + ' ' + 
                      cognition.get('question', '') + ' ' + 
                      cognition.get('answer', '')).lower()
            
            new_topics = []
            
            # 简单的关键词匹配
            if any(keyword in content for keyword in ['ai', '人工智能', '机器学习', '深度学习', 'llm', 'gpt', '模型']):
                new_topics.append('test1')
            
            if any(keyword in content for keyword in ['研究', '论文', '学术', 'arxiv', '实验', '算法']):
                new_topics.append('test2')
            
            # 如果没有匹配到关键词，随机分配一个
            if not new_topics:
                new_topics = ['test1'] if i % 2 == 0 else ['test2']
            
            # 更新认知
            try:
                await client.update_cognition(cognition_id, {'topics': new_topics})
                print(f"为认知 {cognition_id} 添加了topics: {new_topics}")
            except Exception as e:
                print(f"更新认知 {cognition_id} 失败: {e}")
        else:
            print(f"认知 {cognition_id} 已有topics: {current_topics}")

if __name__ == "__main__":
    asyncio.run(add_topics_to_cognitions()) 