# -*- coding: utf-8 -*-
"""
Base64生产环境批量封面生成器 - 使用base64内嵌头像，无路径依赖
"""

import sys
import json
import asyncio
import logging
from pathlib import Path
from typing import List, Dict, Optional
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    from cover_generator import CoverGenerator
    PLAYWRIGHT_AVAILABLE = True
    print("✅ Playwright可用，将使用完整的图片生成功能")
except ImportError as e:
    PLAYWRIGHT_AVAILABLE = False
    print(f"⚠️  Playwright不可用: {e}")
    print("📋 将使用简化版本生成HTML")
    from cover_generator_simple import generate_html_cover

# 导入base64头像匹配器
from avatar_matcher_base64 import Base64AvatarMatcher

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('production_cover_generation_base64.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ProductionCoverGeneratorBase64:
    """使用Base64头像的生产环境封面生成器"""
    
    def __init__(self, output_dir: str = "/home/<USER>/covers"):
        """
        初始化生产环境生成器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        if PLAYWRIGHT_AVAILABLE:
            self.generator = CoverGenerator(
                template_dir=str(Path(__file__).parent),
                output_dir=str(self.output_dir)
            )
        
        # 初始化base64头像匹配器
        self.avatar_matcher = Base64AvatarMatcher()
        
        # 统计信息
        self.stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0,
            'start_time': None,
            'end_time': None,
            'errors': []
        }
    
    def load_cognitions_data(self, json_file_path: str) -> List[Dict]:
        """
        加载认知数据JSON文件
        
        Args:
            json_file_path: JSON文件路径
            
        Returns:
            认知数据列表
        """
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"成功加载 {len(data)} 条认知数据")
            return data
            
        except FileNotFoundError:
            logger.error(f"文件未找到: {json_file_path}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}")
            raise
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            raise
    
    def transform_cognition_data(self, raw_data: Dict) -> Dict:
        """
        转换原始认知数据为生成器需要的格式
        
        Args:
            raw_data: 原始认知数据
            
        Returns:
            转换后的数据
        """
        # 使用id字段作为唯一标识
        cognition_id = raw_data.get('id', raw_data.get('_id', 'unknown'))
        
        # 提取第一个机构作为作者机构
        author_org = None
        institutions = raw_data.get('institutions', [])
        if institutions:
            author_org = institutions[0]
        
        # 提取第一个作者作为主要作者
        source = raw_data.get('source', '')
        authors = raw_data.get('authors', [])
        if authors:
            source = authors[0]
        elif raw_data.get('blogger'):
            source = raw_data.get('blogger')
        
        # 尝试匹配base64头像
        avatar_url = None
        if source:
            avatar_match = self.avatar_matcher.match_avatar(source)
            if avatar_match:
                avatar_url = avatar_match['url']  # base64 data URL
                logger.info(f"🎭 认知 {cognition_id} 匹配到头像: {avatar_match['standard_name']} (base64, 置信度: {avatar_match['confidence']:.2f})")
        
        # 转换为生成器格式
        transformed = {
            'id': cognition_id,
            'tag': raw_data.get('tag', '认知'),
            'primary_topic': raw_data.get('primary_topic', '未知主题'),
            'abstract_zh': raw_data.get('abstract_zh', ''),
            'source': source,
            'author_org': author_org,
            'avatar_url': avatar_url
        }
        
        return transformed
    
    async def generate_cover_for_cognition(self, raw_cognition: Dict) -> Optional[str]:
        """
        为单个认知生成封面
        
        Args:
            raw_cognition: 原始认知数据
            
        Returns:
            生成的图片文件路径，失败时返回None
        """
        try:
            # 转换数据格式
            cognition_data = self.transform_cognition_data(raw_cognition)
            cognition_id = cognition_data['id']
            
            # 生成安全的文件名（移除特殊字符）
            safe_filename = self.sanitize_filename(f"cognition_{cognition_id}")
            
            if PLAYWRIGHT_AVAILABLE:
                # 使用完整的图片生成
                output_filename = f"{safe_filename}.png"
                image_path = await self.generator.generate_image(
                    cognition_data, 
                    output_filename=output_filename
                )
                file_type = "PNG图片"
            else:
                # 使用HTML生成
                output_filename = f"{safe_filename}.html"
                # simple generator 生成固定名称，需要重命名
                temp_path = generate_html_cover(
                    cognition_data,
                    output_dir=str(self.output_dir)
                )
                # 重命名为目标文件名
                target_path = self.output_dir / output_filename
                Path(temp_path).rename(target_path)
                image_path = str(target_path)
                file_type = "HTML文件"
            
            logger.info(f"✅ 认知 {cognition_id} 生成成功: {Path(image_path).name} ({file_type})")
            return image_path
            
        except Exception as e:
            cognition_id = raw_cognition.get('id', raw_cognition.get('_id', 'unknown'))
            error_msg = f"认知 {cognition_id} 生成失败: {e}"
            logger.error(error_msg)
            self.stats['errors'].append(error_msg)
            return None
    
    def sanitize_filename(self, filename: str) -> str:
        """
        清理文件名，移除特殊字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            清理后的文件名
        """
        # 移除或替换特殊字符
        import re
        # 保留字母、数字、下划线、短横线
        safe_name = re.sub(r'[^a-zA-Z0-9_-]', '_', filename)
        # 避免连续的下划线
        safe_name = re.sub(r'_+', '_', safe_name)
        # 移除开头和结尾的下划线
        safe_name = safe_name.strip('_')
        return safe_name
    
    def check_existing_files(self, cognitions: List[Dict]) -> List[Dict]:
        """
        检查哪些文件已经存在，避免重复生成
        
        Args:
            cognitions: 认知数据列表
            
        Returns:
            需要生成的认知数据列表
        """
        need_generate = []
        skipped_count = 0
        
        for cognition in cognitions:
            cognition_id = cognition.get('id', cognition.get('_id', 'unknown'))
            safe_filename = self.sanitize_filename(f"cognition_{cognition_id}")
            
            # 检查PNG和HTML文件
            png_path = self.output_dir / f"{safe_filename}.png"
            html_path = self.output_dir / f"{safe_filename}.html"
            
            if png_path.exists() or html_path.exists():
                logger.info(f"⏭️  跳过已存在的文件: {cognition_id}")
                skipped_count += 1
            else:
                need_generate.append(cognition)
        
        logger.info(f"📊 总数: {len(cognitions)}, 需要生成: {len(need_generate)}, 跳过: {skipped_count}")
        self.stats['skipped'] = skipped_count
        
        return need_generate
    
    async def process_batch(self, 
                          json_file_path: str,
                          batch_size: int = 5,
                          max_total: Optional[int] = None,
                          skip_existing: bool = True) -> Dict:
        """
        批量处理认知封面生成
        
        Args:
            json_file_path: JSON数据文件路径
            batch_size: 每批处理数量
            max_total: 最大处理总数
            skip_existing: 是否跳过已存在的文件
            
        Returns:
            处理统计信息
        """
        self.stats['start_time'] = datetime.now()
        
        try:
            # 加载数据
            cognitions = self.load_cognitions_data(json_file_path)
            
            # 限制处理数量
            if max_total:
                cognitions = cognitions[:max_total]
                logger.info(f"限制处理数量为: {max_total}")
            
            # 检查已存在的文件
            if skip_existing:
                cognitions = self.check_existing_files(cognitions)
            
            self.stats['total'] = len(cognitions)
            
            if self.stats['total'] == 0:
                logger.info("没有需要生成的认知")
                return self.stats
            
            logger.info(f"开始批量生成，共 {self.stats['total']} 个认知")
            
            # 分批处理
            for i in range(0, len(cognitions), batch_size):
                batch = cognitions[i:i + batch_size]
                batch_num = i // batch_size + 1
                total_batches = (len(cognitions) + batch_size - 1) // batch_size
                
                logger.info(f"📦 处理第 {batch_num}/{total_batches} 批 ({len(batch)} 个认知)")
                
                # 并行处理当前批次
                tasks = []
                for cognition in batch:
                    task = self.generate_cover_for_cognition(cognition)
                    tasks.append(task)
                
                # 等待当前批次完成
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 统计结果
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        self.stats['failed'] += 1
                        cognition_id = batch[i].get('id', batch[i].get('_id', 'unknown'))
                        logger.error(f"❌ 认知 {cognition_id} 处理异常: {result}")
                    elif result:
                        self.stats['success'] += 1
                    else:
                        self.stats['failed'] += 1
                
                # 批次间休息
                if i + batch_size < len(cognitions):
                    logger.info(f"批次完成，休息 3 秒...")
                    await asyncio.sleep(3)
        
        except Exception as e:
            logger.error(f"批量处理过程中出错: {e}")
            raise
        
        finally:
            self.stats['end_time'] = datetime.now()
            self.print_summary()
        
        return self.stats
    
    def print_summary(self):
        """打印处理摘要"""
        duration = None
        if self.stats['start_time'] and self.stats['end_time']:
            duration = self.stats['end_time'] - self.stats['start_time']
        
        logger.info("=" * 60)
        logger.info("📊 Base64生产环境批量封面生成摘要")
        logger.info(f"总数: {self.stats['total']}")
        logger.info(f"成功: {self.stats['success']}")
        logger.info(f"失败: {self.stats['failed']}")
        logger.info(f"跳过: {self.stats['skipped']}")
        
        if duration:
            logger.info(f"耗时: {duration}")
            if self.stats['success'] > 0:
                avg_time = duration.total_seconds() / self.stats['success']
                logger.info(f"平均每个: {avg_time:.2f} 秒")
        
        success_rate = (self.stats['success'] / self.stats['total'] * 100) if self.stats['total'] > 0 else 0
        logger.info(f"成功率: {success_rate:.1f}%")
        
        if self.stats['errors']:
            logger.info(f"错误详情 (前10个):")
            for error in self.stats['errors'][:10]:
                logger.info(f"  - {error}")
        
        logger.info(f"📁 输出目录: {self.output_dir}")
        logger.info("🎭 头像方案: Base64内嵌，无路径依赖")
        logger.info("=" * 60)


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Base64生产环境批量生成认知封面")
    parser.add_argument("json_file", 
                       default="/home/<USER>/cognitions_export.json",
                       nargs='?',
                       help="认知数据JSON文件路径")
    parser.add_argument("--output-dir", 
                       type=str, 
                       default="/home/<USER>/covers", 
                       help="输出目录")
    parser.add_argument("--batch-size", 
                       type=int, 
                       default=5, 
                       help="每批处理数量")
    parser.add_argument("--max-total", 
                       type=int, 
                       help="最大处理总数（用于测试）")
    parser.add_argument("--no-skip-existing", 
                       action="store_true", 
                       help="不跳过已存在的文件")
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not Path(args.json_file).exists():
        logger.error(f"输入文件不存在: {args.json_file}")
        sys.exit(1)
    
    # 创建生产环境生成器
    generator = ProductionCoverGeneratorBase64(output_dir=args.output_dir)
    
    try:
        # 开始批量处理
        stats = await generator.process_batch(
            json_file_path=args.json_file,
            batch_size=args.batch_size,
            max_total=args.max_total,
            skip_existing=not args.no_skip_existing
        )
        
        # 根据结果确定退出码
        if stats['failed'] == 0:
            logger.info("🎉 所有封面生成成功！")
            sys.exit(0)
        elif stats['success'] > 0:
            logger.warning(f"⚠️ 部分成功：{stats['success']} 成功，{stats['failed']} 失败")
            sys.exit(1)
        else:
            logger.error("❌ 所有封面生成失败")
            sys.exit(2)
    
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(3)


if __name__ == "__main__":
    asyncio.run(main())
