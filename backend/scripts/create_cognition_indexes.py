#!/usr/bin/env python3
"""
认知平台数据库索引创建脚本
用于优化查询性能
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from motor.motor_asyncio import AsyncIOMotorClient
from backend.config import settings
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def create_cognition_indexes():
    """创建认知相关的数据库索引"""
    client = None
    try:
        # 连接MongoDB
        client = AsyncIOMotorClient(settings.MONGO_URI)
        db = client[settings.MONGO_DB_NAME]
        
        logger.info("开始创建认知平台数据库索引...")
        
        # 认知主表索引
        cognitions_collection = db.cognitions
        
        # 1. 语言类型和创建时间复合索引（用于分页查询）
        await cognitions_collection.create_index([
            ("language_type", 1),
            ("created_at", -1)
        ], name="language_created_idx", background=True)
        logger.info("✓ 创建语言-创建时间索引")
        
        # 2. 点赞数和创建时间复合索引（用于排序）
        await cognitions_collection.create_index([
            ("likes", -1),
            ("created_at", -1)
        ], name="likes_created_idx", background=True)
        logger.info("✓ 创建点赞-创建时间索引")
        
        # 3. 全文搜索索引
        await cognitions_collection.create_index([
            ("abstract", "text"),
            ("think", "text"),
            ("question", "text"),
            ("answer", "text")
        ], name="text_search_idx", background=True)
        logger.info("✓ 创建全文搜索索引")
        
        # 4. 用户投票查询索引
        await cognitions_collection.create_index([
            ("votes.user_id", 1)
        ], name="votes_user_idx", background=True)
        logger.info("✓ 创建投票用户索引")
        
        # 5. 作者ID索引
        await cognitions_collection.create_index([
            ("author_id", 1)
        ], name="author_idx", background=True)
        logger.info("✓ 创建作者索引")
        
        # 收藏夹表索引
        favorites_collection = db.cognition_favorites
        
        # 1. 用户ID和认知ID复合索引（用于查询用户收藏状态）
        await favorites_collection.create_index([
            ("user_id", 1),
            ("cognition_id", 1)
        ], name="user_cognition_idx", background=True)
        logger.info("✓ 创建用户-认知复合索引")
        
        # 2. 收藏夹ID索引
        await favorites_collection.create_index([
            ("collection_id", 1)
        ], name="collection_idx", background=True)
        logger.info("✓ 创建收藏夹索引")
        
        # 3. 认知ID索引（用于删除认知时清理收藏）
        await favorites_collection.create_index([
            ("cognition_id", 1)
        ], name="cognition_idx", background=True)
        logger.info("✓ 创建认知索引")
        
        # 收藏夹集合表索引
        collections_collection = db.cognition_collections
        
        # 1. 用户ID索引
        await collections_collection.create_index([
            ("user_id", 1)
        ], name="user_idx", background=True)
        logger.info("✓ 创建用户收藏夹索引")
        
        # 2. 用户ID和默认收藏夹复合索引
        await collections_collection.create_index([
            ("user_id", 1),
            ("is_default", 1)
        ], name="user_default_idx", background=True)
        logger.info("✓ 创建用户默认收藏夹索引")
        
        logger.info("✅ 所有索引创建完成！")
        
        # 显示索引信息
        await show_index_info(db)
        
    except Exception as e:
        logger.error(f"❌ 创建索引失败: {str(e)}")
        raise
    finally:
        if client:
            client.close()

async def show_index_info(db):
    """显示索引信息"""
    logger.info("\n📋 索引信息:")
    
    collections = ["cognitions", "cognition_favorites", "cognition_collections"]
    
    for collection_name in collections:
        collection = db[collection_name]
        indexes = await collection.list_indexes().to_list(length=None)
        
        logger.info(f"\n{collection_name} 集合索引:")
        for idx in indexes:
            name = idx.get('name', 'unnamed')
            keys = idx.get('key', {})
            logger.info(f"  - {name}: {dict(keys)}")

async def drop_all_indexes():
    """删除所有自定义索引（保留_id索引）"""
    client = None
    try:
        client = AsyncIOMotorClient(settings.MONGO_URI)
        db = client[settings.MONGO_DB_NAME]
        
        logger.info("开始删除所有自定义索引...")
        
        collections = ["cognitions", "cognition_favorites", "cognition_collections"]
        
        for collection_name in collections:
            collection = db[collection_name]
            indexes = await collection.list_indexes().to_list(length=None)
            
            for idx in indexes:
                name = idx.get('name', '')
                if name != '_id_':  # 保留默认的_id索引
                    try:
                        await collection.drop_index(name)
                        logger.info(f"✓ 删除索引: {collection_name}.{name}")
                    except Exception as e:
                        logger.warning(f"⚠️ 删除索引失败: {collection_name}.{name} - {str(e)}")
        
        logger.info("✅ 索引删除完成！")
        
    except Exception as e:
        logger.error(f"❌ 删除索引失败: {str(e)}")
        raise
    finally:
        if client:
            client.close()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="认知平台数据库索引管理工具")
    parser.add_argument("--drop", action="store_true", help="删除所有自定义索引")
    parser.add_argument("--create", action="store_true", help="创建索引")
    
    args = parser.parse_args()
    
    if args.drop:
        asyncio.run(drop_all_indexes())
    elif args.create:
        asyncio.run(create_cognition_indexes())
    else:
        # 默认创建索引
        asyncio.run(create_cognition_indexes()) 