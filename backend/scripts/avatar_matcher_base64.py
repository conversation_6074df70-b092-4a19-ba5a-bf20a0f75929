# -*- coding: utf-8 -*-
"""
Base64头像匹配系统 - 将头像编码为base64直接嵌入HTML，无路径依赖
生产级别版本，包含完整的错误处理、性能优化和配置选项
"""

import re
import base64
import logging
from pathlib import Path
from typing import Optional, Dict, List, Union

# 配置日志
logger = logging.getLogger(__name__)

class Base64AvatarMatcher:
    """Base64头像匹配器，将头像编码后直接嵌入HTML"""
    
    def __init__(self, 
                 assets_dir: str = "/home/<USER>/DAIR/frontend/src/assets",
                 enable_cache: bool = True,
                 log_level: str = "INFO"):
        """
        初始化Base64头像匹配器
        
        Args:
            assets_dir: 前端assets目录路径
            enable_cache: 是否启用base64缓存
            log_level: 日志级别
        """
        self.assets_dir = Path(assets_dir)
        self.enable_cache = enable_cache
        
        # 设置日志级别
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(getattr(logging, log_level.upper()))
        
        # 名人映射表：将各种可能的名字形式映射到图片文件名
        self.celebrity_mapping = {
            # Sam Altman 的各种变体
            'sam altman': 'sam.jpg',
            'sam': 'sam.jpg',
            'altman': 'sam.jpg',
            'samuel altman': 'sam.jpg',
            'samuel h. altman': 'sam.jpg',
            's. altman': 'sam.jpg',
            'samuel harris altman': 'sam.jpg',
            
            # Elon Musk 的各种变体
            'elon musk': 'musk.jpg',
            'musk': 'musk.jpg',
            'elon': 'musk.jpg',
            'elon r. musk': 'musk.jpg',
            'elon reeve musk': 'musk.jpg',
            'e. musk': 'musk.jpg',
            
            # Andrej Karpathy 的各种变体
            'andrej karpathy': 'karpathy.jpg',
            'karpathy': 'karpathy.jpg',
            'andrej': 'karpathy.jpg',
            'a. karpathy': 'karpathy.jpg',
            'andrej karpathy': 'karpathy.jpg',
            
            # Jason Wei 的各种变体
            'jason wei': 'jason.png',
            'jason': 'jason.png',
            'wei': 'jason.png',
            'j. wei': 'jason.png'
        }
        
        # 反向映射：文件名到标准名称
        self.file_to_name = {
            'sam.jpg': 'Sam Altman',
            'musk.jpg': 'Elon Musk', 
            'karpathy.jpg': 'Andrej Karpathy',
            'jason.png': 'Jason Wei'
        }
        
        # Base64缓存和加载统计
        self.base64_cache = {}
        self.load_stats = {
            'total_files': 0,
            'loaded_successfully': 0,
            'failed_to_load': 0,
            'total_cache_size': 0,
            'errors': []
        }
        
        # 预加载所有头像为base64
        if self.enable_cache:
            self._load_avatars_to_base64()
        
    def _get_mime_type(self, filename: str) -> str:
        """
        根据文件扩展名确定MIME类型
        
        Args:
            filename: 文件名
            
        Returns:
            MIME类型字符串
        """
        ext = filename.lower().split('.')[-1]
        mime_mapping = {
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'png': 'image/png',
            'gif': 'image/gif',
            'webp': 'image/webp',
            'svg': 'image/svg+xml'
        }
        return mime_mapping.get(ext, 'image/jpeg')
    
    def _load_avatars_to_base64(self):
        """将所有头像文件加载为base64编码"""
        if not self.assets_dir.exists():
            logger.warning(f"Assets目录不存在: {self.assets_dir}")
            return
        
        logger.info(f"开始从 {self.assets_dir} 加载头像文件...")
        
        for filename in self.file_to_name.keys():
            self.load_stats['total_files'] += 1
            file_path = self.assets_dir / filename
            
            if not file_path.exists():
                error_msg = f"头像文件不存在: {file_path}"
                logger.warning(error_msg)
                self.load_stats['failed_to_load'] += 1
                self.load_stats['errors'].append(error_msg)
                continue
            
            try:
                with open(file_path, 'rb') as f:
                    img_data = f.read()
                    
                if len(img_data) == 0:
                    error_msg = f"头像文件为空: {file_path}"
                    logger.warning(error_msg)
                    self.load_stats['failed_to_load'] += 1
                    self.load_stats['errors'].append(error_msg)
                    continue
                
                # 编码为base64
                img_base64 = base64.b64encode(img_data).decode('utf-8')
                
                # 获取MIME类型
                mime_type = self._get_mime_type(filename)
                
                # 构建data URL
                data_url = f"data:{mime_type};base64,{img_base64}"
                self.base64_cache[filename] = {
                    'data_url': data_url,
                    'size_bytes': len(img_data),
                    'size_base64': len(data_url),
                    'mime_type': mime_type
                }
                
                self.load_stats['loaded_successfully'] += 1
                self.load_stats['total_cache_size'] += len(data_url)
                
                logger.debug(f"加载头像: {filename} ({len(img_data)} bytes -> {len(data_url)} chars)")
                        
            except Exception as e:
                error_msg = f"加载头像失败 {filename}: {e}"
                logger.error(error_msg)
                self.load_stats['failed_to_load'] += 1
                self.load_stats['errors'].append(error_msg)
        
        logger.info(f"头像加载完成: {self.load_stats['loaded_successfully']}/{self.load_stats['total_files']} 成功")
        if self.load_stats['failed_to_load'] > 0:
            logger.warning(f"加载失败: {self.load_stats['failed_to_load']} 个文件")
        
        if self.load_stats['total_cache_size'] > 0:
            cache_size_mb = self.load_stats['total_cache_size'] / (1024 * 1024)
            logger.info(f"Base64缓存总大小: {cache_size_mb:.2f} MB")
    
    def get_load_stats(self) -> Dict:
        """
        获取加载统计信息
        
        Returns:
            加载统计字典
        """
        return self.load_stats.copy()
    
    def get_available_avatars(self) -> List[str]:
        """
        获取可用的头像列表
        
        Returns:
            可用头像的标准名称列表
        """
        results = []
        for filename in self.base64_cache.keys():
            name = self.file_to_name.get(filename)
            if name:
                results.append(name)
        return results
    
    def normalize_name(self, name: str) -> str:
        """
        标准化名字字符串，用于匹配
        
        Args:
            name: 输入的名字字符串
            
        Returns:
            标准化后的名字字符串
        """
        if not name:
            return ""
        
        # 转为小写
        normalized = name.lower().strip()
        
        # 移除标点符号和特殊字符
        normalized = re.sub(r'[^\w\s]', '', normalized)
        
        # 合并多个空格
        normalized = re.sub(r'\s+', ' ', normalized)
        
        # 移除常见的学术称谓
        academic_titles = r'\b(dr|prof|professor|phd|ph\.d|mr|ms|mrs|miss)\b\.?'
        normalized = re.sub(academic_titles, '', normalized)
        
        # 移除et al.
        normalized = re.sub(r'\bet\s+al\.?', '', normalized)
        
        # 移除常见的后缀
        suffixes = r'\b(jr|sr|ii|iii|iv)\b\.?'
        normalized = re.sub(suffixes, '', normalized)
        
        # 清理首尾空格
        normalized = normalized.strip()
        
        return normalized
    
    def calculate_match_score(self, source: str, mapped_name: str) -> float:
        """
        计算两个名字的匹配分数
        
        Args:
            source: 源名字
            mapped_name: 映射表中的名字
            
        Returns:
            匹配分数 (0.0 - 1.0)
        """
        # 完全匹配
        if source == mapped_name:
            return 1.0
        
        # 包含关系匹配
        if mapped_name in source or source in mapped_name:
            return 0.8
        
        # 单词级匹配
        words1 = set(source.split())
        words2 = set(mapped_name.split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        if intersection:
            # 计算Jaccard相似度
            union = words1.union(words2)
            jaccard = len(intersection) / len(union)
            return jaccard * 0.7
        
        return 0.0
    
    def match_avatar(self, source: str, min_confidence: float = 0.7) -> Optional[Dict]:
        """
        匹配头像，返回base64编码的头像数据
        
        Args:
            source: 要匹配的源名字
            min_confidence: 最低置信度阈值
            
        Returns:
            匹配结果字典，包含头像信息；无匹配时返回None
        """
        if not source:
            logger.debug("输入的source为空")
            return None
        
        # 标准化输入
        normalized_source = self.normalize_name(source)
        
        if not normalized_source:
            logger.debug(f"标准化后的source为空: '{source}'")
            return None
        
        best_match = None
        best_score = 0.0
        
        # 在映射表中查找匹配
        for mapped_name, filename in self.celebrity_mapping.items():
            score = self.calculate_match_score(normalized_source, mapped_name)
            
            if score > best_score and score >= min_confidence:
                best_score = score
                
                # 获取base64编码的头像
                if self.enable_cache:
                    avatar_data = self.base64_cache.get(filename)
                    if avatar_data:
                        best_match = {
                            'filename': filename,
                            'url': avatar_data['data_url'],
                            'standard_name': self.file_to_name.get(filename, ''),
                            'confidence': score,
                            'is_base64': True,
                            'size_bytes': avatar_data['size_bytes'],
                            'mime_type': avatar_data['mime_type']
                        }
                    else:
                        logger.warning(f"缓存中未找到头像: {filename}")
                else:
                    # 实时加载模式
                    file_path = self.assets_dir / filename
                    if file_path.exists():
                        try:
                            with open(file_path, 'rb') as f:
                                img_data = f.read()
                            img_base64 = base64.b64encode(img_data).decode('utf-8')
                            mime_type = self._get_mime_type(filename)
                            data_url = f"data:{mime_type};base64,{img_base64}"
                            
                            best_match = {
                                'filename': filename,
                                'url': data_url,
                                'standard_name': self.file_to_name.get(filename, ''),
                                'confidence': score,
                                'is_base64': True,
                                'size_bytes': len(img_data),
                                'mime_type': mime_type
                            }
                        except Exception as e:
                            logger.error(f"实时加载头像失败 {filename}: {e}")
        
        if best_match:
            logger.debug(f"匹配成功: '{source}' -> {best_match['standard_name']} (置信度: {best_score:.2f})")
        else:
            logger.debug(f"未找到匹配: '{source}' (最高分数: {best_score:.2f}, 阈值: {min_confidence})")
        
        return best_match
    
    def batch_match_avatars(self, sources: List[str], min_confidence: float = 0.7) -> Dict[str, Optional[Dict]]:
        """
        批量匹配头像
        
        Args:
            sources: 要匹配的源名字列表
            min_confidence: 最低置信度阈值
            
        Returns:
            匹配结果字典，键为源名字，值为匹配结果
        """
        results = {}
        successful_matches = 0
        
        logger.info(f"开始批量匹配 {len(sources)} 个名字...")
        
        for source in sources:
            match = self.match_avatar(source, min_confidence)
            results[source] = match
            if match:
                successful_matches += 1
        
        match_rate = (successful_matches / len(sources) * 100) if sources else 0
        logger.info(f"批量匹配完成: {successful_matches}/{len(sources)} 成功 ({match_rate:.1f}%)")
        
        return results


def test_base64_avatar_matching():
    """测试base64头像匹配功能"""
    matcher = Base64AvatarMatcher()
    
    # 获取加载统计
    stats = matcher.get_load_stats()
    print(f"\n📊 加载统计:")
    print(f"总文件数: {stats['total_files']}")
    print(f"成功加载: {stats['loaded_successfully']}")
    print(f"加载失败: {stats['failed_to_load']}")
    print(f"缓存大小: {stats['total_cache_size'] / 1024:.1f} KB")
    
    # 获取可用头像
    available = matcher.get_available_avatars()
    print(f"\n🎭 可用头像: {', '.join(available)}")
    
    # 测试用例
    test_sources = [
        "Sam Altman",
        "Andrej Karpathy", 
        "Elon Musk",
        "s. altman",
        "Dr. Andrej Karpathy",
        "Elon R. Musk",
        "Unknown Author",
        "",
        "random person"
    ]
    
    print("\n🧪 测试Base64头像匹配功能")
    for source in test_sources:
        match = matcher.match_avatar(source)
        if match:
            data_url_preview = match['url'][:50] + "..." if len(match['url']) > 50 else match['url']
            print(f"✅ '{source}' -> {match['standard_name']} ({match['filename']}, 置信度: {match['confidence']:.2f})")
            print(f"   Base64大小: {match['size_bytes']} bytes, MIME: {match['mime_type']}")
        else:
            print(f"❌ '{source}' -> 无匹配")
    
    # 测试批量匹配
    print(f"\n📦 批量匹配测试:")
    batch_results = matcher.batch_match_avatars(test_sources[:5])
    for source, match in batch_results.items():
        status = "✅" if match else "❌"
        name = match['standard_name'] if match else "无匹配"
        print(f"{status} '{source}' -> {name}")


if __name__ == "__main__":
    test_base64_avatar_matching() 