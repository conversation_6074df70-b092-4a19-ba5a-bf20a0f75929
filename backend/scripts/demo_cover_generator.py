# -*- coding: utf-8 -*-
"""
封面生成器演示脚本
生成多个不同风格的封面HTML文件用于预览
"""

import sys
import json
from pathlib import Path
from jinja2 import Environment, FileSystemLoader

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from cover_config import (
    get_font_keys, get_theme_keys, get_font_config, get_theme_config, get_card_icon, DEFAULT_CONFIG
)


def create_demo_data():
    """创建演示数据集"""
    demo_data = [
        {
            'id': 1,
            'tag': '验证',
            'primary_topic': 'Chain of Thought',
            'abstract_zh': '步骤分解让大模型推理更加透明可解释',
            'source': '<PERSON> et al.',
            'author_org': 'DeepMind',
            'avatar_url': None
        },
        {
            'id': 2,
            'tag': '洞察',
            'primary_topic': 'Transformer Architecture',
            'abstract_zh': '注意力机制重新定义了序列建模的范式',
            'source': '<PERSON><PERSON><PERSON><PERSON> et al.',
            'author_org': 'Google Research',
            'avatar_url': 'https://via.placeholder.com/128/4285F4/FFFFFF?text=G'
        },
        {
            'id': 3,
            'tag': '猜想',
            'primary_topic': 'Emergent Abilities',
            'abstract_zh': '规模化训练中涌现出意想不到的能力',
            'source': 'Jason Wei',
            'author_org': 'Google',
            'avatar_url': 'https://via.placeholder.com/128/34A853/FFFFFF?text=JW'
        },
        {
            'id': 4,
            'tag': '研究',
            'primary_topic': 'Safety Alignment',
            'abstract_zh': '技术看似独立，规模化时才显现隐藏耦合',
            'source': 'Sam Altman',
            'author_org': 'OpenAI',
            'avatar_url': 'https://via.placeholder.com/128/EA4335/FFFFFF?text=SA'
        },
        {
            'id': 5,
            'tag': '发现',
            'primary_topic': 'RLHF Training',
            'abstract_zh': '人类反馈强化学习改善模型行为对齐',
            'source': 'Paul Christiano',
            'author_org': 'Anthropic',
            'avatar_url': None
        },
        {
            'id': 6,
            'tag': '发现',
            'primary_topic': 'RLHF Training',
            'abstract_zh': '人类反馈强化学习改善模型行为对齐',
            'source': 'Paul Christiano',
            'author_org': 'Anthropic',
            'avatar_url': None
        },        {
            'id': 7,
            'tag': '发现',
            'primary_topic': 'RLHF Training',
            'abstract_zh': '人类反馈强化学习改善模型行为对齐',
            'source': 'Paul Christiano',
            'author_org': 'Anthropic',
            'avatar_url': None
        },        {
                'id': 8,
            'tag': '发现',
            'primary_topic': 'RLHF Training',
            'abstract_zh': '人类反馈强化学习改善模型行为对齐',
            'source': 'Paul Christiano',
            'author_org': 'Anthropic',
            'avatar_url': None
        },
        {
            'id': 21,
            'tag': '洞察',
            'primary_topic': 'Constitutional AI',
            'abstract_zh': '通过宪法训练构建更安全的AI系统',
            'source': 'Claude Team',
            'author_org': 'Anthropic',
            'avatar_url': 'https://via.placeholder.com/128/FBBC04/FFFFFF?text=C'
        }
    ]
    return demo_data


def prepare_template_data_simple(cognition_data):
    """简化的模板数据准备"""
    import hashlib
    
    # 基础数据提取
    cognition_id = cognition_data.get('id', 1)
    card_type = cognition_data.get('tag', DEFAULT_CONFIG['card_type'])
    main_topic = cognition_data.get('primary_topic', DEFAULT_CONFIG['fallback_topic'])
    summary = cognition_data.get('abstract_zh', DEFAULT_CONFIG['fallback_text'])
    author_name = cognition_data.get('source', DEFAULT_CONFIG['fallback_author'])
    author_org = cognition_data.get('author_org', DEFAULT_CONFIG['fallback_org'])
    avatar_url = cognition_data.get('avatar_url')
    
    # 清理文本函数
    def clean_text(text):
        if not text:
            return text
        import re
        emoji_pattern = r'[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF\U00002600-\U000026FF\U00002700-\U000027BF]+'
        text = re.sub(emoji_pattern, '', text)
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    
    # 清理文本（但保留None值）
    main_topic = clean_text(main_topic) if main_topic else main_topic
    summary = clean_text(summary) if summary else summary
    author_name = clean_text(author_name) if author_name else author_name
    author_org = clean_text(author_org) if author_org else author_org
    
    # 确定性选择字体和主题
    font_keys = get_font_keys()
    theme_keys = get_theme_keys()
    
    hash_input = str(cognition_id).encode('utf-8')
    hash_digest = hashlib.md5(hash_input).hexdigest()
    font_seed = int(hash_digest[:8], 16)
    theme_seed = int(hash_digest[8:16], 16)
    font_key = font_keys[font_seed % len(font_keys)]
    theme_key = theme_keys[theme_seed % len(theme_keys)]
    
    font_config = get_font_config(font_key)
    theme_config = get_theme_config(theme_key)
    
    # 处理混合字体格式化
    formatted_summary = summary
    summary_class = ""
    topic_class = ""
    
    if font_config.get("use_mixed_fonts", False):
        # 简化混合字体处理，只分中英文
        import re
        
        # 对摘要进行混合字体处理
        tokenizer = re.compile(r'[a-zA-Z0-9\._-]+|[\u4e00-\u9fa5]|[，。；、,.!?;]')
        tokens = tokenizer.findall(summary)
        
        html_parts = []
        en_class = font_config.get("en_class", "")
        zh_class = font_config.get("zh_class", "")
        
        for token in tokens:
            if re.match(r'[a-zA-Z0-9]', token):
                html_parts.append(f'<span class="{en_class}">{token}</span>')
            elif token in '，。；、,.!?;':
                html_parts.append(f'<span class="{zh_class}">{token}</span>')
            else:
                html_parts.append(f'<span class="{zh_class}">{token}</span>')
        
        formatted_summary = ''.join(html_parts)
        topic_class = en_class  # 主题使用英文字体
    else:
        # 统一字体
        summary_class = font_config.get("container_class", "")
        topic_class = font_config.get("container_class", "")
    
    # 准备模板数据
    template_data = {
        'main_topic': main_topic,
        'formatted_summary': formatted_summary,
        'card_type': card_type,
        'author_name': author_name,  # 可能为None
        'author_org': author_org,    # 可能为None
        'has_avatar': bool(avatar_url),
        'avatar_url': avatar_url or '',
        'font_link': font_config['link'],
        'container_class': font_config.get('container_class', ''),
        'summary_class': summary_class,
        'topic_class': topic_class,
        'gradient_class': theme_config['gradient_class'],
        'shapes': theme_config['shapes'],
        'card_icon': get_card_icon(card_type),
        'meta': {
            'cognition_id': cognition_id,
            'font_key': font_key,
            'theme_key': theme_key,
            'font_name': font_config['name'],
            'theme_name': theme_config['name']
        }
    }
    
    return template_data


def generate_demo_covers():
    """生成演示封面"""
    print("🎨 开始生成封面演示文件...")
    
    # 初始化Jinja2
    script_dir = Path(__file__).parent
    output_dir = script_dir / "demo_output"
    output_dir.mkdir(exist_ok=True)
    
    jinja_env = Environment(
        loader=FileSystemLoader(str(script_dir)),
        autoescape=True
    )
    
    # 检查模板文件
    template_file = script_dir / "cover_template.html"
    if not template_file.exists():
        print(f"❌ 模板文件不存在: {template_file}")
        return False
    
    # 获取演示数据
    demo_data = create_demo_data()
    generated_files = []
    
    try:
        template = jinja_env.get_template("cover_template.html")
        
        for data in demo_data:
            # 准备模板数据
            template_data = prepare_template_data_simple(data)
            
            # 渲染HTML
            html_content = template.render(**template_data)
            
            # 保存文件
            cognition_id = data['id']
            output_file = output_dir / f"cover_demo_{cognition_id}.html"
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            generated_files.append({
                'file': output_file,
                'id': cognition_id,
                'topic': data['primary_topic'],
                'font': template_data['meta']['font_name'],
                'theme': template_data['meta']['theme_name']
            })
            
            print(f"✅ 生成完成: ID {cognition_id:3d} - {template_data['meta']['font_name']} × {template_data['meta']['theme_name']}")
        
        # 生成目录页面
        generate_index_page(output_dir, generated_files)
        
        print(f"\n🎉 演示文件生成完成!")
        print(f"📁 输出目录: {output_dir}")
        print(f"📄 生成文件数: {len(generated_files)}")
        print(f"\n🌐 请在浏览器中打开: {output_dir}/index.html")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def generate_index_page(output_dir, generated_files):
    """生成目录页面"""
    index_html = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认知平台封面生成器 - 演示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .cover-preview {{
            width: 200px;
            height: 300px;
            transform: scale(0.5);
            transform-origin: top left;
            border: 2px solid #e5e7eb;
            border-radius: 16px;
            overflow: hidden;
        }}
        .cover-card {{
            width: 400px;
            height: 600px;
        }}
    </style>
</head>
<body class="bg-gray-50 min-h-screen py-8">
    <div class="max-w-7xl mx-auto px-4">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">🎨 认知平台封面生成器</h1>
            <p class="text-lg text-gray-600">演示不同ID对应的确定性字体和主题选择</p>
            <div class="mt-4 text-sm text-gray-500">
                <span class="inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-full mr-2">总共 {count} 个样本</span>
                <span class="inline-block bg-green-100 text-green-800 px-3 py-1 rounded-full mr-2">{font_count} 种字体</span>
                <span class="inline-block bg-purple-100 text-purple-800 px-3 py-1 rounded-full">{theme_count} 种主题</span>
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {cards}
        </div>
        
        <div class="mt-12 text-center text-gray-500">
            <p>🔧 基于MD5哈希确保每个ID对应固定的视觉风格</p>
            <p class="text-sm mt-2">点击任意封面可在新窗口中查看完整尺寸</p>
        </div>
    </div>
</body>
</html>"""
    
    # 生成卡片HTML
    cards_html = []
    fonts_used = set()
    themes_used = set()
    
    for file_info in generated_files:
        fonts_used.add(file_info['font'])
        themes_used.add(file_info['theme'])
        
        # 使用字符串拼接避免f-string的格式化问题
        card_html = '<div class="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow">'
        card_html += '  <div class="cover-preview mb-4 mx-auto">'
        card_html += f'    <iframe src="{file_info["file"].name}" class="cover-card border-0" scrolling="no"></iframe>'
        card_html += '  </div>'
        card_html += '  <div class="text-center">'
        card_html += f'    <h3 class="font-bold text-gray-900 mb-1">ID: {file_info["id"]}</h3>'
        card_html += f'    <p class="text-sm text-gray-600 mb-2">{file_info["topic"]}</p>'
        card_html += '    <div class="text-xs text-gray-500">'
        card_html += f'      <div class="bg-blue-50 text-blue-700 px-2 py-1 rounded mb-1">{file_info["font"]}</div>'
        card_html += f'      <div class="bg-purple-50 text-purple-700 px-2 py-1 rounded">{file_info["theme"]}</div>'
        card_html += '    </div>'
        card_html += f'    <a href="{file_info["file"].name}" target="_blank" class="inline-block mt-2 text-blue-600 hover:text-blue-800 text-sm underline">'
        card_html += '      查看完整版'
        card_html += '    </a>'
        card_html += '  </div>'
        card_html += '</div>'
        
        cards_html.append(card_html)
    
    # 替换模板变量
    final_html = index_html.format(
        count=len(generated_files),
        font_count=len(fonts_used),
        theme_count=len(themes_used),
        cards='\n'.join(cards_html)
    )
    
    # 保存目录文件
    index_file = output_dir / "index.html"
    with open(index_file, 'w', encoding='utf-8') as f:
        f.write(final_html)
    
    print(f"📋 目录页面生成: {index_file}")


if __name__ == "__main__":
    success = generate_demo_covers()
    sys.exit(0 if success else 1) 