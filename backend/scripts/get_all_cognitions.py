import requests
import json
import os
import argparse
from typing import List, Dict, Any

# --- 配置 ---
# 从环境变量或命令行参数获取，避免硬编码
BASE_URL = os.environ.get("DAIR_BASE_URL", "http://127.0.0.1:8000")
USERNAME = os.environ.get("DAIR_USERNAME", "")
PASSWORD = os.environ.get("DAIR_PASSWORD", "")

def get_auth_token(username, password) -> str:
    """获取认证令牌"""
    login_url = f"{BASE_URL}/auth/login"
    login_data = {
        "username": username,
        "password": password
    }
    try:
        response = requests.post(login_url, data=login_data)
        response.raise_for_status()
        return response.json()["access_token"]
    except requests.exceptions.HTTPError as err:
        print(f"登录失败: {err}")
        if response.status_code == 401:
            print("错误详情: 用户名或密码错误。")
        else:
            print(f"错误详情: {response.text}")
        exit(1)
    except requests.exceptions.RequestException as e:
        print(f"网络错误: {e}")
        exit(1)

def get_all_cognitions(token: str) -> List[Dict[str, Any]]:
    """获取所有认知数据"""
    all_cognitions = []
    page = 0
    page_size = 50  # 每次请求获取的条目数
    
    headers = {"Authorization": f"Bearer {token}"}
    
    while True:
        list_url = f"{BASE_URL}/api/cognition/list?skip={page * page_size}&limit={page_size}"
        print(f"正在获取第 {page + 1} 页数据...")
        
        try:
            response = requests.get(list_url, headers=headers)
            response.raise_for_status()
            data = response.json()
            
            cognitions = data.get("items", [])
            if not cognitions:
                print("已获取全部数据。")
                break
            
            all_cognitions.extend(cognitions)
            
            # 简单分页逻辑：如果返回的条目数小于请求的条目数，说明是最后一页
            if len(cognitions) < page_size:
                print("已到达最后一页。")
                break
                
            page += 1

        except requests.exceptions.HTTPError as err:
            print(f"获取认知列表失败: {err}")
            print(f"错误详情: {response.text}")
            break
        except requests.exceptions.RequestException as e:
            print(f"网络错误: {e}")
            break
            
    return all_cognitions

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="从 DAIR 系统获取所有认知数据。")
    parser.add_argument("--username", default=USERNAME, help="用户名 (可以设置 DAIR_USERNAME 环境变量)")
    parser.add_argument("--password", default=PASSWORD, help="密码 (可以设置 DAIR_PASSWORD 环境变量)")
    parser.add_argument("--base-url", default=BASE_URL, help="DAIR API 的基础 URL (可以设置 DAIR_BASE_URL 环境变量)")
    parser.add_argument("-o", "--output", default="all_cognitions.json", help="输出 JSON 文件名")
    
    args = parser.parse_args()
    
    if not args.username or not args.password:
        print("错误: 必须提供用户名和密码。")
        parser.print_help()
        exit(1)
        
    global BASE_URL
    BASE_URL = args.base_url

    print("开始执行脚本...")
    
    # 1. 获取Token
    print(f"正在为用户 '{args.username}' 获取认证令牌...")
    token = get_auth_token(args.username, args.password)
    print("令牌获取成功！")
    
    # 2. 获取所有认知
    print("开始获取所有认知数据...")
    cognitions = get_all_cognitions(token)
    
    # 3. 保存到文件
    if cognitions:
        print(f"共获取到 {len(cognitions)} 条认知数据。")
        try:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(cognitions, f, ensure_ascii=False, indent=4)
            print(f"数据已成功保存到 {args.output}")
        except IOError as e:
            print(f"写入文件失败: {e}")
    else:
        print("未能获取到任何认知数据。")

if __name__ == "__main__":
    main() 