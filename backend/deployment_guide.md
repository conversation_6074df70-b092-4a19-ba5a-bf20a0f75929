# DAIR 项目环境配置部署指南

## 概述

DAIR项目支持三种环境配置方式，优先级从高到低：
1. 外部YAML配置文件
2. 环境变量
3. 代码默认值

## 环境类型

- `development`: 开发环境（默认）
- `testing`: 测试环境
- `production`: 生产环境

## 配置方式

### 方式1: 环境变量（推荐用于CI/CD）

```bash
# 设置环境类型
export ENVIRONMENT=production

# 其他配置
export MONGO_URI=mongodb://prod-mongo:27017
export MONGO_DB_NAME=dair_prod
export LOG_LEVEL=WARNING
```

### 方式2: 外部YAML文件（推荐用于生产环境）

1. 复制 `config_template.yaml` 到安全位置
2. 修改配置值
3. 设置环境变量指向配置文件：

```bash
export CONFIG_YAML_PATH=/etc/dair/config.yaml
```

或者将配置文件放在以下任一位置（按优先级排序）：
- `/etc/dair/config.yaml`
- `/app/config/config.yaml`
- `/opt/dair/config.yaml`
- `~/.dair/config.yaml`

## 部署方案

### Docker部署

```dockerfile
# 方式1: 挂载配置文件
docker run -d \
  -v /path/to/config.yaml:/app/config/config.yaml \
  -e ENVIRONMENT=production \
  dair-backend

# 方式2: 环境变量
docker run -d \
  -e ENVIRONMENT=production \
  -e MONGO_URI=mongodb://mongo:27017 \
  -e MONGO_DB_NAME=dair_prod \
  dair-backend
```

### Docker Compose

```yaml
services:
  backend:
    image: dair-backend
    environment:
      - ENVIRONMENT=production
      - CONFIG_YAML_PATH=/app/config/config.yaml
    volumes:
      - /secure/path/config.yaml:/app/config/config.yaml:ro
    secrets:
      - dair_config
```

### Kubernetes部署

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dair-backend
spec:
  template:
    spec:
      containers:
      - name: backend
        image: dair-backend
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: CONFIG_YAML_PATH
          value: "/etc/config/config.yaml"
        volumeMounts:
        - name: config-volume
          mountPath: /etc/config
          readOnly: true
      volumes:
      - name: config-volume
        secret:
          secretName: dair-config
```

### CI/CD自动化

#### GitHub Actions示例

```yaml
name: Deploy
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: Create config file
      run: |
        cat > config.yaml << EOF
        environment: "production"
        mongo_uri: "${{ secrets.MONGO_URI }}"
        admin_password: "${{ secrets.ADMIN_PASSWORD }}"
        tos_access_key: "${{ secrets.TOS_ACCESS_KEY }}"
        EOF
    
    - name: Deploy to server
      run: |
        scp config.yaml user@server:/etc/dair/config.yaml
        ssh user@server "docker restart dair-backend"
```

## 安全建议

1. **永远不要将配置文件提交到代码库**
2. **使用加密存储**：配置文件应存储在加密的位置
3. **最小权限**：只给应用必要的文件访问权限
4. **定期轮换**：定期更换敏感配置如密码、密钥
5. **审计日志**：记录配置文件的访问和修改

## 配置验证

启动应用时会输出当前配置信息：

```python
from backend.config import settings

print(f"当前环境: {settings.ENVIRONMENT}")
print(f"是否生产环境: {settings.is_production}")
print(f"数据库: {settings.MONGO_DB_NAME}")
```

## 故障排除

1. **配置文件未加载**：检查文件路径和权限
2. **环境变量未生效**：确保变量名大写且格式正确
3. **YAML格式错误**：使用YAML验证工具检查语法 