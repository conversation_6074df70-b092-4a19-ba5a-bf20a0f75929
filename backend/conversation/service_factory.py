import os
import json
import uuid
import yaml
import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path
import weakref

from backend.config import settings
from backend.utils import get_cn_time

logger = logging.getLogger(__name__)

class ServiceFactory:
    """服务工厂类，负责创建和管理各种服务实例"""
    
    def __init__(self):
        self._services = None
        self._benchmark_data = None
        # 使用弱引用字典管理 research_agent 实例，避免内存泄漏
        self._research_agents: Dict[str, Any] = {}
        # 用于清理过期的 agent 实例
        self._agent_cleanup_callbacks: Dict[str, Any] = {}
    
    def get_services(self):
        """创建并返回服务实例"""
        if self._services is not None:
            return self._services
            
        try:
            # 加载配置
            agents_config = yaml.load(open("dc_agents/config/config.yaml"), Loader=yaml.FullLoader)
            service_config = yaml.load(open("dc_agents/config/service_config.yaml"), Loader=yaml.FullLoader)
            
            # 初始化浏览智能体
            from dc_agents.src.deep_cognition.browse_agent import BrowseAgent
            browse_config = agents_config.get("browse_agent", {})
            browse_agent = BrowseAgent(config=browse_config)
            
            # 初始化搜索服务
            from dc_agents.src.services.search_service import SearchService
            search_service = SearchService(service_config["search_service"])
            
            self._services = {
                "browse_agent": browse_agent,
                "search_service": search_service,
                "agents_config": agents_config
            }
            
            logger.info("服务实例创建成功")
            return self._services
            
        except Exception as e:
            logger.error(f"初始化服务出错: {e}")
            raise e
    
    def get_research_agent(self, conversation_id: str):
        """获取或创建研究智能体实例，确保每个 session_id 只有一个实例"""
        if not conversation_id:
            raise ValueError("conversation_id is required")
        
        # 检查是否已经存在该 conversation_id 的 agent 实例
        if conversation_id in self._research_agents:
            agent = self._research_agents[conversation_id]
            if agent is not None:  # 弱引用还有效
                logger.info(f"复用现有研究智能体实例: {conversation_id}")
                return agent
            else:
                # 弱引用已失效，清理
                logger.info(f"清理失效的研究智能体引用: {conversation_id}")
                del self._research_agents[conversation_id]
                if conversation_id in self._agent_cleanup_callbacks:
                    del self._agent_cleanup_callbacks[conversation_id]
        
        # 创建新的研究智能体实例
        services = self.get_services()
        research_config = services["agents_config"].get("research_agent", {})
        
        logger.info(f"创建新的研究智能体实例: {conversation_id}")
        
        from dc_agents.src.deep_cognition.research_agent import ResearchAgent
        research_agent = ResearchAgent(
            config=research_config,
            search_service=services["search_service"],
            browse_agent=services["browse_agent"],
            conversation_id=conversation_id
        )
        
        # 存储实例到管理器中
        self._research_agents[conversation_id] = research_agent
        
        # 设置清理回调，当 agent 被垃圾回收时自动清理
        def cleanup_callback(conversation_id=conversation_id):
            if conversation_id in self._research_agents:
                logger.info(f"自动清理研究智能体实例: {conversation_id}")
                del self._research_agents[conversation_id]
            if conversation_id in self._agent_cleanup_callbacks:
                del self._agent_cleanup_callbacks[conversation_id]
        
        self._agent_cleanup_callbacks[conversation_id] = weakref.finalize(research_agent, cleanup_callback)
        
        return research_agent
    
    def has_research_agent(self, conversation_id: str) -> bool:
        """检查是否已存在指定 conversation_id 的研究智能体实例"""
        if not conversation_id:
            return False
        
        if conversation_id in self._research_agents:
            agent = self._research_agents[conversation_id]
            return agent is not None
        
        return False
    
    def remove_research_agent(self, conversation_id: str):
        """手动移除研究智能体实例（比如会话结束时）"""
        if conversation_id in self._research_agents:
            logger.info(f"手动移除研究智能体实例: {conversation_id}")
            del self._research_agents[conversation_id]
        
        if conversation_id in self._agent_cleanup_callbacks:
            # 取消弱引用回调
            self._agent_cleanup_callbacks[conversation_id].detach()
            del self._agent_cleanup_callbacks[conversation_id]
    
    def get_active_agents_count(self) -> int:
        """获取当前活跃的研究智能体数量"""
        return len([agent for agent in self._research_agents.values() if agent is not None])
    
    def cleanup_inactive_agents(self):
        """清理不活跃的研究智能体实例"""
        inactive_ids = []
        for conversation_id, agent in self._research_agents.items():
            if agent is None:
                inactive_ids.append(conversation_id)
        
        for conversation_id in inactive_ids:
            logger.info(f"清理不活跃的研究智能体: {conversation_id}")
            del self._research_agents[conversation_id]
            if conversation_id in self._agent_cleanup_callbacks:
                del self._agent_cleanup_callbacks[conversation_id]
        
        if inactive_ids:
            logger.info(f"已清理 {len(inactive_ids)} 个不活跃的研究智能体实例")
    
    def load_benchmark_data(self):
        """加载benchmark测试数据"""
        if self._benchmark_data is not None:
            return self._benchmark_data
            
        try:
            benchmark_file_path = "dc_agents/src/dataset/decrypted_data.json"
            with open(benchmark_file_path, "r", encoding="utf-8") as f:
                self._benchmark_data = json.load(f)
            logger.info(f"Benchmark数据加载成功，共{len(self._benchmark_data)}项")
            return self._benchmark_data
        except Exception as e:
            logger.error(f"加载benchmark数据出错: {e}")
            return []
    
    def save_research_trajectory(self, research_agent, session_id: str, status: str = "完成"):
        """保存研究智能体的研究轨迹为JSON文件"""
        try:
            if not hasattr(research_agent, "memory") or not hasattr(research_agent.memory, "research_trajectory"):
                logger.warning(f"研究智能体没有research_trajectory属性，无法保存轨迹: {session_id}")
                return False
                
            # # 确保目标目录存在
            # trajectory_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "dc_agents", "trajectory")
            # os.makedirs(trajectory_dir, exist_ok=True)
            
            # # 保存研究轨迹为JSON文件
            # trajectory_file = os.path.join(trajectory_dir, f"{session_id}.json")
            # # with open(trajectory_file, "w", encoding="utf-8") as f:
            # #     json.dump(research_agent.memory.research_trajectory, f, ensure_ascii=False, indent=2)
            # logger.info(f"{status}的研究轨迹已保存到: {trajectory_file}")
            return True
        except Exception as e:
            logger.error(f"保存研究轨迹时出错: {e}")
            return False
    
    def ensure_directory_exists(self, directory_path: str) -> None:
        """确保目录存在，如果不存在则创建"""
        try:
            os.makedirs(directory_path, exist_ok=True)
            logger.info(f"目录已创建或已存在: {directory_path}")
        except Exception as e:
            logger.error(f"创建目录失败: {directory_path}, 错误: {str(e)}")
            raise

# 全局服务工厂实例
_service_factory = None

def get_service_factory() -> ServiceFactory:
    """获取服务工厂单例"""
    global _service_factory
    if _service_factory is None:
        _service_factory = ServiceFactory()
    return _service_factory 