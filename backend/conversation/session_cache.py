import json
import logging
from typing import Any, Dict, List, Optional
from datetime import datetime, timezone, timedelta
from backend.redis.redis_client import RedisClient
from backend.config import settings

logger = logging.getLogger(__name__)

class SessionCache:
    """会话缓存管理类，负责将内存中的active_sessions迁移到Redis"""
    
    def __init__(self, redis_client: RedisClient):
        self.redis_client = redis_client
        self.session_prefix = "session:"
        self.sessions_list_key = "active_sessions"
        self.default_expire = settings.REDIS_SESSION_TTL  # 使用配置中的TTL
    
    def _get_session_key(self, session_id: str) -> str:
        """获取会话的Redis键名"""
        return f"{self.session_prefix}{session_id}"
    
    async def set_session(self, session_id: str, session_data: Dict[str, Any], expire: Optional[int] = None) -> bool:
        """
        设置会话数据
        :param session_id: 会话ID
        :param session_data: 会话数据
        :param expire: 过期时间（秒），默认24小时
        :return: 操作是否成功
        """
        try:
            session_key = self._get_session_key(session_id)
            expire_time = expire or self.default_expire
            
            # 添加时间戳（保持datetime对象，让Redis客户端自动序列化）
            session_data["last_activity"] = datetime.now(timezone.utc)
            session_data["created_at"] = session_data.get("created_at", datetime.now(timezone.utc))
            
            # 存储会话数据
            success = await self.redis_client.set(session_key, session_data, ex=expire_time)
            if success:
                # 将session_id添加到活跃会话列表
                await self.redis_client.hset(self.sessions_list_key, {session_id: datetime.now(timezone.utc).isoformat()})
                # logger.info(f"会话缓存设置成功: {session_id}")
            return success
        except Exception as e:
            logger.error(f"设置会话缓存失败 {session_id}: {e}")
            return False
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        获取会话数据
        :param session_id: 会话ID
        :return: 会话数据
        """
        try:
            session_key = self._get_session_key(session_id)
            session_data = await self.redis_client.get(session_key)
            
            if session_data:
                # 更新最后活动时间（保持datetime对象）
                if isinstance(session_data, dict):
                    session_data["last_activity"] = datetime.now(timezone.utc)
                    await self.redis_client.set(session_key, session_data, ex=self.default_expire)
                
                logger.debug(f"会话缓存获取成功: {session_id}")
                return session_data
            else:
                # 如果会话不存在，从活跃列表中移除
                await self.redis_client.hdel(self.sessions_list_key, session_id)
                return None
        except Exception as e:
            logger.error(f"获取会话缓存失败 {session_id}: {e}")
            return None
    
    async def delete_session(self, session_id: str) -> bool:
        """
        删除会话数据
        :param session_id: 会话ID
        :return: 操作是否成功
        """
        try:
            session_key = self._get_session_key(session_id)
            deleted_count = await self.redis_client.delete(session_key)
            
            # 从活跃会话列表中移除
            await self.redis_client.hdel(self.sessions_list_key, session_id)
            
            logger.info(f"会话缓存删除成功: {session_id}")
            return deleted_count > 0
        except Exception as e:
            logger.error(f"删除会话缓存失败 {session_id}: {e}")
            return False
    
    async def exists_session(self, session_id: str) -> bool:
        """
        检查会话是否存在
        :param session_id: 会话ID
        :return: 是否存在
        """
        try:
            session_key = self._get_session_key(session_id)
            return await self.redis_client.exists(session_key)
        except Exception as e:
            logger.error(f"检查会话存在失败 {session_id}: {e}")
            return False
    
    async def update_session_field(self, session_id: str, field: str, value: Any) -> bool:
        """
        更新会话的特定字段
        :param session_id: 会话ID
        :param field: 字段名
        :param value: 字段值
        :return: 操作是否成功
        """
        try:
            session_data = await self.get_session(session_id)
            if session_data is None:
                logger.warning(f"会话不存在，无法更新字段: {session_id}")
                return False
            
            session_data[field] = value
            session_data["last_activity"] = datetime.now(timezone.utc)
            
            return await self.set_session(session_id, session_data)
        except Exception as e:
            logger.error(f"更新会话字段失败 {session_id}.{field}: {e}")
            return False
    
    async def get_active_sessions(self) -> List[str]:
        """
        获取所有活跃会话ID列表
        :return: 会话ID列表
        """
        try:
            sessions_data = await self.redis_client.hgetall(self.sessions_list_key)
            return list(sessions_data.keys()) if sessions_data else []
        except Exception as e:
            logger.error(f"获取活跃会话列表失败: {e}")
            return []
    
    async def get_sessions_count(self) -> int:
        """
        获取活跃会话数量
        :return: 会话数量
        """
        try:
            sessions_data = await self.redis_client.hgetall(self.sessions_list_key)
            return len(sessions_data) if sessions_data else 0
        except Exception as e:
            logger.error(f"获取会话数量失败: {e}")
            return 0
    
    async def extend_session_ttl(self, session_id: str, expire: Optional[int] = None) -> bool:
        """
        延长会话过期时间
        :param session_id: 会话ID
        :param expire: 过期时间（秒），默认24小时
        :return: 操作是否成功
        """
        try:
            session_key = self._get_session_key(session_id)
            expire_time = expire or self.default_expire
            return await self.redis_client.expire(session_key, expire_time)
        except Exception as e:
            logger.error(f"延长会话TTL失败 {session_id}: {e}")
            return False
    
    async def cleanup_expired_sessions(self) -> int:
        """
        清理过期的会话（从活跃列表中移除已过期的会话）
        :return: 清理的会话数量
        """
        try:
            cleaned_count = 0
            sessions_data = await self.redis_client.hgetall(self.sessions_list_key)
            
            if not sessions_data:
                return 0
            
            for session_id in sessions_data.keys():
                session_key = self._get_session_key(session_id)
                if not await self.redis_client.exists(session_key):
                    # 会话已过期，从活跃列表中移除
                    await self.redis_client.hdel(self.sessions_list_key, session_id)
                    cleaned_count += 1
                    logger.debug(f"清理过期会话: {session_id}")
            
            if cleaned_count > 0:
                logger.info(f"清理了 {cleaned_count} 个过期会话")
            
            return cleaned_count
        except Exception as e:
            logger.error(f"清理过期会话失败: {e}")
            return 0
    
    async def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        获取会话信息（包括TTL等元数据）
        :param session_id: 会话ID
        :return: 会话信息
        """
        try:
            session_key = self._get_session_key(session_id)
            session_data = await self.get_session(session_id)
            
            if session_data is None:
                return None
            
            ttl = await self.redis_client.ttl(session_key)
            
            return {
                "session_id": session_id,
                "data": session_data,
                "ttl": ttl,
                "expires_at": datetime.now(timezone.utc) + timedelta(seconds=ttl) if ttl > 0 else None
            }
        except Exception as e:
            logger.error(f"获取会话信息失败 {session_id}: {e}")
            return None
    
    async def migrate_from_memory(self, memory_sessions: Dict[str, Dict[str, Any]]) -> int:
        """
        从内存中的active_sessions迁移到Redis
        :param memory_sessions: 内存中的会话数据
        :return: 迁移的会话数量
        """
        try:
            migrated_count = 0
            for session_id, session_data in memory_sessions.items():
                success = await self.set_session(session_id, session_data)
                if success:
                    migrated_count += 1
                    logger.debug(f"会话迁移成功: {session_id}")
                else:
                    logger.warning(f"会话迁移失败: {session_id}")
            
            logger.info(f"会话迁移完成: {migrated_count}/{len(memory_sessions)}")
            return migrated_count
        except Exception as e:
            logger.error(f"会话迁移失败: {e}")
            return 0 