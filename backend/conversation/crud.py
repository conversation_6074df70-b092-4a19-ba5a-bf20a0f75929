from backend.db.mongodb import MongoD<PERSON>ase
from typing import Optional, List, Dict, Any
from bson import ObjectId
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class ContextRepository(MongoDBBase):
    def __init__(self):
        super().__init__(collection_name="contexts", model_class=None)
    
    async def add_context(self, conversation_id: str, context_type: str, context_data: Dict) -> Dict:
        """添加上下文
        Args:
            conversation_id: 对话ID
            context_type: 上下文类型
            context_data: 上下文数据
        """
        context = {
            "conversation_id": conversation_id,
            "context_type": context_type,
            "context_data": context_data,
            "created_at": datetime.utcnow()
        }
        return await self.create(context)
    
    async def get_contexts(self, conversation_id: str) -> List[Dict]:
        """获取对话的所有上下文"""
        return await self.find_many({"conversation_id": conversation_id})
    
    async def delete_context(self, context_id: str) -> bool:
        """删除上下文"""
        return await self.delete(context_id)
    
    async def delete_conversation_contexts(self, conversation_id: str) -> bool:
        """删除对话的所有上下文"""
        result = await self._collection.delete_many({"conversation_id": conversation_id})
        return result.deleted_count > 0
