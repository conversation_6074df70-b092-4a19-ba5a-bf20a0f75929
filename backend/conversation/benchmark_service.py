import json
import logging
from typing import List, Dict, Any, Optional
from fastapi import HTTPException
from backend.conversation.service_factory import get_service_factory

logger = logging.getLogger(__name__)

class BenchmarkService:
    """Benchmark服务类，负责处理benchmark测试数据相关功能"""
    
    def __init__(self):
        self.service_factory = get_service_factory()
    
    async def get_benchmark_list(self) -> Dict[str, Any]:
        """获取所有benchmark题目列表"""
        try:
            benchmark_data = self.service_factory.load_benchmark_data()
            # 返回简化版本，仅包含ID、问题和话题
            simplified_data = [
                {
                    "id": idx,
                    "problem": item["problem"],
                    "topic": item.get("problem_topic", "未知")
                }
                for idx, item in enumerate(benchmark_data)
            ]
            return {"items": simplified_data}
        except Exception as e:
            logger.error(f"获取benchmark列表失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"获取benchmark列表失败: {str(e)}")
    
    async def get_benchmark_item(self, item_id: int) -> Dict[str, Any]:
        """获取特定benchmark题目详情"""
        try:
            benchmark_data = self.service_factory.load_benchmark_data()
            if 0 <= item_id < len(benchmark_data):
                return benchmark_data[item_id]
            raise HTTPException(status_code=404, detail="找不到指定的benchmark题目")
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取benchmark题目失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"获取benchmark题目失败: {str(e)}")

def get_benchmark_service() -> BenchmarkService:
    """获取benchmark服务实例"""
    return BenchmarkService() 