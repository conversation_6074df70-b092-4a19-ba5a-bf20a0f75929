from fastapi import APIRouter, Depends, HTTPException, status, File, UploadFile, Form, BackgroundTasks, WebSocket, Response, Query
from backend.conversation.service import ConversationService
from backend.conversation.service_factory import get_service_factory
from backend.conversation.benchmark_service import get_benchmark_service
from backend.conversation.models import (
    ResearchRequest, FeedbackRequest, ClarificationAnswerRequest,
    ResearchResponse, CreateSessionResponse, StartResearchResponse,
    CreateSessionRequest, StartResearchRequest, UpdateResearchStatusRequest,
    UserModelPreferences, EditRequest, ContentPreferenceRequest, 
    UpdateSearchStatusRequest, PendingQuestionsResponse, ContextContentResponse,
    DeleteContextRequest
)
from backend.auth.models import UserDB
from backend.auth.dependencies import get_current_active_user, check_user_token_budget
from backend.documents.dependencies import get_document_by_id
from backend.documents.models import Document
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/conversation", tags=["conversation"])

# 获取服务工厂依赖
def get_services():
    """获取服务工厂实例"""
    service_factory = get_service_factory()
    return service_factory.get_services()

# WebSocket路由
@router.websocket("/ws/research/{session_id}")
async def websocket_research(websocket: WebSocket, session_id: str):
    """通过WebSocket接收研究进展"""
    service = ConversationService()
    await service.handle_websocket_connection(websocket, session_id)

# Benchmark API
@router.get("/benchmark/list")
async def get_benchmark_list():
    """获取所有benchmark题目列表"""
    benchmark_service = get_benchmark_service()
    return await benchmark_service.get_benchmark_list()

@router.get("/benchmark/{item_id}")
async def get_benchmark_item(item_id: int):
    """获取特定benchmark题目详情"""
    benchmark_service = get_benchmark_service()
    return await benchmark_service.get_benchmark_item(item_id)

# v1 API（兼容性保持，内部使用v2逻辑）
@router.get("/api/chats/my")
async def get_my_chats(
    skip: int = 0,
    limit: int = 20,
    current_user: UserDB = Depends(get_current_active_user)
):
    """获取我的研究会话
    
    Args:
        skip: 跳过数量，默认0
        limit: 返回数量限制，默认20个，最大100个
    """
    # 限制最大返回数量
    limit = min(limit, 100)
    service = ConversationService()
    return await service.get_my_chats(current_user, skip, limit)

@router.post("/research/start", response_model=ResearchResponse)
async def start_research(
    request: ResearchRequest,
    background_tasks: BackgroundTasks,
    current_user: UserDB = Depends(check_user_token_budget)
):
    """启动新的研究任务（使用v2逻辑）"""
    service = ConversationService()
    return await service.start_research(request, background_tasks, current_user)

@router.get("/research/{session_id}/status")
async def get_research_status(
    session_id: str,
    current_user: UserDB = Depends(get_current_active_user)
):
    """获取研究状态"""
    service = ConversationService()
    return await service.get_research_status(session_id, current_user)

@router.post("/research/{session_id}/feedback")
async def submit_feedback(
    session_id: str, 
    request: FeedbackRequest,
    current_user: UserDB = Depends(get_current_active_user)
):
    """提交用户反馈"""
    service = ConversationService()
    return await service.submit_feedback(session_id, request, current_user)

# v2 API（推荐使用）
@router.post("/api/v2/research/create_session", response_model=CreateSessionResponse)
async def create_session_v2(
    request: CreateSessionRequest,
    current_user: UserDB = Depends(get_current_active_user)
):
    """创建新的研究会话（V2版本）"""
    service = ConversationService()
    return await service.create_session_v2(request, current_user)

@router.post("/api/v2/research/start", response_model=StartResearchResponse)
async def start_research_v2(
    request: StartResearchRequest,
    background_tasks: BackgroundTasks,
    current_user: UserDB = Depends(check_user_token_budget)
):
    """启动新的研究任务（V2版本）"""
    service = ConversationService()
    return await service.start_research_v2(request, background_tasks, current_user)

@router.post("/api/v2/research/update_status")
async def update_research_status(
    request: UpdateResearchStatusRequest,
    current_user: UserDB = Depends(get_current_active_user)
):
    """更新研究状态"""
    service = ConversationService()
    return await service.update_research_status(request, current_user)

# 现有的v2 API（已存在）
@router.post("/v2/research/select_note/{note_id}")
async def add_note_context(
    note_id: str,
    conversation_id: str = Form(...),
    current_user: UserDB = Depends(get_current_active_user),
    document: Document = Depends(get_document_by_id)
):
    try:
        service = ConversationService()
        await service.add_note_context(conversation_id, note_id, document.content, document.title, current_user)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"添加笔记上下文失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"添加笔记上下文失败: {str(e)}")
    
@router.post("/v2/research/upload_file")
async def upload_file(
    conversation_id: str = Form(...),
    file: UploadFile = File(...),
    file_type: str = Form(None),  # 可选参数，前端传递了但后端可能不使用
    current_user: UserDB = Depends(get_current_active_user)
):
    try:
        service = ConversationService()
        await service.add_file_context(conversation_id, file, current_user)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"添加文件上下文失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"添加文件上下文失败: {str(e)}")

@router.get("/v2/research/contexts/{conversation_id}")
async def get_contexts(
    conversation_id: str,
    current_user: UserDB = Depends(get_current_active_user)
):
    try:
        service = ConversationService()
        return await service.get_formatted_contexts(conversation_id, current_user)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取上下文失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取上下文失败: {str(e)}")

@router.post("/research/{session_id}/clarification_answer")
async def submit_clarification_answer(
    session_id: str, 
    request: ClarificationAnswerRequest,
    current_user: UserDB = Depends(get_current_active_user)
):
    """提交澄清问题回答"""
    service = ConversationService()
    return await service.submit_clarification_answer(session_id, request, current_user)

@router.post("/research/{session_id}/pause")
async def pause_research(
    session_id: str,
    current_user: UserDB = Depends(get_current_active_user)
):
    """暂停研究任务"""
    service = ConversationService()
    return await service.pause_research(session_id, current_user)

@router.post("/research/{session_id}/resume")
async def resume_research(
    session_id: str,
    current_user: UserDB = Depends(check_user_token_budget)
):
    """恢复研究任务"""
    service = ConversationService()
    return await service.resume_research(session_id, current_user)

@router.delete("/research/{session_id}")
async def delete_research_session(
    session_id: str,
    current_user: UserDB = Depends(get_current_active_user)
):
    """删除研究会话"""
    service = ConversationService()
    return await service.delete_session(session_id, current_user)

# 模型配置API
@router.get("/api/models/available")
async def get_available_models(current_user: UserDB = Depends(get_current_active_user)):
    """获取可用的模型列表"""
    service = ConversationService()
    return await service.get_available_models(current_user)

@router.post("/api/models/preferences")
async def set_models_preferences(
    preferences: UserModelPreferences,
    current_user: UserDB = Depends(get_current_active_user)
):
    """设置用户模型偏好"""
    service = ConversationService()
    return await service.set_models_preferences(preferences, current_user)

# 新迁移的API

@router.get("/research/{session_id}/pending_questions", response_model=PendingQuestionsResponse)
async def get_pending_questions(
    session_id: str,
    current_user: UserDB = Depends(get_current_active_user)
):
    """获取会话中待处理的澄清问题"""
    service = ConversationService()
    return await service.get_pending_questions(session_id, current_user)

@router.post("/research/{session_id}/edit_request")
async def submit_edit_request(
    session_id: str, 
    request: EditRequest,
    background_tasks: BackgroundTasks,
    current_user: UserDB = Depends(check_user_token_budget)
):
    """提交编辑请求（专门的 chat to edit 功能）"""
    service = ConversationService()
    return await service.submit_edit_request(session_id, request, background_tasks, current_user)

@router.post("/api/research/content_preference")
async def submit_content_preference(
    request: ContentPreferenceRequest,
    current_user: UserDB = Depends(get_current_active_user)
):
    """提交用户对内容的偏好（点赞/取消点赞）"""
    service = ConversationService()
    return await service.submit_content_preference(request, current_user)

@router.post("/api/v2/research/update_search_status")
async def update_search_status(
    request: UpdateSearchStatusRequest,
    current_user: UserDB = Depends(get_current_active_user)
):
    """更新搜索状态"""
    service = ConversationService()
    return await service.update_search_status(request, current_user)

@router.get("/api/research/request_context/{context_id}")
async def get_context_content(
    context_id: str,
    conversation_id: str = Query(..., description="会话ID"),
    current_user: UserDB = Depends(get_current_active_user)
):
    """获取特定上下文的内容"""
    service = ConversationService()
    result = await service.get_context_content(context_id, conversation_id, current_user)
    
    # 如果是文件类型，返回文件内容
    if isinstance(result, dict) and result.get("type") == "file":
        return Response(
            content=result["file_content"],
            media_type=result["content_type"],
            headers={
                "Content-Disposition": f"attachment; filename=\"{result['filename']}\""
            }
        )
    else:
        # 如果是普通上下文，返回JSON
        return result

@router.post("/api/research/contexts/delete")
async def delete_context(
    request: DeleteContextRequest,
    current_user: UserDB = Depends(get_current_active_user)
):
    """删除指定的上下文"""
    service = ConversationService()
    return await service.delete_context(request, current_user)