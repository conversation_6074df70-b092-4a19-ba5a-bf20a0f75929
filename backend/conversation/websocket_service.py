import asyncio
import json
import uuid
import logging
import gzip
import time
from typing import Dict, Any, Optional, List
from fastapi import WebSocket, WebSocketDisconnect
from datetime import datetime, timezone, timedelta
from backend.utils import get_cn_time
from backend.redis.dependencies import get_redis_client
from backend.conversation.session_store import SessionMessage
# from backend.conversation.service import ConversationService
import copy

logger = logging.getLogger(__name__)

# WebSocket心跳配置（优化后）
WS_HEARTBEAT_INTERVAL = 30.0     # 心跳间隔，单位秒（进一步增加间隔，减少干扰）
WS_HEARTBEAT_TIMEOUT = 5.0       # 心跳响应超时，单位秒（增加超时时间）
WS_MAX_HEARTBEAT_FAILURES = 5    # 最大连续心跳失败次数（增加容错次数）
WS_ENABLE_STRICT_HEARTBEAT = False  # 是否启用严格心跳检测（设为False以避免误断开）

class WebSocketService:
    """WebSocket服务类，管理研究会话的WebSocket连接和消息（单例模式，性能优化版本）"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(WebSocketService, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        # 避免重复初始化
        if WebSocketService._initialized:
            return
            
        # 延迟导入以避免循环导入
        from .dependencies import get_session_cache
        
        self.session_cache = get_session_cache()
        self.redis_client = get_redis_client()
        
        # 存储活跃的WebSocket连接
        self.websocket_connections: Dict[str, WebSocket] = {}
        
        # 消息队列前缀（优化后的结构）
        self.queue_prefix = "ws_queue_v2:"
        self.connection_prefix = "ws_conn_v2:"
        self.message_meta_prefix = "ws_meta_v2:"
        
        # 性能优化配置
        self._message_compression_threshold = 1024  # 大于1KB的消息进行压缩
        self._batch_size = 5                        # 批量发送消息数量
        self._batch_timeout = 0.1                   # 批量发送超时时间（秒）
        self._queue_max_size = 2000                 # 队列最大大小（增加以避免丢失消息）
        self._message_ttl = 7200                    # 消息TTL（2小时，增加以保留更多消息）
        
        # 连接管理优化
        self._connection_pool_size = 50             # 连接池大小
        self._idle_timeout = 300                    # 空闲连接超时（5分钟）
        
        # 性能监控
        self._stats = {
            "messages_sent": 0,
            "messages_compressed": 0,
            "messages_batched": 0,
            "connections_active": 0,
            "queue_operations": 0
        }
        
        WebSocketService._initialized = True
        logger.info("WebSocketService单例已初始化（性能优化版本）")
        
    def _get_queue_key(self, session_id: str) -> str:
        """获取消息队列的Redis键名"""
        return f"{self.queue_prefix}{session_id}"
    
    def _get_connection_key(self, session_id: str) -> str:
        """获取连接状态的Redis键名"""
        return f"{self.connection_prefix}{session_id}"
    
    def _get_message_meta_key(self, session_id: str) -> str:
        """获取消息元数据的Redis键名"""
        return f"{self.message_meta_prefix}{session_id}"
    
    def _compress_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """压缩大消息"""
        try:
            message_str = json.dumps(message)
            
            if len(message_str) > self._message_compression_threshold:
                # 压缩消息内容
                compressed_data = gzip.compress(message_str.encode('utf-8'))
                
                # 创建压缩消息格式
                compressed_message = {
                    "type": "compressed",
                    "original_type": message.get("type", "unknown"),
                    "compressed_data": compressed_data.hex(),  # 转换为十六进制字符串
                    "original_size": len(message_str),
                    "compressed_size": len(compressed_data)
                }
                
                self._stats["messages_compressed"] += 1
                logger.debug(f"消息已压缩: {len(message_str)} -> {len(compressed_data)} bytes")
                return compressed_message
            
            return message
            
        except Exception as e:
            logger.error(f"消息压缩失败: {str(e)}")
            return message
    
    def _should_batch_message(self, message: Dict[str, Any]) -> bool:
        """判断消息是否适合批量处理"""
        # 某些重要消息不进行批量处理
        high_priority_types = ["error", "paused", "clarification", "status"]
        return message.get("type") not in high_priority_types
    
    async def add_websocket_connection(self, session_id: str, websocket: WebSocket):
        """添加WebSocket连接（优化版本）"""
        self.websocket_connections[session_id] = websocket
        self._stats["connections_active"] = len(self.websocket_connections)
        
        # 在Redis中记录连接状态（包含性能信息）
        if self.redis_client:
            connection_info = {
                "connected_at": get_cn_time().isoformat(),
                "last_activity": get_cn_time().isoformat(),
                "messages_sent": "0",
                "connection_quality": "good"
            }
            await self.redis_client.hset(
                self._get_connection_key(session_id),
                connection_info
            )
            await self.redis_client.expire(self._get_connection_key(session_id), 3600)
            
        logger.info(f"WebSocket连接已添加: {session_id}, 当前连接数: {len(self.websocket_connections)}")
    
    async def remove_websocket_connection(self, session_id: str):
        """移除WebSocket连接（优化版本）"""
        if session_id in self.websocket_connections:
            del self.websocket_connections[session_id]
            self._stats["connections_active"] = len(self.websocket_connections)
            
        # 从Redis中移除连接状态和清理相关数据
        if self.redis_client:
            try:
                keys_to_delete = [
                    self._get_connection_key(session_id),
                    self._get_message_meta_key(session_id)
                ]
                await self.redis_client.delete(*keys_to_delete)
            except Exception as e:
                logger.error(f"清理连接Redis数据失败: {str(e)}")
                
        logger.info(f"WebSocket连接已移除: {session_id}, 当前连接数: {len(self.websocket_connections)}")
    
    async def enqueue_message(self, session_id: str, message: Dict[str, Any]):
        """将消息放入队列（优化版本）
        
        重要设计原则：
        1. 本方法只负责入队，绝不直接发送消息
        2. 所有消息发送都必须通过 _message_loop 统一处理
        3. 支持消息压缩和批量处理优化
        4. 实现队列大小管理，但尽量避免丢失消息
        """
        try:
            # 处理特殊消息类型
            if message.get("type") == "complete":
                message["type"] = "paused"  # 将complete转换为paused
            
            # 消息深度拷贝确保不被修改
            message_copy = copy.deepcopy(message)
            
            # 添加时间戳和消息ID
            message_copy["timestamp"] = get_cn_time().isoformat()
            message_copy["message_id"] = str(uuid.uuid4())[:8]
            
            # 压缩大消息
            compressed_message = self._compress_message(message_copy)
            
            # 使用Redis作为消息队列
            if self.redis_client:
                queue_key = self._get_queue_key(session_id)
                
                # 检查队列大小，如果过大则清理最旧的消息（而不是拒绝新消息）
                current_size = await self.redis_client.llen(queue_key)
                if current_size >= self._queue_max_size:
                    # 移除一批最旧的消息，为新消息腾出空间
                    remove_count = min(100, current_size - self._queue_max_size + 1)
                    for _ in range(remove_count):
                        await self.redis_client.rpop(queue_key)
                    logger.warning(f"队列过大，清理了{remove_count}条最旧消息: {session_id}")
                
                # 入队新消息
                await self.redis_client.lpush(queue_key, compressed_message)
                await self.redis_client.expire(queue_key, self._message_ttl)
                
                # 更新消息元数据
                meta_key = self._get_message_meta_key(session_id)
                meta_data = {
                    "total_messages": current_size + 1,
                    "last_message_time": get_cn_time().isoformat(),
                    "message_types": message_copy.get("type", "unknown")
                }
                await self.redis_client.set(meta_key, meta_data, ex=self._message_ttl)
                
                self._stats["queue_operations"] += 1
                logger.debug(f"消息已入队到Redis: {session_id}, 类型: {message_copy.get('type', 'unknown')}")
            
            # 检查连接状态用于日志记录
            if session_id in self.websocket_connections:
                logger.debug(f"消息已入队，等待消息循环发送: {session_id}")
            else:
                logger.debug(f"无活跃WebSocket连接，消息已存储等待连接: {session_id}")
                
        except Exception as e:
            logger.error(f"入队消息失败: {e}")
    
    async def get_queued_messages(self, session_id: str) -> List[Dict[str, Any]]:
        """获取队列中的消息（优化版本，支持批量获取）
        
        Redis列表操作说明：
        - lpush: 向列表左侧(头部)插入消息，新消息在前
        - rpop: 从列表右侧(尾部)弹出消息，最早的消息先出
        - 这种组合天然实现FIFO(先进先出)，无需额外处理顺序
        """
        try:
            if not self.redis_client:
                return []
                
            queue_key = self._get_queue_key(session_id)
            messages = []
            
            # 批量获取消息，避免频繁的Redis操作
            batch_count = 0
            while batch_count < self._batch_size:
                message_data = await self.redis_client.rpop(queue_key)
                if not message_data:
                    break
                    
                # 处理压缩消息
                if isinstance(message_data, dict) and message_data.get("type") == "compressed":
                    try:
                        # 解压缩消息
                        compressed_data = bytes.fromhex(message_data["compressed_data"])
                        decompressed_str = gzip.decompress(compressed_data).decode('utf-8')
                        original_message = json.loads(decompressed_str)
                        messages.append(original_message)
                        logger.debug(f"消息已解压缩: {message_data['compressed_size']} -> {message_data['original_size']} bytes")
                    except Exception as e:
                        logger.error(f"解压缩消息失败: {str(e)}")
                        # 如果解压缩失败，返回原始消息
                        messages.append(message_data)
                else:
                    messages.append(message_data)
                    
                batch_count += 1
            
            if messages:
                self._stats["messages_batched"] += len(messages)
                logger.debug(f"从Redis批量获取了 {len(messages)} 条消息: {session_id}")
                
            return messages
            
        except Exception as e:
            logger.error(f"获取队列消息失败: {e}")
            return []
    
    async def handle_websocket_connection(
        self, 
        websocket: WebSocket, 
        session_id: str,
        conversation_service: 'ConversationService'
    ):
        """处理WebSocket连接的完整生命周期（优化版本）"""
        logger.info(f"新的WebSocket连接请求: {session_id}")
        await websocket.accept()
        logger.info(f"WebSocket连接已接受: {session_id}")
        
        # 添加心跳失败计数器
        heartbeat_failures = 0
        max_heartbeat_failures = WS_MAX_HEARTBEAT_FAILURES
        
        try:
            # 添加WebSocket连接
            await self.add_websocket_connection(session_id, websocket)
            
            # 检查会话是否存在，如果不在内存中则加载
            session = await conversation_service._get_session(session_id)
            if not session:
                logger.warning(f"WebSocket连接失败: 找不到会话 {session_id}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "找不到指定的研究会话"
                }))
                await websocket.close()
                return
            
            # 发送优化的初始状态消息
            await self._send_initial_status(websocket, session)
            
            # 发送历史消息（分批发送但不限制数量）
            await self._send_historical_messages_optimized(websocket, session)
            
            # 直接进入优化的消息循环，监听新的实时消息
            await self._message_loop_optimized(websocket, session_id, heartbeat_failures, max_heartbeat_failures)
            
        except WebSocketDisconnect:
            logger.info(f"WebSocket连接断开: {session_id}")
        except Exception as e:
            logger.error(f"WebSocket处理出错: {e}", exc_info=True)
        finally:
            # 清理连接
            await self.remove_websocket_connection(session_id)
    
    async def _send_initial_status(self, websocket: WebSocket, session: Dict[str, Any]):
        """发送初始状态消息"""
        initial_status = {
            "type": "status",
            "status": session["status"],
            "message": f"研究状态: {session.get('status')}",
            "total_messages": session.get("total_messages", 0)
        }
        logger.debug(f"发送初始状态消息: {initial_status}")
        await websocket.send_text(json.dumps(initial_status))
    
    async def _send_historical_messages_optimized(self, websocket: WebSocket, session: Dict[str, Any]):
        """发送历史消息（优化版本，分批发送但不限制数量）"""
        if session["status"] not in ["starting", "cancelled"]:
            message_count = 0
            logger.debug(f"会话状态: {session['status']} 决定是否发送消息历史")
            
            if session["status"] in ["running", "paused", "clarification"]:
                # 收集完整历史消息，分批发送避免一次性传输过多
                messages = session.get("messages", [])
                if messages:
                    # 分批发送历史消息，避免网络拥塞
                    batch_size = 20  # 每批20条消息
                    total_batches = (len(messages) + batch_size - 1) // batch_size
                    
                    for batch_index in range(total_batches):
                        start_idx = batch_index * batch_size
                        end_idx = min(start_idx + batch_size, len(messages))
                        batch_messages = messages[start_idx:end_idx]
                        
                        # 处理批次消息
                        processed_messages = []
                        for message in batch_messages:
                            if message["type"] == "update" or message["type"] == "system":
                                message["content"]["replay"] = True
                                processed_messages.append({
                                    "type": message["type"],
                                    "content": message["content"]
                                })
                                message_count += 1
                        
                        if processed_messages:
                            batch_message = {
                                "type": "batch_updates",
                                "messages": processed_messages,
                                "batch_info": {
                                    "batch_index": batch_index,
                                    "total_batches": total_batches,
                                    "batch_size": len(processed_messages)
                                }
                            }
                            await websocket.send_text(json.dumps(batch_message))
                            
                            # 添加小延迟，避免发送过快
                            if batch_index < total_batches - 1:
                                await asyncio.sleep(0.05)
                    
                    logger.info(f"分批发送了 {message_count} 条历史消息，共 {total_batches} 批")
            
            # 发送重放完成消息
            if message_count > 0:
                await websocket.send_text(json.dumps({
                    "type": "replay_complete",
                    "message": f"已发送 {message_count} 条历史消息"
                }))
        
        # 处理特殊状态消息
        await self._send_special_status_messages(websocket, session)
    
    async def _send_special_status_messages(self, websocket: WebSocket, session: Dict[str, Any]):
        """发送特殊状态消息"""
        # 处理澄清问题
        if session["status"] == "clarification":
            clarification_content = session.get("clarification_questions", [])
            if clarification_content:
                clarification_message = {
                    "type": "clarification",
                    "questions": clarification_content,
                    "message": "研究智能体需要您回答一些澄清问题"
                }
                logger.debug(f"发送存储的澄清问题: {len(clarification_content)}个问题")
                await websocket.send_text(json.dumps(clarification_message))
        
        # 如果会话已paused且有报告，发送报告
        if session["status"] == "paused" and session.get("report"):
            paused_message = {
                "type": "paused",
                "report": session["report"]
            }
            logger.debug(f"发送paused状态消息和报告")
            await websocket.send_text(json.dumps(paused_message))
    
    async def _message_loop_optimized(
        self, 
        websocket: WebSocket, 
        session_id: str, 
        heartbeat_failures: int, 
        max_heartbeat_failures: int
    ):
        """WebSocket消息循环（优化版本）- 统一的消息发送出口
        
        架构设计说明：
        1. 这是所有实时消息的唯一发送出口
        2. 所有消息必须通过Redis队列进入此循环，严格保序
        3. 支持消息压缩、批量发送等性能优化
        4. 智能心跳检测和连接质量监控
        """
        last_message_time = time.time()
        
        while True:
            try:
                # 从Redis队列批量获取待发送的消息
                queued_messages = await self.get_queued_messages(session_id)
                
                if queued_messages:
                    # 批量发送消息，提高性能
                    await self._send_messages_batch(websocket, queued_messages, session_id)
                    last_message_time = time.time()
                
                # 简化的连接检测（只在长时间无消息时进行简单的ping检测）
                time_since_last_message = time.time() - last_message_time
                should_check_connection = time_since_last_message > WS_HEARTBEAT_INTERVAL
                
                if should_check_connection:
                    # 根据配置选择心跳检测方式
                    if WS_ENABLE_STRICT_HEARTBEAT:
                        # 使用严格的心跳检测（等待响应）
                        connection_result = await self._handle_heartbeat(websocket, session_id)
                    else:
                        # 使用简单的ping检测（不等待响应，更稳定）
                        connection_result = await self._simple_connection_check(websocket, session_id)
                    
                    if connection_result == "failed":
                        heartbeat_failures += 1
                        if heartbeat_failures >= max_heartbeat_failures:
                            logger.error(f"连续{max_heartbeat_failures}次连接检测失败，断开连接: {session_id}")
                            break
                    else:
                        heartbeat_failures = 0
                        last_message_time = time.time()  # 重置最后消息时间
                
                # 动态调整循环间隔
                sleep_time = 0.1 if queued_messages else 0.5
                await asyncio.sleep(sleep_time)
                
            except Exception as e:
                logger.error(f"消息循环处理出错: {e}")
                break
    
    async def _send_messages_batch(self, websocket: WebSocket, messages: List[Dict[str, Any]], session_id: str):
        """批量发送消息"""
        try:
            for message in messages:
                await websocket.send_text(json.dumps(message))
                self._stats["messages_sent"] += 1
                
            logger.debug(f"批量发送了 {len(messages)} 条消息: {session_id}")
            
            # 更新连接活跃状态
            if self.redis_client:
                connection_key = self._get_connection_key(session_id)
                await self.redis_client.hset(connection_key, {
                    "last_activity": get_cn_time().isoformat(),
                    "messages_sent": str(self._stats["messages_sent"])
                })
                
        except Exception as e:
            logger.error(f"批量发送消息失败: {e}")
            raise
    
    async def _handle_heartbeat(self, websocket: WebSocket, session_id: str) -> str:
        """处理心跳检测（改进版本）"""
        try:
            heartbeat_id = str(uuid.uuid4())[:8]
            heartbeat_msg = json.dumps({"type": "heartbeat", "id": heartbeat_id})
            
            # 发送心跳消息
            await websocket.send_text(heartbeat_msg)
            logger.debug(f"发送心跳: {heartbeat_id} -> {session_id}")
            
            # 使用更宽松的心跳检测策略
            # 尝试接收多次响应，允许接收到其他消息
            for attempt in range(3):  # 最多尝试3次接收
                try:
                    response = await asyncio.wait_for(
                        websocket.receive_text(), 
                        timeout=WS_HEARTBEAT_TIMEOUT / 3  # 分割超时时间
                    )
                    
                    try:
                        response_data = json.loads(response)
                        message_type = response_data.get("type", "")
                        response_id = response_data.get("id", "")
                        
                        # 检查是否是心跳响应
                        if (message_type == "heartbeat_response" and response_id == heartbeat_id) or \
                           message_type == "pong":
                            logger.debug(f"收到心跳响应: {response_id} <- {session_id}")
                            return "success"
                        else:
                            # 收到其他消息，说明连接正常，也算成功
                            logger.debug(f"收到其他消息（连接正常）: {message_type} <- {session_id}")
                            # 但继续尝试接收心跳响应
                            continue
                            
                    except json.JSONDecodeError:
                        logger.debug(f"收到非JSON消息（连接正常）: {session_id}")
                        continue
                        
                except asyncio.TimeoutError:
                    if attempt == 2:  # 最后一次尝试超时
                        logger.warning(f"心跳超时（尝试{attempt+1}/3）: {session_id}")
                        return "failed"
                    else:
                        logger.debug(f"心跳等待超时（尝试{attempt+1}/3）: {session_id}")
                        continue
            
            # 如果所有尝试都没有收到有效响应，但也没有异常，认为连接可能正常
            logger.debug(f"心跳检测完成，未收到明确响应但连接似乎正常: {session_id}")
            return "success"  # 更宽松的策略
                
        except Exception as e:
            logger.error(f"心跳处理失败: {e}")
            return "failed"
    
    async def _simple_connection_check(self, websocket: WebSocket, session_id: str) -> str:
        """简化的连接检测（非阻塞方式）"""
        try:
            # 发送一个简单的ping消息，不等待响应
            ping_msg = json.dumps({"type": "ping", "timestamp": int(time.time() * 1000)})
            await websocket.send_text(ping_msg)
            logger.debug(f"发送ping检测: {session_id}")
            
            # 立即返回成功，不等待响应
            # 如果连接真的断开了，发送消息本身就会抛出异常
            return "success"
            
        except Exception as e:
            logger.warning(f"连接检测失败（连接已断开）: {session_id}, 错误: {e}")
            return "failed"
    
    def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        return {
            **self._stats,
            "queue_prefix": self.queue_prefix,
            "compression_threshold": self._message_compression_threshold,
            "batch_size": self._batch_size,
            "active_connections": list(self.websocket_connections.keys())
        } 