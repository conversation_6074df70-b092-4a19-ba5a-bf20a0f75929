import os
import uuid
import asyncio
from typing import Any, Optional, List, Dict
from datetime import datetime

from fastapi import File, HTTPException, UploadFile, status, BackgroundTasks
from backend.conversation.models import (
    ResearchRequest, FeedbackRequest, ClarificationAnswerRequest, CreateSessionRequest, 
    StartResearchRequest, UpdateResearchStatusRequest, 
    EditRequest, ContentPreferenceRequest, UpdateSearchStatusRequest, 
    PendingQuestionsResponse, ContextContentResponse, DeleteContextRequest
)
from backend.conversation.service_factory import get_service_factory
from backend.conversation.websocket_service import WebSocketService
from backend.conversation.research_runner import get_research_runner
from backend.config import settings
from backend.auth.models import UserDB
from .dependencies import get_session_cache, get_session_repository
from backend.redis.dependencies import get_redis_client
from backend.utils import get_cn_time
import logging
from backend.conversation.dependencies import get_websocket_service

logger = logging.getLogger(__name__)

class ConversationService:
    """对话主服务（性能优化版本）"""
    def __init__(self):
        self.session_repo = get_session_repository()  # 使用SessionRepository统一管理
        self.service_factory = get_service_factory()
        self.websocket_service = get_websocket_service()  # 使用单例
        self.research_runner = get_research_runner(self.websocket_service)
        self.session_cache = get_session_cache()
        self.redis_client = get_redis_client()
        
        # 性能监控（简化版本）
        self._performance_stats = {
            "session_requests": 0,
            "agent_requests": 0
        }
    

    
    async def _get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话数据 - 直接使用SessionRepository"""
        try:
            return await self.session_repo.get_session(session_id)
        except Exception as e:
            logger.error(f"获取会话失败: {str(e)}")
            return None
    
    async def _save_session(self, session_id: str, session_data: Dict[str, Any]):
        """保存会话数据 - 通过SessionRepository处理（兼容性方法）"""
        try:
            # 提取需要更新的字段
            updates = {}
            for key in ["status", "question", "last_activity", "enable_cognition_search", "error", "report"]:
                if key in session_data:
                    updates[key] = session_data[key]
            
            if updates:
                await self.session_repo.update_session(session_id, updates)
        except Exception as e:
            logger.error(f"保存会话数据失败: {str(e)}")
    
    def _validate_serializable_session(self, session_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证并清理会话数据，确保可序列化"""
        import pickle
        
        serializable_session = {}
        
        for key, value in session_data.items():
            # 跳过agent字段
            if key == "agent":
                continue
                
            try:
                # 尝试序列化测试
                pickle.dumps(value)
                serializable_session[key] = value
            except Exception as e:
                logger.warning(f"字段 {key} 无法序列化，跳过: {type(value)} - {e}")
                # 对于无法序列化的字段，保存基本信息
                if hasattr(value, '__str__'):
                    try:
                        serializable_session[key] = str(value)
                    except:
                        serializable_session[key] = f"<无法序列化的{type(value).__name__}对象>"
                else:
                    serializable_session[key] = f"<无法序列化的{type(value).__name__}对象>"
        
        return serializable_session
    

    
    async def add_note_context(self, conversation_id: str, note_id: str, content: str, title: str, current_user: UserDB):
        """添加笔记上下文"""
        session = await self._get_session(conversation_id)
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="会话不存在"
            )
        if session["user_id"] != str(current_user.id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此会话"
            )
        research_agent = await self.session_repo.get_agent(conversation_id)

        try: 
            context_id = await research_agent.add_note_context(note_id, str(current_user.id), content, title)
            return context_id
        except Exception as e:
            logger.error(f"添加笔记上下文失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"添加笔记上下文失败: {str(e)}"
            )
    
    async def add_file_context(self, conversation_id: str, file: UploadFile, current_user: UserDB):
        """添加文件上下文"""
        session = await self._get_session(conversation_id)
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="会话不存在"
            )
        if session["user_id"] != str(current_user.id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此会话"
            )
        research_agent = await self.session_repo.get_agent(conversation_id)

        file_path = settings.UPLOAD_PATH + "/" + file.filename

        try:
            contents = await file.read()

            with open(file_path, 'wb') as f:
                f.write(contents)
            try:
                context_id = await research_agent.add_file_context(str(file_path))
                logger.info(f"文件上下文添加成功，context_id: {context_id}")
                return context_id
            except Exception as e:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.error(f"文件处理失败，已删除文件: {file_path}")
                raise e
        except Exception as e:
            logger.error(f"添加文件上下文失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    
    async def get_contexts(self, conversation_id: str, current_user: UserDB):
        """获取上下文列表"""
        session = await self.session_repo.get_session(conversation_id)
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="会话不存在"
            )
            
        # 检查权限
        if session["user_id"] != str(current_user.id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此会话"
            )
            
        research_agent = await self.session_repo.get_agent(conversation_id)
        try:
            contexts = research_agent.memory.get_all_contexts()
            return contexts
        except Exception as e:
            logger.error(f"获取上下文列表失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取上下文列表失败: {str(e)}"
            )

    async def get_formatted_contexts(self, conversation_id: str, current_user: UserDB):
        """获取格式化后的上下文列表"""
        contexts = await self.get_contexts(conversation_id, current_user)
        
        # 格式化上下文列表
        try:
            context_list = []
            for ctx in contexts:
                context_info = {
                    "context_id": ctx["context_id"],
                    "metadata": ctx["metadata"],
                    "is_valid": True
                }
                context_list.append(context_info)
                
            return {
                "status": "success", 
                "contexts": context_list,
                "total": len(context_list)
            }
        except Exception as e:
            logger.error(f"格式化上下文列表失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"格式化上下文列表失败: {str(e)}"
            )

    async def get_my_chats(self, current_user: UserDB, skip: int = 0, limit: int = 20):
        """获取我的研究会话
        
        Args:
            current_user: 当前用户
            skip: 跳过数量，默认0
            limit: 返回数量限制，默认20个
            
        Returns:
            格式化的会话列表
        """
        try:
            logger.info(f"获取会话列表: {current_user.id}, skip={skip}, limit={limit}")
            sessions = await self.session_repo.list_sessions(
                user_id=str(current_user.id),
                skip=skip,
                limit=limit
            )
            
            # 格式化返回结果
            chats = []
            for session in sessions:
                chat = {
                    "id": session.session_id,
                    "prompt": session.question,
                    "created_at": session.created_at.isoformat() if session.created_at else None,
                    "updated_at": session.last_activity.isoformat() if session.last_activity else None,
                    "status": session.status
                }
                chats.append(chat)
            
            return {
                "chats": chats,
                "total": len(chats),
                "skip": skip,
                "limit": limit
            }
        except Exception as e:
            logger.error(f"获取会话列表失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"获取会话列表失败: {str(e)}")

    async def create_session_v2(self, request: CreateSessionRequest, current_user: UserDB):
        """创建新的研究会话（V2版本）"""
        # 创建会话ID
        session_id = str(uuid.uuid4())
        logger.info(f"用户{current_user.id} 创建研究会话: {session_id}")
        
        # 通过 service_factory 创建研究智能体，确保唯一性
        research_agent = self.service_factory.get_research_agent(session_id)
        
        # 获取用户的模型偏好设置
        user_models_config = await self.session_repo.get_user_models_preference(str(current_user.id))
        
        # 如果有用户模型偏好，应用到研究智能体
        if user_models_config:
            research_agent.set_models(user_models_config)
            logger.info(f"应用用户模型偏好: {user_models_config}")
        
        # 创建会话数据
        session_data = {
            "status": "created",  # 初始状态为created
            "agent": research_agent,
            "last_activity": get_cn_time(),
            "question": "",  # 初始问题为空
            "started_at": get_cn_time(),
            "messages": [],
            "report": "",
            "error": "",
            "clarification_questions": [],
            "user_id": str(current_user.id),
            "models_config": user_models_config,
            "enable_cognition_search": False,
            "enable_search": True
        }
        
        # 直接通过Repository创建会话（自动处理缓存和持久化）
        await self.session_repo.create_session(
            session_id=session_id,
            question="",  # 初始问题为空
            user_id=str(current_user.id),
            metadata={"models_config": user_models_config} if user_models_config else None
        )
        
        return {
            "session_id": session_id,
            "message": "研究会话已创建"
        }

    async def start_research_v2(
        self, 
        request: StartResearchRequest,
        background_tasks: BackgroundTasks,
        current_user: UserDB
    ):
        """启动新的研究任务（V2版本）"""
        # 检查会话是否存在
        session = await self._get_session(request.session_id)
        if not session:
            raise HTTPException(status_code=404, detail="找不到指定的研究会话")
        
        # 验证会话所有权
        if session["user_id"] and session["user_id"] != str(current_user.id):
            raise HTTPException(status_code=403, detail="无权访问此研究会话")
        logger.info(f"request: {request}")
        logger.info(f"开始启动研究任务: {request.session_id}, {request.question}, {request.enable_cognition_search}, {request.enable_search}")
        
        research_agent = await self.session_repo.get_agent(request.session_id)
        research_agent.set_enable_cognition_search(request.enable_cognition_search)
        research_agent.set_enable_search(request.enable_search)
        
        # 再次检查用户模型偏好（以防在会话创建后配置发生变化）
        user_models_config = await self.session_repo.get_user_models_preference(str(current_user.id))
        if user_models_config:
            research_agent.set_models(user_models_config)
            logger.info(f"在研究任务启动前再次应用用户模型偏好: {user_models_config}")
            
            # 更新会话中的模型配置记录
            session["models_config"] = user_models_config
            await self.session_repo.set_session_models_config(request.session_id, user_models_config)
        
        # 准备输入数据
        input_data = {
            "question": request.question,
            "user_id": str(current_user.id),
        }
        
        # 如果有benchmark_id，添加到输入数据中
        if request.benchmark_id is not None:
            input_data["benchmark_id"] = request.benchmark_id
            
            # 如果是benchmark，尝试加载标准答案
            benchmark_data = self.service_factory.load_benchmark_data()
            if 0 <= request.benchmark_id < len(benchmark_data):
                benchmark_item = benchmark_data[request.benchmark_id]
                research_agent.standard_answer = benchmark_item.get("answer", "未提供标准答案")
        
        # 更新会话状态
        session["status"] = "running"
        session["question"] = request.question
        session["last_activity"] = get_cn_time()
        session["enable_cognition_search"] = request.enable_cognition_search
        session["enable_search"] = request.enable_search
        
        # 更新会话到Repository（自动处理缓存和持久化）
        await self.session_repo.update_session(request.session_id, {
            "status": "running",
            "question": request.question,
            "enable_cognition_search": request.enable_cognition_search,
            "enable_search": request.enable_search
        })
        

        
        # 在后台启动研究任务
        background_tasks.add_task(
            self.research_runner.run_agent_and_send_to_websocket,
            research_agent, 
            input_data, 
            request.session_id
        )
        
        return {
            "session_id": request.session_id,
            "status": "running",
            "message": "研究任务已启动，请通过WebSocket连接获取结果"
        }

    async def update_research_status(self, request: UpdateResearchStatusRequest, current_user: UserDB):
        """更新研究状态"""
        session_id = request.session_id
        session = await self._get_session(session_id)
        
        if not session:
            raise HTTPException(status_code=404, detail="找不到指定的研究会话")
        
        # 更新认知搜索状态
        research_agent = await self.session_repo.get_agent(session_id)
        research_agent.set_enable_cognition_search(request.enable_cognition_search)

        logger.info(f"更新认知搜索状态: {request.enable_cognition_search}")
        
        # 更新会话状态（自动处理缓存和持久化）
        await self.session_repo.update_session(
            session_id=session_id,
            updates={"enable_cognition_search": request.enable_cognition_search}
        )
        
        return {"status": "success", "message": "研究状态已更新"}

    # 用v2逻辑替换原有的start_research方法
    async def start_research(
        self, 
        request: ResearchRequest,
        background_tasks: BackgroundTasks,
        current_user: UserDB
    ):
        """启动新的研究任务（使用v2逻辑的v1兼容接口）"""
        # 先创建会话
        create_request = CreateSessionRequest(
            user_id=str(current_user.id),
            metadata=None
        )
        create_result = await self.create_session_v2(create_request, current_user)
        session_id = create_result["session_id"]
        
        # 然后启动研究
        start_request = StartResearchRequest(
            session_id=session_id,
            question=request.question,
            benchmark_id=request.benchmark_id,
            enable_cognition_search=request.enable_cognition_search,
            enable_search=True
        )
        
        await self.start_research_v2(start_request, background_tasks, current_user)
        
        return {
            "session_id": session_id,
            "message": "研究任务已启动，请通过WebSocket连接获取结果"
        }

    async def get_research_status(self, session_id: str, current_user: UserDB):
        """获取研究状态"""
        session = await self.session_repo.get_session(session_id)
        logger.info(f"开始获取研究状态: {session_id}")
        if not session:
            raise HTTPException(status_code=404, detail="找不到指定的研究会话")
        
        # 验证会话所有权
        if session["user_id"] and session["user_id"] != str(current_user.id):
            raise HTTPException(status_code=403, detail="无权访问此研究会话")
        
        # 获取会话状态
        status_data = {
            "session_id": session_id,
            "status": session["status"],
            "question": session["question"],
            "last_activity": session["last_activity"],
            "in_memory": True  # 现在都是从Redis缓存获取
        }
        
        # 如果有报告，添加报告
        if session.get("report"):
            status_data["report"] = session["report"]
        
        # 如果有错误，添加错误信息
        if session.get("error"):
            status_data["error"] = session["error"]
            
        # 如果有澄清问题，添加澄清问题
        if session.get("clarification_questions"):
            status_data["clarification_questions"] = session["clarification_questions"]
        
        # 添加认知搜索状态
        status_data["enable_cognition_search"] = session.get("enable_cognition_search", False)
        status_data["enable_search"] = session.get("enable_search", True)
        
        return status_data

    async def submit_feedback(self, session_id: str, request: FeedbackRequest, current_user: UserDB):
        """提交用户反馈"""
        session = await self.session_repo.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="找不到指定的研究会话")
        
        logger.info(f"收到用户反馈: {session_id}, {request.feedback}")
        
        research_agent = await self.session_repo.get_agent(session_id)
        research_agent.add_user_feedback(request.feedback)
        
        # 创建feedback消息
        feedback_message = {
            "type": "update",
            "content": {
                "agent": "Feedback",
                "content": request.feedback,
                "round": research_agent.memory.round
            }
        }
        
        # 添加到内存中的messages
        session["messages"].append(feedback_message)
        
        # 保存会话到Redis
        await self._save_session(session_id, session)
        
        # 将反馈添加到持久化存储
        await self.session_repo.add_user_feedback(session_id, request.feedback)
        
        # 将feedback消息也保存到数据库的messages字段中
        await self.session_repo.add_message(session_id, "update", {
            "agent": "Feedback", 
            "content": request.feedback,
            "round": research_agent.memory.round
        })
        
        return {"status": "success", "message": "反馈已提交"}

    async def submit_clarification_answer(
        self, 
        session_id: str, 
        request: ClarificationAnswerRequest,
        current_user: UserDB
    ):
        """提交对澄清问题的回答"""
        session = await self.session_repo.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="找不到指定的研究会话")
        
        research_agent = await self.session_repo.get_agent(session_id)
        
        logger.info(f"收到澄清问题回答: {session_id}, {len(request.answers)}个回答")
        
        try:
            # 获取澄清问题
            questions = await self.session_repo.get_clarification_questions(session_id)
            if not questions:
                questions = session.get("clarification_questions", [])
                if not questions:
                    questions = research_agent.get_pending_questions()
            
            # 处理回答
            answers_list = []
            feedback_records = []
            
            for answer_item in request.answers:
                if isinstance(answer_item, dict) and "id" in answer_item and "answer" in answer_item:
                    q_id = answer_item["id"]
                    answer_text = answer_item["answer"]
                    
                    if 0 <= q_id < len(questions):
                        question_content = questions[q_id].get('question_content', questions[q_id].get('question', '未知问题'))
                        feedback = f"问题{q_id+1}: {question_content}\n回答: {answer_text}"
                        feedback_records.append(feedback)
                        answers_list.append({"id": q_id, "answer": answer_text})
            
            # 清除澄清问题
            session["clarification_questions"] = []
            await self._save_session(session_id, session)
            
            # 调用研究智能体的回答问题方法
            result = await research_agent.answer_pending_questions(answers_list)
            logger.info(f"处理澄清问题回答成功: {session_id}, 剩余问题: {result.get('remaining_questions', 0)}")
            
            # 将澄清问题回答保存到数据库的messages字段中
            if feedback_records:
                clarification_answer_message = {
                    "type": "update",
                    "content": {
                        "agent": "ClarificationAnswer",
                        "content": "\n\n".join(feedback_records),
                        "round": research_agent.memory.round
                    }
                }
                
                session["messages"].append(clarification_answer_message)
                await self._save_session(session_id, session)
                
                await self.session_repo.add_message(session_id, "update", {
                    "agent": "ClarificationAnswer", 
                    "content": "\n\n".join(feedback_records),
                    "round": research_agent.memory.round
                })
                logger.info(f"澄清问题回答已保存到数据库: {session_id}")
            
            return {"status": "success", "message": "澄清问题回答已提交", "remaining_questions": result.get("remaining_questions", 0)}
        
        except Exception as e:
            logger.error(f"处理澄清问题回答失败: {str(e)}")
            session["status"] = "error"
            await self._save_session(session_id, session)
            await self.session_repo.update_status(session_id, "error", str(e))
            
            return {"status": "error", "message": f"处理澄清问题回答失败: {str(e)}"}

    async def pause_research(self, session_id: str, current_user: UserDB):
        """暂停研究任务"""
        session = await self.session_repo.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="找不到指定的研究会话")
        
        # 检查会话状态
        if session["status"] != "running":
            raise HTTPException(status_code=400, detail=f"研究任务当前状态为 {session['status']}，无法暂停")
        
        # 暂停研究智能体
        research_agent = await self.session_repo.get_agent(session_id)
        await research_agent.pause()
        
        # 保存研究轨迹
        self.service_factory.save_research_trajectory(research_agent, session_id, "暂停")
        
        # 更新会话状态
        session["status"] = "paused"
        session["last_activity"] = get_cn_time().strftime("%Y-%m-%d %H:%M:%S")
        
        await self._save_session(session_id, session)
        await self.session_repo.update_status(session_id, "paused")
        
        return {"status": "success", "message": "研究任务已暂停"}

    async def resume_research(self, session_id: str, current_user: UserDB):
        """恢复已暂停的研究任务"""
        session = await self.session_repo.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="找不到指定的研究会话")
        
        logger.info(f"恢复研究任务: {session_id}, 当前状态: {session['status']}")
        if session["status"] != "paused":
            raise HTTPException(status_code=400, detail="研究任务未处于暂停状态")
        
        research_agent = await self.session_repo.get_agent(session_id)
        
        try:
            # 检查是否有未回答的澄清问题
            pending_questions = research_agent.get_pending_questions()
            if pending_questions:
                raise HTTPException(
                    status_code=400, 
                    detail=f"还有{len(pending_questions)}个未回答的澄清问题，请先回答这些问题再恢复研究"
                )
            
            # 恢复前保存当前研究轨迹
            self.service_factory.save_research_trajectory(research_agent, session_id, "恢复前")
            
            # 更新会话状态
            session["status"] = "running"
            session["last_activity"] = get_cn_time()
            
            await self._save_session(session_id, session)
            await self.session_repo.update_status(session_id, "running")
            logger.info(f"会话状态已更新为running: {session_id}")
            
            # 向WebSocket发送状态更新消息
            status_message = {
                "type": "status",
                "status": "running",
                "message": "研究任务已恢复"
            }
            await self.websocket_service.enqueue_message(session_id, status_message)
            logger.info(f"向WebSocket发送状态更新消息: {session_id}")
            
            # 恢复研究智能体，传入resume标记
            input_data = {
                "resume": True, 
                "conversation_id": session_id,
                "user_id": str(current_user.id)
            }
            logger.info(f"创建新的研究代理运行任务: {session_id}")
            asyncio.create_task(
                self.research_runner.run_agent_and_send_to_websocket(
                    research_agent, input_data, session_id
                )
            )
            
            return {"status": "success", "message": "研究任务已恢复"}
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"恢复研究任务出错: {e}")
            raise HTTPException(status_code=500, detail=f"恢复研究任务出错: {str(e)}")

    async def get_available_models(self, current_user: UserDB):
        """获取可用的模型列表"""
        import yaml
        
        try:
            # 加载服务配置
            service_config_path = "dc_agents/config/service_config.yaml"
            with open(service_config_path, "r", encoding="utf-8") as f:
                service_config = yaml.safe_load(f)
            
            # 加载当前研究智能体配置
            agent_config_path = "dc_agents/config/config.yaml"
            with open(agent_config_path, "r", encoding="utf-8") as f:
                agent_config = yaml.safe_load(f)
            
            # 获取用户当前模型偏好
            user_preferences = await self.session_repo.get_user_models_preference(str(current_user.id))
            
            # 准备返回的数据
            available_models = []
            
            # 从llms配置中获取可用模型
            if "llms" in service_config:
                for model_name, model_config in service_config["llms"].items():
                    available_models.append({
                        "id": model_name,
                        "name": model_name,
                        "api_url": model_config.get("api_url", ""),
                        "is_reasoning_model": model_config.get("is_reasoning_model", False)
                    })
            
            # 获取当前配置中使用的模型
            current_models = {
                "action_selection_model": agent_config.get("research_agent", {}).get("action_selection_model", {}).get("name", ""),
                "report_editing_model": agent_config.get("research_agent", {}).get("report_editing_model", {}).get("name", ""),
                "url_selection_model": agent_config.get("research_agent", {}).get("url_selection_model", {}).get("name", "")
            }
            
            # 加入用户自定义的配置（如果有）
            if user_preferences:
                if "action_selection_model" in user_preferences and "name" in user_preferences["action_selection_model"]:
                    current_models["action_selection_model"] = user_preferences["action_selection_model"]["name"]
                if "report_editing_model" in user_preferences and "name" in user_preferences["report_editing_model"]:
                    current_models["report_editing_model"] = user_preferences["report_editing_model"]["name"]
                if "url_selection_model" in user_preferences and "name" in user_preferences["url_selection_model"]:
                    current_models["url_selection_model"] = user_preferences["url_selection_model"]["name"]
            
            return {
                "available_models": available_models,
                "current_models": current_models,
                "default_models": {
                    "action_selection_model": agent_config.get("research_agent", {}).get("action_selection_model", {}).get("name", ""),
                    "report_editing_model": agent_config.get("research_agent", {}).get("report_editing_model", {}).get("name", ""),
                    "url_selection_model": agent_config.get("research_agent", {}).get("url_selection_model", {}).get("name", "")
                }
            }
        except Exception as e:
            logger.error(f"获取可用模型列表失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"获取可用模型列表失败: {str(e)}")

    async def set_models_preferences(self, preferences, current_user: UserDB):
        """设置用户模型偏好"""
        try:
            # 验证用户输入的模型偏好
            models_config = {}
            
            # 验证并设置行动选择模型
            if preferences.action_selection_model and "name" in preferences.action_selection_model:
                model_name = preferences.action_selection_model["name"]
                action_model_config = {
                    "name": model_name,
                    "temperature": preferences.action_selection_model.get("temperature", 0.6),
                    "max_tokens": preferences.action_selection_model.get("max_tokens", 8000)
                }
                models_config["action_selection_model"] = action_model_config
            
            # 验证并设置报告编辑模型
            if preferences.report_editing_model and "name" in preferences.report_editing_model:
                model_name = preferences.report_editing_model["name"]
                report_model_config = {
                    "name": model_name,
                    "temperature": preferences.report_editing_model.get("temperature", 0.6),
                    "max_tokens": preferences.report_editing_model.get("max_tokens", 20000)
                }
                models_config["report_editing_model"] = report_model_config
            
            # 验证并设置URL选择模型
            if preferences.url_selection_model and "name" in preferences.url_selection_model:
                model_name = preferences.url_selection_model["name"]
                url_model_config = {
                    "name": model_name,
                    "temperature": preferences.url_selection_model.get("temperature", 0.6),
                    "max_tokens": preferences.url_selection_model.get("max_tokens", 500)
                }
                models_config["url_selection_model"] = url_model_config
            
            # 保存用户模型偏好
            success = await self.session_repo.set_user_models_preference(str(current_user.id), models_config)
            
            if not success:
                raise HTTPException(status_code=500, detail="保存用户模型偏好失败")
            
            return {"status": "success", "message": "用户模型偏好已更新", "models_config": models_config}
        
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"设置用户模型偏好失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"设置用户模型偏好失败: {str(e)}")

    # WebSocket处理方法
    async def handle_websocket_connection(self, websocket, session_id: str):
        """处理WebSocket连接"""
        return await self.websocket_service.handle_websocket_connection(
            websocket, session_id, self
        )

    async def delete_session(self, session_id: str, current_user: UserDB):
        """删除研究会话"""
        session = await self.session_repo.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="找不到指定的研究会话")
        
        # 验证会话所有权
        if session["user_id"] and session["user_id"] != str(current_user.id):
            raise HTTPException(status_code=403, detail="无权删除此研究会话")
        
        try:
            # 如果会话正在运行，先暂停
            if session["status"] == "running":
                research_agent = await self.session_repo.get_agent(session_id)
                await research_agent.pause()
                logger.info(f"删除前暂停会话: {session_id}")
            
            # 从 Redis 缓存中删除
            if self.session_cache:
                await self.session_cache.delete_session(session_id)
            
            # 从持久化存储中删除
            await self.session_repo.delete_session(session_id)
            
            # 从 service_factory 中清理 agent 实例
            self.service_factory.remove_research_agent(session_id)
            
            logger.info(f"会话已删除: {session_id}")
            return {"status": "success", "message": "研究会话已删除"}
            
        except Exception as e:
            logger.error(f"删除会话失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"删除会话失败: {str(e)}")

    async def cleanup_expired_sessions(self):
        """清理过期的会话（可以作为定时任务运行）"""
        try:
            # 清理 service_factory 中的不活跃 agent 实例
            self.service_factory.cleanup_inactive_agents()
            
            # 可以添加更多清理逻辑，比如清理很久没有活动的会话
            logger.info("会话清理完成")
            
        except Exception as e:
            logger.error(f"会话清理失败: {str(e)}")

    async def get_pending_questions(self, session_id: str, current_user: UserDB) -> PendingQuestionsResponse:
        """获取会话中待处理的澄清问题"""
        session = await self.session_repo.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="找不到指定的研究会话")
        
        # 验证会话所有权
        if session["user_id"] and session["user_id"] != str(current_user.id):
            raise HTTPException(status_code=403, detail="无权访问此研究会话")
        
        research_agent = await self.session_repo.get_agent(session_id)
        
        try:
            # 获取待处理的澄清问题
            pending_questions = research_agent.get_pending_questions()
            
            return PendingQuestionsResponse(
                status="success",
                pending_questions=pending_questions,
                count=len(pending_questions)
            )
        except Exception as e:
            logger.error(f"获取待处理澄清问题出错: {e}")
            raise HTTPException(status_code=500, detail=f"获取待处理澄清问题出错: {str(e)}")

    async def submit_edit_request(
        self, 
        session_id: str, 
        request: EditRequest,
        background_tasks: BackgroundTasks,
        current_user: UserDB
    ):
        """提交编辑请求（专门的 chat to edit 功能）"""
        session = await self.session_repo.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="找不到指定的研究会话")
        
        # 验证会话所有权
        if session["user_id"] and session["user_id"] != str(current_user.id):
            raise HTTPException(status_code=403, detail="无权访问此研究会话")
        
        research_agent = await self.session_repo.get_agent(session_id)
        
        logger.info(f"收到编辑请求: {session_id}, 内容: {request.edit_content[:100]}...")
        
        # 再次检查用户模型偏好
        user_models_config = await self.session_repo.get_user_models_preference(str(current_user.id))
        if user_models_config:
            research_agent.set_models(user_models_config)
            logger.info(f"在编辑请求前应用用户模型偏好: {user_models_config}")
            
            # 更新会话中的模型配置记录
            session["models_config"] = user_models_config
            await self.session_repo.set_session_models_config(session_id, user_models_config)

        # 重要：将编辑请求消息也保存到数据库的messages字段中
        edit_message = {
            "type": "update",
            "content": {
                "agent": "Feedback",
                "content": request.edit_content,
                "round": research_agent.memory.round,
            }
        }
        
        # 添加到内存中的messages
        session["messages"].append(edit_message)
        await self._save_session(session_id, session)
        
        # 保存到数据库
        await self.session_repo.add_message(session_id, "update", {
            "agent": "Feedback",
            "content": request.edit_content,
            "round": research_agent.memory.round,
        })
        
        # 设置编辑指令和模式
        research_agent.set_edit_instruction(request.edit_content)
        
        # 准备输入数据，标记这是一个编辑请求
        input_data = {
            "edit_request": True,
            "edit_content": request.edit_content,
            "conversation_id": session_id,
            "user_id": str(current_user.id),
            "resume": True  # 让research agent知道要恢复执行
        }
        
        # 向WebSocket发送状态更新消息
        status_message = {
            "type": "status",
            "status": "running",
            "message": "研究任务已恢复"
        }
        await self.websocket_service.enqueue_message(session_id, status_message)
        logger.info(f"向WebSocket发送状态更新消息: {session_id}")
        
        # 使用后台任务运行编辑处理
        background_tasks.add_task(
            self.research_runner.run_agent_and_send_to_websocket,
            research_agent, input_data, session_id
        )
        
        return {
            "message": "编辑请求已提交，正在处理中",
            "session_id": session_id,
            "edit_content": request.edit_content
        }

    async def submit_content_preference(
        self, 
        request: ContentPreferenceRequest, 
        current_user: UserDB
    ):
        """提交用户对内容的偏好（点赞/取消点赞）"""
        try:
            logger.info(f"用户 {current_user.id} 提交内容偏好: {request}")
            
            # 检查会话是否存在
            conversation_id = request.conversation_id
            session = await self.session_repo.get_session(conversation_id)
            if not session:
                raise HTTPException(status_code=404, detail="找不到指定的研究会话")
            
            # 验证会话所有权
            if session["user_id"] != str(current_user.id):
                raise HTTPException(status_code=403, detail="无权访问此研究会话")
            
            # 获取研究智能体
            research_agent = await self.session_repo.get_agent(conversation_id)
            
            # 构建元数据
            metadata = {
                "title": request.title,
                "timestamp": request.timestamp or get_cn_time().isoformat(),
                "user_id": str(current_user.id)
            }
            
            # 根据liked状态调用不同的方法
            if request.liked:
                # 用户点赞：调用add_user_content_preference
                result = research_agent.add_user_content_preference(
                    url=request.url,
                    metadata=metadata
                )
            else:
                # 用户取消点赞：调用remove_user_content_preference
                result = research_agent.remove_user_content_preference(
                    url=request.url
                )
            
            if result:
                return {
                    "status": "success",
                    "message": f"内容偏好已{'添加' if request.liked else '移除'}",
                    "data": {
                        "url": request.url,
                        "title": request.title,
                        "liked": request.liked,
                        "timestamp": metadata["timestamp"]
                    }
                }
            else:
                raise HTTPException(status_code=500, detail="保存内容偏好失败")
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"提交内容偏好失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"提交内容偏好失败: {str(e)}")

    async def update_search_status(
        self, 
        request: UpdateSearchStatusRequest, 
        current_user: UserDB
    ):
        """更新搜索状态"""
        session = await self.session_repo.get_session(request.session_id)
        if not session:
            raise HTTPException(status_code=404, detail="找不到指定的研究会话")
        
        # 验证会话所有权
        if session["user_id"] and session["user_id"] != str(current_user.id):
            raise HTTPException(status_code=403, detail="无权访问此研究会话")
        
        research_agent = await self.session_repo.get_agent(request.session_id)
        
        # 更新搜索状态
        research_agent.set_enable_search(request.enable_search)
        
        logger.info(f"更新搜索状态: {request.enable_search}")
        
        # 更新会话状态（自动处理缓存和持久化）
        await self.session_repo.update_session(
            session_id=request.session_id,
            updates={"enable_search": request.enable_search}
        )
        
        return {"status": "success", "message": "搜索状态已更新"}

    async def get_context_content(
        self, 
        context_id: str, 
        conversation_id: str, 
        current_user: UserDB
    ):
        """获取特定上下文的内容"""
        try:
            logger.info(f"获取上下文内容: {context_id}, {conversation_id}")
            
            session = await self.session_repo.get_session(conversation_id)
            if not session:
                raise HTTPException(status_code=404, detail="找不到指定的研究会话")
            
            # 验证会话所有权
            if session["user_id"] and session["user_id"] != str(current_user.id):
                raise HTTPException(status_code=403, detail="无权访问此研究会话")
            
            research_agent = await self.session_repo.get_agent(conversation_id)
            memory = research_agent.memory
            
            if not memory:
                raise HTTPException(status_code=404, detail="找不到会话内存")
                
            # 获取所有上下文
            contexts = memory.get_all_contexts()
            
            # 查找指定的上下文
            target_context = None
            for ctx in contexts:
                if ctx["context_id"] == context_id:
                    target_context = ctx
                    break
                    
            if not target_context:
                raise HTTPException(status_code=404, detail="找不到指定的上下文")
                
            # 获取上下文类型
            context_type = target_context.get("metadata", {}).get("type")
            
            if context_type == "file":
                # 如果是文件类型，读取文件内容
                file_path = target_context.get("metadata", {}).get("file_path")
                if not file_path or not os.path.exists(file_path):
                    raise HTTPException(status_code=404, detail="找不到指定的文件")
                    
                # 读取文件内容
                with open(file_path, "rb") as f:
                    file_content = f.read()
                    
                # 获取文件类型
                file_type = os.path.splitext(file_path)[1].lower()
                
                # 根据文件类型设置正确的 Content-Type
                content_type = {
                    ".pdf": "application/pdf",
                    ".doc": "application/msword",
                    ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    ".txt": "text/plain",
                    ".md": "text/markdown",
                    ".jpg": "image/jpeg",
                    ".jpeg": "image/jpeg",
                    ".png": "image/png"
                }.get(file_type, "application/octet-stream")
                
                # 返回文件内容（这里需要在router层处理Response对象）
                return {
                    "type": "file",
                    "file_content": file_content,
                    "content_type": content_type,
                    "filename": os.path.basename(file_path)
                }
            else:
                # 如果不是文件类型，返回上下文内容
                return ContextContentResponse(
                    context_id=context_id,
                    content=target_context.get("content", ""),
                    metadata=target_context.get("metadata", {})
                )
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取上下文内容失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"获取上下文内容失败: {str(e)}")

    async def delete_context(
        self, 
        request: DeleteContextRequest, 
        current_user: UserDB
    ):
        """删除指定的上下文"""
        try:
            session = await self.session_repo.get_session(request.conversation_id)
            if not session:
                raise HTTPException(status_code=404, detail="找不到指定的研究会话")
            
            # 验证会话所有权
            if session["user_id"] and session["user_id"] != str(current_user.id):
                raise HTTPException(status_code=403, detail="无权访问此研究会话")
            
            # 删除上下文
            research_agent = await self.session_repo.get_agent(request.conversation_id)
            success = research_agent.remove_context(request.context_id)
            
            if not success:
                raise HTTPException(status_code=404, detail="找不到指定的上下文")
            
            # 保存到MongoDB
            await research_agent.memory.save_to_mongodb()
            
            return {
                "status": "success",
                "message": "上下文已成功删除"
            }
            
        except HTTPException:
            raise
        except Exception as e:
            import traceback
            error_stack = traceback.format_exc()
            logger.error(f"删除上下文失败: {str(e)}\n{error_stack}")
            raise HTTPException(status_code=500, detail=f"删除上下文失败: {str(e)}")

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return {
            **self._performance_stats,
            "active_agents": self.service_factory.get_active_agent_count(),
            "cache_hit_rate": (
                self._performance_stats["session_cache_hits"] / 
                max(self._performance_stats["session_cache_hits"] + self._performance_stats["session_cache_misses"], 1)
            ) * 100
        }
    
    async def cleanup_inactive_sessions(self, max_inactive_hours: int = 24):
        """清理不活跃的会话"""
        try:
            # 清理 service_factory 中的不活跃 agent 实例
            self.service_factory.cleanup_inactive_agents()
            
            # 清理Redis中的过期会话缓存
            if self.session_cache:
                cleaned_count = await self.session_cache.cleanup_expired_sessions()
                logger.info(f"清理了{cleaned_count}个过期会话缓存")
            
            logger.info("不活跃会话清理完成")
            
        except Exception as e:
            logger.error(f"清理不活跃会话失败: {str(e)}")
