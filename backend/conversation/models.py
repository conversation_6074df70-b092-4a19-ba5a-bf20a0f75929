from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from backend.db.mongodb import PyObjectId
from backend.utils import get_cn_time
from bson import ObjectId

# 注意：ResearchSessionMessage和ResearchSessionData模型已移动到session_store.py
# 现在使用SessionMessage和SessionData

# 研究会话请求模型
class ResearchRequest(BaseModel):
    question: str
    benchmark_id: Optional[int] = None
    enable_cognition_search: Optional[bool] = False

class FeedbackRequest(BaseModel):
    feedback: str

class ClarificationAnswerRequest(BaseModel):
    answers: Union[List[str], List[Dict[str, Any]]]

# 研究会话响应模型
class ResearchResponse(BaseModel):
    session_id: str
    message: str

class CreateSessionResponse(BaseModel):
    session_id: str
    message: str

class StartResearchResponse(BaseModel):
    session_id: str
    status: str
    message: str

# 用户模型配置模型
class UserModelPreferences(BaseModel):
    action_selection_model: Optional[Dict[str, Any]] = None
    report_editing_model: Optional[Dict[str, Any]] = None
    url_selection_model: Optional[Dict[str, Any]] = None

# v2版本请求响应模型
class CreateSessionRequest(BaseModel):
    """创建会话请求模型"""
    user_id: str
    metadata: Optional[Dict[str, Any]] = None

class StartResearchRequest(BaseModel):
    """启动研究请求模型"""
    session_id: str
    question: str
    benchmark_id: Optional[int] = None
    enable_cognition_search: Optional[bool] = False
    enable_search: bool

class UpdateResearchStatusRequest(BaseModel):
    """更新研究状态请求模型"""
    session_id: str
    enable_cognition_search: bool

class EditRecordRequest(BaseModel):
    """用户编辑记录请求模型"""
    type: str
    timestamp: str
    original_content: str
    edited_content: str

class ReportFeedbackRequest(BaseModel):
    """报告反馈请求模型"""
    sessionId: str
    rating: str
    comment: str
    timestamp: str

class DeleteContextRequest(BaseModel):
    """删除上下文请求模型"""
    conversation_id: str
    context_id: str

class MarkdownToPdfRequest(BaseModel):
    """Markdown转PDF请求模型"""
    markdown_content: str
    filename: str = "document.pdf"

class EditRequest(BaseModel):
    """编辑请求模型（chat to edit功能）"""
    edit_content: str
    edit_type: Optional[str] = "general"

class ContentPreferenceRequest(BaseModel):
    """内容偏好请求模型（点赞/取消点赞）"""
    conversation_id: str
    url: str
    title: str
    liked: bool
    timestamp: Optional[str] = None

class UpdateSearchStatusRequest(BaseModel):
    """更新搜索状态请求模型"""
    session_id: str
    enable_search: bool

class PendingQuestionsResponse(BaseModel):
    """待处理澄清问题响应模型"""
    status: str
    pending_questions: List[Dict[str, Any]]
    count: int

class ContextContentResponse(BaseModel):
    """上下文内容响应模型"""
    context_id: str
    content: str
    metadata: Dict[str, Any]

    