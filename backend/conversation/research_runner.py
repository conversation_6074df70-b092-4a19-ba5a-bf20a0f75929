import asyncio
import logging
from typing import Dict, Any
from datetime import datetime

from backend.utils import get_cn_time
from backend.conversation.service_factory import get_service_factory
from backend.conversation.websocket_service import WebSocketService
from backend.conversation.dependencies import get_session_repository

logger = logging.getLogger(__name__)

class ResearchRunner:
    """研究代理运行器，负责运行研究任务并通过WebSocket发送消息"""
    
    def __init__(self, websocket_service: WebSocketService):
        self.websocket_service = websocket_service
        self.session_repo = get_session_repository()
        self.service_factory = get_service_factory()
    
    async def run_agent_and_send_to_websocket(
        self, 
        research_agent, 
        input_data: Dict[str, Any], 
        session_id: str
    ):
        """运行研究代理并发送结果到WebSocket
        
        Args:
            research_agent: 研究代理实例
            input_data: 输入数据
            session_id: 会话ID
        """
        try:
            logger.info(f"开始运行研究代理: {session_id}, 输入数据: {input_data}")
            
            # 获取会话数据
            session_data = await self.session_repo.get_session(session_id)
            if not session_data:
                logger.error(f"会话不存在: {session_id}")
                return
            
            # 设置代理实例到会话中
            await self.session_repo.set_agent_active(session_id)
            
            # 初始化token使用统计
            if not session_data.get("token_usage"):
                await self.session_repo.update_token_usage(session_id, {
                    "total_input_tokens": 0,
                    "total_output_tokens": 0,
                    "total_cost": 0,
                    "models_usage": {}
                })
            
            # 发送开始消息
            start_msg = {
                "type": "status",
                "content": {
                    "status": "running",
                    "message": "研究任务已开始"
                }
            }
            await self.websocket_service.enqueue_message(session_id, start_msg)
            await self.session_repo.add_message(session_id, "status", {
                "status": "running",
                "message": "研究任务已开始"
            })
            
            await self.session_repo.update_status(session_id, "running")
            
            # 运行研究流程并处理输出
            async for chunk in research_agent.run(input_data):
                # 处理token使用信息
                if "stage" in chunk and chunk["stage"] == "token_usage" and chunk.get("action") == "token_info":
                    await self._handle_token_usage(chunk, session_id)
                    continue
                
                # 处理澄清问题
                if "stage" in chunk and chunk["stage"] == "clarify" and chunk["action"] == "clarify":
                    await self._handle_clarification(chunk, session_id)
                    # 澄清是关键时刻，强制刷新消息
                    await self.session_repo.force_flush_messages(session_id)
                    continue
                
                # 处理系统信息
                if "stage" in chunk and chunk["stage"] == "system":
                    await self._handle_system_message(chunk, session_id)
                    continue
                
                # 处理暂停状态
                if "stage" in chunk and chunk["stage"] == "paused" and chunk["action"] == "paused":
                    await self._handle_pause_state(chunk, session_id)
                    # 暂停是关键时刻，强制刷新消息
                    await self.session_repo.force_flush_messages(session_id)
                    continue
                
                # 处理常规更新消息
                await self._handle_update_message(chunk, session_id)
            
            # 研究完成处理
            await self._handle_research_completion(research_agent, session_id)
            
        except Exception as e:
            await self._handle_research_error(e, research_agent, session_id)
    
    async def _handle_token_usage(self, chunk: Dict[str, Any], session_id: str):
        """处理token使用信息"""
        token_usage = chunk.get("token_usage", {})
        logger.info(f"收到token使用信息: {token_usage}")
        
        if token_usage:
            # 更新会话中的token使用统计
            await self.session_repo.update_token_usage(session_id, token_usage)
            logger.info(f"更新token使用统计: {session_id}, 总成本: {token_usage.get('total_cost', 0):.4f}")
            
            # 实时同步token使用量到用户表
            try:
                from backend.auth.dependencies import record_token_usage_for_session
                
                session_data = await self.session_repo.get_session(session_id)
                user_id = session_data.get("user_id") if session_data else None
                
                if user_id:
                    current_cost = token_usage.get("total_cost", 0.0)
                    previous_cost = session_data.get("previous_token_cost", 0.0)
                    cost_increment = current_cost - previous_cost
                    
                    if cost_increment > 0:
                        success = await record_token_usage_for_session(
                            user_id=str(user_id),
                            session_id=session_id,
                            token_cost=cost_increment
                        )
                        if success:
                            logger.info(f"用户 {user_id} token使用量已同步: +{cost_increment:.4f}")
                        else:
                            logger.warning(f"用户 {user_id} token使用量同步失败")
                    
                    # 更新previous_token_cost
                    await self.session_repo.update_session(session_id, {"previous_token_cost": current_cost})
                    
            except Exception as e:
                logger.error(f"同步token使用量到用户表失败: {str(e)}")
            
            # 发送token使用信息到客户端
            token_message = {
                "type": "token_usage",
                "token_usage": token_usage
            }
            await self.websocket_service.enqueue_message(session_id, token_message)
    
    async def _handle_clarification(self, chunk: Dict[str, Any], session_id: str):
        """处理澄清问题"""
        # 更新会话状态
        await self.session_repo.update_status(session_id, "clarification")
        logger.info(f"研究代理需要澄清: {session_id}")
        
        # 存储澄清问题
        clarification_content = chunk.get("content", [])
        if clarification_content:
            await self.session_repo.set_clarification_questions(session_id, clarification_content)
            logger.info(f"存储澄清问题: {session_id}, {len(clarification_content)}个问题")
            
            # 通过WebSocket发送澄清问题
            message = {
                "type": "clarification",
                "questions": clarification_content,
                "message": "研究智能体需要您回答一些澄清问题"
            }
            logger.info(f"发送澄清消息: {session_id}")
            await self.websocket_service.enqueue_message(session_id, message)
            
            # 添加澄清消息到会话记录
            await self.session_repo.add_message(session_id, "clarification", {
                "questions": clarification_content,
                "message": "研究智能体需要您回答一些澄清问题"
            })
            
            # 模型已自动暂停，状态已在代理内部更新
            await self.session_repo.update_status(session_id, "paused")
            logger.info(f"研究状态更新为暂停: {session_id}")
    
    async def _handle_system_message(self, chunk: Dict[str, Any], session_id: str):
        """处理系统消息"""
        system_message = {
            "type": "system",
            "content": chunk
        }
        
        await self.websocket_service.enqueue_message(session_id, system_message)
        
        # 添加到会话消息中
        await self.session_repo.add_message(session_id, "system", chunk)
    
    async def _handle_pause_state(self, chunk: Dict[str, Any], session_id: str):
        """处理暂停状态"""
        # 更新会话状态为暂停
        await self.session_repo.update_status(session_id, "paused")
        logger.info(f"研究代理暂停: {session_id}")
        
        # 通过WebSocket发送暂停状态
        message = {
            "type": "status",
            "status": "paused",
            "message": chunk.get("content", "研究已暂停")
        }
        logger.info(f"发送暂停状态消息: {session_id}")
        await self.websocket_service.enqueue_message(session_id, message)
        
        # 添加到会话消息中
        await self.session_repo.add_message(session_id, "status", {
            "status": "paused",
            "message": chunk.get("content", "研究已暂停")
        })
    
    async def _handle_update_message(self, chunk: Dict[str, Any], session_id: str):
        """处理常规更新消息"""
        # 向客户端发送进度更新
        message = {
            "type": "update",
            "content": chunk
        }
        
        await self.websocket_service.enqueue_message(session_id, message)
        
        # 添加到会话消息中（使用批量缓存机制）
        await self.session_repo.add_message(session_id, "update", chunk)
    
    async def _handle_research_completion(self, research_agent, session_id: str):
        """处理研究完成"""
        # 获取最终报告
        final_report = research_agent.memory.get_report_draft()
        logger.info(f"研究完成: {session_id}, 报告长度: {len(final_report)}")
        
        # 保存研究轨迹为JSON文件
        self.service_factory.save_research_trajectory(research_agent, session_id, "完成")
        
        # 更新会话状态 - 完成的任务设为paused，可以继续操作
        await self.session_repo.set_report(session_id, final_report)
        
        # 发送完成消息
        complete_message = {
            "type": "paused",
            "report": final_report
        }
        logger.info(f"发送完成消息: {session_id}")
        await self.websocket_service.enqueue_message(session_id, complete_message)
        
        # 保存完成消息
        await self.session_repo.add_message(session_id, "paused", {"report": final_report})
        
        # 研究完成是关键时刻，强制刷新所有消息到数据库
        await self.session_repo.force_flush_messages(session_id)
    
    async def _handle_research_error(self, error: Exception, research_agent, session_id: str):
        """处理研究错误"""
        logger.error(f"研究任务出错: {str(error)}", exc_info=True)
        
        # 保存研究轨迹为JSON文件(即使出错也保存)
        self.service_factory.save_research_trajectory(research_agent, session_id, "错误")
        
        # 更新会话状态
        await self.session_repo.update_status(session_id, "error", str(error))
        
        # 发送错误消息
        error_message = {
            "type": "error",
            "message": f"研究过程中发生错误: {str(error)}"
        }
        logger.info(f"发送错误消息: {session_id}")
        await self.websocket_service.enqueue_message(session_id, error_message)
        
        # 保存错误消息
        await self.session_repo.add_message(session_id, "error", {"message": f"研究过程中发生错误: {str(error)}"})
        
        # 错误是关键时刻，强制刷新消息到数据库
        await self.session_repo.force_flush_messages(session_id)

def get_research_runner(websocket_service: WebSocketService) -> ResearchRunner:
    """获取研究代理运行器实例"""
    return ResearchRunner(websocket_service) 