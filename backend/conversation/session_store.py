from typing import Dict, Any, List, Optional, AsyncGenerator
from datetime import datetime, timezone, timedelta
import logging
import asyncio
from bson import ObjectId
from pydantic import BaseModel, Field

from backend.db.mongodb import MongoDBBase, PyObjectId

logger = logging.getLogger(__name__)

# 返回东八区（北京时间）的当前时间
def get_cn_time():
    """返回东八区（北京时间）的当前时间"""
    return datetime.now(timezone(timedelta(hours=8)))


class SessionMessage(BaseModel):
    """WebSocket消息模型"""
    timestamp: datetime = Field(default_factory=get_cn_time)
    type: str  # 消息类型: update, status, clarification, complete, error等
    content: Any  # 消息内容
    
    def dict(self, *args, **kwargs):
        """转换为字典 (兼容旧版Pydantic)"""
        return self.model_dump(*args, **kwargs)
    
    @classmethod
    def validate_for_mongodb(cls, data: Dict[str, Any]) -> Dict[str, Any]:
        """确保数据可以被MongoDB序列化"""
        if isinstance(data, cls):
            data = data.model_dump()
        return data


class SessionData(BaseModel):
    """会话数据MongoDB模型"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    session_id: str  # 会话ID，和conversation_id不同，这是接口API层面的ID
    status: str = "starting"  # 会话状态: starting, running, paused, clarification, completed, cancelled, error
    question: str  # 研究问题
    language: str = "zh"  # 语言
    conversation_id: Optional[str] = None  # 对应研究内存记录的conversation_id
    started_at: datetime = Field(default_factory=get_cn_time)  # 开始时间
    last_activity: datetime = Field(default_factory=get_cn_time)  # 最后活动时间
    messages: List[SessionMessage] = []  # 消息历史记录
    clarification_questions: List[Dict[str, Any]] = []  # 澄清问题
    user_feedbacks: List[str] = []  # 用户反馈
    report: Optional[str] = None  # 最终报告
    benchmark_id: Optional[int] = None  # benchmark ID
    error: Optional[str] = None  # 错误信息
    user_id: Optional[str] = None  # 用户ID
    models_config: Optional[Dict[str, Any]] = None  # 用户模型偏好设置
    token_usage: Optional[Dict[str, Any]] = Field(
        default_factory=lambda: {
            "total_input_tokens": 0,
            "total_output_tokens": 0,
            "total_cost": 0,
            "models_usage": {}
        }
    )  # token使用统计
    metadata: Optional[Dict[str, Any]] = None  # 元数据
    created_at: datetime = Field(default_factory=get_cn_time)
    updated_at: datetime = Field(default_factory=get_cn_time)


class SessionStore:
    """会话状态存储管理器，基于MongoDB实现持久化
    
    作为conversation模块的内部组件，负责会话数据的MongoDB持久化
    """
    
    def __init__(self, collection_name: str = "research_sessions"):
        """初始化会话存储"""
        self.collection_name = collection_name
        try:
            self.db = MongoDBBase(self.collection_name, SessionData)
            self.mongodb_available = True
            
            # 确保关键字段有索引
            asyncio.create_task(self._ensure_indexes())
            
            logger.info(f"SessionStore初始化成功: {collection_name}")
        except Exception as e:
            logger.error(f"SessionStore初始化失败: {str(e)}")
            self.mongodb_available = False
    
    async def _ensure_indexes(self):
        """确保数据库索引存在"""
        if not self.mongodb_available:
            return
        
        try:
            collection = self.db._collection
            
            # 为session_id创建唯一索引（最重要）
            # await collection.create_index("session_id", unique=True)
            
            # # 为user_id创建索引（用于用户会话列表查询）
            # await collection.create_index("user_id")
            
            # # 为last_activity创建索引（用于排序和清理过期会话）
            # await collection.create_index("last_activity")
            
            # # 为status创建索引（用于状态过滤）
            # await collection.create_index("status")
            
            # # 为token_usage查询创建复合索引
            # await collection.create_index([("user_id", 1), ("token_usage", 1)])
            
            logger.info(f"MongoDB索引创建完成: {self.collection_name}")
        except Exception as e:
            logger.warning(f"创建MongoDB索引失败: {str(e)}")
    
    async def create_session(self, session_id: str, question: str, language: str = "zh", 
                            benchmark_id: Optional[int] = None, user_id: Optional[str] = None,
                            metadata: Optional[Dict[str, Any]] = None) -> Optional[SessionData]:
        """创建新会话"""
        if not self.mongodb_available:
            logger.warning("MongoDB不可用，无法创建会话")
            return None
        
        try:
            # 准备会话数据
            session_data = {
                "session_id": session_id,
                "status": "starting",
                "question": question,
                "language": language,
                "started_at": get_cn_time(),
                "last_activity": get_cn_time(),
                "benchmark_id": benchmark_id,
                "user_id": user_id,
            }
            
            # 添加元数据
            if metadata:
                session_data["metadata"] = metadata
            
            # 创建会话记录
            session = await self.db.create(session_data)
            logger.info(f"创建会话成功: {session_id}")
            return session
        except Exception as e:
            logger.error(f"创建会话失败: {str(e)}")
            return None
    
    async def get_session(self, session_id: str) -> Optional[SessionData]:
        """获取会话"""
        if not self.mongodb_available:
            logger.warning("MongoDB不可用，无法获取会话")
            return None
        
        try:
            session = await self.db.find_one({"session_id": session_id})
            return session
        except Exception as e:
            logger.error(f"获取会话失败: {str(e)}")
            return None
    
    async def update_session(
        self, 
        session_id: str, 
        updates: Optional[Dict[str, Any]] = None,
        question: Optional[str] = None,
        status: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """更新会话状态
        
        Args:
            session_id: 会话ID
            updates: 要更新的字段字典（可选）
            question: 要更新的问题（可选）
            status: 要更新的状态（可选）
            metadata: 要更新的元数据（可选）
        """
        if not self.mongodb_available:
            logger.warning("MongoDB不可用，无法更新会话")
            return False
        
        try:
            # 获取会话
            session = await self.get_session(session_id)
            if not session:
                logger.warning(f"更新会话失败: 找不到会话 {session_id}")
                return False
            
            # 准备更新数据
            update_data = updates or {}
            
            # 如果提供了独立参数，添加到更新数据中
            if question is not None:
                update_data["question"] = question
            if status is not None:
                update_data["status"] = status
            
            # 更新last_activity
            update_data["last_activity"] = get_cn_time()
            
            # 更新metadata,增量更新
            if metadata:
                # 获取现有的metadata,如果不存在则创建空字典
                existing_metadata = session.metadata or {}
                # 更新metadata,保留原有的key
                existing_metadata.update(metadata)
                update_data["metadata"] = existing_metadata
            
            # 更新会话
            await self.db.update(str(session.id), update_data)
            logger.info(f"更新会话成功: {session_id}")
            return True
        except Exception as e:
            logger.error(f"更新会话失败: {str(e)}")
            return False
    
    async def update_status(self, session_id: str, status: str, error: Optional[str] = None) -> bool:
        """更新会话状态"""
        updates = {"status": status}
        if error:
            updates["error"] = error
        return await self.update_session(session_id, updates)
    
    async def add_message(self, session_id: str, message_type: str, content: Any) -> bool:
        """添加消息到会话"""
        if not self.mongodb_available:
            logger.warning("MongoDB不可用，无法添加消息")
            return False
        
        try:
            # 获取会话
            session = await self.get_session(session_id)
            if not session:
                logger.warning(f"添加消息失败: 找不到会话 {session_id}")
                return False
            
            # 创建消息并确保可序列化
            # 直接创建字典而不是Pydantic对象
            message_dict = {
                "timestamp": get_cn_time(),
                "type": message_type,
                "content": content
            }
            
            # 添加消息到会话
            messages = list(session.messages) if session.messages else []
            
            # 确保会话中现有的消息也是字典格式
            processed_messages = []
            for msg in messages:
                if isinstance(msg, dict):
                    processed_messages.append(msg)
                elif hasattr(msg, 'dict'):
                    processed_messages.append(msg.dict())
                else:
                    # 尝试转换为字典
                    try:
                        processed_messages.append(dict(msg))
                    except:
                        logger.warning(f"无法序列化消息，跳过: {type(msg)}")
            
            # 添加新消息
            processed_messages.append(message_dict)
            
            # 更新会话
            await self.db.update(str(session.id), {
                "messages": processed_messages,
                "last_activity": get_cn_time()
            })
            
            return True
        except Exception as e:
            logger.error(f"添加消息失败: {str(e)}")
            return False
    
    async def add_messages_batch(self, session_id: str, messages: List[Dict[str, Any]]) -> bool:
        """批量添加多条消息到会话，减少数据库操作次数"""
        if not self.mongodb_available:
            logger.warning("MongoDB不可用，无法批量添加消息")
            return False
        
        if not messages:
            return True  # 没有消息要添加
            
        try:
            # 获取会话
            session = await self.get_session(session_id)
            if not session:
                logger.warning(f"批量添加消息失败: 找不到会话 {session_id}")
                return False
            
            # 处理所有消息为可序列化格式
            message_dicts = []
            for msg in messages:
                if isinstance(msg, SessionMessage):
                    message_dicts.append(msg.dict())
                elif isinstance(msg, dict) and "type" in msg:
                    # 如果已经是字典格式但缺少timestamp
                    if "timestamp" not in msg:
                        msg["timestamp"] = get_cn_time()
                    message_dicts.append(msg)
                else:
                    try:
                        # 尝试其他转换方法
                        if hasattr(msg, 'model_dump'):
                            message_dicts.append(msg.model_dump())
                        elif hasattr(msg, 'to_dict'):
                            message_dicts.append(msg.to_dict())
                        elif hasattr(msg, '__dict__'):
                            message_dicts.append(msg.__dict__)
                        else:
                            logger.warning(f"跳过无效的消息格式: {type(msg)}")
                    except Exception as e:
                        logger.warning(f"序列化消息失败: {e}")
            
            # 获取现有消息并确保它们也是字典格式
            existing_messages = []
            current_messages = list(session.messages) if session.messages else []
            for msg in current_messages:
                if isinstance(msg, dict):
                    existing_messages.append(msg)
                elif hasattr(msg, 'dict'):
                    existing_messages.append(msg.dict())
                else:
                    try:
                        if hasattr(msg, 'model_dump'):
                            existing_messages.append(msg.model_dump())
                        elif hasattr(msg, 'to_dict'):
                            existing_messages.append(msg.to_dict())
                        elif hasattr(msg, '__dict__'):
                            existing_messages.append(msg.__dict__)
                        else:
                            logger.warning(f"跳过无效的现有消息格式: {type(msg)}")
                    except Exception as e:
                        logger.warning(f"序列化现有消息失败: {e}")
            
            # 将所有新消息添加到现有消息列表
            all_messages = existing_messages + message_dicts
            
            # 一次性更新会话
            try:
                await self.db.update(str(session.id), {
                    "messages": all_messages,
                    "last_activity": get_cn_time()
                })
                
                logger.info(f"批量添加消息成功: {session_id}, 添加了{len(message_dicts)}条消息")
                return True
            except Exception as e:
                import traceback
                traceback.print_exc()
                # 如果更新失败，可能是因为消息仍然包含无法序列化的对象
                logger.error(f"更新消息到MongoDB失败: {str(e)}")
                
                # 再次尝试，这次使用更保守的JSON序列化再反序列化方法
                try:
                    # 使用JSON序列化再反序列化来确保所有对象都是原生类型
                    import json
                    json_safe = json.loads(json.dumps(all_messages, default=str))
                    
                    await self.db.update(str(session.id), {
                        "messages": json_safe,
                        "last_activity": get_cn_time()
                    })
                    
                    logger.info(f"使用JSON序列化方式成功批量添加消息: {session_id}")
                    return True
                except Exception as e2:
                    import traceback
                    traceback.print_exc()   
                    logger.error(f"JSON序列化后更新消息到MongoDB仍然失败: {str(e2)}")
                    return False
                
        except Exception as e:
            logger.error(f"批量添加消息失败: {str(e)}")
            return False
    
    async def set_clarification_questions(self, session_id: str, questions: List[Dict[str, Any]]) -> bool:
        """设置澄清问题"""
        return await self.update_session(session_id, {"clarification_questions": questions, "status": "clarification"})
    
    async def add_user_feedback(self, session_id: str, feedback: str) -> bool:
        """添加用户反馈"""
        try:
            # 获取会话
            session = await self.get_session(session_id)
            if not session:
                logger.warning(f"添加用户反馈失败: 找不到会话 {session_id}")
                return False
            
            # 添加反馈
            feedbacks = list(session.user_feedbacks) if session.user_feedbacks else []
            feedbacks.append(feedback)
            
            # 更新会话
            return await self.update_session(session_id, {"user_feedbacks": feedbacks})
        except Exception as e:
            logger.error(f"添加用户反馈失败: {str(e)}")
            return False
    
    async def set_report(self, session_id: str, report: str) -> bool:
        """设置最终报告"""
        return await self.update_session(session_id, {"report": report, "status": "paused"})
    
    async def set_conversation_id(self, session_id: str, conversation_id: str) -> bool:
        """设置对应的研究内存记录conversation_id"""
        return await self.update_session(session_id, {"conversation_id": conversation_id})
    
    async def list_sessions(self, user_id: Optional[str] = None, 
                           status: Optional[str] = None, 
                           skip: int = 0, 
                           limit: int = 20) -> List[SessionData]:
        """获取会话列表
        
        Args:
            user_id: 用户ID过滤
            status: 状态过滤
            skip: 跳过数量
            limit: 返回数量限制，默认20个
            
        Returns:
            会话列表，只包含必要字段
        """
        if not self.mongodb_available:
            logger.warning("MongoDB不可用，无法获取会话列表")
            return []
        
        try:
            # 构建查询条件
            query = {}
            if user_id:
                query["user_id"] = user_id
            if status:
                query["status"] = status
            
            # 定义投影，只获取必要字段
            projection = {
                "session_id": 1,
                "status": 1,
                "question": 1,
                "created_at": 1,
                "last_activity": 1,
                "user_id": 1,
                "started_at": 1,
            }
            
            # 执行查询，按最后活动时间倒序排序
            cursor = self.db._collection.find(query, projection).sort("last_activity", -1).skip(skip).limit(limit)
            documents = await cursor.to_list(length=limit)
            
            # 转换为SessionData对象，但只包含基本字段
            sessions = []
            for doc in documents:
                # 创建简化的SessionData对象
                session_data = {
                    "session_id": doc.get("session_id"),
                    "status": doc.get("status", "unknown"),
                    "question": doc.get("question", ""),
                    "created_at": doc.get("created_at"),
                    "last_activity": doc.get("last_activity"),
                    "started_at": doc.get("started_at"),
                    "user_id": doc.get("user_id"),
                    # 设置默认值以满足SessionData模型要求
                    "language": "zh",
                    "conversation_id": None,
                    "messages": [],
                    "clarification_questions": [],
                    "user_feedbacks": [],
                    "report": None,
                    "benchmark_id": None,
                    "error": None,
                    "models_config": None,
                    "token_usage": {
                        "total_input_tokens": 0,
                        "total_output_tokens": 0,
                        "total_cost": 0,
                        "models_usage": {}
                    },
                    "metadata": None,
                    "updated_at": doc.get("last_activity")
                }
                
                # 直接使用SessionData模型创建对象
                session = SessionData.model_validate(session_data)
                sessions.append(session)
            
            return sessions
        except Exception as e:
            logger.error(f"获取会话列表失败: {str(e)}")
            return []
    
    async def get_user_feedbacks(self, session_id: str) -> List[str]:
        """获取用户反馈"""
        try:
            session = await self.get_session(session_id)
            if not session:
                return []
            return list(session.user_feedbacks) if session.user_feedbacks else []
        except Exception as e:
            logger.error(f"获取用户反馈失败: {str(e)}")
            return []
    
    async def get_clarification_questions(self, session_id: str) -> List[Dict[str, Any]]:
        """获取澄清问题"""
        try:
            session = await self.get_session(session_id)
            if not session:
                return []
            return list(session.clarification_questions) if session.clarification_questions else []
        except Exception as e:
            logger.error(f"获取澄清问题失败: {str(e)}")
            return []
    
    async def clear_user_feedbacks(self, session_id: str) -> bool:
        """清空用户反馈"""
        return await self.update_session(session_id, {"user_feedbacks": []})
    
    async def delete_session(self, session_id: str) -> bool:
        """删除会话"""
        if not self.mongodb_available:
            logger.warning("MongoDB不可用，无法删除会话")
            return False
        
        try:
            # 获取会话
            session = await self.get_session(session_id)
            if not session:
                logger.warning(f"删除会话失败: 找不到会话 {session_id}")
                return False
            
            # 删除会话
            await self.db.delete(str(session.id))
            logger.info(f"删除会话成功: {session_id}")
            return True
        except Exception as e:
            logger.error(f"删除会话失败: {str(e)}")
            return False
    
    async def set_user_models_preference(self, user_id: str, models_config: Dict[str, Any]) -> bool:
        """设置用户模型偏好"""
        if not self.mongodb_available:
            logger.warning("MongoDB不可用，无法设置用户模型偏好")
            return False
        
        try:
            # 直接更新用户的所有会话
            user_model_collection = MongoDBBase("user_model_preferences")
            await user_model_collection.update_or_create(
                {"user_id": user_id},
                {"user_id": user_id, "models_config": models_config, "updated_at": get_cn_time()}
            )
            logger.info(f"设置用户模型偏好成功: {user_id}")
            return True
        except Exception as e:
            logger.error(f"设置用户模型偏好失败: {str(e)}")
            return False

    async def get_user_models_preference(self, user_id: str) -> Dict[str, Any]:
        """获取用户模型偏好"""
        if not self.mongodb_available:
            logger.warning("MongoDB不可用，无法获取用户模型偏好")
            return {}
        
        try:
            # 查询用户模型偏好
            user_model_collection = MongoDBBase("user_model_preferences")
            prefs = await user_model_collection.find_one({"user_id": user_id})
            
            if prefs and "models_config" in prefs:
                return prefs["models_config"]
            
            # 如果没有找到，返回空字典
            return {}
        except Exception as e:
            logger.error(f"获取用户模型偏好失败: {str(e)}")
            return {}

    async def set_session_models_config(self, session_id: str, models_config: Dict[str, Any]) -> bool:
        """设置会话的模型配置"""
        return await self.update_session(session_id, {"models_config": models_config})

    async def get_session_models_config(self, session_id: str) -> Dict[str, Any]:
        """获取会话的模型配置"""
        session = await self.get_session(session_id)
        if session and hasattr(session, "models_config") and session.models_config:
            return session.models_config
        return {}
        
    async def update_token_usage(self, session_id: str, token_usage: Dict[str, Any]) -> bool:
        """更新会话的token使用统计
        
        Args:
            session_id: 会话ID
            token_usage: token使用统计信息，包含输入输出token数量和成本
            
        Returns:
            是否成功更新
        """
        return await self.update_session(session_id, {"token_usage": token_usage})
    
    async def get_all_users_token_usage(self) -> List[Dict[str, Any]]:
        """获取所有用户的token使用统计
        
        Returns:
            所有用户的token使用统计列表，每个元素包含用户ID和对应的token使用统计
        """
        if not self.mongodb_available:
            logger.warning("MongoDB不可用，无法获取token使用统计")
            return []
        
        try:
            # 聚合查询：按用户ID分组，汇总token使用情况
            pipeline = [
                {
                    # 只选择有token_usage的会话
                    "$match": {
                        "token_usage": {"$exists": True, "$ne": None}
                    }
                },
                {
                    # 按用户ID分组
                    "$group": {
                        "_id": "$user_id",
                        "user_id": {"$first": "$user_id"},  # 用户ID
                        "sessions_count": {"$sum": 1},  # 会话数量
                        "total_input_tokens": {"$sum": "$token_usage.total_input_tokens"},  # 总输入token数
                        "total_output_tokens": {"$sum": "$token_usage.total_output_tokens"},  # 总输出token数
                        "total_cost": {"$sum": "$token_usage.total_cost"},  # 总成本
                        "latest_usage": {"$max": "$last_activity"}  # 最近使用时间
                    }
                },
                {
                    # 按总成本降序排序
                    "$sort": {"total_cost": -1}
                }
            ]
            
            # 执行聚合查询
            results = await self.db._collection.aggregate(pipeline).to_list(length=None)
            
            # 导入UserRepository用于查询用户信息
            from backend.auth.crud import UserRepository
            user_repo = UserRepository()
            
            # 转换ObjectId为字符串，并查询用户名
            for result in results:
                user_id = result["_id"]
                if user_id is None:
                    result["username"] = "未知用户"
                else:
                    # 查询用户信息以获取用户名
                    try:
                        user = await user_repo.get_by_id(str(user_id))
                        if user:
                            result["username"] = user.username
                        else:
                            result["username"] = f"用户ID: {user_id}"
                    except Exception as e:
                        logger.error(f"查询用户信息失败: {str(e)}")
                        result["username"] = f"用户ID: {user_id}"
                
                result["_id"] = str(user_id) if user_id else "unknown"
                
            logger.info(f"获取所有用户token使用统计成功，共{len(results)}个用户")
            return results
        except Exception as e:
            logger.error(f"获取所有用户token使用统计失败: {str(e)}")
            return []
    
    async def get_user_token_usage(self, user_id: str) -> Dict[str, Any]:
        """获取指定用户的token使用统计
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户的token使用统计，包含总输入输出token数量、总成本、会话数量等
        """
        if not self.mongodb_available:
            logger.warning("MongoDB不可用，无法获取用户token使用统计")
            return {}
        
        try:
            # 聚合查询：按用户ID过滤，汇总token使用情况
            pipeline = [
                {
                    # 只选择指定用户且有token_usage的会话
                    "$match": {
                        "user_id": user_id,
                        "token_usage": {"$exists": True, "$ne": None}
                    }
                },
                {
                    # 按用户ID分组
                    "$group": {
                        "_id": "$user_id",
                        "user_id": {"$first": "$user_id"},  # 用户ID
                        "sessions_count": {"$sum": 1},  # 会话数量
                        "total_input_tokens": {"$sum": "$token_usage.total_input_tokens"},  # 总输入token数
                        "total_output_tokens": {"$sum": "$token_usage.total_output_tokens"},  # 总输出token数
                        "total_cost": {"$sum": "$token_usage.total_cost"},  # 总成本
                        "latest_usage": {"$max": "$last_activity"}  # 最近使用时间
                    }
                }
            ]
            
            # 执行聚合查询
            results = await self.db._collection.aggregate(pipeline).to_list(length=None)
            
            # 查询用户信息
            from backend.auth.crud import UserRepository
            user_repo = UserRepository()
            
            # 如果有结果，处理并返回
            if results and len(results) > 0:
                result = results[0]
                
                # 查询用户信息以获取用户名
                try:
                    user = await user_repo.get_by_id(str(user_id))
                    if user:
                        result["username"] = user.username
                    else:
                        result["username"] = f"用户ID: {user_id}"
                except Exception as e:
                    logger.error(f"查询用户信息失败: {str(e)}")
                    result["username"] = f"用户ID: {user_id}"
                
                result["_id"] = str(result["_id"]) if result["_id"] else "unknown"
                
                # 获取模型使用明细
                try:
                    # 查询该用户所有会话中的模型使用明细
                    models_usage_pipeline = [
                        {
                            "$match": {
                                "user_id": user_id,
                                "token_usage.models_usage": {"$exists": True, "$ne": {}}
                            }
                        },
                        {
                            "$project": {
                                "models_usage": "$token_usage.models_usage"
                            }
                        }
                    ]
                    
                    models_usage_results = await self.db._collection.aggregate(models_usage_pipeline).to_list(length=None)
                    
                    # 合并所有会话的模型使用
                    models_usage = {}
                    for usage_doc in models_usage_results:
                        session_models = usage_doc.get("models_usage", {})
                        for model_name, model_usage in session_models.items():
                            if model_name not in models_usage:
                                models_usage[model_name] = {
                                    "input_tokens": 0,
                                    "output_tokens": 0,
                                    "cost": 0
                                }
                            
                            # 累加token使用量和成本
                            models_usage[model_name]["input_tokens"] += model_usage.get("input_tokens", 0)
                            models_usage[model_name]["output_tokens"] += model_usage.get("output_tokens", 0)
                            models_usage[model_name]["cost"] += model_usage.get("cost", 0)
                    
                    # 添加到结果中
                    result["models_usage"] = models_usage
                    
                except Exception as e:
                    logger.error(f"获取模型使用明细失败: {str(e)}")
                    result["models_usage"] = {}
                
                logger.info(f"获取用户token使用统计成功: {user_id}")
                return result
            else:
                # 如果没有查到结果，返回空数据
                logger.info(f"用户 {user_id} 没有token使用记录")
                return {
                    "user_id": user_id,
                    "total_input_tokens": 0,
                    "total_output_tokens": 0,
                    "total_cost": 0,
                    "sessions_count": 0,
                    "models_usage": {}
                }
        except Exception as e:
            logger.error(f"获取用户token使用统计失败: {str(e)}")
            return {
                "user_id": user_id,
                "total_input_tokens": 0,
                "total_output_tokens": 0,
                "total_cost": 0,
                "sessions_count": 0,
                "models_usage": {},
                "error": str(e)
            }
    
    async def get_all_sessions_token_usage(self, limit: int = 100, skip: int = 0) -> List[Dict[str, Any]]:
        """获取所有会话的token使用统计
        
        Args:
            limit: 返回结果数量限制
            skip: 跳过结果数量
            
        Returns:
            所有会话的token使用统计列表
        """
        if not self.mongodb_available:
            logger.warning("MongoDB不可用，无法获取token使用统计")
            return []
        
        try:
            # 只查询有token_usage的会话
            query = {"token_usage": {"$exists": True, "$ne": None}}
            
            # 投影：只返回需要的字段
            projection = {
                "session_id": 1,
                "user_id": 1,
                "question": 1,
                "started_at": 1,
                "last_activity": 1,
                "status": 1,
                "token_usage": 1
            }
            
            # 执行查询
            sessions = await self.db._collection.find(
                query, 
                projection
            ).sort("last_activity", -1).skip(skip).limit(limit).to_list(length=limit)
            
            # 导入UserRepository用于查询用户信息
            from backend.auth.crud import UserRepository
            user_repo = UserRepository()
            
            # 用于缓存已查询过的用户信息，减少数据库查询
            user_cache = {}
            
            # 转换结果
            results = []
            for session in sessions:
                # 提取token使用信息
                token_usage = session.get("token_usage", {})
                user_id = session.get("user_id")
                
                # 获取用户名
                username = "未知用户"
                if user_id:
                    # 检查缓存中是否有该用户信息
                    if user_id in user_cache:
                        username = user_cache[user_id]
                    else:
                        try:
                            user = await user_repo.get_by_id(str(user_id))
                            if user:
                                username = user.username
                            else:
                                username = f"用户ID: {user_id}"
                            # 添加到缓存
                            user_cache[user_id] = username
                        except Exception as e:
                            logger.error(f"查询用户信息失败: {str(e)}")
                            username = f"用户ID: {user_id}"
                
                results.append({
                    "session_id": session.get("session_id"),
                    "user_id": user_id,
                    "username": username,  # 添加用户名字段
                    "question": session.get("question", "无问题描述"),
                    "started_at": session.get("started_at"),
                    "last_activity": session.get("last_activity"),
                    "status": session.get("status", "unknown"),
                    "total_input_tokens": token_usage.get("total_input_tokens", 0),
                    "total_output_tokens": token_usage.get("total_output_tokens", 0),
                    "total_cost": token_usage.get("total_cost", 0),
                    "models_usage": token_usage.get("models_usage", {})
                })
            
            logger.info(f"获取所有会话token使用统计成功，共{len(results)}个会话")
            return results
        except Exception as e:
            logger.error(f"获取所有会话token使用统计失败: {str(e)}")
            return []
    
    async def add_report_feedback(self, session_id: str, user_id: str, rating: str, comment: str) -> bool:
        if not self.mongodb_available:
            logger.warning("MongoDB不可用，无法添加报告反馈")
            return False
        try:
            # 检查feedback table是否存在，不存在则创建
            feedback_db = MongoDBBase("feedback")   
            # 创建反馈记录
            feedback_data = {
                "session_id": session_id,
                "user_id": user_id,
                "rating": rating,
                "comment": comment,
                "created_at": get_cn_time()
            }
            # 保存到数据库
            await feedback_db.create(feedback_data)
            logger.info(f"添加报告反馈成功: {session_id}, 用户: {user_id}")
            return True
        except Exception as e:
            logger.error(f"添加报告反馈失败: {str(e)}")
            return False 