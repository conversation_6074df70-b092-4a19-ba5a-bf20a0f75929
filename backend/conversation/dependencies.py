from backend.conversation.websocket_service import WebSocketService
from backend.conversation.session_repository import SessionRepository
import logging
from .session_cache import SessionCache
from backend.redis.dependencies import get_redis_client

logger = logging.getLogger(__name__)

# WebSocket服务单例
_websocket_service_instance = None

# SessionRepository单例
_session_repository_instance = None

# 全局会话缓存实例
_session_cache = None

def get_websocket_service() -> WebSocketService:
    """获取WebSocket服务单例"""
    global _websocket_service_instance
    if _websocket_service_instance is None:
        _websocket_service_instance = WebSocketService()
    return _websocket_service_instance

def get_session_repository() -> SessionRepository:
    """获取SessionRepository单例"""
    global _session_repository_instance
    if _session_repository_instance is None:
        _session_repository_instance = SessionRepository()
    return _session_repository_instance

def get_session_cache() -> SessionCache:
    """
    获取会话缓存实例（单例模式）
    :return: 会话缓存实例
    """
    global _session_cache
    if _session_cache is None:
        redis_client = get_redis_client()
        if redis_client is not None:
            _session_cache = SessionCache(redis_client)
            logger.info("会话缓存初始化成功")
        else:
            logger.warning("Redis客户端未可用，会话缓存无法初始化")
            _session_cache = None
    
    return _session_cache

def shutdown_session_cache():
    """
    关闭会话缓存
    """
    global _session_cache
    if _session_cache:
        _session_cache = None
        logger.info("会话缓存已关闭") 