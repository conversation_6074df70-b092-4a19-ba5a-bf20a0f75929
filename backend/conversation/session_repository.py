import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from backend.redis.dependencies import get_redis_client
from .session_cache import SessionCache
from backend.conversation.session_store import SessionStore
from backend.utils import get_cn_time

logger = logging.getLogger(__name__)

class SessionRepository:
    """会话仓库管理者
    
    作为conversation模块的专用仓库，统一管理会话数据的缓存(Redis)和持久化(MongoDB)
    负责协调内存缓存和持久化存储，确保数据一致性
    """
    
    def __init__(self):
        """初始化会话仓库"""
        try:
            # 初始化Redis缓存
            self.redis_client = get_redis_client()
            self.session_cache = SessionCache(self.redis_client)
            
            # 初始化MongoDB持久化存储
            self.session_store = SessionStore()
            
            # Agent管理 - 延迟初始化避免循环依赖
            self._service_factory = None
            
            # 消息批量写入配置
            self.message_buffer_size = 50  # 缓冲区大小，达到此数量自动写入
            self.message_batch_interval = 10  # 定时写入间隔（秒）
            self.session_message_buffers = {}  # 每个session的消息缓冲区
            
            # 启动定时批量写入任务
            self._batch_write_task = None
            self._start_batch_write_task()
            
            logger.info("SessionRepository初始化成功")
        except Exception as e:
            logger.error(f"SessionRepository初始化失败: {str(e)}")
            raise
    
    def _start_batch_write_task(self):
        """启动定时批量写入任务"""
        if self._batch_write_task is None or self._batch_write_task.done():
            self._batch_write_task = asyncio.create_task(self._batch_write_messages_periodically())
            logger.info("启动消息批量写入定时任务")
    
    async def _batch_write_messages_periodically(self):
        """定时批量写入消息到数据库"""
        while True:
            try:
                await asyncio.sleep(self.message_batch_interval)
                await self._flush_all_message_buffers()
            except asyncio.CancelledError:
                logger.info("批量写入任务被取消")
                break
            except Exception as e:
                logger.error(f"定时批量写入消息失败: {str(e)}")
    
    async def _flush_all_message_buffers(self):
        """刷新所有会话的消息缓冲区到数据库"""
        if not self.session_message_buffers:
            return
            
        flush_tasks = []
        for session_id in list(self.session_message_buffers.keys()):
            if self.session_message_buffers.get(session_id):
                flush_tasks.append(self._flush_session_message_buffer(session_id))
        
        if flush_tasks:
            results = await asyncio.gather(*flush_tasks, return_exceptions=True)
            success_count = sum(1 for r in results if r is True)
            logger.info(f"批量刷新消息缓冲区完成: {success_count}/{len(flush_tasks)} 个会话成功")
    
    async def _flush_session_message_buffer(self, session_id: str) -> bool:
        """刷新单个会话的消息缓冲区到数据库"""
        if session_id not in self.session_message_buffers:
            return True
            
        messages = self.session_message_buffers.get(session_id, [])
        if not messages:
            return True
        
        try:
            # 批量写入到数据库
            success = await self.session_store.add_messages_batch(session_id, messages)
            if success:
                # 清空缓冲区
                self.session_message_buffers[session_id] = []
                logger.debug(f"批量写入消息成功: {session_id}, {len(messages)}条消息")
                return True
            else:
                logger.warning(f"批量写入消息失败: {session_id}")
                return False
        except Exception as e:
            logger.error(f"刷新会话消息缓冲区失败 {session_id}: {str(e)}")
            return False
    
    async def shutdown(self):
        """关闭仓库，确保所有缓冲的消息都写入数据库"""
        logger.info("SessionRepository正在关闭，刷新所有消息缓冲区...")
        
        # 取消定时任务
        if self._batch_write_task and not self._batch_write_task.done():
            self._batch_write_task.cancel()
            try:
                await self._batch_write_task
            except asyncio.CancelledError:
                pass
        
        # 最后一次刷新所有缓冲区
        await self._flush_all_message_buffers()
        logger.info("SessionRepository关闭完成")
    
    async def create_session(
        self, 
        session_id: str, 
        question: str, 
        language: str = "zh",
        user_id: Optional[str] = None,
        benchmark_id: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """创建新会话
        
        Args:
            session_id: 会话ID
            question: 研究问题
            language: 语言
            user_id: 用户ID
            benchmark_id: benchmark ID
            metadata: 元数据
            
        Returns:
            是否创建成功
        """
        try:
            # 准备会话数据
            session_data = {
                "session_id": session_id,
                "status": "starting",
                "question": question,
                "language": language,
                "user_id": user_id,
                "benchmark_id": benchmark_id,
                "metadata": metadata or {},
                "messages": [],
                "clarification_questions": [],
                "user_feedbacks": [],
                "report": None,
                "error": None,
                "has_agent": False,  # 标记是否有agent实例，配合ServiceFactory使用
                # 注意：不存储agent对象本身，避免序列化问题
                "token_usage": {
                    "total_input_tokens": 0,
                    "total_output_tokens": 0,
                    "total_cost": 0,
                    "models_usage": {}
                },
                "started_at": get_cn_time(),
                "last_activity": get_cn_time()
            }
            
            # 同时保存到Redis缓存和MongoDB
            cache_success = await self.session_cache.set_session(session_id, session_data)
            db_success = await self.session_store.create_session(
                session_id, question, language, benchmark_id, user_id, metadata
            )
            
            if cache_success and db_success:
                logger.info(f"创建会话成功: {session_id}")
                return True
            else:
                logger.warning(f"创建会话部分失败: cache={cache_success}, db={db_success}")
                return cache_success  # Redis缓存成功就认为成功，MongoDB作为备份
                
        except Exception as e:
            logger.error(f"创建会话失败: {str(e)}")
            return False
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话数据
        
        优先从Redis缓存获取，如果缓存中没有则从MongoDB恢复到缓存
        注意：不返回agent对象，agent对象由ServiceFactory管理
        
        Args:
            session_id: 会话ID
            
        Returns:
            会话数据（不包含agent对象）
        """
        try:
            # 优先从Redis缓存获取
            session_data = await self.session_cache.get_session(session_id)
            
            if session_data:
                logger.debug(f"从Redis缓存获取会话: {session_id}")
                # 确保返回的是字典类型，如果是字符串则反序列化
                if isinstance(session_data, str):
                    try:
                        import json
                        session_data = json.loads(session_data)
                    except (json.JSONDecodeError, TypeError) as e:
                        logger.warning(f"反序列化会话数据失败: {e}")
                        # 如果反序列化失败，从Redis中删除这个有问题的数据
                        await self.session_cache.delete_session(session_id)
                        return None
                
                # 确保不返回agent对象
                if "agent" in session_data:
                    del session_data["agent"]
                
                return session_data
            
            # 如果缓存中没有，从MongoDB恢复
            db_session = await self.session_store.get_session(session_id)
            if db_session:
                # 转换MongoDB数据为缓存格式
                cache_data = self._convert_db_to_cache_format(db_session)
                
                # 恢复到Redis缓存
                await self.session_cache.set_session(session_id, cache_data)
                logger.info(f"从MongoDB恢复会话到Redis缓存: {session_id}")
                return cache_data
            
            logger.warning(f"会话不存在: {session_id}")
            return None
            
        except Exception as e:
            logger.error(f"获取会话失败: {str(e)}")
            return None
    
    async def update_session(
        self, 
        session_id: str, 
        updates: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> bool:
        """更新会话数据
        
        同时更新Redis缓存和MongoDB
        
        Args:
            session_id: 会话ID
            updates: 要更新的字段字典（可选）
            **kwargs: 其他要更新的字段，会合并到updates中
            
        Returns:
            是否更新成功
        """
        try:
            # 获取当前会话数据
            session_data = await self.get_session(session_id)
            if not session_data:
                logger.warning(f"更新会话失败: 会话不存在 {session_id}")
                return False
            
            # 合并更新数据
            all_updates = updates or {}
            all_updates.update(kwargs)
            
            if not all_updates:
                logger.warning(f"更新会话失败: 没有提供更新数据 {session_id}")
                return False
            
            # 更新数据
            session_data.update(all_updates)
            session_data["last_activity"] = get_cn_time()
            
            # 同时更新Redis缓存和MongoDB
            cache_success = await self.session_cache.set_session(session_id, session_data)
            db_success = await self.session_store.update_session(session_id, all_updates)
            
            if cache_success and db_success:
                logger.debug(f"更新会话成功: {session_id}")
                return True
            else:
                logger.warning(f"更新会话部分失败: cache={cache_success}, db={db_success}")
                return cache_success  # Redis缓存成功就认为成功
                
        except Exception as e:
            logger.error(f"更新会话失败: {str(e)}")
            return False
    
    async def update_status(self, session_id: str, status: str, error: Optional[str] = None) -> bool:
        """更新会话状态"""
        updates = {"status": status}
        if error:
            updates["error"] = error
        return await self.update_session(session_id, updates)
    
    async def add_message(self, session_id: str, message_type: str, content: Any) -> bool:
        """添加消息到会话（优先使用缓存，定时批量写入数据库）"""
        try:
            session_data = await self.get_session(session_id)
            if not session_data:
                logger.warning(f"添加消息失败: 会话不存在 {session_id}")
                return False
            
            # 确保session_data是字典类型
            if not isinstance(session_data, dict):
                logger.error(f"添加消息失败: 会话数据类型错误 {type(session_data)}")
                return False
            
            # 创建消息
            message = {
                "timestamp": get_cn_time(),
                "type": message_type,
                "content": content
            }
            
            # 更新缓存中的消息列表
            if "messages" not in session_data:
                session_data["messages"] = []
            session_data["messages"].append(message)
            
            # 更新缓存
            cache_success = await self.session_cache.set_session(session_id, session_data)
            
            # 添加到消息缓冲区，等待批量写入
            if session_id not in self.session_message_buffers:
                self.session_message_buffers[session_id] = []
            
            self.session_message_buffers[session_id].append(message)
            
            # 如果缓冲区达到阈值，立即刷新
            if len(self.session_message_buffers[session_id]) >= self.message_buffer_size:
                await self._flush_session_message_buffer(session_id)
            
            return cache_success
                
        except Exception as e:
            logger.error(f"添加消息失败: {str(e)}")
            return False
    
    async def set_agent_active(self, session_id: str) -> bool:
        """标记会话中Agent为活跃状态
        
        注意：Agent对象不存储，只在ServiceFactory中管理
        """
        try:
            # 只更新has_agent标记和活动时间
            updates = {
                "has_agent": True,
                "last_activity": get_cn_time()
            }
            
            success = await self.update_session(session_id, updates)
            if success:
                logger.info(f"标记会话Agent为活跃: {session_id}")
            return success
            
        except Exception as e:
            logger.error(f"标记会话Agent活跃状态失败: {str(e)}")
            return False
    
    async def get_agent(self, session_id: str):
        """获取会话的Agent实例
        
        通过ServiceFactory获取Agent实例，如果不存在则创建
        """
        try:
            service_factory = self._get_service_factory()
            
            # 如果ServiceFactory中已有，直接返回
            if service_factory.has_research_agent(session_id):
                return service_factory.get_research_agent(session_id)
            
            # 如果没有，检查会话是否存在，然后创建Agent
            session_data = await self.get_session(session_id)
            if not session_data:
                logger.warning(f"获取Agent失败: 会话不存在 {session_id}")
                return None
            
            # 确保ServiceFactory中有Agent实例
            if await self._ensure_agent_in_service_factory(session_id):
                return service_factory.get_research_agent(session_id)
            
            return None
        except Exception as e:
            logger.error(f"获取Agent实例失败: {str(e)}")
            return None
    
    async def set_clarification_questions(self, session_id: str, questions: List[Dict[str, Any]]) -> bool:
        """设置澄清问题"""
        updates = {
            "clarification_questions": questions,
            "status": "clarification"
        }
        return await self.update_session(session_id, updates)
    
    async def add_user_feedback(self, session_id: str, feedback: str) -> bool:
        """添加用户反馈"""
        try:
            session_data = await self.get_session(session_id)
            if not session_data:
                logger.warning(f"添加用户反馈失败: 会话不存在 {session_id}")
                return False
            
            # 添加反馈
            if "user_feedbacks" not in session_data:
                session_data["user_feedbacks"] = []
            session_data["user_feedbacks"].append(feedback)
            
            # 更新会话
            return await self.update_session(session_id, {"user_feedbacks": session_data["user_feedbacks"]})
            
        except Exception as e:
            logger.error(f"添加用户反馈失败: {str(e)}")
            return False
    
    async def set_report(self, session_id: str, report: str) -> bool:
        """设置最终报告"""
        updates = {
            "report": report,
            "status": "paused"  # 完成后设为paused，可以继续操作
        }
        return await self.update_session(session_id, updates)
    
    async def update_token_usage(self, session_id: str, token_usage: Dict[str, Any]) -> bool:
        """更新token使用统计"""
        return await self.update_session(session_id, {"token_usage": token_usage})
    
    async def update_metadata(self, session_id: str, metadata: Dict[str, Any], merge: bool = True) -> bool:
        """更新会话元数据
        
        Args:
            session_id: 会话ID
            metadata: 要更新的元数据
            merge: 是否与现有metadata合并，如果为False则完全替换
            
        Returns:
            是否更新成功
        """
        if merge:
            # 获取现有metadata并合并
            session_data = await self.get_session(session_id)
            if session_data:
                existing_metadata = session_data.get("metadata", {})
                existing_metadata.update(metadata)
                return await self.update_session(session_id, metadata=existing_metadata)
            else:
                logger.warning(f"更新metadata失败: 会话不存在 {session_id}")
                return False
        else:
            # 直接替换
            return await self.update_session(session_id, metadata=metadata)
    
    async def update_session_field(self, session_id: str, field: str, value: Any) -> bool:
        """更新会话的单个字段
        
        Args:
            session_id: 会话ID
            field: 字段名
            value: 字段值
            
        Returns:
            是否更新成功
        """
        return await self.update_session(session_id, **{field: value})
    
    async def delete_session(self, session_id: str) -> bool:
        """删除会话"""
        try:
            # 同时删除Redis缓存和MongoDB记录
            cache_success = await self.session_cache.delete_session(session_id)
            db_success = await self.session_store.delete_session(session_id)
            
            logger.info(f"删除会话: {session_id}, cache={cache_success}, db={db_success}")
            return cache_success or db_success
            
        except Exception as e:
            logger.error(f"删除会话失败: {str(e)}")
            return False
    
    async def get_active_sessions(self) -> List[str]:
        """获取所有活跃会话ID列表"""
        try:
            return await self.session_cache.get_active_sessions()
        except Exception as e:
            logger.error(f"获取活跃会话列表失败: {str(e)}")
            return []
    
    async def cleanup_expired_sessions(self) -> int:
        """清理过期的会话"""
        try:
            return await self.session_cache.cleanup_expired_sessions()
        except Exception as e:
            logger.error(f"清理过期会话失败: {str(e)}")
            return 0
    
    def _convert_db_to_cache_format(self, db_session) -> Dict[str, Any]:
        """将MongoDB格式的会话数据转换为缓存格式"""
        try:
            if hasattr(db_session, 'model_dump'):
                data = db_session.model_dump()
            elif hasattr(db_session, 'dict'):
                data = db_session.dict()
            else:
                data = dict(db_session)
            
            # 确保必要字段存在
            cache_data = {
                "session_id": data.get("session_id"),
                "status": data.get("status", "starting"),
                "question": data.get("question", ""),
                "language": data.get("language", "zh"),
                "user_id": data.get("user_id"),
                "benchmark_id": data.get("benchmark_id"),
                "metadata": data.get("metadata", {}),
                "messages": data.get("messages", []),
                "clarification_questions": data.get("clarification_questions", []),
                "user_feedbacks": data.get("user_feedbacks", []),
                "report": data.get("report"),
                "error": data.get("error"),
                "has_agent": data.get("has_agent", False),  # 标记是否有agent实例
                # 注意：不存储agent对象本身，避免序列化问题
                "token_usage": data.get("token_usage", {
                    "total_input_tokens": 0,
                    "total_output_tokens": 0,
                    "total_cost": 0,
                    "models_usage": {}
                }),
                "started_at": data.get("started_at"),
                "last_activity": data.get("last_activity")
            }
            
            return cache_data
            
        except Exception as e:
            logger.error(f"转换数据库格式失败: {str(e)}")
            return {}
    
    async def force_flush_messages(self, session_id: str) -> bool:
        """强制刷新指定会话的消息缓冲区到数据库"""
        return await self._flush_session_message_buffer(session_id)
    
    async def force_flush_all_messages(self) -> bool:
        """强制刷新所有会话的消息缓冲区到数据库"""
        await self._flush_all_message_buffers()
        return True
    
    def _get_service_factory(self):
        """获取ServiceFactory实例（延迟初始化避免循环依赖）"""
        if self._service_factory is None:
            from backend.conversation.service_factory import get_service_factory
            self._service_factory = get_service_factory()
        return self._service_factory
    
    async def _ensure_agent_in_service_factory(self, session_id: str) -> bool:
        """确保ServiceFactory中有Agent实例（不存储到session_data）"""
        try:
            service_factory = self._get_service_factory()
            
            # 检查是否已存在Agent实例
            if service_factory.has_research_agent(session_id):
                logger.debug(f"ServiceFactory中已有Agent实例: {session_id}")
                return True
            else:
                # 创建新实例
                agent = service_factory.get_research_agent(session_id)
                
                # 从MongoDB加载Agent状态
                try:
                    await agent.memory.load_from_mongodb(session_id)
                    logger.info(f"创建Agent实例并加载状态完成: {session_id}")
                    return True
                except Exception as e:
                    logger.error(f"加载Agent状态失败: {str(e)}")
                    return False
                
        except Exception as e:
            logger.error(f"确保Agent实例失败 {session_id}: {str(e)}")
            return False
    
    # 下面添加更多SessionStore的代理方法，让Service层完全通过Repository访问
    
    async def list_sessions(self, user_id: Optional[str] = None, status: Optional[str] = None, 
                           skip: int = 0, limit: int = 20):
        """获取会话列表"""
        return await self.session_store.list_sessions(user_id, status, skip, limit)
    
    async def get_user_models_preference(self, user_id: str) -> Dict[str, Any]:
        """获取用户模型偏好"""
        return await self.session_store.get_user_models_preference(user_id)
    
    async def set_user_models_preference(self, user_id: str, models_config: Dict[str, Any]) -> bool:
        """设置用户模型偏好"""
        return await self.session_store.set_user_models_preference(user_id, models_config)
    
    async def set_session_models_config(self, session_id: str, models_config: Dict[str, Any]) -> bool:
        """设置会话的模型配置"""
        return await self.session_store.set_session_models_config(session_id, models_config)
    
    async def get_clarification_questions(self, session_id: str) -> List[Dict[str, Any]]:
        """获取澄清问题"""
        return await self.session_store.get_clarification_questions(session_id)


# SessionRepository类定义结束，不再提供单例管理 