[pytest]
log_cli = true
log_cli_level = WARNING
log_cli_format = %(asctime)s [%(levelname)8s] %(message)s (%(filename)s:%(lineno)s)
log_cli_date_format = %Y-%m-%d %H:%M:%S

asyncio_mode = auto
asyncio_default_fixture_loop_scope = function

# 测试发现
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# 标记
markers =
    unit: 单元测试 (使用mock，快速执行)
    integration: 集成测试 (需要真实服务)
    slow: 慢速测试 (可能需要较长时间)
    auth: 认证相关测试
    conversation: 对话相关测试
    document: 文档管理相关测试
    redis: Redis相关测试
    mongodb: MongoDB相关测试
    tos: TOS存储相关测试

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning 