import requests

def search_cognition_(query, k=10):
    request_body = {
        "size": k,
        "query": {
            "hybrid": {
                "queries": [
                    {
                        "match": {
                            "qa": query
                        }
                    },
                    {
                        "remote_neural": {
                            "qa_knn": {
                                "query_text": query,
                                "k": k
                            }
                        }
                    }
                ]
            }
        }
    }
    headers = {"Content-Type": "application/json"}

    try:
        response = requests.post(
            # "https://opensearch-o-00ctn8ofx5w3.escloud.ivolces.com:9200/remote_sementic_dc/_search?search_pipeline=search_pipeline_dc", 
            "https://opensearch-o-00ctn8ofx5w3.escloud.ivolces.com:9200/remote_sementic_all_cognitions/_search?search_pipeline=search_pipeline_dc",
            json=request_body, 
            headers=headers,
            auth=("admin", "tfL6JYRev@bbUW3"),
            verify=False
        )
        
        response.raise_for_status()
        results = response.json()
        hits = results.get("hits", {}).get("hits", [])
        return hits
    except Exception as e:
        print(f"RAG搜索失败: {str(e)}")
        return []

def get_related_cognitions_(current_cognition, threshold=0.01, max_results=5):
    """
    获取与当前认知相关的其它认知推荐

    Args:
        current_cognition (dict): 当前认知，应包含 'question' 和 'answer' 字段
        threshold (float): 相似度分数的阈值，默认为 0.5
        max_results (int): 最大返回结果数量，默认为 5

    Returns:
        list: 相关认知列表，按相似度分数降序排列，每项包含认知内容和相似度分数
    """
    if not current_cognition or 'question' not in current_cognition or 'answer' not in current_cognition:
        return []
    
    # 构建查询文本 - 使用问题和答案的组合以获得更好的语义匹配
    # query_text = f"{current_cognition['question']}"
    # query_text = f"{current_cognition['question']} {current_cognition['answer']}"
    query_text = f"{current_cognition['answer']} "

    # 搜索相关认知
    search_results = search_cognition_(query_text, k=20)  # 先获取较多结果，后续过滤
    
    # 过滤和处理结果
    related_cognitions = []
    current_id = current_cognition.get('id')

    max_score = 0

    # for hit in search_results:
    #     score = hit.get('_score', 0)
    #     source = hit.get('_source', {})
    #     print(f"the score is {score}")
    #     print(f"question: {source.get('question', '')}")
    #     print(f"answer: {source.get('answer', '')}")
        
    
    for hit in search_results:
        # 获取相似度分数
        score = hit.get('_score', 0)
        source = hit.get('_source', {})
        hit_id = hit.get('_id')

        # 跳过当前认知本身
        if hit_id == current_id:
            continue
        
        max_score = max(max_score, score)
        
        # 根据阈值过滤
        if score >= threshold:
            print
            related_cognitions.append({
                'id': hit_id,
                'question': source.get('question', ''),
                'answer': source.get('answer', ''),
                'similarity_score': score
            })
    
    # 按相似度分数降序排序
    related_cognitions.sort(key=lambda x: x['similarity_score'], reverse=True)

    if related_cognitions.__len__() == 0:
        print(f"当前认知 '{current_cognition['question']}' 的相似度分数均低于阈值 {threshold}，最好相似度为 {max_score}，无相关认知推荐。")
        return []
    
    # 限制返回数量
    return related_cognitions[:max_results]

def format_recommendations_for_display_(related_cognitions):
    """
    将推荐的认知格式化为易于展示的格式

    Args:
        related_cognitions (list): 相关认知列表

    Returns:
        list: 格式化后的推荐列表
    """
    formatted_recommendations = []
    
    for i, cognition in enumerate(related_cognitions, 1):
        formatted_recommendations.append({
            'id': cognition['id'],
            'title': cognition['question'],
            'preview': cognition['answer'][:100] + ('...' if len(cognition['answer']) > 100 else ''),
            'score': round(cognition['similarity_score'], 2)
        })
    
    return formatted_recommendations
