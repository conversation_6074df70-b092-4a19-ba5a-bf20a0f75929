"""
认知投票相关的数据模型
"""
from datetime import datetime
from typing import Optional, Literal, Any, Annotated
from pydantic import BaseModel, Field, ConfigDict, BeforeValidator
from bson import ObjectId

def validate_object_id(v: Any) -> ObjectId:
    """验证并转换ObjectId"""
    if isinstance(v, ObjectId):
        return v
    if isinstance(v, str) and ObjectId.is_valid(v):
        return ObjectId(v)
    raise ValueError("Invalid ObjectId")

# 使用Annotated类型来定义PyObjectId
PyObjectId = Annotated[ObjectId, BeforeValidator(validate_object_id)]

class CognitionVote(BaseModel):
    """认知投票模型"""
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str}
    )
    
    id: Optional[PyObjectId] = Field(default_factory=ObjectId, alias="_id")
    user_id: str = Field(..., description="用户ID")
    cognition_id: str = Field(..., description="认知ID")
    vote_type: Literal["like", "neutral", "dislike"] = Field(..., description="投票类型")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")

class CognitionVoteCreate(BaseModel):
    """创建投票请求模型"""
    cognition_id: str = Field(..., description="认知ID")
    vote_type: Literal["like", "neutral", "dislike"] = Field(..., description="投票类型")

class CognitionVoteUpdate(BaseModel):
    """更新投票请求模型"""
    vote_type: Literal["like", "neutral", "dislike"] = Field(..., description="投票类型")

class VoteStats(BaseModel):
    """投票统计模型"""
    like_count: int = Field(default=0, description="点赞数")
    neutral_count: int = Field(default=0, description="中立数")
    dislike_count: int = Field(default=0, description="点踩数")
    user_vote: Optional[str] = Field(default=None, description="当前用户的投票类型")

class CognitionWithVotes(BaseModel):
    """带投票信息的认知模型"""
    # 这里会包含原有的认知字段，加上投票统计
    vote_stats: VoteStats = Field(default_factory=VoteStats, description="投票统计") 