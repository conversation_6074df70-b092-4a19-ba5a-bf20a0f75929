"""
认知投票数据库操作
"""
from datetime import datetime
from typing import Optional, List, Dict
from motor.motor_asyncio import AsyncIOMotorDatabase
from pymongo.errors import DuplicateKeyError
from .vote_models import CognitionVote, VoteStats

class VoteDatabase:
    def __init__(self, database: AsyncIOMotorDatabase):
        self.db = database
        self.votes_collection = database.cognition_votes

    async def create_vote(self, user_id: str, cognition_id: str, vote_type: str) -> Optional[CognitionVote]:
        """创建或更新投票"""
        try:
            # 尝试插入新投票
            vote_data = {
                "user_id": user_id,
                "cognition_id": cognition_id,
                "vote_type": vote_type,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
            
            result = await self.votes_collection.insert_one(vote_data)
            vote_data["_id"] = result.inserted_id
            return CognitionVote(**vote_data)
            
        except DuplicateKeyError:
            # 如果已存在，则更新投票
            return await self.update_vote(user_id, cognition_id, vote_type)

    async def update_vote(self, user_id: str, cognition_id: str, vote_type: str) -> Optional[CognitionVote]:
        """更新用户投票"""
        result = await self.votes_collection.find_one_and_update(
            {"user_id": user_id, "cognition_id": cognition_id},
            {
                "$set": {
                    "vote_type": vote_type,
                    "updated_at": datetime.utcnow()
                }
            },
            return_document=True
        )
        
        if result:
            return CognitionVote(**result)
        return None

    async def delete_vote(self, user_id: str, cognition_id: str) -> bool:
        """删除用户投票"""
        result = await self.votes_collection.delete_one({
            "user_id": user_id,
            "cognition_id": cognition_id
        })
        return result.deleted_count > 0

    async def get_user_vote(self, user_id: str, cognition_id: str) -> Optional[CognitionVote]:
        """获取用户对特定认知的投票"""
        result = await self.votes_collection.find_one({
            "user_id": user_id,
            "cognition_id": cognition_id
        })
        
        if result:
            return CognitionVote(**result)
        return None

    async def get_cognition_vote_stats(self, cognition_id: str, user_id: Optional[str] = None) -> VoteStats:
        """获取认知的投票统计"""
        # 聚合查询统计各种投票类型的数量
        pipeline = [
            {"$match": {"cognition_id": cognition_id}},
            {
                "$group": {
                    "_id": "$vote_type",
                    "count": {"$sum": 1}
                }
            }
        ]
        
        results = await self.votes_collection.aggregate(pipeline).to_list(None)
        
        # 初始化统计数据
        stats = {
            "like_count": 0,
            "neutral_count": 0,
            "dislike_count": 0,
            "user_vote": None
        }
        
        # 填充统计数据
        for result in results:
            vote_type = result["_id"]
            count = result["count"]
            if vote_type == "like":
                stats["like_count"] = count
            elif vote_type == "neutral":
                stats["neutral_count"] = count
            elif vote_type == "dislike":
                stats["dislike_count"] = count

        # 获取当前用户的投票
        if user_id:
            user_vote = await self.get_user_vote(user_id, cognition_id)
            if user_vote:
                stats["user_vote"] = user_vote.vote_type

        return VoteStats(**stats)

    async def get_user_votes(self, user_id: str, skip: int = 0, limit: int = 100) -> List[CognitionVote]:
        """获取用户的所有投票记录"""
        cursor = self.votes_collection.find({"user_id": user_id}).skip(skip).limit(limit).sort("created_at", -1)
        results = await cursor.to_list(None)
        return [CognitionVote(**result) for result in results]

    async def get_cognition_votes(self, cognition_id: str, skip: int = 0, limit: int = 100) -> List[CognitionVote]:
        """获取认知的所有投票记录"""
        cursor = self.votes_collection.find({"cognition_id": cognition_id}).skip(skip).limit(limit).sort("created_at", -1)
        results = await cursor.to_list(None)
        return [CognitionVote(**result) for result in results]

    async def get_multiple_cognitions_vote_stats(self, cognition_ids: List[str], user_id: Optional[str] = None) -> Dict[str, VoteStats]:
        """批量获取多个认知的投票统计"""
        # 聚合查询统计
        pipeline = [
            {"$match": {"cognition_id": {"$in": cognition_ids}}},
            {
                "$group": {
                    "_id": {
                        "cognition_id": "$cognition_id",
                        "vote_type": "$vote_type"
                    },
                    "count": {"$sum": 1}
                }
            }
        ]
        
        results = await self.votes_collection.aggregate(pipeline).to_list(None)
        
        # 初始化所有认知的统计数据
        stats_dict = {}
        for cognition_id in cognition_ids:
            stats_dict[cognition_id] = {
                "like_count": 0,
                "neutral_count": 0,
                "dislike_count": 0,
                "user_vote": None
            }
        
        # 填充统计数据
        for result in results:
            cognition_id = result["_id"]["cognition_id"]
            vote_type = result["_id"]["vote_type"]
            count = result["count"]
            
            if cognition_id in stats_dict:
                if vote_type == "like":
                    stats_dict[cognition_id]["like_count"] = count
                elif vote_type == "neutral":
                    stats_dict[cognition_id]["neutral_count"] = count
                elif vote_type == "dislike":
                    stats_dict[cognition_id]["dislike_count"] = count

        # 获取当前用户的投票
        if user_id:
            user_votes = await self.votes_collection.find({
                "user_id": user_id,
                "cognition_id": {"$in": cognition_ids}
            }).to_list(None)
            
            for vote in user_votes:
                cognition_id = vote["cognition_id"]
                if cognition_id in stats_dict:
                    stats_dict[cognition_id]["user_vote"] = vote["vote_type"]

        # 转换为VoteStats对象
        return {
            cognition_id: VoteStats(**stats)
            for cognition_id, stats in stats_dict.items()
        } 