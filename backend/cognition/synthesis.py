import re
import yaml
from dc_agents.src.services.llm_service import LLMService

config = yaml.load(open("dc_agents/config/config.yaml"), Loader=yaml.FullLoader)

llm_service = LLMService(config['cognition_synthesis_model'])

async def synthesize_cognitions_by_llm(cognitions):
    """
    Synthesize multiple cognitions into a higher-level abstracted cognition.
    
    Args:
        cognitions (list): List of cognition dictionaries with 'question' and 'answer' keys
        
    Returns:
        dict: A new cognition with synthesized question and answer
    """
    if not cognitions or len(cognitions) < 2:
        return None
    
    # Prepare the input for the model
    cognition_text = ""
    for i, cog in enumerate(cognitions):
        question = cog.get("question", "")
        answer = cog.get("answer", "")
        if question and answer:
            cognition_text += f"认知{i+1}:\n问题: {question}\n答案: {answer}\n\n"
    
    # Enhanced prompt for synthesizing cognitions with natural conversation style
    prompt = f"""嗨，我刚刚读了几个认知，觉得它们之间有一些很有意思的联系。让我跟你分享一下我的思考过程吧。

这些认知是：
{cognition_text}

你知道吗，当我把这些放在一起看的时候，突然有了一个很深的领悟。让我慢慢告诉你我是怎么想到的...

但请你用以下格式来回答我，这样我能更好地理解你的思路：

<synthesized_cognition>
<abstract>
[用一句话说出你最核心的洞察 - 就是那种让人"啊！原来如此"的瞬间]
</abstract>

<source_analysis>
[自然地聊聊这几个认知各自在说什么，就像在和朋友解释一样。不要列条目，要像讲故事, 用mardown引用认知的原文]
</source_analysis>

<contradictions>
[坦率地说说你注意到哪些地方它们意见不一致，以及你觉得为什么会这样。要真诚，不要掩饰分歧]
</contradictions>

<synthesis_reasoning>
[分享你的"啊哈时刻" - 你是怎么突然意识到它们其实在讲同一件事的？这个过程要生动，要让人感受到你思考的轨迹]
</synthesis_reasoning>

<question>
[基于你的理解，提出一个真正让你好奇的问题 - 不是为了问而问，而是你真的想知道答案的那种]
</question>

<answer>
[很诚实地分享你对这个问题的思考。可以说"我觉得..."、"也许是因为..."，不要装成什么都知道的样子。要有自己的观点，但也要承认不确定的地方]
</answer>

<think>
[反思一下这次思考给你的启发。就像和朋友聊天结束时会说"通过这次聊天我意识到..."那样自然]
</think>
</synthesized_cognition>

记住，我要的不是报告，而是真诚的思考分享。一句深刻的洞察比十个标准答案更有价值。如果某个部分你觉得没什么特别的，就诚实地说出来，不要硬凑内容。

现在，告诉我你的想法吧："""

    response = await llm_service.get_completion_sync(
        messages=[{"role": "user", "content": prompt}],
        temperature=config['cognition_synthesis_model']['temperature'],
        max_tokens=config['cognition_synthesis_model']['max_tokens']
    )
    print(response)
    result_text = response["response"].choices[0].message.content
    
    # Extract synthesized cognition with enhanced fields
    abstract_pattern = r"<abstract>(.*?)</abstract>"
    source_analysis_pattern = r"<source_analysis>(.*?)</source_analysis>"
    contradictions_pattern = r"<contradictions>(.*?)</contradictions>"
    synthesis_reasoning_pattern = r"<synthesis_reasoning>(.*?)</synthesis_reasoning>"
    question_pattern = r"<question>(.*?)</question>"
    answer_pattern = r"<answer>(.*?)</answer>"
    think_pattern = r"<think>(.*?)</think>"
    
    abstract_match = re.search(abstract_pattern, result_text, re.DOTALL)
    source_analysis_match = re.search(source_analysis_pattern, result_text, re.DOTALL)
    contradictions_match = re.search(contradictions_pattern, result_text, re.DOTALL)
    synthesis_reasoning_match = re.search(synthesis_reasoning_pattern, result_text, re.DOTALL)
    question_match = re.search(question_pattern, result_text, re.DOTALL)
    answer_match = re.search(answer_pattern, result_text, re.DOTALL)
    think_match = re.search(think_pattern, result_text, re.DOTALL)
    
    synthesized_cognition = {
        "abstract": abstract_match.group(1).strip() if abstract_match else "",
        "source_analysis": source_analysis_match.group(1).strip() if source_analysis_match else "",
        "contradictions": contradictions_match.group(1).strip() if contradictions_match else "",
        "synthesis_reasoning": synthesis_reasoning_match.group(1).strip() if synthesis_reasoning_match else "",
        "question": question_match.group(1).strip() if question_match else "",
        "answer": answer_match.group(1).strip() if answer_match else "",
        "think": think_match.group(1).strip() if think_match else "",
    }
    
    return synthesized_cognition