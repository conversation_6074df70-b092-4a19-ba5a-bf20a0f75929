"""
认知平台测试数据插入脚本
"""

import asyncio
from datetime import datetime
from backend.cognition.database import CognitionDatabase
from backend.db.mongodb import get_cn_time
import logging

logger = logging.getLogger(__name__)

# 示例认知数据
SAMPLE_COGNITIONS = [
    {
        "abstract": "人工智能的发展对教育领域的深远影响",
        "think": "随着AI技术的快速发展，我开始思考它将如何改变传统教育模式。AI可以提供个性化学习体验，但同时也面临着替代传统教师角色的质疑。",
        "question": "人工智能是否会完全取代传统的教育模式？",
        "answer": "AI不会完全取代传统教育，而是会与之融合。AI可以处理重复性的教学任务，提供个性化学习路径，但教师的情感支持、创造性引导和批判性思维培养仍然不可替代。未来的教育将是人机协作的模式，AI辅助教师更好地关注每个学生的个性化需求。",
        "source": "基于MIT教育科技研究报告和个人教学经验",
        "author_id": "system",
        "author_name": "系统管理员"
    },
    {
        "abstract": "区块链技术在供应链管理中的实际应用案例",
        "think": "传统供应链管理面临透明度不足、追踪困难等问题。区块链的不可篡改特性似乎能够解决这些痛点，但实际应用中还需要考虑成本和技术成熟度。",
        "question": "区块链技术能否真正解决供应链管理中的信任问题？",
        "answer": "区块链在供应链管理中确实能够提高透明度和可追溯性，但并非万能解决方案。它能够有效记录商品流转过程，防止数据篡改，但仍需要配合物联网设备、人工审核等手段。成本效益分析显示，对于高价值、监管要求严格的商品(如药品、奢侈品)，区块链应用价值显著；但对于普通商品，传统方案可能更经济。",
        "source": "沃尔玛、马士基等企业区块链供应链项目实践",
        "author_id": "system",
        "author_name": "系统管理员"
    },
    {
        "abstract": "远程工作模式对团队协作效率的影响研究",
        "think": "疫情推动了远程工作的普及，我观察到团队在沟通方式、工作节奏、创新能力等方面都发生了变化。有些改变是积极的，有些则带来了新的挑战。",
        "question": "远程工作是否真的能够维持甚至提升团队的整体效率？",
        "answer": "远程工作对效率的影响呈现分化趋势。对于专注型工作(如编程、写作、数据分析)，远程工作通常能提升效率，减少办公室干扰。但对于需要频繁协作、头脑风暴的创新型工作，面对面交流仍有优势。关键在于建立有效的远程协作工具和流程，以及培养团队成员的自我管理能力。混合办公模式可能是最优解。",
        "source": "斯坦福远程工作效率研究、公司内部调研数据",
        "author_id": "system",
        "author_name": "系统管理员"
    },
    {
        "abstract": "可持续消费理念与经济增长的平衡思考",
        "think": "面对环境危机，可持续消费成为热议话题。但这与传统的经济增长模式存在张力：企业需要利润，消费者需要生活品质，如何在两者间找到平衡？",
        "question": "可持续消费理念是否与经济发展存在根本性冲突？",
        "answer": "可持续消费与经济发展不是零和关系，而可以形成良性循环。短期内确实存在转换成本，但长期看来，可持续消费推动了绿色技术创新、循环经济模式发展，创造了新的就业机会和商业模式。关键是政策引导、技术创新和消费者教育的协同推进。北欧国家的实践证明，高环保标准与高生活水平是可以并存的。",
        "source": "联合国可持续发展报告、丹麦循环经济实践案例",
        "author_id": "system",
        "author_name": "系统管理员"
    },
    {
        "abstract": "社交媒体算法对青少年认知发展的潜在影响",
        "think": "算法推荐系统越来越精准，能够准确预测用户喜好。但对于认知尚在发展的青少年，这种'回音室效应'可能限制他们接触多元化信息，影响批判性思维的培养。",
        "question": "社交媒体的算法推荐是否会阻碍青少年的全面认知发展？",
        "answer": "算法推荐确实存在加剧信息茧房、影响认知多样性的风险。研究显示，长期接触同质化内容可能导致认知偏见固化、减少跨领域思考能力。但解决方案不是完全禁止，而是：1)平台方增加内容多样性机制；2)家长和学校进行媒体素养教育；3)培养青少年主动探索不同观点的习惯。技术本身是中性的，关键在于如何理性使用。",
        "source": "哈佛大学青少年认知发展研究、欧盟数字素养报告",
        "author_id": "system",
        "author_name": "系统管理员"
    },
    {
        "abstract": "虚拟现实技术在心理治疗中的创新应用",
        "think": "传统心理治疗依赖谈话疗法，但VR技术的出现为治疗创伤、恐惧症等心理问题提供了新的可能。通过沉浸式体验，患者可以在安全环境中面对恐惧，这种暴露疗法效果如何？",
        "question": "虚拟现实技术能否有效改善传统心理治疗的局限性？",
        "answer": "VR技术在心理治疗中展现出巨大潜力。对于恐惧症、PTSD等疾病，VR暴露疗法可以提供可控、安全的治疗环境，让患者逐步适应恐惧源。研究表明，VR治疗在某些情况下效果不亚于传统疗法，且成本更低、更便于标准化。但VR无法完全替代人际互动的治疗价值，最佳应用是作为辅助工具，结合传统疗法形成综合治疗方案。",
        "source": "斯坦福VR心理学实验室研究成果",
        "author_id": "system",
        "author_name": "系统管理员"
    },
    {
        "abstract": "量子计算对现代密码学体系的颠覆性冲击",
        "think": "量子计算机一旦实现，将能够轻易破解目前广泛使用的RSA等加密算法。这意味着我们现在的网络安全体系可能在一夜之间变得脆弱不堪，这个威胁有多严重？",
        "question": "量子计算的发展是否会导致现有网络安全体系的全面崩溃？",
        "answer": "量子计算确实对传统密码学构成严重威胁，但'全面崩溃'言过其实。首先，实用化的量子计算机距离我们还有相当距离；其次，密码学界已在开发后量子密码算法，这些算法即使面对量子计算也是安全的。关键是要提前做好过渡准备：逐步部署抗量子加密、建立新的安全标准、培训相关人才。历史上每次密码学变革都伴随着新的解决方案，量子时代也不例外。",
        "source": "NIST后量子密码标准化项目报告",
        "author_id": "system",
        "author_name": "系统管理员"
    },
    {
        "abstract": "城市垂直农业对粮食安全的战略意义",
        "think": "随着城市化进程加速和气候变化影响，传统农业面临巨大压力。垂直农业在城市建筑中进行，能节约土地、控制环境，但成本高昂。这种模式能否真正解决粮食安全问题？",
        "question": "垂直农业是否能成为解决全球粮食危机的可行方案？",
        "answer": "垂直农业在特定场景下具有重要价值，但不是万能解决方案。优势包括：不受天气影响、节水节地、减少农药使用、就近供应。但目前成本仍然很高，主要适用于高价值作物如蔬菜、草药等。对于玉米、小麦等大宗粮食作物，传统农业仍是主力。未来随着技术进步和成本下降，垂直农业将与传统农业形成互补，在都市生鲜供应、极端环境农业等领域发挥重要作用。",
        "source": "联合国粮农组织垂直农业评估报告",
        "author_id": "system",
        "author_name": "系统管理员"
    },
    {
        "abstract": "基因编辑技术的伦理边界与社会责任",
        "think": "CRISPR等基因编辑技术为治疗遗传疾病带来希望，但同时也引发了对'设计婴儿'、基因歧视等问题的担忧。科技进步与伦理约束之间如何平衡？",
        "question": "基因编辑技术应该在哪些方面受到限制，哪些方面可以放开？",
        "answer": "基因编辑的伦理框架应该基于'治疗vs增强'的区分。治疗严重遗传疾病应该支持，但需要严格的监管和透明的程序。对于增强型应用(如提高智力、外貌改造)，需要更加谨慎，防止加剧社会不平等。关键原则包括：知情同意、风险可控、社会公正、后代影响评估。同时要加强国际合作，建立全球统一的伦理标准，防止'基因天堂'现象。科学发展不能脱离人文关怀。",
        "source": "世界卫生组织基因编辑伦理指导原则",
        "author_id": "system",
        "author_name": "系统管理员"
    },
    {
        "abstract": "数字货币对传统金融体系的重构影响",
        "think": "从比特币到央行数字货币(CBDC)，数字货币正在改变我们对金钱的认知。这种变化会如何影响银行体系、货币政策，甚至国际金融秩序？",
        "question": "数字货币的普及是否会导致传统银行业的衰落？",
        "answer": "数字货币将重塑而非摧毁银行业。银行的核心价值在于信用中介、风险管理、支付服务等，这些功能在数字货币时代仍然重要。但银行必须适应变化：发展数字支付能力、提供数字货币相关服务、加强网络安全。央行数字货币可能会减少银行的部分中介作用，但也会创造新的业务机会。关键是银行要从单纯的资金中介转向综合金融服务提供商，在数字金融生态中找到新定位。",
        "source": "国际清算银行数字货币研究报告",
        "author_id": "system",
        "author_name": "系统管理员"
    }
]

async def insert_sample_cognitions():
    """插入示例认知数据"""
    try:
        cognition_db = CognitionDatabase()
        logger.info("开始插入认知平台测试数据...")
        
        for cognition_data in SAMPLE_COGNITIONS:
            try:
                # 检查是否已存在相同的认知
                existing = await cognition_db.collection.find_one({"abstract": cognition_data["abstract"]})
                if existing:
                    logger.info(f"认知已存在，跳过: {cognition_data['abstract'][:30]}...")
                    continue
                
                created_cognition = await cognition_db.create_cognition(cognition_data)
                logger.info(f"成功创建认知: {created_cognition.abstract[:30]}...")
                
            except Exception as e:
                logger.error(f"创建认知失败: {str(e)}")
                continue
        
        logger.info("认知平台测试数据插入完成！")
        
    except Exception as e:
        logger.error(f"插入测试数据失败: {str(e)}")
        raise

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    # 运行插入数据
    asyncio.run(insert_sample_cognitions()) 