from backend.cognition.synthesis import synthesize_cognitions_by_llm
from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional
from datetime import datetime
import asyncio
from pydantic import BaseModel

from backend.auth.dependencies import get_current_active_user, get_current_admin_user
from backend.auth.models import UserDB
from backend.cognition.models import (
    CognitionResponse, CognitionCreate, CommentCreate, VoteRequest, CognitionComment, SupabaseCognitionResponse,
    CollectionCreate, CollectionResponse, FavoriteRequest, FavoriteStatusResponse, 
    PaginatedCollectionResponse, CollectionCognitionsResponse,
    ReadStatusRequest, ReadStatusResponse
)
# 替换Supabase客户端为MongoDB数据库操作
from backend.cognition.database import (
    CognitionDatabase, CognitionCollectionsDatabase, CognitionFavoritesDatabase,
    get_cognition_database, get_cognition_collections_database, get_cognition_favorites_database,
    CognitionReadStatusDatabase, get_cognition_read_status_database
)
from backend.db.mongodb import get_cn_time
import logging

from backend.cognition.recommend import search_cognition_, get_related_cognitions_, format_recommendations_for_display_

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/cognition", tags=["认知平台"])

class PaginatedCognitionResponse(BaseModel):
    """分页认知响应模型"""
    items: List[SupabaseCognitionResponse]
    total: int
    page: int
    size: int
    pages: int

class SynthesisRequest(BaseModel):
    """合成请求模型"""
    cognition_ids: List[str]

class BatchCognitionRequest(BaseModel):
    """批量获取认知请求模型"""
    ids: List[str]

def convert_mongodb_to_response(mongodb_data: dict, user_vote: Optional[str] = None, language: str = "zh") -> SupabaseCognitionResponse:
    """将MongoDB数据转换为响应模型"""
    # 处理时间字符串
    created_at = None
    updated_at = None
    
    if mongodb_data.get("created_at"):
        created_at = mongodb_data["created_at"]
        if isinstance(created_at, str):
            try:
                created_at = datetime.fromisoformat(created_at.replace("Z", "+00:00"))
            except (ValueError, AttributeError):
                pass
    
    if mongodb_data.get("raw_at"):
        raw_at = mongodb_data["raw_at"]
        if isinstance(raw_at, str):
            try:
                raw_at = datetime.fromisoformat(raw_at.replace("Z", "+00:00"))
            except (ValueError, AttributeError):
                pass
    
    if mongodb_data.get("updated_at"):
        updated_at = mongodb_data["updated_at"] 
        if isinstance(updated_at, str):
            try:
                updated_at = datetime.fromisoformat(updated_at.replace("Z", "+00:00"))
            except (ValueError, AttributeError):
                pass
    
    # 转换字段名：数据库的"related_topic" -> 前端的"related_topics"（为了前端兼容性）
    related_topics = mongodb_data.get("related_topic", [])
    if not isinstance(related_topics, list):
        related_topics = []
    
    return SupabaseCognitionResponse(
        id=mongodb_data.get("id", ""),
        abstract_zh=mongodb_data.get("abstract_zh", ""),
        abstract_en=mongodb_data.get("abstract_en", ""),
        think_zh=mongodb_data.get("think_zh", ""),
        think_en=mongodb_data.get("think_en", ""),
        question_zh=mongodb_data.get("question_zh", ""),
        question_en=mongodb_data.get("question_en", ""),
        answer_zh=mongodb_data.get("answer_zh", ""),
        answer_en=mongodb_data.get("answer_en", ""),
        source=mongodb_data.get("source", ""),
        author_id=mongodb_data.get("author_id", ""),
        author_name=mongodb_data.get("author_name", ""),
        blogger=mongodb_data.get("blogger"),
        link=mongodb_data.get("link"),
        content=mongodb_data.get("content"),
        likes=mongodb_data.get("likes", 0) or 0,
        neutral=mongodb_data.get("neutral", 0) or 0,
        dislikes=mongodb_data.get("dislikes", 0) or 0,
        comments=mongodb_data.get("comments", []) or [],
        votes=mongodb_data.get("votes", []) or [],
        created_at=created_at,
        raw_at=raw_at,
        updated_at=updated_at,
        user_vote=user_vote,
        primary_topic=mongodb_data.get("primary_topic"),
        related_topics=related_topics,
        tag=mongodb_data.get("tag"),
        cover_url=mongodb_data.get("cover_url"),
        source_analysis=mongodb_data.get("source_analysis"),
        contradictions=mongodb_data.get("contradictions"),
        synthesis_reasoning=mongodb_data.get("synthesis_reasoning"),
        source_cognition_ids=mongodb_data.get("source_cognition_ids")
    )

# 在现有路由后添加这个新路由

@router.get("/{cognition_id}/recommendations")
async def get_cognition_recommendations(
    cognition_id: str,
    max_results: int = Query(3, description="返回的最大推荐数"),
    threshold: float = Query(0.01, description="相似度阈值"),
    current_user: UserDB = Depends(get_current_active_user),
    cognition_db: CognitionDatabase = Depends(get_cognition_database)
):
    """获取与特定认知相关的推荐"""
    try:
        print(f"获取认知推荐: {cognition_id}, 最大结果: {max_results}, 阈值: {threshold}")
        # 1. 获取当前认知详情
        current_cognition = await cognition_db.get_cognition_by_id(cognition_id)
        if not current_cognition:
            raise HTTPException(status_code=404, detail="认知不存在")
        
        # 2. 获取相关推荐
        # 准备查询数据
        query_cognition = {
            'id': current_cognition.get('id'),
            'question': current_cognition.get('question_zh') or current_cognition.get('question_en') or '',
            'answer': current_cognition.get('answer_zh') or current_cognition.get('answer_en') or ''
        }

        print(f"查询认知: {query_cognition['id']}, 问题: {query_cognition['question']}, 答案: {query_cognition['answer']}")
        print(f"阈值: {threshold}, 最大结果: {max_results}")
        
        # 使用推荐功能获取相关认知
        related_cognitions = get_related_cognitions_(query_cognition, threshold, max_results)
        
        # 3. 格式化结果
        recommendations = format_recommendations_for_display_(related_cognitions)
        
        # 4. 从数据库补充更多详细信息
        for rec in recommendations:
            # 获取完整认知信息
            cognition = await cognition_db.get_cognition_by_id(rec['id'])
            if cognition:
                # 使用数据库中的字段更新标题和预览
                rec['title'] = cognition.get('abstract_zh') or cognition.get('abstract_en') or rec['title']
                if not rec['title'] or rec['title'] == '':
                    rec['title'] = cognition.get('question_zh') or cognition.get('question_en') or '未知标题'
                    
                # 截断标题
                if len(rec['title']) > 50:
                    rec['title'] = rec['title'][:50] + '...'
                
                # 使用answer作为预览
                preview_text = cognition.get('answer_zh') or cognition.get('answer_en') or ''
                if preview_text:
                    rec['preview'] = preview_text[:100] + ('...' if len(preview_text) > 100 else '')

                # 添加标签信息
                rec['primary_topic'] = cognition.get('primary_topic')
                rec['related_topics'] = cognition.get('related_topic', [])  # 注意MongoDB中是related_topic而不是related_topics 
                rec['tag'] = cognition.get('tag', [])
                rec['blogger'] = cognition.get('blogger', {})
        
        return recommendations
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取认知推荐失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取推荐失败")

@router.post("/batch", response_model=List[SupabaseCognitionResponse])
async def get_cognitions_by_ids(
    request: BatchCognitionRequest,
    current_user: UserDB = Depends(get_current_active_user),
    cognition_db: CognitionDatabase = Depends(get_cognition_database)
):
    """根据ID列表批量获取认知"""
    try:
        cognitions_data = await cognition_db.get_cognitions_by_ids(request.ids, str(current_user.id))
        
        response_list = []
        for cognition_data in cognitions_data:
            response_data = convert_mongodb_to_response(cognition_data, cognition_data.get('user_vote'))
            response_list.append(response_data)
            
        return response_list
    except Exception as e:
        logger.error(f"批量获取认知失败: {str(e)}")
        raise HTTPException(status_code=500, detail="批量获取认知失败")

@router.get("/list", response_model=PaginatedCognitionResponse)
async def get_cognitions(
    skip: int = Query(0, description="跳过的记录数"),
    limit: int = Query(20, description="返回的最大记录数"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    language: Optional[str] = Query("zh", description="显示语言：zh(中文), en(英文)"),
    sort: Optional[str] = Query("latest", description="排序方式：latest(最新), hot(最热), most_liked(点赞最多)"),
    topics: Optional[List[str]] = Query(None, description="话题筛选，支持多个话题"),
    time_filter: Optional[str] = Query("all", description="时间筛选: all, day, week, month, yesterday, last_week, last_month"),
    search_scope: Optional[str] = Query("all", description="搜索范围: all, favorites"),
    source_filter: Optional[str] = Query("all", description="来源筛选: all, Hugging Face Paper, Arxiv, Sam Altman, Andrej Karpathy, Elon Musk, Reddit"),
    current_user: UserDB = Depends(get_current_active_user),
    cognition_db: CognitionDatabase = Depends(get_cognition_database),
    favorites_db: CognitionFavoritesDatabase = Depends(get_cognition_favorites_database),
    read_status_db: CognitionReadStatusDatabase = Depends(get_cognition_read_status_database)
):
    """获取认知列表"""
    try:
        if search_scope == 'favorites' and current_user:
            # 在收藏中搜索或列出所有收藏
            data = await favorites_db.get_favorited_cognitions(
                user_id=str(current_user.id),
                skip=skip,
                limit=limit,
                search=search,
                time_filter=time_filter,
                source_filter=source_filter,
                sort=sort
            )
            cognitions_data = data.get("items", [])
            total_count = data.get("total", 0)
        else:
            # 全局搜索
            cognitions_data, total_count = await asyncio.gather(
                cognition_db.get_cognitions_paginated(
                    skip=skip, limit=limit, search=search, sort=sort, 
                    topics=topics, user_id=str(current_user.id), time_filter=time_filter,
                    source_filter=source_filter
                ),
                cognition_db.get_cognitions_count(
                    search=search, topics=topics, time_filter=time_filter,
                    source_filter=source_filter
                )
            )
        
            # 批量获取收藏状态和已读状态
            cognition_ids = [cognition.get("id", "") for cognition in cognitions_data]
            favorite_statuses, read_statuses = await asyncio.gather(
                favorites_db.get_favorite_statuses_batch(cognition_ids, str(current_user.id)),
                read_status_db.get_read_statuses_batch(cognition_ids, str(current_user.id))
            )
            
            # 为全局搜索结果附加收藏状态和已读状态
            for cog in cognitions_data:
                cog['favorite_status'] = favorite_statuses.get(cog.get('id', ''), {"is_favorited": False, "collections": []})
                cog['read_status'] = read_statuses.get(cog.get('id', ''), {"is_read": False, "read_at": None})
             
        
        # 为收藏搜索结果也添加已读状态
        if search_scope == 'favorites' and cognitions_data:
            cognition_ids = [cognition.get("id", "") for cognition in cognitions_data]
            read_statuses = await read_status_db.get_read_statuses_batch(cognition_ids, str(current_user.id))
            for cog in cognitions_data:
                cog['read_status'] = read_statuses.get(cog.get('id', ''), {"is_read": False, "read_at": None})
        
        # 转换为响应模型
        response_list = []
        for cognition_data in cognitions_data:
            # favorite_status 和 read_status 已经在上面附加好了
            response_data = convert_mongodb_to_response(cognition_data, cognition_data.get('user_vote'), language or "zh")
            response_data.favorite_status = cognition_data.get('favorite_status')
            response_data.read_status = cognition_data.get('read_status')
            response_list.append(response_data)
        
        return PaginatedCognitionResponse(
            items=response_list,
            total=total_count,
            page=(skip // limit) + 1,
            size=limit,
            pages=(total_count + limit - 1) // limit
        )
    except Exception as e:
        logger.error(f"获取认知列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取认知列表失败")

@router.get("/topics")
async def get_available_topics(
    cognition_db: CognitionDatabase = Depends(get_cognition_database)
):
    """获取所有可用的话题标签及其颜色"""
    try:
        topics_data = await cognition_db.get_all_topics()
        return topics_data
    except Exception as e:
        logger.error(f"获取可用话题失败: {str(e)}")
        # 返回默认话题作为备选
        return {
            "topics": {
                "AI": {"color": "#FF6B6B", "background": "#FFE5E5"},
                "Machine Learning": {"color": "#4ECDC4", "background": "#E5F9F7"},
                "Deep Learning": {"color": "#45B7D1", "background": "#E5F4FD"},
                "Computer Vision": {"color": "#96CEB4", "background": "#F0F9F5"},
                "Natural Language Processing": {"color": "#FFEAA7", "background": "#FDF8E7"},
                "Research": {"color": "#FD79A8", "background": "#FEF0F5"},
            },
            "available_list": ["AI", "Machine Learning", "Deep Learning", "Computer Vision", "Natural Language Processing", "Research"]
        }

@router.get("/trend-stats")
async def get_trend_stats(
    time_filter: Optional[str] = Query("all", description="时间筛选: all, day, week, month, yesterday, last_week, last_month"),
    topics: Optional[List[str]] = Query(None, description="话题筛选，支持多个话题"),
    search_scope: Optional[str] = Query("all", description="搜索范围: all, favorites"),
    source_filter: Optional[str] = Query("all", description="来源筛选: all, Hugging Face Paper, Arxiv, Sam Altman, Andrej Karpathy, Elon Musk, Reddit"),
    current_user: UserDB = Depends(get_current_active_user),
    cognition_db: CognitionDatabase = Depends(get_cognition_database),
    favorites_db: CognitionFavoritesDatabase = Depends(get_cognition_favorites_database)
):
    """获取趋势统计数据（增强：支持按raw_at日期和季度分组）"""
    try:
        # 根据搜索范围确定数据源
        if search_scope == 'favorites' and current_user:
            data = await favorites_db.get_favorited_cognitions(
                user_id=str(current_user.id),
                skip=0,
                limit=10000,
                time_filter=time_filter,
                source_filter=source_filter
            )
            cognitions = data.get("items", [])
        else:
            cognitions = await cognition_db.get_cognitions_paginated(
                skip=0,
                limit=10000,
                topics=topics,
                time_filter=time_filter,
                source_filter=source_filter,
                user_id=str(current_user.id)
            )

        # 统计各个topic的数量
        topic_stats = {}
        for cognition in cognitions:
            cognition_topics = []
            if cognition.get('primary_topic'):
                cognition_topics.append(cognition['primary_topic'])
            if cognition.get('related_topic'):
                if isinstance(cognition['related_topic'], list):
                    cognition_topics.extend(cognition['related_topic'])
                else:
                    cognition_topics.append(cognition['related_topic'])
            for topic in cognition_topics:
                if topic:
                    topic_stats[topic] = topic_stats.get(topic, 0) + 1

        total_cognitions = len(cognitions)
        sorted_topics = sorted(topic_stats.items(), key=lambda x: x[1], reverse=True)
        trend_data = []
        for topic, count in sorted_topics:
            percentage = (count / total_cognitions * 100) if total_cognitions > 0 else 0
            trend_data.append({
                "topic": topic,
                "count": count,
                "percentage": round(percentage, 2),
                "trend": "stable"
            })

        # 新增：按raw_at前10位分组
        date_map = {}
        for cognition in cognitions:
            raw_at = cognition.get("raw_at")
            date_str = raw_at[:10] if raw_at and len(raw_at) >= 10 else None
            if not date_str:
                continue
            if date_str not in date_map:
                date_map[date_str] = []
            date_map[date_str].append(cognition)
        date_stats = [
            {"date": date, "count": len(items), "items": items}
            for date, items in sorted(date_map.items())
        ]

        # 新增：按季度分组
        from datetime import datetime
        def get_quarter(date_str):
            try:
                if "_" in date_str:
                    dt = datetime.strptime(date_str, "%Y_%m_%d")
                else:
                    dt = datetime.strptime(date_str, "%Y-%m-%d")
                quarter = (dt.month - 1) // 3 + 1
                return f"{dt.year}-Q{quarter}"
            except Exception:
                return "未知"

        quarter_map = {}
        for cognition in cognitions:
            raw_at = cognition.get("raw_at")
            date_str = raw_at[:10] if raw_at and len(raw_at) >= 10 else None
            if not date_str:
                continue
            quarter = get_quarter(date_str)
            if quarter not in quarter_map:
                quarter_map[quarter] = []
            quarter_map[quarter].append(cognition)
        quarter_stats = [
            {"quarter": quarter, "count": len(items), "items": items}
            for quarter, items in sorted(quarter_map.items())
        ]

        # 保持原有返回结构，并新增字段
        return {
            "data": trend_data,
            "total": total_cognitions,
            "time_filter": time_filter,
            "search_scope": search_scope,
            "raw_cognitions": cognitions,
            "date_stats": date_stats,
            "quarter_stats": quarter_stats
        }

    except Exception as e:
        logger.error(f"获取趋势统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取趋势统计失败")

# 收藏相关路由 - 必须在 /{cognition_id} 路由之前定义
@router.get("/collections", response_model=List[CollectionResponse])
async def get_user_collections(
    current_user: UserDB = Depends(get_current_active_user),
    collections_db: CognitionCollectionsDatabase = Depends(get_cognition_collections_database)
):
    """获取用户的收藏夹列表"""
    try:
        # 确保用户有默认收藏夹
        await collections_db.ensure_default_collection(str(current_user.id))
        
        # 批量获取收藏夹及其认知数量
        collections_data = await collections_db.get_collections_with_counts_batch(str(current_user.id))
        
        # 转换为响应模型
        collections = []
        for collection_data in collections_data:
            collection = CollectionResponse(
                id=collection_data["id"],
                user_id=collection_data["user_id"],
                name=collection_data["name"],
                description=collection_data.get("description"),
                is_default=collection_data.get("is_default", False),
                created_at=collection_data["created_at"],
                updated_at=collection_data["updated_at"],
                cognition_count=collection_data.get("cognition_count", 0)
            )
            collections.append(collection)
        
        return collections
    except Exception as e:
        logger.error(f"获取收藏夹列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取收藏夹列表失败")

@router.post("/collections", response_model=CollectionResponse)
async def create_collection(
    collection_data: CollectionCreate,
    current_user: UserDB = Depends(get_current_active_user),
    collections_db: CognitionCollectionsDatabase = Depends(get_cognition_collections_database)
):
    """创建收藏夹"""
    try:
        created_collection = await collections_db.create_collection(
            str(current_user.id), 
            collection_data.name, 
            collection_data.description or "",
            False
        )
        
        if not created_collection:
            raise HTTPException(status_code=500, detail="创建收藏夹失败")
        
        return CollectionResponse(
            id=created_collection["id"],
            user_id=created_collection["user_id"],
            name=created_collection["name"],
            description=created_collection.get("description"),
            is_default=created_collection.get("is_default", False),
            created_at=created_collection["created_at"],
            updated_at=created_collection["updated_at"],
            cognition_count=0
        )
    except Exception as e:
        logger.error(f"创建收藏夹失败: {str(e)}")
        raise HTTPException(status_code=500, detail="创建收藏夹失败")

@router.delete("/collections/{collection_id}")
async def delete_collection(
    collection_id: str,
    current_user: UserDB = Depends(get_current_active_user),
    collections_db: CognitionCollectionsDatabase = Depends(get_cognition_collections_database)
):
    """删除收藏夹"""
    try:
        success = await collections_db.delete_collection(collection_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="收藏夹不存在")
        
        return {"message": "收藏夹删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除收藏夹失败: {str(e)}")
        raise HTTPException(status_code=500, detail="删除收藏夹失败")

@router.put("/collections/{collection_id}", response_model=CollectionResponse)
async def update_collection(
    collection_id: str,
    collection_data: CollectionCreate,
    current_user: UserDB = Depends(get_current_active_user),
    collections_db: CognitionCollectionsDatabase = Depends(get_cognition_collections_database)
):
    """更新收藏夹"""
    try:
        updated_collection = await collections_db.update_collection(
            collection_id,
            collection_data.name,
            collection_data.description or ""
        )
        
        if not updated_collection:
            raise HTTPException(status_code=404, detail="收藏夹不存在")
        
        return CollectionResponse(
            id=updated_collection["id"],
            user_id=updated_collection["user_id"],
            name=updated_collection["name"],
            description=updated_collection.get("description"),
            is_default=updated_collection.get("is_default", False),
            created_at=updated_collection["created_at"],
            updated_at=updated_collection["updated_at"],
            cognition_count=0  # 这里可以优化为实际计算
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新收藏夹失败: {str(e)}")
        raise HTTPException(status_code=500, detail="更新收藏夹失败")

@router.get("/collections/{collection_id}/cognitions", response_model=CollectionCognitionsResponse)
async def get_collection_cognitions(
    collection_id: str,
    skip: int = Query(0, description="跳过的记录数"),
    limit: int = Query(20, description="返回的最大记录数"),
    current_user: UserDB = Depends(get_current_active_user),
    collections_db: CognitionCollectionsDatabase = Depends(get_cognition_collections_database),
    favorites_db: CognitionFavoritesDatabase = Depends(get_cognition_favorites_database),
    cognition_db: CognitionDatabase = Depends(get_cognition_database)
):
    """获取收藏夹中的认知列表"""
    try:
        # 获取收藏夹信息
        collections = await collections_db.get_user_collections(str(current_user.id))
        collection_info = None
        for c in collections:
            if c["id"] == collection_id:
                collection_info = c
                break
        
        if not collection_info:
            raise HTTPException(status_code=404, detail="收藏夹不存在")
        
        # 获取认知列表和总数
        data = await favorites_db.get_collection_cognitions(collection_id, skip, limit)
        cognitions_data = data.get("items", [])
        total_count = data.get("total", 0)
        
        # 批量获取用户投票状态，避免N+1查询
        cognition_ids = [cognition.get("id", "") for cognition in cognitions_data]
        user_votes = await cognition_db.get_user_votes_batch(cognition_ids, str(current_user.id))
        
        # 转换为响应模型
        response_list = []
        for cognition_data in cognitions_data:
            cognition_id = cognition_data.get("id", "")
            user_vote = user_votes.get(cognition_id)
            response_data = convert_mongodb_to_response(cognition_data, user_vote)
            response_list.append(response_data)
        
        collection_response = CollectionResponse(
            id=collection_info["id"],
            user_id=collection_info["user_id"],
            name=collection_info["name"],
            description=collection_info.get("description"),
            is_default=collection_info.get("is_default", False),
            created_at=collection_info["created_at"],
            updated_at=collection_info["updated_at"],
            cognition_count=total_count
        )
        
        return CollectionCognitionsResponse(
            collection=collection_response,
            items=response_list,
            total=total_count,
            page=(skip // limit) + 1,
            pages=(total_count + limit - 1) // limit,
            page_size=limit
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取收藏夹认知列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取收藏夹认知列表失败")

@router.get("/{cognition_id}", response_model=SupabaseCognitionResponse)
async def get_cognition(
    cognition_id: str,
    language: Optional[str] = Query("zh", description="显示语言：zh(中文), en(英文)"),
    current_user: UserDB = Depends(get_current_active_user),
    cognition_db: CognitionDatabase = Depends(get_cognition_database)
):
    """获取单个认知详情"""
    try:
        cognition_data = await cognition_db.get_cognition_by_id(cognition_id, str(current_user.id))
        if not cognition_data:
            raise HTTPException(status_code=404, detail="认知不存在")
        
        # 获取用户投票状态
        user_vote = cognition_data.get('user_vote')
        
        return convert_mongodb_to_response(cognition_data, user_vote, language)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取认知详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取认知详情失败")

@router.post("/create", response_model=SupabaseCognitionResponse)
async def create_cognition(
    cognition_data: CognitionCreate,
    current_user: UserDB = Depends(get_current_active_user),
    cognition_db: CognitionDatabase = Depends(get_cognition_database)
):
    """创建认知"""
    try:
        # 准备认知数据
        cognition_dict = cognition_data.model_dump()
        cognition_dict["author_id"] = str(current_user.id)
        cognition_dict["author_name"] = current_user.username
        
        # 转换字段名：前端的related_topics -> 数据库的"related_topic"
        if "related_topics" in cognition_dict:
            cognition_dict["related_topic"] = cognition_dict.pop("related_topics", [])
        
        # 创建认知
        created_cognition = await cognition_db.create_cognition(cognition_dict)
        
        # 转换为响应格式
        return convert_mongodb_to_response(created_cognition, None, "zh")
    except Exception as e:
        logger.error(f"创建认知失败: {str(e)}")
        raise HTTPException(status_code=500, detail="创建认知失败")

@router.put("/{cognition_id}", response_model=SupabaseCognitionResponse)
async def update_cognition(
    cognition_id: str,
    cognition_data: CognitionCreate,
    current_admin: UserDB = Depends(get_current_admin_user),
    cognition_db: CognitionDatabase = Depends(get_cognition_database)
):
    """更新认知（仅管理员）"""
    try:
        # 准备更新数据
        update_dict = cognition_data.model_dump(exclude_unset=True)
        
        # 转换字段名：前端的related_topics -> 数据库的"related_topic"
        if "related_topics" in update_dict:
            update_dict["related_topic"] = update_dict.pop("related_topics", [])
        
        # 更新认知
        updated_cognition = await cognition_db.update_cognition(cognition_id, update_dict)
        
        if not updated_cognition:
            raise HTTPException(status_code=404, detail="认知不存在")
        
        # 转换为响应格式
        return convert_mongodb_to_response(updated_cognition, None, "zh")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新认知失败: {str(e)}")
        raise HTTPException(status_code=500, detail="更新认知失败")

@router.delete("/{cognition_id}")
async def delete_cognition(
    cognition_id: str,
    current_admin: UserDB = Depends(get_current_admin_user),
    cognition_db: CognitionDatabase = Depends(get_cognition_database)
):
    """删除认知（仅管理员）"""
    try:
        success = await cognition_db.delete_cognition(cognition_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="认知不存在")
        
        return {"message": "认知删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除认知失败: {str(e)}")
        raise HTTPException(status_code=500, detail="删除认知失败")

@router.post("/{cognition_id}/comment")
async def add_comment(
    cognition_id: str,
    comment_data: CommentCreate,
    current_user: UserDB = Depends(get_current_active_user),
    cognition_db: CognitionDatabase = Depends(get_cognition_database)
):
    """添加评论"""
    try:
        comment_dict = {
            "user_id": str(current_user.id),
            "username": current_user.username,
            "content": comment_data.content,
            "parent_id": comment_data.parent_id,
            "reply_to_username": comment_data.reply_to_username
        }
        
        success = await cognition_db.add_comment(cognition_id, comment_dict)
        
        if not success:
            raise HTTPException(status_code=404, detail="认知不存在")
        
        return {"message": "评论添加成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加评论失败: {str(e)}")
        raise HTTPException(status_code=500, detail="添加评论失败")

@router.post("/{cognition_id}/vote")
async def vote_cognition(
    cognition_id: str,
    vote_data: VoteRequest,
    current_user: UserDB = Depends(get_current_active_user),
    cognition_db: CognitionDatabase = Depends(get_cognition_database)
):
    """投票"""
    try:
        success = await cognition_db.update_vote(cognition_id, str(current_user.id), vote_data.vote_type)
        
        if not success:
            raise HTTPException(status_code=404, detail="认知不存在")
        
        return {"message": "投票成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"投票失败: {str(e)}")
        raise HTTPException(status_code=500, detail="投票失败")

@router.get("/{cognition_id}/vote")
async def get_user_vote(
    cognition_id: str,
    current_user: UserDB = Depends(get_current_active_user),
    cognition_db: CognitionDatabase = Depends(get_cognition_database)
):
    """获取用户投票状态"""
    try:
        vote_type = await cognition_db.get_user_vote(cognition_id, str(current_user.id))
        return {"vote_type": vote_type}
    except Exception as e:
        logger.error(f"获取用户投票状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取投票状态失败")

@router.post("/{cognition_id}/favorite")
async def add_to_favorites(
    cognition_id: str,
    favorite_data: FavoriteRequest,
    current_user: UserDB = Depends(get_current_active_user),
    favorites_db: CognitionFavoritesDatabase = Depends(get_cognition_favorites_database)
):
    """添加到收藏夹"""
    try:
        success = await favorites_db.add_to_favorites(
            str(current_user.id),
            cognition_id,
            favorite_data.collection_id
        )
        
        if not success:
            raise HTTPException(status_code=500, detail="添加到收藏夹失败")
        
        return {"message": "收藏成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加到收藏夹失败: {str(e)}")
        raise HTTPException(status_code=500, detail="收藏失败")

@router.delete("/{cognition_id}/favorite")
async def remove_from_favorites(
    cognition_id: str,
    collection_id: Optional[str] = Query(None, description="收藏夹ID，不提供则移除所有收藏"),
    current_user: UserDB = Depends(get_current_active_user),
    favorites_db: CognitionFavoritesDatabase = Depends(get_cognition_favorites_database)
):
    """从收藏夹中移除"""
    try:
        success = await favorites_db.remove_from_favorites(
            str(current_user.id),
            cognition_id,
            collection_id
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="收藏记录不存在")
        
        return {"message": "移除收藏成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"移除收藏失败: {str(e)}")
        raise HTTPException(status_code=500, detail="移除收藏失败")

@router.get("/{cognition_id}/favorite-status", response_model=FavoriteStatusResponse)
async def get_favorite_status(
    cognition_id: str,
    current_user: UserDB = Depends(get_current_active_user),
    favorites_db: CognitionFavoritesDatabase = Depends(get_cognition_favorites_database)
):
    """获取认知收藏状态"""
    try:
        status = await favorites_db.get_favorite_status(str(current_user.id), cognition_id)
        return FavoriteStatusResponse(**status)
    except Exception as e:
        logger.error(f"获取收藏状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取收藏状态失败") 

@router.post("/synthesize", response_model=SupabaseCognitionResponse)
async def synthesize_cognitions(
    request: SynthesisRequest,
    current_user: UserDB = Depends(get_current_active_user),
    cognition_db: CognitionDatabase = Depends(get_cognition_database)
):
    """
    合成多个认知。
    """
    try:
        if len(request.cognition_ids) < 2:
            raise HTTPException(status_code=400, detail="至少需要两个认知才能合成")

        cognitions_to_synthesize = []
        for cog_id in request.cognition_ids:
            cognition = await cognition_db.get_cognition_by_id(cog_id)
            if cognition:
                cognitions_to_synthesize.append(cognition)
        
        if len(cognitions_to_synthesize) != len(request.cognition_ids):
            raise HTTPException(status_code=404, detail="一个或多个指定的认知不存在")

        # 调用LLM进行认知合成
        synthesis_result = await synthesize_cognitions_by_llm([
            {
                "question": cognition.get("question_zh") or cognition.get("question_en"),
                "answer": cognition.get("answer_zh") or cognition.get("answer_en")
            } for cognition in cognitions_to_synthesize
        ])

        if not synthesis_result:
            raise HTTPException(status_code=500, detail="合成失败，请稍后重试")

        # 构建包含丰富信息的合成认知数据
        synthesized_cognition_data = {
            "id": f"synth_{get_cn_time().isoformat()}",
            "abstract_zh": synthesis_result.get("abstract", ""),
            "question_zh": synthesis_result.get("question", ""),
            "answer_zh": synthesis_result.get("answer", ""),
            "think_zh": synthesis_result.get("think", ""),
            "source": "认知合成",
            "author_id": str(current_user.id),
            "author_name": current_user.username,
            "created_at": get_cn_time(),
            "updated_at": get_cn_time(),
            "raw_at": get_cn_time(),
            "likes": 0,
            "neutral": 0,
            "dislikes": 0,
            "comments": [],
            "votes": [],
            "tag": "合成",
            # 新增的合成相关字段
            "source_analysis": synthesis_result.get("source_analysis", ""),
            "contradictions": synthesis_result.get("contradictions", ""),
            "synthesis_reasoning": synthesis_result.get("synthesis_reasoning", ""),
            "source_cognition_ids": request.cognition_ids  # 记录源认知ID
        }

        return convert_mongodb_to_response(synthesized_cognition_data)

    except HTTPException:
        raise
    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"合成认知失败: {str(e)}")
        raise HTTPException(status_code=500, detail="合成认知失败") 

# 已读状态相关API
@router.post("/{cognition_id}/read-status", response_model=ReadStatusResponse)
async def set_read_status(
    cognition_id: str,
    read_data: ReadStatusRequest,
    current_user: UserDB = Depends(get_current_active_user),
    read_status_db: CognitionReadStatusDatabase = Depends(get_cognition_read_status_database)
):
    """设置认知的已读状态"""
    try:
        status = await read_status_db.set_read_status(
            str(current_user.id), 
            cognition_id, 
            read_data.is_read
        )
        return ReadStatusResponse(
            is_read=status.get("is_read", False),
            read_at=status.get("read_at")
        )
    except Exception as e:
        logger.error(f"设置已读状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail="设置已读状态失败")

@router.get("/{cognition_id}/read-status", response_model=ReadStatusResponse)
async def get_read_status(
    cognition_id: str,
    current_user: UserDB = Depends(get_current_active_user),
    read_status_db: CognitionReadStatusDatabase = Depends(get_cognition_read_status_database)
):
    """获取认知的已读状态"""
    try:
        status = await read_status_db.get_read_status(str(current_user.id), cognition_id)
        return ReadStatusResponse(
            is_read=status.get("is_read", False),
            read_at=status.get("read_at")
        )
    except Exception as e:
        logger.error(f"获取已读状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取已读状态失败") 