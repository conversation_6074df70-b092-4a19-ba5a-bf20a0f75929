# Redis配置文件示例
# 用于DAIR项目的Redis实例

# 网络配置
bind 0.0.0.0
port 6379
timeout 300
keepalive 60

# 内存管理
maxmemory 256mb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1      # 900秒内至少1个key改变则进行快照
save 300 10     # 300秒内至少10个key改变则进行快照  
save 60 10000   # 60秒内至少10000个key改变则进行快照

# AOF持久化（可选，提供更好的数据安全性）
appendonly yes
appendfsync everysec
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 日志配置
loglevel notice
logfile ""

# 客户端连接
maxclients 1000

# 数据库数量
databases 16

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 安全配置
# requirepass your_password_here  # 取消注释并设置密码

# 性能优化
tcp-keepalive 60
tcp-backlog 511 