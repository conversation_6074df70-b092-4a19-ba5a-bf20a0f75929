from typing import Optional
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase, AsyncIOMotorCollection
from fastapi import Depends
import logging

from backend.config import settings

logger = logging.getLogger(__name__)

class MongoDBService:
    """MongoDB服务类，提供统一的数据库连接管理"""
    
    def __init__(self):
        self._client: Optional[AsyncIOMotorClient] = None
        self._db: Optional[AsyncIOMotorDatabase] = None
        self._connected = False
    
    async def connect(self) -> None:
        """建立MongoDB连接"""
        if self._connected:
            return
            
        try:
            # 使用用户名和密码连接MongoDB
            mongo_uri = settings.MONGO_URI
            if "mongodb://" in mongo_uri and "@" not in mongo_uri:
                # 如果URI没有包含认证信息，添加认证
                mongo_uri = mongo_uri.replace("mongodb://", "mongodb://dair:Dair1234@")
            
            # 设置连接超时
            self._client = AsyncIOMotorClient(
                mongo_uri,
                serverSelectionTimeoutMS=5000,  # 5秒超时
                connectTimeoutMS=10000,        # 10秒连接超时
                socketTimeoutMS=45000,         # 45秒套接字超时
                maxPoolSize=100                # 连接池大小
            )
            
            self._db = self._client[settings.MONGO_DB_NAME]
            self._connected = True
            logger.info("MongoDB连接建立成功")
        except Exception as e:
            logger.error(f"MongoDB连接失败: {e}")
            self._client = None
            self._db = None
            self._connected = False
            raise RuntimeError(f"无法连接到MongoDB: {str(e)}")
    
    async def disconnect(self) -> None:
        """关闭MongoDB连接"""
        if self._client:
            self._client.close()
            self._client = None
            self._db = None
            self._connected = False
            logger.info("MongoDB连接已关闭")
    
    def get_database(self) -> AsyncIOMotorDatabase:
        """获取数据库对象"""
        if not self._connected or self._db is None:
            raise RuntimeError("MongoDB未连接，请先调用connect()")
        return self._db
    
    def get_collection(self, collection_name: str) -> AsyncIOMotorCollection:
        """获取集合对象"""
        if not self._connected or self._db is None:
            raise RuntimeError("MongoDB未连接，请先调用connect()")
        return self._db[collection_name]
    
    @property
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self._connected


# 全局MongoDB服务实例
_mongodb_service = MongoDBService()


async def get_mongodb_service() -> MongoDBService:
    """获取MongoDB服务实例（依赖注入）"""
    if not _mongodb_service.is_connected:
        await _mongodb_service.connect()
    return _mongodb_service


async def get_mongodb_database() -> AsyncIOMotorDatabase:
    """获取MongoDB数据库对象（依赖注入）"""
    service = await get_mongodb_service()
    return service.get_database()


def get_mongodb_collection(collection_name: str):
    """获取MongoDB集合对象的依赖注入工厂函数"""
    async def _get_collection() -> AsyncIOMotorCollection:
        service = await get_mongodb_service()
        return service.get_collection(collection_name)
    return _get_collection


# 应用启动时连接
async def startup_mongodb():
    """应用启动时连接MongoDB"""
    await get_mongodb_service()


# 应用关闭时断开连接
async def shutdown_mongodb():
    """应用关闭时断开MongoDB连接"""
    await _mongodb_service.disconnect() 