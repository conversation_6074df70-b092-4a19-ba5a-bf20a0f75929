# MongoDB依赖注入迁移指南

## 概述

为了解决MongoDB连接管理的问题，我们引入了依赖注入模式。这样可以：

1. **统一连接管理**：整个应用共享一个MongoDB连接池
2. **更好的测试支持**：可以轻松注入Mock对象进行测试
3. **解耦架构**：Repository不再直接依赖具体的连接实现
4. **向后兼容**：现有代码可以继续工作

## 新的架构

### 1. MongoDB服务层 (`backend/db/dependencies.py`)
```python
from backend.db.dependencies import get_mongodb_service, get_mongodb_collection

# 获取MongoDB服务
service = await get_mongodb_service()

# 获取特定集合
collection = await get_mongodb_collection("users")()
```

### 2. Repository基类 (`backend/db/repository.py`)
```python
from backend.db.repository import BaseRepository

# 使用依赖注入的Repository
class UserRepository(BaseRepository[UserModel]):
    def __init__(self, collection: AsyncIOMotorCollection):
        super().__init__(collection, UserModel)
```

### 3. 依赖注入函数
```python
from fastapi import Depends
from backend.db.dependencies import get_mongodb_collection

async def get_user_repository(
    collection: AsyncIOMotorCollection = Depends(get_mongodb_collection("users"))
) -> UserRepository:
    return UserRepository(collection)
```

## 迁移步骤

### 旧代码（不推荐）
```python
class UserRepository(MongoDBBase[UserDB]):
    def __init__(self):
        super().__init__(collection_name="users", model_class=UserDB)
    
    async def custom_operation(self):
        # 直接访问collection
        result = await self.collection.update_one(...)
```

### 新代码（推荐）
```python
class UserRepository(MongoDBBase[UserDB]):
    def __init__(self, collection: Optional[AsyncIOMotorCollection] = None):
        if collection:
            # 使用依赖注入
            super().__init__(collection_name="users", model_class=UserDB, injected_collection=collection)
        else:
            # 向后兼容
            super().__init__(collection_name="users", model_class=UserDB)
    
    async def custom_operation(self):
        # 使用_collection属性
        result = await self._collection.update_one(...)

# 依赖注入函数
async def get_user_repository(
    collection: AsyncIOMotorCollection = Depends(get_mongodb_collection("users"))
) -> UserRepository:
    return UserRepository(collection)
```

### FastAPI路由中使用
```python
from fastapi import APIRouter, Depends

@router.get("/users/{user_id}")
async def get_user(
    user_id: str,
    user_repo: UserRepository = Depends(get_user_repository)
):
    return await user_repo.get_by_id(user_id)
```

## 兼容性说明

### 向后兼容
- 现有的 `MongoDBBase` 继承类可以继续工作
- `self.collection` 属性仍然可用（通过属性代理）
- 不需要立即修改所有现有代码

### 推荐的迁移路径
1. **第一阶段**：修改 `self.collection` 为 `self._collection`
2. **第二阶段**：添加依赖注入支持
3. **第三阶段**：在新的路由中使用依赖注入
4. **第四阶段**：逐步迁移现有路由

## 测试支持

### Mock MongoDB集合
```python
import pytest
from unittest.mock import AsyncMock

@pytest.fixture
async def mock_collection():
    mock = AsyncMock()
    mock.find_one.return_value = {"_id": "123", "name": "test"}
    return mock

async def test_user_repository(mock_collection):
    repo = UserRepository(mock_collection)
    user = await repo.find_one({"name": "test"})
    assert user is not None
```

## 性能优化

### 连接池共享
- 所有Repository共享同一个连接池
- 减少连接开销
- 更好的资源利用

### 懒加载
- 连接只在需要时建立
- 支持应用启动时预连接

## 故障排除

### 常见问题

1. **AttributeError: 'NoneType' object has no attribute 'update_one'**
   - 原因：直接访问 `self.collection` 但连接未建立
   - 解决：使用 `self._collection` 或确保依赖注入正确

2. **连接超时**
   - 检查MongoDB服务是否运行
   - 检查网络连接
   - 调整连接超时参数

3. **依赖注入失败**
   - 确保在 `main.py` 中调用了 `startup_mongodb()`
   - 检查依赖注入函数的参数类型

### 调试技巧
```python
# 检查连接状态
from backend.db.dependencies import get_mongodb_service

service = await get_mongodb_service()
print(f"MongoDB连接状态: {service.is_connected}")
```

## 最佳实践

1. **在FastAPI路由中使用依赖注入**
2. **在测试中使用Mock对象**
3. **避免直接访问 `self.collection`，使用 `self._collection`**
4. **在应用启动时建立连接**
5. **在应用关闭时正确清理连接** 