from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase, AsyncIOMotorCollection
from pydantic import BaseModel
from typing import Any, Dict, List, Optional, TypeVar, Generic, Type, Annotated
from datetime import datetime, timezone, timedelta
from bson import ObjectId
import logging
import time
import inspect
from pydantic_core import core_schema

from backend.config import settings

logger = logging.getLogger(__name__)

# 返回东八区（北京时间）的当前时间
def get_cn_time():
    """返回东八区（北京时间）的当前时间"""
    return datetime.now(timezone(timedelta(hours=8)))

def get_caller_info():
    """获取调用者信息"""
    try:
        # 获取调用堆栈
        frame = inspect.currentframe()
        # 跳过当前函数和被装饰的函数
        caller_frame = frame.f_back.f_back.f_back
        if caller_frame:
            filename = caller_frame.f_code.co_filename
            line_number = caller_frame.f_lineno
            function_name = caller_frame.f_code.co_name
            # 只显示文件名，不显示完整路径
            filename = filename.split('/')[-1]
            return f"{filename}:{function_name}:{line_number}"
        return "未知调用者"
    except:
        return "获取调用者失败"
    finally:
        del frame  # 避免内存泄漏

class PyObjectId(ObjectId):
    """处理MongoDB的ObjectId与Pydantic的兼容"""
    
    @classmethod
    def __get_pydantic_core_schema__(
        cls,
        _source_type: Any,
        _handler: Any,
    ) -> core_schema.CoreSchema:
        """定义Pydantic核心验证模式"""
        return core_schema.json_or_python_schema(
            python_schema=core_schema.union_schema([
                core_schema.is_instance_schema(ObjectId),
                core_schema.chain_schema([
                    core_schema.str_schema(),
                    core_schema.no_info_plain_validator_function(cls.validate),
                ])
            ]),
            json_schema=core_schema.chain_schema([
                core_schema.str_schema(),
                core_schema.no_info_plain_validator_function(cls.validate),
            ]),
            serialization=core_schema.plain_serializer_function_ser_schema(
                lambda instance: str(instance), return_schema=core_schema.str_schema()
            ),
        )
    
    @classmethod
    def validate(cls, value):
        """验证ObjectId"""
        if isinstance(value, ObjectId):
            return value
        if not ObjectId.is_valid(value):
            raise ValueError(f"无效的ObjectId: {value}")
        return ObjectId(value)


class MongoDBManager:
    """MongoDB连接管理器 - 单例模式"""
    _instance = None
    _client = None
    _db = None
    _connected = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def _connect(self):
        """建立MongoDB连接"""
        if self._connected:
            return
            
        try:
            # 使用用户名和密码连接MongoDB
            mongo_uri = settings.MONGO_URI
            if "mongodb://" in mongo_uri and "@" not in mongo_uri:
                # 如果URI没有包含认证信息，添加认证
                mongo_uri = mongo_uri.replace("mongodb://", "mongodb://dair:Dair1234@")
            
            # 设置连接超时 - 针对大文档优化
            self._client = AsyncIOMotorClient(
                mongo_uri,
                serverSelectionTimeoutMS=5000,  # 5秒超时
                connectTimeoutMS=15000,         # 15秒连接超时
                socketTimeoutMS=120000,         # 120秒套接字超时（针对大文档）
                maxPoolSize=50,                 # 减少连接池大小
                maxIdleTimeMS=30000,            # 30秒空闲超时
                waitQueueTimeoutMS=10000        # 10秒等待队列超时
            )
            
            self._db = self._client[settings.MONGO_DB_NAME]
            self._connected = True
            logger.info("MongoDB全局连接建立成功")
        except Exception as e:
            logger.error(f"MongoDB连接失败: {e}")
            # 设置为None以便后续操作可以检查
            self._client = None
            self._db = None
            self._connected = False
            raise RuntimeError(f"无法连接到MongoDB: {str(e)}")
    
    @property
    def client(self):
        if not self._connected:
            self._connect()
        return self._client
    
    @property
    def db(self):
        if not self._connected:
            self._connect()
        return self._db
    
    def get_collection(self, collection_name: str):
        """获取集合对象"""
        if not self._connected:
            self._connect()
        return self._db[collection_name]
    
    def close(self):
        """关闭连接"""
        if self._client:
            self._client.close()
            self._client = None
            self._db = None
            self._connected = False


# 全局连接管理器实例
mongo_manager = MongoDBManager()

T = TypeVar('T', bound=BaseModel)

class MongoDBBase(Generic[T]):
    """
    MongoDB操作基类
    提供基本的CRUD操作
    """
    
    def __init__(self, collection_name: str, model_class: Optional[Type[T]] = None, 
                 injected_collection: Optional[AsyncIOMotorCollection] = None):
        """
        初始化MongoDB连接
        :param collection_name: 集合名称
        :param model_class: 模型类（可选）
        :param injected_collection: 注入的集合对象（可选）
        """
        self.collection_name = collection_name
        self.model_class = model_class
        self._injected_collection = injected_collection
        
        # 保持兼容性，同时支持新的属性访问方式
        self.client = None
        self.db = None
        self._collection_cache = None
        
        if injected_collection is None:
            self.connect()
    
    def connect(self):
        """建立MongoDB连接（现在使用共享连接）"""
        try:
            # 通过管理器获取连接，确保初始化
            _ = mongo_manager.db
            logger.info(f"MongoDB连接成功: {self.collection_name}")
        except Exception as e:
            logger.error(f"MongoDB连接失败: {e}")
            raise RuntimeError(f"无法连接到MongoDB: {str(e)}")
    
    @property
    def _collection(self):
        """获取集合对象"""
        # 如果有注入的集合，优先使用
        if self._injected_collection is not None:
            return self._injected_collection
        return mongo_manager.get_collection(self.collection_name)
    
    @property
    def collection(self):
        """向后兼容的collection属性"""
        if not hasattr(self, '_collection_cache') or self._collection_cache is None:
            self._collection_cache = self._collection
        return self._collection_cache
    
    async def find_one(self, query: Dict) -> Optional[Any]:
        """
        查找单个文档
        :param query: 查询条件
        :return: 文档对象
        """
        caller_info = get_caller_info()
        start_time = time.time()
        # logger.info(f"[MongoDB] 开始查询单个文档 - 调用者: {caller_info}, 集合: {self.collection_name}, 查询条件: {query}")
        
        try:
            document = await self._collection.find_one(query)
            elapsed_time = time.time() - start_time
            logger.info(f"[MongoDB] 查询单个文档完成 - 调用者: {caller_info}, 集合: {self.collection_name}, 耗时: {elapsed_time:.3f}秒, 结果: {'找到' if document else '未找到'}")
            
            if document:
                if self.model_class:
                    return self.model_class.model_validate(document)
                return document
            return None
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"[MongoDB] 查询单个文档错误 - 调用者: {caller_info}, 集合: {self.collection_name}, 耗时: {elapsed_time:.3f}秒, 查询条件: {query}, 错误: {str(e)}")
            raise
    
    async def find_many(self, query: Dict = None, skip: int = 0, limit: int = 500) -> List[Any]:
        """
        查找多个文档
        :param query: 查询条件
        :param skip: 跳过数量
        :param limit: 限制数量
        :return: 文档对象列表
        """
        if query is None:
            query = {}
        
        caller_info = get_caller_info()
        start_time = time.time()
        # logger.info(f"[MongoDB] 开始查询多个文档 - 调用者: {caller_info}, 集合: {self.collection_name}, 查询条件: {query}, skip: {skip}, limit: {limit}")
            
        try:
            cursor = self._collection.find(query).skip(skip).limit(limit)
            documents = await cursor.to_list(length=limit)
            elapsed_time = time.time() - start_time
            logger.info(f"[MongoDB] 查询多个文档完成 - 调用者: {caller_info}, 集合: {self.collection_name}, 耗时: {elapsed_time:.3f}秒, 结果数量: {len(documents)}")
            
            if self.model_class:
                return [self.model_class.model_validate(doc) for doc in documents]
            return documents
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"[MongoDB] 查询多个文档错误 - 调用者: {caller_info}, 集合: {self.collection_name}, 耗时: {elapsed_time:.3f}秒, 错误: {str(e)}")
            raise
    
    async def create(self, data: Dict) -> Any:
        """
        创建文档
        :param data: 文档数据
        :return: 创建的文档对象
        """
        caller_info = get_caller_info()
        start_time = time.time()
        logger.info(f"[MongoDB] 开始创建文档 - 调用者: {caller_info}, 集合: {self.collection_name}")
        
        try:
            data["created_at"] = get_cn_time()
            data["updated_at"] = get_cn_time()
            result = await self._collection.insert_one(data)
            document = await self._collection.find_one({"_id": result.inserted_id})
            elapsed_time = time.time() - start_time
            logger.info(f"[MongoDB] 创建文档完成 - 调用者: {caller_info}, 集合: {self.collection_name}, 耗时: {elapsed_time:.3f}秒, ID: {result.inserted_id}")
            
            if self.model_class:
                return self.model_class.model_validate(document)
            return document
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"[MongoDB] 创建文档错误 - 调用者: {caller_info}, 集合: {self.collection_name}, 耗时: {elapsed_time:.3f}秒, 错误: {str(e)}")
            raise
    
    async def update(self, id: str, data: Dict) -> Optional[Any]:
        """
        更新文档
        :param id: 文档ID
        :param data: 更新数据
        :return: 更新后的文档对象
        """
        caller_info = get_caller_info()
        start_time = time.time()
        logger.info(f"[MongoDB] 开始更新文档 - 调用者: {caller_info}, 集合: {self.collection_name}, ID: {id}")
        
        try:
            data["updated_at"] = get_cn_time()
            await self._collection.update_one(
                {"_id": ObjectId(id)},
                {"$set": data}
            )
            document = await self._collection.find_one({"_id": ObjectId(id)})
            elapsed_time = time.time() - start_time
            logger.info(f"[MongoDB] 更新文档完成 - 调用者: {caller_info}, 集合: {self.collection_name}, 耗时: {elapsed_time:.3f}秒, ID: {id}, 结果: {'成功' if document else '未找到'}")
            
            if document:
                if self.model_class:
                    return self.model_class.model_validate(document)
                return document
            return None
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"[MongoDB] 更新文档错误 - 调用者: {caller_info}, 集合: {self.collection_name}, 耗时: {elapsed_time:.3f}秒, ID: {id}, 错误: {str(e)}")
            raise
    
    async def delete(self, id: str) -> bool:
        """
        删除文档
        :param id: 文档ID
        :return: 是否删除成功
        """
        caller_info = get_caller_info()
        start_time = time.time()
        logger.info(f"[MongoDB] 开始删除文档 - 调用者: {caller_info}, 集合: {self.collection_name}, ID: {id}")
        
        try:
            result = await self._collection.delete_one({"_id": ObjectId(id)})
            elapsed_time = time.time() - start_time
            success = result.deleted_count > 0
            logger.info(f"[MongoDB] 删除文档完成 - 调用者: {caller_info}, 集合: {self.collection_name}, 耗时: {elapsed_time:.3f}秒, ID: {id}, 结果: {'成功' if success else '未找到'}")
            return success
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"[MongoDB] 删除文档错误 - 调用者: {caller_info}, 集合: {self.collection_name}, 耗时: {elapsed_time:.3f}秒, ID: {id}, 错误: {str(e)}")
            raise
            
    async def aggregate(self, pipeline: List[Dict]) -> List[Dict]:
        """
        执行聚合查询
        :param pipeline: 聚合管道
        :return: 聚合结果
        """
        caller_info = get_caller_info()
        start_time = time.time()
        logger.info(f"[MongoDB] 开始聚合查询 - 调用者: {caller_info}, 集合: {self.collection_name}, 管道阶段数: {len(pipeline)}")
        
        try:
            cursor = self._collection.aggregate(pipeline)
            
            # 检查pipeline中是否有$limit阶段，如果有则使用该限制，否则使用None
            limit = None
            for stage in pipeline:
                if "$limit" in stage:
                    limit = stage["$limit"]
                    # 为了安全起见，设置一个合理的上限
                    limit = min(limit, 1000)
                    break
            
            results = await cursor.to_list(length=limit)
            elapsed_time = time.time() - start_time
            logger.info(f"[MongoDB] 聚合查询完成 - 调用者: {caller_info}, 集合: {self.collection_name}, 耗时: {elapsed_time:.3f}秒, 结果数量: {len(results)}, 限制: {limit}")
            return results
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"[MongoDB] 聚合查询错误 - 调用者: {caller_info}, 集合: {self.collection_name}, 耗时: {elapsed_time:.3f}秒, 错误: {str(e)}")
            raise

    async def update_or_create(self, query: Dict, data: Dict) -> T:
        """
        更新或创建文档
        :param query: 查询条件
        :param data: 更新/创建数据
        :return: 更新或创建的文档对象
        """
        caller_info = get_caller_info()
        start_time = time.time()
        logger.info(f"[MongoDB] 开始更新或创建文档 - 调用者: {caller_info}, 集合: {self.collection_name}, 查询条件: {query}")
        
        try:
            # 查找是否存在匹配文档
            document = await self._collection.find_one(query)
            
            if document:
                # 如果存在，更新文档
                data["updated_at"] = get_cn_time()
                # 保留ID值，不在更新中改变
                doc_id = document["_id"]
                await self._collection.update_one(
                    {"_id": doc_id},
                    {"$set": data}
                )
                # 获取更新后的文档
                updated_doc = await self._collection.find_one({"_id": doc_id})
                elapsed_time = time.time() - start_time
                logger.info(f"[MongoDB] 更新或创建文档完成(更新) - 调用者: {caller_info}, 集合: {self.collection_name}, 耗时: {elapsed_time:.3f}秒, ID: {doc_id}")
                
                if self.model_class:
                    return self.model_class.model_validate(updated_doc)
                return updated_doc
            else:
                # 如果不存在，创建新文档
                data["created_at"] = get_cn_time()
                data["updated_at"] = get_cn_time()
                
                # 合并查询条件和数据
                create_data = {**query, **data}
                result = await self._collection.insert_one(create_data)
                new_doc = await self._collection.find_one({"_id": result.inserted_id})
                elapsed_time = time.time() - start_time
                logger.info(f"[MongoDB] 更新或创建文档完成(创建) - 调用者: {caller_info}, 集合: {self.collection_name}, 耗时: {elapsed_time:.3f}秒, ID: {result.inserted_id}")
                
                if self.model_class:
                    return self.model_class.model_validate(new_doc)
                return new_doc
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"[MongoDB] 更新或创建文档错误 - 调用者: {caller_info}, 集合: {self.collection_name}, 耗时: {elapsed_time:.3f}秒, 错误: {str(e)}")
            raise 