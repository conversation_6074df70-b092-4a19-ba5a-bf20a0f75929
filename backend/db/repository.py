from typing import Dict, Any, List, Optional, TypeVar, Generic, Type
from motor.motor_asyncio import AsyncIOMotorCollection
from pydantic import BaseModel
from datetime import datetime, timezone, timedelta
from bson import ObjectId
import logging

from backend.db.mongodb import MongoDBBase, get_cn_time

logger = logging.getLogger(__name__)

T = TypeVar('T', bound=BaseModel)

class BaseRepository(Generic[T]):
    """
    基础Repository类，支持依赖注入
    提供标准的CRUD操作接口
    """
    
    def __init__(self, collection: AsyncIOMotorCollection, model_class: Type[T]):
        """
        初始化Repository
        
        Args:
            collection: 注入的MongoDB集合对象
            model_class: Pydantic模型类
        """
        self.collection = collection
        self.model_class = model_class
        self._mongodb_base = MongoDBBase(
            collection_name="", # 不需要集合名称，因为使用注入的集合
            model_class=model_class,
            injected_collection=collection
        )
    
    async def find_one(self, query: Dict[str, Any]) -> Optional[T]:
        """查找单个文档"""
        return await self._mongodb_base.find_one(query)
    
    async def find_many(self, query: Dict[str, Any] = None, skip: int = 0, limit: int = 500) -> List[T]:
        """查找多个文档"""
        return await self._mongodb_base.find_many(query, skip, limit)
    
    async def create(self, data: Dict[str, Any]) -> T:
        """创建文档"""
        return await self._mongodb_base.create(data)
    
    async def update(self, id: str, data: Dict[str, Any]) -> Optional[T]:
        """更新文档"""
        return await self._mongodb_base.update(id, data)
    
    async def delete(self, id: str) -> bool:
        """删除文档"""
        return await self._mongodb_base.delete(id)
    
    async def update_or_create(self, query: Dict[str, Any], data: Dict[str, Any]) -> T:
        """更新或创建文档"""
        return await self._mongodb_base.update_or_create(query, data)
    
    async def aggregate(self, pipeline: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """执行聚合查询"""
        return await self._mongodb_base.aggregate(pipeline)


# 工厂函数：创建Repository实例
def create_repository(collection: AsyncIOMotorCollection, model_class: Type[T]) -> BaseRepository[T]:
    """
    创建Repository实例的工厂函数
    
    Args:
        collection: MongoDB集合对象
        model_class: Pydantic模型类
        
    Returns:
        Repository实例
    """
    return BaseRepository(collection, model_class) 