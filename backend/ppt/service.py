import os
import asyncio
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime
import uuid
import hashlib

from pptx import Presentation
from loguru import logger

from backend.ppt.models import (
    PPTReviewSession,
    PPTFileInfo,
    PPTReviewReport,
    PPTProgress,
)
from dc_agents.src.deep_cognition.ppt_review_agent import PPTReviewAgent


class PPTAnalysisService:
    """PPT分析服务类"""

    def __init__(self):
        self.upload_dir = Path("uploads/ppt")
        self.upload_dir.mkdir(parents=True, exist_ok=True)

        self.review_dir = Path("ppt_reviews")
        self.review_dir.mkdir(parents=True, exist_ok=True)

        # 活跃的评审会话
        self.active_sessions: Dict[str, PPTReviewAgent] = {}

    async def upload_and_validate_ppt(
        self, file_content: bytes, filename: str, user_id: str
    ) -> PPTFileInfo:
        """
        上传并验证PPT文件
        """
        try:
            # 验证文件类型
            if not filename.lower().endswith((".ppt", ".pptx")):
                raise ValueError("只支持PPT和PPTX文件格式")

            # 验证文件大小 (50MB限制)
            max_size = 50 * 1024 * 1024
            if len(file_content) > max_size:
                raise ValueError(
                    f"文件大小不能超过50MB，当前文件大小：{len(file_content) / 1024 / 1024:.1f}MB"
                )

            # 生成文件路径
            session_id = str(uuid.uuid4())
            file_path = self.upload_dir / f"{session_id}_{filename}"

            # 保存文件
            with open(file_path, "wb") as f:
                f.write(file_content)

            # 验证PPT文件完整性
            total_slides = await self._validate_ppt_file(str(file_path))

            file_info = PPTFileInfo(
                file_name=filename,
                file_path=str(file_path),
                file_size=len(file_content),
                total_slides=total_slides,
                upload_time=datetime.now(),
            )

            logger.info(
                f"PPT文件上传成功: {filename}, 大小: {len(file_content)}字节, 幻灯片数: {total_slides}"
            )
            return file_info

        except Exception as e:
            logger.error(f"PPT文件上传失败: {str(e)}")
            raise ValueError(f"PPT文件处理失败: {str(e)}")

    async def _validate_ppt_file(self, file_path: str) -> int:
        """
        验证PPT文件完整性并返回幻灯片数量
        """
        try:
            prs = Presentation(file_path)
            slide_count = len(prs.slides)

            if slide_count == 0:
                raise ValueError("PPT文件中没有幻灯片")

            if slide_count > 100:
                raise ValueError(
                    f"PPT文件幻灯片数量过多({slide_count}页)，最多支持100页"
                )

            return slide_count

        except Exception as e:
            raise ValueError(f"PPT文件格式无效或损坏: {str(e)}")

    async def create_review_session(
        self,
        user_id: str,
        file_info: PPTFileInfo,
        language: str = "zh",
        review_depth: str = "standard",
        additional_context: str = "",
    ) -> PPTReviewSession:
        """
        创建PPT评审会话
        """
        session_id = str(uuid.uuid4())

        session = PPTReviewSession(
            session_id=session_id,
            user_id=user_id,
            file_info=file_info.dict(),
            status="pending",
            progress=0,
            current_stage="initialization",
            created_at=datetime.now(),
        )

        logger.info(
            f"创建PPT评审会话: {session_id}, 用户: {user_id}, 文件: {file_info.file_name}"
        )
        return session

    async def start_review_process(
        self,
        session: PPTReviewSession,
        language: str = "zh",
        review_depth: str = "standard",
        additional_context: str = "",
    ) -> None:
        """
        启动PPT评审流程
        """
        try:
            # 获取配置
            import yaml

            config_path = Path("dc_agents/config/config.yaml")
            with open(config_path, "r", encoding="utf-8") as f:
                config = yaml.safe_load(f)

            ppt_config = config.get("ppt_agent", {})

            # 创建PPT Review Agent
            agent = PPTReviewAgent(
                name="PPTReviewAgent",
                config=ppt_config,
                conversation_id=session.session_id,
            )

            # 保存到活跃会话
            self.active_sessions[session.session_id] = agent

            # 准备输入数据
            input_data = {
                "file_path": session.file_info["file_path"],
                "language": language,
                "review_depth": review_depth,
                "additional_context": additional_context,
            }

            # 异步执行评审
            asyncio.create_task(self._process_review(session.session_id, input_data))

        except Exception as e:
            logger.error(
                f"启动PPT评审流程失败: session_id={session.session_id}, error={str(e)}"
            )
            raise

    async def _process_review(
        self, session_id: str, input_data: Dict[str, Any]
    ) -> None:
        """
        处理PPT评审流程
        """
        try:
            agent = self.active_sessions.get(session_id)
            if not agent:
                raise ValueError(f"评审会话未找到: {session_id}")

            logger.info(f"开始执行PPT评审: session_id={session_id}")

            # 执行评审
            review_results = []
            async for result in agent.run(input_data):
                review_results.append(result)

                # 处理进度更新
                await self._update_review_progress(session_id, result)

            # 评审完成，生成最终报告
            if review_results:
                final_result = review_results[-1]
                await self._finalize_review(session_id, final_result)

        except Exception as e:
            logger.error(f"PPT评审处理失败: session_id={session_id}, error={str(e)}")
            await self._handle_review_error(session_id, str(e))
        finally:
            # 清理活跃会话
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]

    async def _update_review_progress(
        self, session_id: str, result: Dict[str, Any]
    ) -> None:
        """
        更新评审进度
        """
        stage = result.get("stage", "unknown")
        content_type = result.get("content_type", "unknown")
        content = result.get("content", "")

        # 定义阶段进度映射
        progress_map = {
            "initialization": 10,
            "material_ingestion": 20,
            "macro_structure_analysis": 40,
            "page_by_page_analysis": 70,
            "comprehensive_evaluation": 85,
            "final_report": 100,
        }

        progress = progress_map.get(stage, 0)

        # 构建进度消息
        stage_messages = {
            "initialization": "正在初始化投资人角色...",
            "material_ingestion": "正在分析PPT文件结构...",
            "macro_structure_analysis": "正在进行宏观结构分析...",
            "page_by_page_analysis": "正在进行逐页详细分析...",
            "comprehensive_evaluation": "正在生成综合评估...",
            "final_report": "正在生成最终评审报告...",
        }

        message = stage_messages.get(stage, f"处理中... ({stage})")

        # 这里应该更新到数据库或缓存中
        # 暂时记录日志
        logger.info(f"评审进度更新: {session_id} - {stage} ({progress}%): {message}")

    async def _finalize_review(
        self, session_id: str, final_result: Dict[str, Any]
    ) -> None:
        """
        完成评审并保存结果
        """
        try:
            content = final_result.get("content", {})

            # 构建评审报告
            report = PPTReviewReport(
                session_id=session_id,
                overall_impression=content.get("overall_impression", ""),
                top_strengths=content.get("top_strengths", []),
                top_concerns=content.get("top_concerns", []),
                page_reviews=content.get("page_reviews", []),
                key_questions=content.get("key_questions", []),
                recommendations=content.get("recommendations", {}),
                investment_readiness=content.get("investment_readiness", {}),
                final_report_text=content.get("final_report_text", ""),
                generated_at=datetime.now(),
            )

            # 保存报告到文件
            report_path = self.review_dir / f"review_{session_id}.json"
            with open(report_path, "w", encoding="utf-8") as f:
                import json

                json.dump(report.dict(), f, ensure_ascii=False, indent=2, default=str)

            logger.info(
                f"PPT评审完成: session_id={session_id}, 报告已保存到: {report_path}"
            )

        except Exception as e:
            logger.error(f"保存评审结果失败: session_id={session_id}, error={str(e)}")
            raise

    async def _handle_review_error(self, session_id: str, error_message: str) -> None:
        """
        处理评审错误
        """
        logger.error(f"PPT评审失败: session_id={session_id}, error={error_message}")

        # 这里应该更新会话状态为失败
        # 暂时记录日志

    async def get_review_report(self, session_id: str) -> Optional[PPTReviewReport]:
        """
        获取评审报告
        """
        try:
            report_path = self.review_dir / f"review_{session_id}.json"

            if not report_path.exists():
                return None

            with open(report_path, "r", encoding="utf-8") as f:
                import json

                data = json.load(f)
                return PPTReviewReport(**data)

        except Exception as e:
            logger.error(f"获取评审报告失败: session_id={session_id}, error={str(e)}")
            return None

    async def handle_qa_question(
        self, session_id: str, question: str, context: str = ""
    ) -> str:
        """
        处理交互式问答
        """
        try:
            # 检查是否有活跃的agent
            agent = self.active_sessions.get(session_id)

            if not agent:
                # 尝试重新创建agent并加载评审报告
                report = await self.get_review_report(session_id)
                if not report:
                    raise ValueError("评审报告未找到，无法进行问答")

                # 重新创建agent
                import yaml

                config_path = Path("dc_agents/config/config.yaml")
                with open(config_path, "r", encoding="utf-8") as f:
                    config = yaml.safe_load(f)

                ppt_config = config.get("ppt_agent", {})
                agent = PPTReviewAgent(
                    name="PPTReviewAgent", config=ppt_config, conversation_id=session_id
                )

                # 设置评审会话状态
                agent.review_session["review_report"] = report.dict()

            # 处理问答
            answer = await agent.handle_qa_question(question, context)

            logger.info(
                f"PPT评审问答完成: session_id={session_id}, question_length={len(question)}"
            )
            return answer

        except Exception as e:
            logger.error(f"PPT评审问答失败: session_id={session_id}, error={str(e)}")
            raise ValueError(f"问答处理失败: {str(e)}")

    async def cleanup_old_files(self, days: int = 7) -> None:
        """
        清理过期文件
        """
        try:
            import time

            current_time = time.time()
            cutoff_time = current_time - (days * 24 * 60 * 60)

            # 清理上传的PPT文件
            for file_path in self.upload_dir.glob("*"):
                if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    logger.info(f"删除过期PPT文件: {file_path}")

            # 清理评审报告文件
            for file_path in self.review_dir.glob("*.json"):
                if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    logger.info(f"删除过期评审报告: {file_path}")

        except Exception as e:
            logger.error(f"清理过期文件失败: {str(e)}")


class PPTServiceManager:
    """PPT服务管理器 - 单例模式"""

    _instance = None
    _service = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def get_service(self) -> PPTAnalysisService:
        if self._service is None:
            self._service = PPTAnalysisService()
        return self._service


# 全局服务实例
ppt_service_manager = PPTServiceManager()


def get_ppt_analysis_service() -> PPTAnalysisService:
    """获取PPT分析服务实例"""
    return ppt_service_manager.get_service()
