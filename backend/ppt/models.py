from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field
from datetime import datetime


class PPTReviewRequest(BaseModel):
    """PPT评审请求模型"""

    language: str = Field("zh", description="语言偏好 (zh/en)")
    review_depth: str = Field(
        "standard", description="评审深度 (basic/standard/comprehensive)"
    )
    additional_context: Optional[str] = Field(None, description="额外上下文信息")


class PPTReviewResponse(BaseModel):
    """PPT评审响应模型"""

    session_id: str = Field(..., description="评审会话ID")
    message: str = Field(..., description="响应消息")
    status: str = Field(..., description="评审状态")


class PPTReviewSession(BaseModel):
    """PPT评审会话模型"""

    session_id: str = Field(..., description="会话ID")
    user_id: str = Field(..., description="用户ID")
    file_info: Dict[str, Any] = Field(..., description="文件信息")
    status: str = Field(
        ..., description="评审状态"
    )  # pending, processing, completed, failed
    progress: int = Field(0, description="进度百分比 (0-100)")
    current_stage: str = Field("pending", description="当前阶段")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")


class PPTReviewReport(BaseModel):
    """PPT评审报告模型"""

    session_id: str = Field(..., description="会话ID")
    overall_impression: str = Field(..., description="总体印象")
    top_strengths: List[str] = Field(..., description="主要优势")
    top_concerns: List[str] = Field(..., description="主要担忧")
    page_reviews: List[Dict[str, Any]] = Field(..., description="逐页评审")
    key_questions: List[str] = Field(..., description="关键问题")
    recommendations: Dict[str, Any] = Field(..., description="改进建议")
    investment_readiness: Dict[str, Any] = Field(..., description="可投资性评估")
    final_report_text: str = Field(..., description="最终报告文本")
    generated_at: datetime = Field(default_factory=datetime.now, description="生成时间")


class PPTStatusResponse(BaseModel):
    """PPT评审状态响应模型"""

    session_id: str = Field(..., description="会话ID")
    status: str = Field(..., description="评审状态")
    progress: int = Field(..., description="进度百分比")
    current_stage: str = Field(..., description="当前阶段")
    message: str = Field(..., description="状态消息")
    result: Optional[Dict[str, Any]] = Field(None, description="结果信息")
    error: Optional[str] = Field(None, description="错误信息")


class PPTQuestionRequest(BaseModel):
    """PPT问答请求模型"""

    question: str = Field(..., description="用户问题")
    context: Optional[str] = Field(None, description="相关上下文")


class PPTQuestionResponse(BaseModel):
    """PPT问答响应模型"""

    answer: str = Field(..., description="回答内容")
    session_id: str = Field(..., description="会话ID")
    generated_at: datetime = Field(default_factory=datetime.now, description="生成时间")


class PPTFileInfo(BaseModel):
    """PPT文件信息模型"""

    file_name: str = Field(..., description="文件名")
    file_path: str = Field(..., description="文件路径")
    file_size: int = Field(..., description="文件大小 (bytes)")
    total_slides: Optional[int] = Field(None, description="总幻灯片数")
    upload_time: datetime = Field(default_factory=datetime.now, description="上传时间")


class PPTProgress(BaseModel):
    """PPT评审进度模型"""

    stage: str = Field(..., description="当前阶段")
    progress: int = Field(..., description="进度百分比 (0-100)")
    message: str = Field(..., description="进度消息")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")


class PPTReviewHistory(BaseModel):
    """PPT评审历史模型"""

    session_id: str = Field(..., description="会话ID")
    file_name: str = Field(..., description="文件名")
    review_date: datetime = Field(..., description="评审日期")
    status: str = Field(..., description="评审状态")
    summary: str = Field(..., description="评审摘要")


class PPTListResponse(BaseModel):
    """PPT评审列表响应模型"""

    reviews: List[PPTReviewHistory] = Field(..., description="评审历史列表")
    total: int = Field(..., description="总数")
    page: int = Field(1, description="当前页")
    page_size: int = Field(20, description="每页大小")


class PPTFeedbackRequest(BaseModel):
    """PPT评审反馈请求模型"""

    session_id: str = Field(..., description="会话ID")
    rating: int = Field(..., description="评分 (1-5)")
    feedback: str = Field(..., description="反馈内容")
    improvement_suggestions: Optional[str] = Field(None, description="改进建议")


# 保留一些原有的模型以兼容现有代码
class PPTGenerateRequest(BaseModel):
    """PPT生成请求模型 (兼容性保留)"""

    requirement: str = Field(..., description="PPT需求描述")
    template_type: Optional[str] = Field("business", description="模板类型")
    slide_count: Optional[int] = Field(10, description="期望幻灯片数量")
    language: Optional[str] = Field("zh", description="语言偏好")
    conversation_id: Optional[str] = Field(None, description="会话ID")


class PPTGenerateResponse(BaseModel):
    """PPT生成响应模型 (兼容性保留)"""

    session_id: str = Field(..., description="会话ID")
    message: str = Field(..., description="响应消息")
    ppt_path: Optional[str] = Field(None, description="生成的PPT文件路径")
    slide_count: Optional[int] = Field(None, description="实际生成的幻灯片数量")


class PPTSlideInfo(BaseModel):
    """PPT幻灯片信息模型"""

    title: str = Field(..., description="幻灯片标题")
    content: str = Field(..., description="幻灯片内容")
    slide_type: str = Field(..., description="幻灯片类型")
    slide_number: int = Field(..., description="幻灯片序号")


class PPTStructure(BaseModel):
    """PPT结构模型"""

    title: str = Field(..., description="演示文稿标题")
    subtitle: Optional[str] = Field(None, description="副标题")
    slides: List[Dict[str, Any]] = Field(..., description="幻灯片列表")
    total_slides: int = Field(..., description="总幻灯片数")


class PPTDownloadRequest(BaseModel):
    """PPT下载请求模型"""

    session_id: str = Field(..., description="会话ID")
    ppt_path: str = Field(..., description="PPT文件路径")


class PPTTemplateInfo(BaseModel):
    """PPT模板信息模型"""

    template_id: str = Field(..., description="模板ID")
    template_name: str = Field(..., description="模板名称")
    template_type: str = Field(..., description="模板类型")
    description: str = Field(..., description="模板描述")
    preview_image: Optional[str] = Field(None, description="预览图片")


class PPTEditRequest(BaseModel):
    """PPT编辑请求模型"""

    session_id: str = Field(..., description="会话ID")
    edit_type: str = Field(..., description="编辑类型")
    slide_number: Optional[int] = Field(None, description="幻灯片序号")
    edit_content: str = Field(..., description="编辑内容")
