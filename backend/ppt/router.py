import asyncio
import os
import uuid
from typing import Dict, List, Optional, Any
from pathlib import Path

from fastapi import (
    APIRouter,
    HTTPException,
    Depends,
    BackgroundTasks,
    Response,
    UploadFile,
    File,
    Form,
)
from fastapi.responses import FileResponse
from loguru import logger

from backend.auth.dependencies import get_current_active_user, check_user_token_budget
from backend.auth.models import UserDB
from backend.ppt.models import (
    PPTReviewRequest,
    PPTReviewResponse,
    PPTStatusResponse,
    PPTListResponse,
    PPTQuestionRequest,
    PPTQuestionResponse,
    PPTFeedbackRequest,
    PPTReviewHistory,
)

# 导入PPT Review Agent相关模块
from dc_agents.src.deep_cognition.ppt_review_agent import PPTReviewAgent
from dc_agents.src.services.llm_service import LLMService

router = APIRouter(prefix="/api/ppt", tags=["PPT Review"])

# 存储活跃的PPT评审会话
active_ppt_sessions: Dict[str, Dict[str, Any]] = {}

# PPT评审状态
ppt_review_status: Dict[str, Dict[str, Any]] = {}


def get_ppt_services():
    """获取PPT Review Agent所需的服务"""
    import yaml
    from pathlib import Path

    # 加载配置
    config_path = Path("dc_agents/config/config.yaml")
    with open(config_path, "r", encoding="utf-8") as f:
        config = yaml.safe_load(f)

    return config


def get_ppt_review_agent(conversation_id: str = None):
    """创建PPT Review Agent实例"""
    config = get_ppt_services()
    ppt_config = config.get("ppt_agent", {})

    return PPTReviewAgent(
        name="PPTReviewAgent", config=ppt_config, conversation_id=conversation_id
    )


@router.post("/review", response_model=PPTReviewResponse)
async def start_ppt_review(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    language: str = Form("zh"),
    review_depth: str = Form("standard"),
    additional_context: str = Form(""),
    current_user: UserDB = Depends(check_user_token_budget),
):
    """
    启动PPT评审
    """
    try:
        # 验证文件类型
        if not file.filename.lower().endswith((".ppt", ".pptx")):
            raise HTTPException(status_code=400, detail="只支持PPT和PPTX文件格式")

        # 生成会话ID
        session_id = str(uuid.uuid4())

        # 保存上传的文件
        upload_dir = Path("uploads/ppt")
        upload_dir.mkdir(parents=True, exist_ok=True)

        file_path = upload_dir / f"{session_id}_{file.filename}"
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        # 初始化会话状态，并创建一个完成事件
        ppt_review_status[session_id] = {
            "status": "initializing",
            "progress": 0,
            "current_stage": "initialization",
            "message": "正在初始化PPT评审任务...",
            "user_id": str(current_user.id),
            "file_info": {
                "file_name": file.filename,
                "file_path": str(file_path),
                "file_size": len(content),
            },
            "created_at": asyncio.get_event_loop().time(),
            "completion_event": asyncio.Event(),
        }

        # 启动后台评审任务
        background_tasks.add_task(
            run_ppt_review_task,
            session_id,
            str(file_path),
            language,
            review_depth,
            additional_context,
            str(current_user.id),
        )

        logger.info(
            f"PPT评审任务已启动: session_id={session_id}, user_id={str(current_user.id)}, file={file.filename}"
        )

        return PPTReviewResponse(
            session_id=session_id,
            message="PPT评审任务已启动，请通过状态查询接口获取进度",
            status="started",
        )

    except Exception as e:
        logger.error(f"启动PPT评审任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"启动PPT评审任务失败: {str(e)}")


@router.get("/review/{session_id}/status", response_model=PPTStatusResponse)
async def get_ppt_review_status(
    session_id: str, current_user: UserDB = Depends(get_current_active_user)
):
    """
    获取PPT评审状态
    """
    if session_id not in ppt_review_status:
        raise HTTPException(status_code=404, detail="PPT评审会话未找到")

    status_info = ppt_review_status[session_id]

    # 检查用户权限
    if status_info.get("user_id") != str(current_user.id):
        raise HTTPException(status_code=403, detail="无权访问此PPT评审会话")

    # 确保result字段是字典类型或None
    result_data = status_info.get("result")
    if result_data is not None and not isinstance(result_data, dict):
        # 如果result不是字典，将其包装在一个字典中
        result_data = {"content": result_data}

    return PPTStatusResponse(
        session_id=session_id,
        status=status_info.get("status", "unknown"),
        progress=status_info.get("progress", 0),
        current_stage=status_info.get("current_stage", "unknown"),
        message=status_info.get("message", ""),
        result=result_data,
        error=status_info.get("error"),
    )


@router.get("/review/{session_id}/report")
async def get_ppt_review_report(
    session_id: str, current_user: UserDB = Depends(get_current_active_user)
):
    """
    获取PPT评审报告
    """
    logger.info(f"获取报告请求: session_id={session_id}, user_id={str(current_user.id)}")
    
    if session_id not in ppt_review_status:
        logger.warning(f"会话未找到: session_id={session_id}")
        raise HTTPException(status_code=404, detail="PPT评审会话未找到")

    status_info = ppt_review_status[session_id]
    
    logger.info(f"会话状态信息: {status_info}")

    # 检查用户权限
    if status_info.get("user_id") != str(current_user.id):
        logger.warning(f"权限检查失败: 会话用户={status_info.get('user_id')}, 当前用户={str(current_user.id)}")
        raise HTTPException(status_code=403, detail="无权访问此PPT评审会话")

    # 如果任务已完成但结果尚不可用，请等待后台任务发出完成信号
    if status_info.get("status") == "completed" and not status_info.get("result"):
        try:
            logger.debug(f"报告结果为空，等待 {session_id} 的完成信号...")
            completion_event = status_info.get("completion_event")
            if completion_event:
                await asyncio.wait_for(completion_event.wait(), timeout=5)
            # 事件触发后，重新获取最新的状态信息
            status_info = ppt_review_status[session_id]
            logger.debug(f"信号已收到或超时，重新获取的状态: {status_info.get('status')}")
        except asyncio.TimeoutError:
            logger.warning(f"等待报告生成超时: session_id={session_id}")
            raise HTTPException(status_code=408, detail="等待报告生成超时，请稍后重试。")

    # 检查状态
    if status_info.get("status") != "completed":
        logger.warning(f"评审尚未完成: 当前状态={status_info.get('status')}")
        # 更明确地告知前端，任务正在处理中
        if status_info.get("status") == "processing":
            raise HTTPException(status_code=202, detail="PPT评审正在处理中，请稍后重试")
        raise HTTPException(status_code=400, detail="PPT评审尚未完成或已失败")

    result = status_info.get("result", {})
    logger.info(f"返回结果类型: {type(result)}")
    logger.info(f"返回结果内容: {result}")
    
    # 如果结果为空，尝试从raw_results中获取
    if not result and "raw_results" in status_info:
        logger.info("result为空，尝试从raw_results获取")
        raw_results = status_info.get("raw_results", [])
        logger.info(f"raw_results: {raw_results}")
        
        # 尝试找到包含评审报告的结果
        for raw_result in reversed(raw_results):
            if isinstance(raw_result, dict) and raw_result.get("stage") == "final_report":
                result = raw_result.get("content", raw_result)
                logger.info(f"从raw_results找到final_report: {result}")
                break

    # 如果在所有检查后结果仍然为空，则返回一个明确的错误
    if not result or result == {}:
        logger.error(f"评审已完成但报告为空: session_id={session_id}")
        raise HTTPException(status_code=404, detail="评审已完成，但未能生成有效报告。")

    return result


@router.post("/review/{session_id}/question", response_model=PPTQuestionResponse)
async def ask_question_about_review(
    session_id: str,
    request: PPTQuestionRequest,
    current_user: UserDB = Depends(get_current_active_user),
):
    """
    对评审结果进行交互式问答
    """
    if session_id not in ppt_review_status:
        raise HTTPException(status_code=404, detail="PPT评审会话未找到")

    status_info = ppt_review_status[session_id]

    # 检查用户权限
    if status_info.get("user_id") != str(current_user.id):
        raise HTTPException(status_code=403, detail="无权访问此PPT评审会话")

    # 检查状态
    if status_info.get("status") != "completed":
        raise HTTPException(status_code=400, detail="PPT评审尚未完成，无法进行问答")

    try:
        # 创建PPT Review Agent实例进行问答
        agent = get_ppt_review_agent(session_id)

        # 设置评审会话状态
        if "review_report" in status_info.get("result", {}):
            agent.review_session["review_report"] = status_info["result"][
                "review_report"
            ]

        # 处理问答
        answer = await agent.handle_qa_question(request.question, request.context or "")

        return PPTQuestionResponse(answer=answer, session_id=session_id)

    except Exception as e:
        logger.error(f"PPT评审问答失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"问答处理失败: {str(e)}")


@router.get("/reviews/history", response_model=PPTListResponse)
async def get_user_ppt_reviews(
    skip: int = 0,
    limit: int = 20,
    current_user: UserDB = Depends(get_current_active_user),
):
    """
    获取用户的PPT评审历史
    """
    user_reviews = []

    # 从内存状态中获取用户的评审历史
    for session_id, status_info in ppt_review_status.items():
        if status_info.get("user_id") == str(current_user.id):
            file_info = status_info.get("file_info", {})
            user_reviews.append(
                PPTReviewHistory(
                    session_id=session_id,
                    file_name=file_info.get("file_name", "unknown"),
                    review_date=status_info.get("created_at", ""),
                    status=status_info.get("status", "unknown"),
                    summary=status_info.get("message", ""),
                )
            )

    # 分页处理
    total = len(user_reviews)
    user_reviews = user_reviews[skip : skip + limit]

    return PPTListResponse(
        reviews=user_reviews, total=total, page=skip // limit + 1, page_size=limit
    )


@router.post("/feedback/{session_id}")
async def submit_ppt_feedback(
    session_id: str,
    request: PPTFeedbackRequest,
    current_user: UserDB = Depends(get_current_active_user),
):
    """
    提交PPT评审反馈
    """
    if session_id not in ppt_review_status:
        raise HTTPException(status_code=404, detail="PPT评审会话未找到")

    status_info = ppt_review_status[session_id]

    # 检查用户权限
    if status_info.get("user_id") != str(current_user.id):
        raise HTTPException(status_code=403, detail="无权访问此PPT评审会话")

    try:
        # 保存反馈信息
        feedback_info = {
            "session_id": session_id,
            "user_id": str(current_user.id),
            "rating": request.rating,
            "feedback": request.feedback,
            "improvement_suggestions": request.improvement_suggestions,
            "submitted_at": asyncio.get_event_loop().time(),
        }

        # 添加到会话状态中
        if "feedback" not in status_info:
            status_info["feedback"] = []
        status_info["feedback"].append(feedback_info)

        logger.info(
            f"收到PPT评审反馈: session_id={session_id}, rating={request.rating}"
        )

        return {"message": "反馈提交成功", "session_id": session_id}

    except Exception as e:
        logger.error(f"提交PPT评审反馈失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提交反馈失败: {str(e)}")


async def run_ppt_review_task(
    session_id: str,
    file_path: str,
    language: str,
    review_depth: str,
    additional_context: str,
    user_id: str,
):
    """
    运行PPT评审任务
    """
    try:
        logger.info(f"开始执行PPT评审任务: session_id={session_id}")

        # 创建PPT Review Agent
        agent = get_ppt_review_agent(session_id)

        # 准备输入数据
        input_data = {
            "file_path": file_path,
            "language": language,
            "review_depth": review_depth,
            "additional_context": additional_context,
        }

        # 执行评审
        review_results = []
        async for result in agent.run(input_data):
            review_results.append(result)

            # 更新进度
            stage = result.get("stage", "unknown")
            content_type = result.get("content_type", "unknown")

            if stage in [
                "initialization",
                "material_ingestion",
                "macro_structure_analysis",
                "page_by_page_analysis",
                "comprehensive_evaluation",
                "final_report",
            ]:
                progress_map = {
                    "initialization": 10,
                    "material_ingestion": 20,
                    "macro_structure_analysis": 40,
                    "page_by_page_analysis": 70,
                    "comprehensive_evaluation": 85,
                    "final_report": 100,
                }

                ppt_review_status[session_id].update(
                    {
                        "status": (
                            "processing" if progress_map[stage] < 100 else "completed"
                        ),
                        "progress": progress_map[stage],
                        "current_stage": stage,
                        "message": result.get("content", "处理中..."),
                    }
                )

        # 评审完成，保存结果
        final_result_data = review_results[-1] if review_results else {}
        
        logger.info(f"PPT评审完成，开始保存最终结果: session_id={session_id}")
        logger.debug(f"原始final_result_data: {final_result_data}")

        # 提取并展平最终报告，使其成为一个扁平的对象
        report_payload = {}
        if isinstance(final_result_data, dict) and "content" in final_result_data:
            content_wrapper = final_result_data.get("content", {})
            if isinstance(content_wrapper, dict):
                # 提取核心报告对象
                if "report" in content_wrapper and isinstance(content_wrapper["report"], dict):
                    report_payload.update(content_wrapper["report"])
                
                # 提取并合并 token_usage
                if "token_usage" in content_wrapper:
                    report_payload["token_usage"] = content_wrapper["token_usage"]
        
        logger.info(f"最终处理后的 report_payload 长度: {len(str(report_payload))}")
        logger.debug(f"最终 report_payload 内容: {report_payload}")

        ppt_review_status[session_id].update(
            {
                "status": "completed",
                "progress": 100,
                "current_stage": "completed",
                "message": "PPT评审已完成",
                "result": report_payload,
                "raw_results": review_results,
                "completed_at": asyncio.get_event_loop().time(),
            }
        )

        logger.info(f"PPT评审任务完成: session_id={session_id}")

    except Exception as e:
        logger.error(f"PPT评审任务失败: session_id={session_id}, error={str(e)}")
        logger.exception("详细错误堆栈信息:")

        ppt_review_status[session_id].update(
            {
                "status": "failed",
                "progress": 0,
                "current_stage": "error",
                "message": "评审失败",
                "error": str(e),
            }
        )
    finally:
        # 确保无论成功还是失败，都设置事件，以唤醒等待的请求
        if session_id in ppt_review_status:
            completion_event = ppt_review_status[session_id].get("completion_event")
            if completion_event:
                completion_event.set()
                logger.debug(f"已设置完成事件: session_id={session_id}")


# 保留一些原有的接口以兼容现有代码
@router.get("/templates")
async def get_ppt_templates(current_user: UserDB = Depends(get_current_active_user)):
    """
    获取可用的PPT模板列表 (兼容性保留)
    """
    templates = [
        {
            "template_id": "investor_review",
            "template_name": "投资人评审模板",
            "template_type": "review",
            "description": "基于资深VC投资合伙人视角的专业评审模板",
        }
    ]
    return templates
