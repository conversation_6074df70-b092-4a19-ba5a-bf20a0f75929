FROM python:3.12-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update 
RUN apt-get install -y \
    build-essential \
    python3-dev \
    python3-pip \
    python3-setuptools \
    python3-wheel \
    python3-cffi \
    libcairo2 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libgdk-pixbuf2.0-0 \
    libffi-dev \
    shared-mime-info \
    libglib2.0-0 \
    pango1.0-tools \
    libpango1.0-dev \
    libharfbuzz-dev \
    libcairo2-dev \
    fonts-liberation \
    libjpeg-dev \
    libpng-dev \
    libtiff-dev \
    libwebp-dev \
    fonts-noto-cjk \
    libx11-dev \
    libxext-dev \
    libxrender-dev \
    libxt-dev \
    libxft-dev \
    libfreetype6-dev \
    libfontconfig1-dev \
    libxml2-dev \
    libxslt1-dev \
    zlib1g-dev
RUN rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY backend/requirements.txt .

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制 backend 目录内容
COPY backend/ /app/backend

# 复制 dc_agents 目录内容
COPY dc_agents/ /app/dc_agents

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "backend.main:app", "--host", "0.0.0.0", "--port", "8000"]