# DC Backend 文档

⚠️ 请阅读本文档，请使用cursor进行开发

## 代码结构

### 数据库模块 (db)
- **描述**：数据库相关代码
- **依赖**：无
- **文件**：
  - `mongodb.py`：对任意数据库名进行增删改查的基类
  - `session_store.py`：实际上是conversation相关的存储逻辑

    > 计划将移动到conversation模块下面

### 测试模块 (tests)
- **描述**：存放单元/集成测试

  > 还在build中，首先实现对core功能进行冒烟测试
- **依赖**：无
- **文件**：待完善

### 认证模块 (auth)
- **描述**：用户验证
- **依赖**：
  - db
- **文件**：
  - `dependencies.py`：通过token确认用户身份等函数
  - `router.py`：暴露的登陆注册接口

### 对话模块 (conversation)
- **描述**：处理对话

  > 建设中，计划把main当中的逻辑进行分拆，与conversation相关的
- **依赖**：
  - dc_agent(外部)
  - auth
  - db
- **文件**：待完善

### 全局配置
- **文件**：`config.py`
- **描述**：全局配置

### 程序入口
- **文件**： `main.py`
- **描述**： 目前所有对话相关的核心代码都在这里，需要拆分至该在的模块

## 模块结构
推荐将模块在后端尽量分开封装，各自独立一个文件夹，例如
```
- auth
    - router.py # 路由出口（若为业务接口）
    - dependencies.py # 向其他模块暴露依赖
    - service.py 
    - utils.py
    - config.py # 服务配置
    - schemas.py # Pydantic 模型
    - ....
```
在开发迭代过程当中，以这种模块，具有可靠的扩展性
当前正在向这种状态过渡，渐进重构中，最好暂时留下旧接口，逐步切换过去


## 依赖库配置
- requirements： python 依赖库请在 requirement当中进行修改
- Dockerfile：其它系统依赖请在 Dockerfile 当中进行修改
