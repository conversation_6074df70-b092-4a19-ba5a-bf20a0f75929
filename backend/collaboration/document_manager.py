"""
Y.js文档管理器

负责管理Y.js文档的生命周期，包括创建、同步、持久化等功能。
"""

import asyncio
import logging
from typing import Dict, Optional, List, Any, Callable
from datetime import datetime, timedelta
try:
    import pycrdt as Y
    HAS_YCRDT = True
except ImportError:
    HAS_YCRDT = False
    # 如果pycrdt不可用，使用模拟实现
    class MockYDoc:
        def __init__(self):
            self._content = ""
        
        def get_text(self, name):
            return self
        
        def clear(self):
            self._content = ""
        
        def insert(self, pos, text):
            self._content = self._content[:pos] + text + self._content[pos:]
        
        def __str__(self):
            return self._content
        
        def observe(self, callback):
            pass
    
    class Y:
        Doc = MockYDoc
        
        @staticmethod
        def get_update(doc):
            return b''
        
        @staticmethod
        def get_state(doc):
            return b''

from .models import DocumentSnapshot, YjsUpdate, UserAwareness
from .storage import MongoCollaborationStorage

logger = logging.getLogger(__name__)

class YDocumentManager:
    """Y.js文档管理器"""
    
    def __init__(self, storage: MongoCollaborationStorage):
        self.storage = storage
        self._documents: Dict[str, Any] = {}  # 内存中的文档实例
        self._document_locks: Dict[str, asyncio.Lock] = {}  # 文档操作锁
        self._observers: Dict[str, List[Callable]] = {}  # 文档观察者
        self._last_access: Dict[str, datetime] = {}  # 最后访问时间
        self._cleanup_task: Optional[asyncio.Task] = None
        
        # 配置
        self.auto_save_interval = 10.0  # 自动保存间隔（秒）
        self.document_timeout = 3600.0  # 文档超时时间（秒）
        
    async def start(self):
        """启动文档管理器"""
        logger.info("启动Y.js文档管理器")
        if HAS_YCRDT:
            logger.info("使用pycrdt作为Y.js后端")
        else:
            logger.warning("pycrdt不可用，使用模拟实现")
        # 启动清理任务
        self._cleanup_task = asyncio.create_task(self._cleanup_documents())
        
    async def stop(self):
        """停止文档管理器"""
        logger.info("停止Y.js文档管理器")
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # 保存所有文档
        for doc_id in list(self._documents.keys()):
            await self._save_document(doc_id)
            
    async def get_document(self, document_id: str) -> Any:
        """获取或创建Y.js文档实例"""
        if document_id not in self._document_locks:
            self._document_locks[document_id] = asyncio.Lock()
            
        async with self._document_locks[document_id]:
            if document_id in self._documents:
                self._last_access[document_id] = datetime.utcnow()
                return self._documents[document_id]
                
            # 创建新的文档实例
            ydoc = Y.Doc()
            
            # 尝试从存储中加载文档
            try:
                stored_doc = await self.storage.load_document(document_id)
                if stored_doc:
                    logger.info(f"从存储加载文档: {document_id}")
                    # 应用存储的Y.js状态
                    if stored_doc.get('yjs_state') and HAS_YCRDT:
                        # pycrdt使用不同的API来应用更新
                        with ydoc.begin_transaction() as txn:
                            # 这里需要根据pycrdt的实际API调整
                            pass
                    elif stored_doc.get('content'):
                        # 兼容旧格式，从纯文本创建Y.js文档
                        text = ydoc.get_text('content')
                        text.clear()
                        text.insert(0, stored_doc['content'])
                else:
                    logger.info(f"创建新文档: {document_id}")
                    # 初始化空文档
                    text = ydoc.get_text('content')
                    text.insert(0, '')
                    
            except Exception as e:
                logger.error(f"加载文档失败 {document_id}: {e}")
                # 创建空文档作为降级方案
                text = ydoc.get_text('content')
                text.insert(0, '')
            
            # 设置文档观察者
            self._setup_document_observer(document_id, ydoc)
            
            # 缓存文档
            self._documents[document_id] = ydoc
            self._last_access[document_id] = datetime.utcnow()
            
            return ydoc
    
    def _setup_document_observer(self, document_id: str, ydoc: Any):
        """设置文档变更观察者"""
        if not HAS_YCRDT:
            return
            
        def on_update(update: bytes):
            """文档更新回调"""
            asyncio.create_task(self._handle_document_update(
                document_id, update, None
            ))
            
        # 监听文档更新
        try:
            ydoc.observe(on_update)
        except Exception as e:
            logger.error(f"设置文档观察者失败 {document_id}: {e}")
        
    async def _handle_document_update(self, document_id: str, update: bytes, origin: Any = None):
        """处理文档更新"""
        try:
            # 广播给其他连接（由上层处理）
            if hasattr(self, '_update_callback') and self._update_callback:
                await self._update_callback(document_id, update, origin)
                
            # 延迟保存（避免频繁IO）
            await self._schedule_save(document_id)
            
        except Exception as e:
            logger.error(f"处理文档更新失败 {document_id}: {e}")
    
    async def apply_update(self, document_id: str, update: bytes, origin: Any = None) -> bool:
        """应用Y.js更新到文档"""
        try:
            ydoc = await self.get_document(document_id)
            
            if HAS_YCRDT:
                # pycrdt的应用更新方式
                with ydoc.begin_transaction() as txn:
                    # 这里需要根据pycrdt的实际API调整
                    pass
            
            logger.debug(f"应用更新到文档 {document_id}")
            return True
            
        except Exception as e:
            logger.error(f"应用更新失败 {document_id}: {e}")
            return False
    
    async def get_document_state(self, document_id: str) -> bytes:
        """获取文档的完整状态"""
        ydoc = await self.get_document(document_id)
        if HAS_YCRDT:
            return Y.get_state(ydoc)
        return b''
    
    async def get_document_content(self, document_id: str) -> str:
        """获取文档的纯文本内容"""
        ydoc = await self.get_document(document_id)
        text = ydoc.get_text('content')
        return str(text)
    
    async def _schedule_save(self, document_id: str):
        """延迟保存文档"""
        # 简单的延迟保存策略
        await asyncio.sleep(self.auto_save_interval)
        await self._save_document(document_id)
    
    async def _save_document(self, document_id: str):
        """保存文档到存储"""
        try:
            if document_id not in self._documents:
                return
                
            ydoc = self._documents[document_id]
            
            # 获取Y.js状态和纯文本内容
            if HAS_YCRDT:
                yjs_state = Y.get_state(ydoc)
            else:
                yjs_state = b''
            content = str(ydoc.get_text('content'))
            
            # 保存到存储
            await self.storage.save_document(document_id, {
                'content': content,
                'yjs_state': yjs_state,
                'updated_at': datetime.utcnow()
            })
            
            logger.debug(f"文档已保存: {document_id}")
            
        except Exception as e:
            logger.error(f"保存文档失败 {document_id}: {e}")
    
    async def _cleanup_documents(self):
        """清理不活跃的文档"""
        while True:
            try:
                await asyncio.sleep(300)  # 每5分钟清理一次
                
                current_time = datetime.utcnow()
                inactive_docs = []
                
                for doc_id, last_access in self._last_access.items():
                    if (current_time - last_access).total_seconds() > self.document_timeout:
                        inactive_docs.append(doc_id)
                
                for doc_id in inactive_docs:
                    await self._remove_document(doc_id)
                    
                if inactive_docs:
                    logger.info(f"清理了 {len(inactive_docs)} 个不活跃的文档")
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"文档清理任务出错: {e}")
    
    async def _remove_document(self, document_id: str):
        """移除文档实例"""
        try:
            # 保存文档
            await self._save_document(document_id)
            
            # 从内存中移除
            if document_id in self._documents:
                del self._documents[document_id]
            if document_id in self._last_access:
                del self._last_access[document_id]
            if document_id in self._document_locks:
                del self._document_locks[document_id]
            if document_id in self._observers:
                del self._observers[document_id]
                
            logger.debug(f"文档已从内存移除: {document_id}")
            
        except Exception as e:
            logger.error(f"移除文档失败 {document_id}: {e}")
    
    def set_update_callback(self, callback: Callable):
        """设置文档更新回调"""
        self._update_callback = callback
    
    async def create_snapshot(self, document_id: str, created_by: str) -> DocumentSnapshot:
        """创建文档快照"""
        ydoc = await self.get_document(document_id)
        content = await self.get_document_content(document_id)
        yjs_state = await self.get_document_state(document_id)
        
        snapshot = DocumentSnapshot(
            document_id=document_id,
            version=1,  # TODO: 实现版本管理
            content=content,
            yjs_state=yjs_state,
            created_by=created_by
        )
        
        # 保存快照
        await self.storage.save_snapshot(snapshot)
        
        return snapshot 