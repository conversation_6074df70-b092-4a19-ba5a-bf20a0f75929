"""
MongoDB协作存储适配器

处理Y.js文档与现有MongoDB数据库的集成。
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from motor.motor_asyncio import AsyncIOMotorDatabase, AsyncIOMotorCollection
from bson import ObjectId
from .models import DocumentSnapshot, CollaborationSession, UserAwareness

logger = logging.getLogger(__name__)

class MongoCollaborationStorage:
    """MongoDB协作存储适配器"""
    
    def __init__(self, database: AsyncIOMotorDatabase):
        self.db = database
        self.documents_collection: AsyncIOMotorCollection = database.documents
        self.snapshots_collection: AsyncIOMotorCollection = database.collaboration_snapshots
        self.sessions_collection: AsyncIOMotorCollection = database.collaboration_sessions
        
    async def load_document(self, document_id: str) -> Optional[Dict[str, Any]]:
        """从MongoDB加载文档"""
        try:
            # 尝试用ObjectId查询
            try:
                query = {"_id": ObjectId(document_id)}
            except:
                # 如果不是有效的ObjectId，使用字符串查询
                query = {"_id": document_id}
                
            document = await self.documents_collection.find_one(query)
            
            if not document:
                logger.warning(f"文档不存在: {document_id}")
                return None
                
            return {
                'content': document.get('content', ''),
                'yjs_state': document.get('yjs_state'),
                'updated_at': document.get('updated_at'),
                'version': document.get('version', 1)
            }
            
        except Exception as e:
            logger.error(f"加载文档失败 {document_id}: {e}")
            return None
    
    async def save_document(self, document_id: str, data: Dict[str, Any]) -> bool:
        """保存文档到MongoDB"""
        try:
            # 准备更新数据
            update_data = {
                'content': data.get('content', ''),
                'updated_at': data.get('updated_at', datetime.utcnow())
            }
            
            # 保存Y.js状态（如果提供）
            if 'yjs_state' in data:
                update_data['yjs_state'] = data['yjs_state']
            
            # 尝试用ObjectId更新
            try:
                query = {"_id": ObjectId(document_id)}
            except:
                query = {"_id": document_id}
            
            result = await self.documents_collection.update_one(
                query,
                {"$set": update_data},
                upsert=False  # 不创建新文档，只更新现有文档
            )
            
            if result.matched_count > 0:
                logger.debug(f"文档已更新: {document_id}")
                return True
            else:
                logger.warning(f"文档不存在，无法更新: {document_id}")
                return False
                
        except Exception as e:
            logger.error(f"保存文档失败 {document_id}: {e}")
            return False
    
    async def save_snapshot(self, snapshot: DocumentSnapshot) -> bool:
        """保存文档快照"""
        try:
            snapshot_data = {
                'document_id': snapshot.document_id,
                'version': snapshot.version,
                'content': snapshot.content,
                'yjs_state': snapshot.yjs_state,
                'created_at': snapshot.created_at,
                'created_by': snapshot.created_by
            }
            
            await self.snapshots_collection.insert_one(snapshot_data)
            logger.debug(f"快照已保存: {snapshot.document_id}:{snapshot.version}")
            return True
            
        except Exception as e:
            logger.error(f"保存快照失败: {e}")
            return False
    
    async def get_snapshots(self, document_id: str, limit: int = 10) -> List[DocumentSnapshot]:
        """获取文档快照历史"""
        try:
            cursor = self.snapshots_collection.find(
                {'document_id': document_id}
            ).sort('created_at', -1).limit(limit)
            
            snapshots = []
            async for doc in cursor:
                snapshot = DocumentSnapshot(
                    document_id=doc['document_id'],
                    version=doc['version'],
                    content=doc['content'],
                    yjs_state=doc['yjs_state'],
                    created_at=doc['created_at'],
                    created_by=doc['created_by']
                )
                snapshots.append(snapshot)
            
            return snapshots
            
        except Exception as e:
            logger.error(f"获取快照失败 {document_id}: {e}")
            return []
    
    async def create_collaboration_session(self, session: CollaborationSession) -> bool:
        """创建协作会话"""
        try:
            session_data = {
                'session_id': session.session_id,
                'document_id': session.document_id,
                'participants': [p.model_dump() for p in session.participants],
                'created_at': session.created_at,
                'last_activity': session.last_activity,
                'is_active': session.is_active
            }
            
            await self.sessions_collection.insert_one(session_data)
            logger.debug(f"协作会话已创建: {session.session_id}")
            return True
            
        except Exception as e:
            logger.error(f"创建协作会话失败: {e}")
            return False
    
    async def update_collaboration_session(self, session_id: str, data: Dict[str, Any]) -> bool:
        """更新协作会话"""
        try:
            await self.sessions_collection.update_one(
                {'session_id': session_id},
                {'$set': data}
            )
            return True
            
        except Exception as e:
            logger.error(f"更新协作会话失败 {session_id}: {e}")
            return False
    
    async def get_collaboration_session(self, session_id: str) -> Optional[CollaborationSession]:
        """获取协作会话"""
        try:
            doc = await self.sessions_collection.find_one({'session_id': session_id})
            if not doc:
                return None
                
            participants = [UserAwareness(**p) for p in doc.get('participants', [])]
            
            return CollaborationSession(
                session_id=doc['session_id'],
                document_id=doc['document_id'],
                participants=participants,
                created_at=doc['created_at'],
                last_activity=doc['last_activity'],
                is_active=doc['is_active']
            )
            
        except Exception as e:
            logger.error(f"获取协作会话失败 {session_id}: {e}")
            return None
    
    async def get_active_sessions(self, document_id: str) -> List[CollaborationSession]:
        """获取文档的活跃协作会话"""
        try:
            cursor = self.sessions_collection.find({
                'document_id': document_id,
                'is_active': True
            })
            
            sessions = []
            async for doc in cursor:
                participants = [UserAwareness(**p) for p in doc.get('participants', [])]
                session = CollaborationSession(
                    session_id=doc['session_id'],
                    document_id=doc['document_id'],
                    participants=participants,
                    created_at=doc['created_at'],
                    last_activity=doc['last_activity'],
                    is_active=doc['is_active']
                )
                sessions.append(session)
            
            return sessions
            
        except Exception as e:
            logger.error(f"获取活跃会话失败 {document_id}: {e}")
            return []
    
    async def end_collaboration_session(self, session_id: str) -> bool:
        """结束协作会话"""
        try:
            await self.sessions_collection.update_one(
                {'session_id': session_id},
                {
                    '$set': {
                        'is_active': False,
                        'ended_at': datetime.utcnow()
                    }
                }
            )
            logger.debug(f"协作会话已结束: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"结束协作会话失败 {session_id}: {e}")
            return False
    
    async def cleanup_inactive_sessions(self, timeout_hours: int = 24) -> int:
        """清理不活跃的协作会话"""
        try:
            timeout_time = datetime.utcnow() - timedelta(hours=timeout_hours)
            
            result = await self.sessions_collection.update_many(
                {
                    'is_active': True,
                    'last_activity': {'$lt': timeout_time}
                },
                {
                    '$set': {
                        'is_active': False,
                        'ended_at': datetime.utcnow()
                    }
                }
            )
            
            logger.info(f"清理了 {result.modified_count} 个不活跃的协作会话")
            return result.modified_count
            
        except Exception as e:
            logger.error(f"清理不活跃会话失败: {e}")
            return 0
    
    async def get_document_permissions(self, document_id: str, user_id: str) -> List[str]:
        """获取用户对文档的权限"""
        try:
            # 尝试用ObjectId查询
            try:
                query = {"_id": ObjectId(document_id)}
            except:
                query = {"_id": document_id}
                
            document = await self.documents_collection.find_one(query)
            
            if not document:
                return []
            
            permissions = []
            
            # 检查是否是文档所有者
            if document.get('owner_id') == user_id or document.get('creator_id') == user_id:
                permissions = ['read', 'write', 'admin']
            # 检查是否在协作者列表中
            elif user_id in document.get('collaborators', []):
                permissions = ['read', 'write']
            # 检查具体权限设置
            elif 'permissions' in document:
                perm_dict = document['permissions']
                if user_id in perm_dict.get('admin', []):
                    permissions = ['read', 'write', 'admin']
                elif user_id in perm_dict.get('write', []):
                    permissions = ['read', 'write']
                elif user_id in perm_dict.get('read', []):
                    permissions = ['read']
            
            return permissions
            
        except Exception as e:
            logger.error(f"获取文档权限失败 {document_id}: {e}")
            return [] 