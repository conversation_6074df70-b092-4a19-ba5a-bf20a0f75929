"""
协作编辑相关的数据模型
"""

from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

class ConnectionStatus(str, Enum):
    """连接状态枚举"""
    CONNECTING = "connecting"
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    SYNCING = "syncing"
    SYNCED = "synced"
    ERROR = "error"

class UserAwareness(BaseModel):
    """用户感知信息模型"""
    user_id: str
    username: str
    avatar: Optional[str] = None
    cursor_position: Optional[int] = None
    selection_start: Optional[int] = None
    selection_end: Optional[int] = None
    color: str = "#7c4dff"  # 用户在协作中的颜色标识
    last_active: datetime = Field(default_factory=datetime.utcnow)

class DocumentSnapshot(BaseModel):
    """文档快照模型"""
    document_id: str
    version: int
    content: str  # 纯文本内容（兼容现有系统）
    yjs_state: bytes  # Y.js二进制状态
    created_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: str

class CollaborationSession(BaseModel):
    """协作会话模型"""
    session_id: str
    document_id: str
    participants: List[UserAwareness] = []
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_activity: datetime = Field(default_factory=datetime.utcnow)
    is_active: bool = True

class YjsUpdate(BaseModel):
    """Y.js更新消息模型"""
    document_id: str
    update_data: bytes
    origin: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    user_id: Optional[str] = None

class CollaborationMessage(BaseModel):
    """协作消息模型"""
    type: str  # 'sync', 'awareness', 'update', 'auth'
    document_id: str
    data: Any
    user_id: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class CollaborationToken(BaseModel):
    """协作认证令牌模型"""
    user_id: str
    document_id: str
    permissions: List[str] = ["read", "write"]
    expires_at: datetime
    session_id: Optional[str] = None

class DocumentPermission(str, Enum):
    """文档权限枚举"""
    READ = "read"
    WRITE = "write"
    ADMIN = "admin"
    COMMENT = "comment"

class CollaborationConfig(BaseModel):
    """协作配置模型"""
    max_connections_per_document: int = 50
    heartbeat_interval: float = 30.0
    sync_timeout: float = 5.0
    auto_save_interval: float = 10.0
    enable_awareness: bool = True
    enable_history: bool = True
    max_history_size: int = 100 