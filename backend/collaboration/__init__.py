"""
DAIR协作编辑模块

提供基于Y.js的实时协作编辑功能，兼容现有的MongoDB存储架构。
"""

from .server import CollaborationServer
from .provider import CollaborationProvider  
from .document_manager import YDocumentManager
from .auth import CollaborationAuth
from .storage import MongoCollaborationStorage

__version__ = "1.0.0"
__all__ = [
    "CollaborationServer",
    "CollaborationProvider", 
    "YDocumentManager",
    "CollaborationAuth",
    "MongoCollaborationStorage"
] 