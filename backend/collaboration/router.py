"""
协作编辑路由

提供协作编辑相关的API端点。
"""

import logging
from fastapi import APIRouter, WebSocket, Depends, HTTPException, Body
from typing import Dict, List, Optional
from pydantic import BaseModel

from backend.auth.dependencies import get_current_active_user
from backend.auth.models import UserDB
from backend.db.dependencies import get_mongodb_database
from .server import get_collaboration_server

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/collaboration", tags=["协作编辑"])

# 请求模型
class TokenRequest(BaseModel):
    document_id: str

class SnapshotRequest(BaseModel):
    document_id: str

# WebSocket端点
@router.websocket("/{document_id}")
async def websocket_collaboration(websocket: WebSocket, document_id: str):
    """
    WebSocket协作编辑端点
    
    用于实时协作编辑，客户端通过此端点连接并发送Y.js消息。
    """
    try:
        collaboration_server = get_collaboration_server()
        if not collaboration_server or not collaboration_server.is_running:
            await websocket.close(code=1011, reason="协作服务不可用")
            return
            
        await collaboration_server.handle_websocket(websocket, document_id)
    except Exception as e:
        logger.error(f"WebSocket协作处理失败: {e}")
        try:
            await websocket.close(code=1011, reason="服务器错误")
        except:
            pass

# API端点
@router.post("/token")
async def generate_collaboration_token(
    request: TokenRequest,
    current_user: UserDB = Depends(get_current_active_user)
):
    """
    生成协作编辑token
    
    用于WebSocket连接的认证，确保用户有权限访问指定文档。
    """
    try:
        collaboration_server = get_collaboration_server()
        if not collaboration_server or not collaboration_server.is_running:
            raise HTTPException(status_code=503, detail="协作服务不可用")
            
        token = await collaboration_server.generate_collaboration_token(
            current_user.username,  # 使用username作为user_id
            request.document_id
        )
        
        return {
            "success": True,
            "token": token,
            "document_id": request.document_id,
            "user_id": current_user.username
        }
        
    except HTTPException:
        raise
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except Exception as e:
        logger.error(f"生成协作token失败: {e}")
        raise HTTPException(status_code=500, detail="生成token失败")

@router.get("/{document_id}/content")
async def get_document_content(
    document_id: str,
    current_user: UserDB = Depends(get_current_active_user)
):
    """
    获取文档内容
    
    返回文档的当前纯文本内容。
    """
    try:
        collaboration_server = get_collaboration_server()
        if not collaboration_server or not collaboration_server.is_running:
            raise HTTPException(status_code=503, detail="协作服务不可用")
            
        content = await collaboration_server.get_document_content(document_id)
        
        return {
            "success": True,
            "document_id": document_id,
            "content": content
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档内容失败: {e}")
        raise HTTPException(status_code=500, detail="获取文档内容失败")

@router.get("/{document_id}/collaborators")
async def get_active_collaborators(
    document_id: str,
    current_user: UserDB = Depends(get_current_active_user)
):
    """
    获取活跃的协作者
    
    返回当前正在编辑文档的用户列表及其状态信息。
    """
    try:
        collaboration_server = get_collaboration_server()
        if not collaboration_server or not collaboration_server.is_running:
            raise HTTPException(status_code=503, detail="协作服务不可用")
            
        collaborators = await collaboration_server.get_active_collaborators(document_id)
        
        return {
            "success": True,
            "document_id": document_id,
            "collaborators": collaborators,
            "total": len(collaborators)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取协作者失败: {e}")
        raise HTTPException(status_code=500, detail="获取协作者失败")

@router.post("/{document_id}/snapshot")
async def create_document_snapshot(
    document_id: str,
    current_user: UserDB = Depends(get_current_active_user)
):
    """
    创建文档快照
    
    保存文档的当前状态作为快照，用于版本管理和恢复。
    """
    try:
        collaboration_server = get_collaboration_server()
        snapshot = await collaboration_server.create_document_snapshot(
            document_id,
            current_user.username
        )
        
        return {
            "success": True,
            "snapshot": {
                "document_id": snapshot.document_id,
                "version": snapshot.version,
                "created_at": snapshot.created_at.isoformat(),
                "created_by": snapshot.created_by
            }
        }
        
    except Exception as e:
        logger.error(f"创建文档快照失败: {e}")
        raise HTTPException(status_code=500, detail="创建快照失败")

@router.get("/{document_id}/snapshots")
async def get_document_snapshots(
    document_id: str,
    limit: int = 10,
    current_user: UserDB = Depends(get_current_active_user)
):
    """
    获取文档快照历史
    
    返回文档的历史快照列表，用于版本查看和回滚。
    """
    try:
        collaboration_server = get_collaboration_server()
        snapshots = await collaboration_server.get_document_snapshots(document_id, limit)
        
        snapshot_list = []
        for snapshot in snapshots:
            snapshot_list.append({
                "version": snapshot.version,
                "created_at": snapshot.created_at.isoformat(),
                "created_by": snapshot.created_by,
                "content_preview": snapshot.content[:100] + "..." if len(snapshot.content) > 100 else snapshot.content
            })
        
        return {
            "success": True,
            "document_id": document_id,
            "snapshots": snapshot_list,
            "total": len(snapshot_list)
        }
        
    except Exception as e:
        logger.error(f"获取文档快照失败: {e}")
        raise HTTPException(status_code=500, detail="获取快照失败")

@router.get("/stats")
async def get_collaboration_stats(
    current_user: UserDB = Depends(get_current_active_user)
):
    """
    获取协作编辑统计信息
    
    返回服务器运行状态和连接统计，用于监控和调试。
    """
    try:
        collaboration_server = get_collaboration_server()
        stats = await collaboration_server.get_server_stats()
        
        return {
            "success": True,
            "stats": stats
        }
        
    except Exception as e:
        logger.error(f"获取协作统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取统计信息失败")

# 健康检查端点
@router.get("/health")
async def collaboration_health_check():
    """
    协作服务健康检查
    
    检查协作服务的运行状态和依赖项。
    """
    try:
        collaboration_server = get_collaboration_server()
        
        # 检查Y.js依赖
        yjs_available = True
        try:
            import ypy
        except ImportError:
            yjs_available = False
        
        if not collaboration_server:
            return {
                "status": "error",
                "message": "协作服务器未初始化",
                "yjs_available": yjs_available,
                "server_running": False
            }
        
        server_running = collaboration_server.is_running
        
        return {
            "status": "ok" if server_running and yjs_available else "degraded",
            "message": "协作服务正常" if server_running else "协作服务不可用",
            "yjs_available": yjs_available,
            "server_running": server_running,
            "features": {
                "real_time_collaboration": server_running and yjs_available,
                "document_snapshots": server_running,
                "user_awareness": server_running and yjs_available
            }
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "error",
            "message": f"健康检查失败: {str(e)}",
            "yjs_available": False,
            "server_running": False
        } 