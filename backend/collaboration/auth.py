"""
协作编辑认证模块

处理协作编辑的JWT token验证和权限检查。
"""

import jwt
import logging
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from .models import CollaborationToken, DocumentPermission
from .storage import MongoCollaborationStorage

logger = logging.getLogger(__name__)

class CollaborationAuth:
    """协作编辑认证管理器"""
    
    def __init__(self, storage: MongoCollaborationStorage, secret_key: str, algorithm: str = "HS256"):
        self.storage = storage
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.token_expire_hours = 24
    
    async def generate_collaboration_token(
        self, 
        user_id: str, 
        document_id: str,
        permissions: Optional[List[str]] = None
    ) -> str:
        """生成协作编辑JWT token"""
        try:
            # 获取用户对文档的权限
            if permissions is None:
                permissions = await self.storage.get_document_permissions(document_id, user_id)
            
            if not permissions:
                raise PermissionError(f"用户 {user_id} 无权限访问文档 {document_id}")
            
            # 创建token payload
            payload = {
                "user_id": user_id,
                "document_id": document_id,
                "permissions": permissions,
                "iat": datetime.utcnow(),
                "exp": datetime.utcnow() + timedelta(hours=self.token_expire_hours)
            }
            
            # 生成JWT token
            token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
            
            logger.debug(f"为用户 {user_id} 生成协作token，文档 {document_id}")
            return token
            
        except Exception as e:
            logger.error(f"生成协作token失败: {e}")
            raise
    
    def verify_collaboration_token(self, token: str) -> Optional[CollaborationToken]:
        """验证协作编辑JWT token"""
        try:
            # 解码JWT token
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # 创建CollaborationToken对象
            collaboration_token = CollaborationToken(
                user_id=payload["user_id"],
                document_id=payload["document_id"],
                permissions=payload["permissions"],
                expires_at=datetime.fromtimestamp(payload["exp"])
            )
            
            # 检查token是否过期
            if collaboration_token.expires_at < datetime.utcnow():
                logger.warning(f"协作token已过期: {collaboration_token.user_id}")
                return None
            
            return collaboration_token
            
        except jwt.ExpiredSignatureError:
            logger.warning("协作token已过期")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"无效的协作token: {e}")
            return None
        except Exception as e:
            logger.error(f"验证协作token失败: {e}")
            return None
    
    async def check_permission(
        self, 
        user_id: str, 
        document_id: str, 
        required_permission: DocumentPermission
    ) -> bool:
        """检查用户是否有指定权限"""
        try:
            permissions = await self.storage.get_document_permissions(document_id, user_id)
            
            # 权限层级：admin > write > read
            permission_levels = {
                DocumentPermission.READ: 1,
                DocumentPermission.WRITE: 2, 
                DocumentPermission.ADMIN: 3,
                DocumentPermission.COMMENT: 1  # 评论权限等同于读权限
            }
            
            required_level = permission_levels.get(required_permission, 0)
            
            # 检查用户权限
            user_level = 0
            for perm in permissions:
                if perm in permission_levels:
                    user_level = max(user_level, permission_levels[perm])
            
            return user_level >= required_level
            
        except Exception as e:
            logger.error(f"检查权限失败 {user_id}@{document_id}: {e}")
            return False
    
    async def authenticate_websocket_message(self, message: Dict) -> Optional[CollaborationToken]:
        """认证WebSocket消息中的token"""
        try:
            # 从消息中提取token
            token = message.get("token")
            if not token:
                logger.warning("WebSocket消息缺少token")
                return None
            
            # 验证token
            collaboration_token = self.verify_collaboration_token(token)
            if not collaboration_token:
                logger.warning("WebSocket消息token验证失败")
                return None
            
            # 检查文档ID匹配
            message_doc_id = message.get("document_id")
            if message_doc_id and message_doc_id != collaboration_token.document_id:
                logger.warning(f"文档ID不匹配: {message_doc_id} vs {collaboration_token.document_id}")
                return None
            
            return collaboration_token
            
        except Exception as e:
            logger.error(f"WebSocket消息认证失败: {e}")
            return None
    
    def create_auth_response(self, success: bool, message: str = "", token: str = "") -> Dict:
        """创建认证响应消息"""
        return {
            "type": "auth_response",
            "success": success,
            "message": message,
            "token": token,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    async def refresh_token(self, old_token: str) -> Optional[str]:
        """刷新协作token"""
        try:
            # 验证旧token（允许已过期）
            try:
                payload = jwt.decode(
                    old_token, 
                    self.secret_key, 
                    algorithms=[self.algorithm],
                    options={"verify_exp": False}  # 不验证过期时间
                )
            except jwt.InvalidTokenError:
                logger.warning("无法刷新无效的token")
                return None
            
            user_id = payload["user_id"]
            document_id = payload["document_id"]
            
            # 重新检查权限
            permissions = await self.storage.get_document_permissions(document_id, user_id)
            if not permissions:
                logger.warning(f"用户 {user_id} 已失去文档 {document_id} 的权限")
                return None
            
            # 生成新token
            new_token = await self.generate_collaboration_token(user_id, document_id, permissions)
            
            logger.debug(f"已刷新用户 {user_id} 的协作token")
            return new_token
            
        except Exception as e:
            logger.error(f"刷新token失败: {e}")
            return None
    
    def extract_user_from_token(self, token: str) -> Optional[Dict[str, str]]:
        """从token中提取用户信息"""
        try:
            collaboration_token = self.verify_collaboration_token(token)
            if collaboration_token:
                return {
                    "user_id": collaboration_token.user_id,
                    "document_id": collaboration_token.document_id,
                    "permissions": collaboration_token.permissions
                }
            return None
            
        except Exception as e:
            logger.error(f"提取用户信息失败: {e}")
            return None 