"""
WebSocket协作提供者

处理实时消息传递和用户感知状态同步。
"""

import json
import asyncio
import logging
from typing import Dict, List, Set, Optional, Any
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect
from .models import CollaborationMessage, UserAwareness, CollaborationSession
from .document_manager import YDocumentManager
from .auth import CollaborationAuth
from .storage import MongoCollaborationStorage

logger = logging.getLogger(__name__)

class CollaborationProvider:
    """WebSocket协作提供者"""
    
    def __init__(
        self, 
        document_manager: YDocumentManager,
        auth: CollaborationAuth,
        storage: MongoCollaborationStorage
    ):
        self.document_manager = document_manager
        self.auth = auth
        self.storage = storage
        
        # 连接管理
        self.connections: Dict[str, WebSocket] = {}  # session_id -> websocket
        self.document_connections: Dict[str, Set[str]] = {}  # document_id -> set of session_ids
        self.user_sessions: Dict[str, str] = {}  # user_id -> session_id
        self.session_info: Dict[str, Dict[str, Any]] = {}  # session_id -> user/document info
        
        # 用户感知状态
        self.awareness_states: Dict[str, Dict[str, UserAwareness]] = {}  # document_id -> user_id -> awareness
        
        # 设置文档管理器的更新回调
        self.document_manager.set_update_callback(self._broadcast_document_update)
    
    async def handle_websocket_connection(self, websocket: WebSocket, document_id: str):
        """处理WebSocket连接"""
        session_id = f"session_{datetime.utcnow().timestamp()}"
        
        try:
            await websocket.accept()
            logger.info(f"WebSocket连接已建立: {session_id}")
            
            # 等待认证消息
            auth_message = await websocket.receive_json()
            
            # 验证认证
            token_info = await self.auth.authenticate_websocket_message(auth_message)
            if not token_info:
                await websocket.send_json({
                    "type": "auth_error",
                    "message": "认证失败"
                })
                await websocket.close()
                return
            
            # 检查文档访问权限
            if token_info.document_id != document_id:
                await websocket.send_json({
                    "type": "auth_error",
                    "message": "文档访问权限不匹配"
                })
                await websocket.close()
                return
            
            # 注册连接
            await self._register_connection(session_id, websocket, token_info)
            
            # 发送认证成功消息
            await websocket.send_json({
                "type": "auth_success",
                "session_id": session_id,
                "user_id": token_info.user_id,
                "document_id": document_id
            })
            
            # 发送初始文档状态
            await self._send_initial_sync(websocket, document_id)
            
            # 处理消息循环
            await self._handle_message_loop(websocket, session_id)
            
        except WebSocketDisconnect:
            logger.info(f"WebSocket连接已断开: {session_id}")
        except Exception as e:
            logger.error(f"WebSocket连接处理失败 {session_id}: {e}")
        finally:
            await self._cleanup_connection(session_id)
    
    async def _register_connection(self, session_id: str, websocket: WebSocket, token_info):
        """注册WebSocket连接"""
        user_id = token_info.user_id
        document_id = token_info.document_id
        
        # 存储连接信息
        self.connections[session_id] = websocket
        self.session_info[session_id] = {
            "user_id": user_id,
            "document_id": document_id,
            "permissions": token_info.permissions,
            "connected_at": datetime.utcnow()
        }
        
        # 更新文档连接映射
        if document_id not in self.document_connections:
            self.document_connections[document_id] = set()
        self.document_connections[document_id].add(session_id)
        
        # 更新用户会话映射
        self.user_sessions[user_id] = session_id
        
        # 初始化用户感知状态
        if document_id not in self.awareness_states:
            self.awareness_states[document_id] = {}
        
        # 创建用户感知信息
        awareness = UserAwareness(
            user_id=user_id,
            username=f"用户{user_id}",  # TODO: 从用户系统获取真实用户名
            color=self._generate_user_color(user_id)
        )
        self.awareness_states[document_id][user_id] = awareness
        
        # 广播用户加入事件
        await self._broadcast_user_joined(document_id, user_id, awareness)
        
        logger.debug(f"用户 {user_id} 加入文档 {document_id}")
    
    async def _cleanup_connection(self, session_id: str):
        """清理WebSocket连接"""
        if session_id not in self.session_info:
            return
        
        session_info = self.session_info[session_id]
        user_id = session_info["user_id"]
        document_id = session_info["document_id"]
        
        # 移除连接
        if session_id in self.connections:
            del self.connections[session_id]
        
        # 更新文档连接映射
        if document_id in self.document_connections:
            self.document_connections[document_id].discard(session_id)
            if not self.document_connections[document_id]:
                del self.document_connections[document_id]
        
        # 更新用户会话映射
        if user_id in self.user_sessions and self.user_sessions[user_id] == session_id:
            del self.user_sessions[user_id]
        
        # 移除用户感知状态
        if document_id in self.awareness_states and user_id in self.awareness_states[document_id]:
            del self.awareness_states[document_id][user_id]
            
            # 广播用户离开事件
            await self._broadcast_user_left(document_id, user_id)
        
        # 清理会话信息
        del self.session_info[session_id]
        
        logger.debug(f"用户 {user_id} 离开文档 {document_id}")
    
    async def _send_initial_sync(self, websocket: WebSocket, document_id: str):
        """发送初始同步数据"""
        try:
            # 获取文档完整状态
            document_state = await self.document_manager.get_document_state(document_id)
            
            # 发送文档状态
            await websocket.send_json({
                "type": "sync_step1",
                "document_id": document_id,
                "state": document_state.hex()  # 将bytes转换为hex字符串
            })
            
            # 发送当前用户感知状态
            if document_id in self.awareness_states:
                awareness_data = {}
                for user_id, awareness in self.awareness_states[document_id].items():
                    awareness_data[user_id] = awareness.model_dump()
                
                await websocket.send_json({
                    "type": "awareness_update",
                    "document_id": document_id,
                    "awareness": awareness_data
                })
                
        except Exception as e:
            logger.error(f"发送初始同步失败 {document_id}: {e}")
    
    async def _handle_message_loop(self, websocket: WebSocket, session_id: str):
        """处理消息循环"""
        while True:
            try:
                message = await websocket.receive_json()
                await self._handle_message(websocket, session_id, message)
                
            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"消息处理失败 {session_id}: {e}")
                break
    
    async def _handle_message(self, websocket: WebSocket, session_id: str, message: Dict):
        """处理单个消息"""
        try:
            message_type = message.get("type")
            
            if message_type == "sync_step2":
                await self._handle_sync_step2(websocket, session_id, message)
            elif message_type == "update":
                await self._handle_update(session_id, message)
            elif message_type == "awareness_update":
                await self._handle_awareness_update(session_id, message)
            elif message_type == "heartbeat":
                await self._handle_heartbeat(websocket, session_id)
            else:
                logger.warning(f"未知消息类型: {message_type}")
                
        except Exception as e:
            logger.error(f"处理消息失败 {session_id}: {e}")
    
    async def _handle_sync_step2(self, websocket: WebSocket, session_id: str, message: Dict):
        """处理同步步骤2"""
        try:
            document_id = message.get("document_id")
            update_data = message.get("update", "")
            
            if update_data:
                # 将hex字符串转换回bytes
                update_bytes = bytes.fromhex(update_data)
                
                # 应用更新到文档
                await self.document_manager.apply_update(document_id, update_bytes, session_id)
            
            # 发送同步完成消息
            await websocket.send_json({
                "type": "sync_complete",
                "document_id": document_id
            })
            
        except Exception as e:
            logger.error(f"处理同步步骤2失败: {e}")
    
    async def _handle_update(self, session_id: str, message: Dict):
        """处理文档更新"""
        try:
            document_id = message.get("document_id")
            update_data = message.get("update", "")
            
            if not update_data:
                return
            
            # 将hex字符串转换回bytes
            update_bytes = bytes.fromhex(update_data)
            
            # 应用更新到文档
            success = await self.document_manager.apply_update(document_id, update_bytes, session_id)
            
            if success:
                # 广播更新给其他用户（由文档管理器的回调处理）
                pass
            
        except Exception as e:
            logger.error(f"处理文档更新失败: {e}")
    
    async def _handle_awareness_update(self, session_id: str, message: Dict):
        """处理用户感知状态更新"""
        try:
            document_id = message.get("document_id")
            awareness_data = message.get("awareness", {})
            
            session_info = self.session_info.get(session_id)
            if not session_info:
                return
                
            user_id = session_info["user_id"]
            
            # 更新用户感知状态
            if document_id in self.awareness_states and user_id in self.awareness_states[document_id]:
                current_awareness = self.awareness_states[document_id][user_id]
                for key, value in awareness_data.items():
                    if hasattr(current_awareness, key):
                        setattr(current_awareness, key, value)
                current_awareness.last_active = datetime.utcnow()
                
                # 广播感知状态更新
                await self._broadcast_awareness_update(document_id, user_id, current_awareness)
            
        except Exception as e:
            logger.error(f"处理感知状态更新失败: {e}")
    
    async def _handle_heartbeat(self, websocket: WebSocket, session_id: str):
        """处理心跳消息"""
        try:
            await websocket.send_json({
                "type": "heartbeat_response",
                "timestamp": datetime.utcnow().isoformat()
            })
            
        except Exception as e:
            logger.error(f"处理心跳失败: {e}")
    
    async def _broadcast_document_update(self, document_id: str, update: bytes, origin: Any = None):
        """广播文档更新"""
        if document_id not in self.document_connections:
            return
        
        # 构造更新消息
        update_message = {
            "type": "update",
            "document_id": document_id,
            "update": update.hex(),
            "origin": str(origin) if origin else None,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # 广播给所有连接的用户（除了发起者）
        for session_id in self.document_connections[document_id]:
            if session_id == origin:  # 不发送给发起者
                continue
                
            if session_id in self.connections:
                try:
                    await self.connections[session_id].send_json(update_message)
                except Exception as e:
                    logger.error(f"广播更新失败 {session_id}: {e}")
    
    async def _broadcast_user_joined(self, document_id: str, user_id: str, awareness: UserAwareness):
        """广播用户加入事件"""
        if document_id not in self.document_connections:
            return
        
        message = {
            "type": "user_joined",
            "document_id": document_id,
            "user_id": user_id,
            "awareness": awareness.model_dump(),
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await self._broadcast_to_document(document_id, message, exclude_user=user_id)
    
    async def _broadcast_user_left(self, document_id: str, user_id: str):
        """广播用户离开事件"""
        if document_id not in self.document_connections:
            return
        
        message = {
            "type": "user_left",
            "document_id": document_id,
            "user_id": user_id,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await self._broadcast_to_document(document_id, message)
    
    async def _broadcast_awareness_update(self, document_id: str, user_id: str, awareness: UserAwareness):
        """广播感知状态更新"""
        if document_id not in self.document_connections:
            return
        
        message = {
            "type": "awareness_update",
            "document_id": document_id,
            "user_id": user_id,
            "awareness": awareness.model_dump(),
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await self._broadcast_to_document(document_id, message, exclude_user=user_id)
    
    async def _broadcast_to_document(self, document_id: str, message: Dict, exclude_user: str = None):
        """向文档的所有连接广播消息"""
        if document_id not in self.document_connections:
            return
        
        for session_id in self.document_connections[document_id]:
            if exclude_user:
                session_info = self.session_info.get(session_id)
                if session_info and session_info["user_id"] == exclude_user:
                    continue
            
            if session_id in self.connections:
                try:
                    await self.connections[session_id].send_json(message)
                except Exception as e:
                    logger.error(f"广播消息失败 {session_id}: {e}")
    
    def _generate_user_color(self, user_id: str) -> str:
        """为用户生成唯一颜色"""
        colors = [
            "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FECA57",
            "#FF9FF3", "#54A0FF", "#5F27CD", "#00D2D3", "#FF9F43",
            "#E74C3C", "#3498DB", "#2ECC71", "#F39C12", "#9B59B6"
        ]
        
        # 基于用户ID生成颜色索引
        hash_value = hash(user_id)
        color_index = abs(hash_value) % len(colors)
        
        return colors[color_index]
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        return {
            "total_connections": len(self.connections),
            "documents_with_connections": len(self.document_connections),
            "total_awareness_states": sum(len(states) for states in self.awareness_states.values()),
            "document_stats": {
                doc_id: len(sessions) 
                for doc_id, sessions in self.document_connections.items()
            }
        } 