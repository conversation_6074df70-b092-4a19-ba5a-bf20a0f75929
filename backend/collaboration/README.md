# DAIR协作编辑模块

基于Y.js的实时协作编辑模块，完全兼容现有的MongoDB存储架构。

## 🏗️ 架构概览

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端Tiptap    │    │   前端Tiptap    │    │   前端Tiptap    │
│     + Y.js      │    │     + Y.js      │    │     + Y.js      │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │ WebSocket
                                 │
┌─────────────────────────────────┼─────────────────────────────────┐
│                 Python Hocuspocus Server                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ CollaborationProvider │ YDocumentManager │ CollaborationAuth │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│                            │                                   │
│  ┌─────────────────────────┼─────────────────────────────────┐ │
│  │           MongoCollaborationStorage                       │ │
│  └─────────────────────────┼─────────────────────────────────┘ │
└─────────────────────────────┼─────────────────────────────────┘
                              │
                    ┌─────────┼─────────┐
                    │    MongoDB        │
                    │  现有documents集合 │
                    │  + 协作扩展字段    │
                    └───────────────────┘
```

## 📦 核心组件

### 1. YDocumentManager
- **功能**: 管理Y.js文档生命周期
- **特性**: 内存缓存、自动持久化、文档清理
- **存储**: 兼容现有MongoDB schema，增加`yjs_state`字段

### 2. CollaborationProvider
- **功能**: WebSocket连接管理和消息分发
- **特性**: 用户感知、实时同步、连接状态管理
- **协议**: 兼容Y.js WebSocket protocol

### 3. CollaborationAuth
- **功能**: JWT token认证和权限管理
- **特性**: 与现有用户系统集成、文档权限检查
- **安全**: 基于现有的认证体系

### 4. MongoCollaborationStorage
- **功能**: MongoDB存储适配器
- **特性**: 无缝集成现有数据库、快照管理、会话追踪
- **兼容**: 保持向后兼容，不破坏现有数据

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install ypy websockets
```

### 2. 初始化协作服务器
```python
from backend.collaboration import init_collaboration_server
from backend.db.dependencies import get_database
from backend.config import settings

# 在FastAPI应用启动时初始化
database = get_database()
collaboration_server = init_collaboration_server(
    database=database,
    secret_key=settings.SECRET_KEY
)
```

### 3. 集成到现有main.py
```python
from backend.collaboration.server import startup_collaboration_server, shutdown_collaboration_server
from backend.collaboration.router import router as collaboration_router

# 在lifespan函数中添加启动/关闭事件
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 现有启动代码...
    
    # 启动协作服务器
    await startup_collaboration_server()
    
    yield
    
    # 现有关闭代码...
    
    # 关闭协作服务器
    await shutdown_collaboration_server()

# 添加路由
app.include_router(collaboration_router)
```

## 🔌 API端点

### WebSocket连接
```
WS /api/collaboration/{document_id}
```

### REST API
- `POST /api/collaboration/token` - 生成协作token
- `GET /api/collaboration/{document_id}/content` - 获取文档内容
- `GET /api/collaboration/{document_id}/collaborators` - 获取活跃协作者
- `POST /api/collaboration/{document_id}/snapshot` - 创建文档快照
- `GET /api/collaboration/{document_id}/snapshots` - 获取快照历史
- `GET /api/collaboration/stats` - 服务器统计信息
- `GET /api/collaboration/health` - 健康检查

## 📋 数据库Schema扩展

### documents集合新增字段
```javascript
{
  // 现有字段保持不变...
  "content": "原有的markdown内容",
  
  // 新增协作相关字段
  "yjs_state": BinData,        // Y.js二进制状态
  "collaboration_enabled": true, // 是否启用协作
  "last_collaboration_at": ISODate,
  
  // 现有权限字段可直接使用
  "owner_id": "用户ID",
  "collaborators": ["用户ID列表"]
}
```

### 新增集合
```javascript
// 协作快照
collaboration_snapshots: {
  "document_id": "文档ID",
  "version": 数字,
  "content": "纯文本内容",
  "yjs_state": BinData,
  "created_at": ISODate,
  "created_by": "用户ID"
}

// 协作会话
collaboration_sessions: {
  "session_id": "会话ID", 
  "document_id": "文档ID",
  "participants": [用户感知信息],
  "created_at": ISODate,
  "last_activity": ISODate,
  "is_active": Boolean
}
```

## 🔧 配置选项

```python
from backend.collaboration.models import CollaborationConfig

config = CollaborationConfig(
    max_connections_per_document=50,  # 每文档最大连接数
    heartbeat_interval=30.0,          # 心跳间隔(秒)
    sync_timeout=5.0,                 # 同步超时(秒)
    auto_save_interval=10.0,          # 自动保存间隔(秒)
    enable_awareness=True,            # 启用用户感知
    enable_history=True,              # 启用历史记录
    max_history_size=100              # 最大历史记录数
)
```

## 🔐 安全特性

1. **JWT Token认证**: 基于现有用户系统生成专用的协作token
2. **权限继承**: 完全继承现有文档权限系统
3. **连接隔离**: 每个文档的协作会话相互隔离
4. **自动清理**: 定期清理不活跃的连接和会话

## ⚡ 性能优化

1. **内存缓存**: 活跃文档保持在内存中，提高响应速度
2. **延迟保存**: 合并频繁的更新操作，减少数据库写入
3. **连接池**: 复用WebSocket连接，减少建立连接开销
4. **增量同步**: 只传输文档变更部分，不是完整文档

## 🛠️ 使用示例

### 前端集成 (Vue.js + Tiptap)
```javascript
import { Editor } from '@tiptap/core'
import Collaboration from '@tiptap/extension-collaboration'
import * as Y from 'yjs'
import { WebsocketProvider } from 'y-websocket'

// 创建Y.js文档
const ydoc = new Y.Doc()

// 建立协作连接
const provider = new WebsocketProvider(
  `ws://localhost:8000/api/collaboration/${documentId}`,
  documentId,
  ydoc,
  {
    params: { token: collaborationToken }
  }
)

// 创建编辑器
const editor = new Editor({
  extensions: [
    StarterKit.configure({ history: false }),
    Collaboration.configure({
      document: ydoc,
    }),
    // 其他扩展...
  ]
})
```

## 🐛 调试和监控

### 日志等级
- `DEBUG`: 详细的操作日志
- `INFO`: 重要事件和状态变更  
- `WARNING`: 潜在问题和降级处理
- `ERROR`: 错误和异常情况

### 监控指标
- 活跃连接数
- 文档并发编辑数
- 消息处理延迟
- 存储操作成功率

## 📈 扩展性

1. **水平扩展**: 支持多实例部署，通过Redis共享状态
2. **垂直扩展**: 内存和CPU使用优化，支持大量并发
3. **功能扩展**: 模块化设计，易于添加新功能
4. **存储扩展**: 抽象存储层，支持其他数据库

## 🔄 与现有系统的集成

### 完全兼容
- ✅ 现有documents API继续工作
- ✅ 现有用户权限系统无需修改
- ✅ 现有前端界面可逐步升级
- ✅ 数据库schema向后兼容

### 渐进式升级
1. **第一阶段**: 部署协作服务，现有功能不受影响
2. **第二阶段**: 特定文档启用协作编辑
3. **第三阶段**: 全面替换为协作编辑模式

## 🚨 注意事项

1. **内存使用**: 活跃文档会占用内存，需要监控内存使用情况
2. **网络连接**: WebSocket连接需要稳定的网络环境
3. **浏览器兼容**: 确保目标浏览器支持WebSocket和Y.js
4. **数据一致性**: 在高并发场景下注意数据一致性问题

## 📞 技术支持

如有问题或需要扩展功能，请联系开发团队或查看相关文档。 