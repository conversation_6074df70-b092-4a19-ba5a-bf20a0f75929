"""
协作编辑服务器

整合所有协作编辑组件，提供统一的服务接口。
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from fastapi import WebSocket, FastAPI, Depends, HTTPException
from motor.motor_asyncio import AsyncIOMotorDatabase

from .models import CollaborationConfig, DocumentSnapshot
from .document_manager import YDocumentManager
from .provider import CollaborationProvider
from .auth import CollaborationAuth
from .storage import MongoCollaborationStorage

logger = logging.getLogger(__name__)

class CollaborationServer:
    """协作编辑服务器"""
    
    def __init__(
        self, 
        database: AsyncIOMotorDatabase,
        secret_key: str,
        config: Optional[CollaborationConfig] = None
    ):
        self.database = database
        self.secret_key = secret_key
        self.config = config or CollaborationConfig()
        
        # 初始化组件
        self.storage = MongoCollaborationStorage(database)
        self.auth = CollaborationAuth(self.storage, secret_key)
        self.document_manager = YDocumentManager(self.storage)
        self.provider = CollaborationProvider(
            self.document_manager, 
            self.auth, 
            self.storage
        )
        
        self._running = False
        self._cleanup_task: Optional[asyncio.Task] = None
    
    async def start(self):
        """启动协作服务器"""
        if self._running:
            logger.warning("协作服务器已在运行中")
            return
        
        logger.info("启动协作编辑服务器")
        
        try:
            # 启动文档管理器
            await self.document_manager.start()
            
            # 启动清理任务
            self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
            
            self._running = True
            logger.info("协作编辑服务器启动成功")
            
        except Exception as e:
            logger.error(f"启动协作服务器失败: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """停止协作服务器"""
        if not self._running:
            return
        
        logger.info("停止协作编辑服务器")
        
        try:
            # 停止清理任务
            if self._cleanup_task:
                self._cleanup_task.cancel()
                try:
                    await self._cleanup_task
                except asyncio.CancelledError:
                    pass
            
            # 停止文档管理器
            await self.document_manager.stop()
            
            self._running = False
            logger.info("协作编辑服务器已停止")
            
        except Exception as e:
            logger.error(f"停止协作服务器失败: {e}")
    
    async def handle_websocket(self, websocket: WebSocket, document_id: str):
        """处理WebSocket连接"""
        await self.provider.handle_websocket_connection(websocket, document_id)
    
    async def generate_collaboration_token(self, user_id: str, document_id: str) -> str:
        """生成协作编辑token"""
        return await self.auth.generate_collaboration_token(user_id, document_id)
    
    async def get_document_content(self, document_id: str) -> str:
        """获取文档内容"""
        return await self.document_manager.get_document_content(document_id)
    
    async def create_document_snapshot(self, document_id: str, created_by: str) -> DocumentSnapshot:
        """创建文档快照"""
        return await self.document_manager.create_snapshot(document_id, created_by)
    
    async def get_document_snapshots(self, document_id: str, limit: int = 10) -> List[DocumentSnapshot]:
        """获取文档快照历史"""
        return await self.storage.get_snapshots(document_id, limit)
    
    async def get_active_collaborators(self, document_id: str) -> List[Dict[str, Any]]:
        """获取活跃的协作者"""
        collaborators = []
        
        if document_id in self.provider.awareness_states:
            for user_id, awareness in self.provider.awareness_states[document_id].items():
                collaborators.append({
                    "user_id": user_id,
                    "username": awareness.username,
                    "color": awareness.color,
                    "cursor_position": awareness.cursor_position,
                    "last_active": awareness.last_active.isoformat()
                })
        
        return collaborators
    
    async def get_server_stats(self) -> Dict[str, Any]:
        """获取服务器统计信息"""
        provider_stats = self.provider.get_connection_stats()
        
        return {
            "running": self._running,
            "config": self.config.model_dump(),
            "connections": provider_stats,
            "documents_loaded": len(self.document_manager._documents),
            "uptime": datetime.utcnow().isoformat()
        }
    
    async def _periodic_cleanup(self):
        """定期清理任务"""
        while True:
            try:
                await asyncio.sleep(3600)  # 每小时运行一次
                
                # 清理不活跃的协作会话
                await self.storage.cleanup_inactive_sessions(24)
                
                logger.debug("执行了定期清理任务")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"定期清理任务出错: {e}")
    
    def register_routes(self, app: FastAPI, prefix: str = "/api/collaboration"):
        """注册协作相关的API路由"""
        
        @app.websocket(f"{prefix}/{{document_id}}")
        async def websocket_collaboration(websocket: WebSocket, document_id: str):
            """WebSocket协作端点"""
            await self.handle_websocket(websocket, document_id)
        
        @app.post(f"{prefix}/token")
        async def generate_token(
            document_id: str,
            current_user: dict = Depends(self._get_current_user)  # 需要实现用户依赖
        ):
            """生成协作token"""
            try:
                token = await self.generate_collaboration_token(
                    current_user["user_id"], 
                    document_id
                )
                return {"token": token}
            except Exception as e:
                raise HTTPException(status_code=403, detail=str(e))
        
        @app.get(f"{prefix}/{{document_id}}/content")
        async def get_content(
            document_id: str,
            current_user: dict = Depends(self._get_current_user)
        ):
            """获取文档内容"""
            content = await self.get_document_content(document_id)
            return {"content": content}
        
        @app.get(f"{prefix}/{{document_id}}/collaborators")
        async def get_collaborators(
            document_id: str,
            current_user: dict = Depends(self._get_current_user)
        ):
            """获取活跃协作者"""
            collaborators = await self.get_active_collaborators(document_id)
            return {"collaborators": collaborators}
        
        @app.post(f"{prefix}/{{document_id}}/snapshot")
        async def create_snapshot(
            document_id: str,
            current_user: dict = Depends(self._get_current_user)
        ):
            """创建文档快照"""
            snapshot = await self.create_document_snapshot(
                document_id, 
                current_user["user_id"]
            )
            return {"snapshot": snapshot.model_dump()}
        
        @app.get(f"{prefix}/{{document_id}}/snapshots")
        async def get_snapshots(
            document_id: str,
            limit: int = 10,
            current_user: dict = Depends(self._get_current_user)
        ):
            """获取文档快照历史"""
            snapshots = await self.get_document_snapshots(document_id, limit)
            return {"snapshots": [s.model_dump() for s in snapshots]}
        
        @app.get(f"{prefix}/stats")
        async def get_stats():
            """获取服务器统计信息"""
            stats = await self.get_server_stats()
            return stats
    
    def _get_current_user(self):
        """获取当前用户（占位符）"""
        # 这里需要实现真实的用户认证逻辑
        # 可以集成到现有的认证系统中
        pass
    
    @property
    def is_running(self) -> bool:
        """检查服务器是否在运行"""
        return self._running


# 全局协作服务器实例（单例）
_collaboration_server: Optional[CollaborationServer] = None

def get_collaboration_server() -> CollaborationServer:
    """获取全局协作服务器实例"""
    global _collaboration_server
    if _collaboration_server is None:
        raise RuntimeError("协作服务器未初始化")
    return _collaboration_server

def init_collaboration_server(
    database: AsyncIOMotorDatabase,
    secret_key: str,
    config: Optional[CollaborationConfig] = None
) -> CollaborationServer:
    """初始化全局协作服务器"""
    global _collaboration_server
    if _collaboration_server is None:
        _collaboration_server = CollaborationServer(database, secret_key, config)
    return _collaboration_server

async def startup_collaboration_server():
    """启动全局协作服务器"""
    try:
        global _collaboration_server
        if _collaboration_server and _collaboration_server.is_running:
            logger.warning("协作服务器已在运行中")
            return
        
        # 检查必要的依赖是否可用
        try:
            import ypy
            logger.info("Y.js Python绑定可用，启用完整协作功能")
        except ImportError:
            logger.warning("Y.js Python绑定不可用，使用模拟实现")
        
        if not _collaboration_server:
            logger.error("协作服务器未初始化，请先调用init_collaboration_server")
            return
        
        await _collaboration_server.start()
        logger.info("全局协作服务器启动成功")
        
    except Exception as e:
        logger.error(f"启动协作服务器失败: {e}")
        # 不抛出异常，让应用能够继续启动
        logger.info("应用将在没有协作功能的情况下继续运行")

async def shutdown_collaboration_server():
    """关闭全局协作服务器"""
    try:
        global _collaboration_server
        if _collaboration_server:
            await _collaboration_server.stop()
            logger.info("全局协作服务器已关闭")
    except Exception as e:
        logger.error(f"关闭协作服务器失败: {e}")
        # 不抛出异常，让应用能够正常关闭 