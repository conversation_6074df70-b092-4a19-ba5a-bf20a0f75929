from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime
from backend.db.mongodb import PyObjectId
from backend.utils import get_cn_time

class Document(BaseModel):
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    title: str
    creator_id: str
    parent_id: Optional[str] = None  # 父节点ID，None表示根节点
    content: str = ""
    owner_id: str
    collaborators: List[str] = []
    permissions: Dict[str, List[str]] = {
        "read": [],
        "write": [],
        "admin": []
    }
    shared: bool = False  # 分享状态标识
    share_token: Optional[str] = None  # 当前有效的分享token
    is_folder_share: bool = False  # 是否为文件夹分享
    created_at: datetime = Field(default_factory=get_cn_time)
    updated_at: datetime = Field(default_factory=get_cn_time)
    updated_by: Optional[str] = None  # 最后修改者ID
    updated_by_name: Optional[str] = None  # 最后修改者姓名
    # 原始文档信息（用于从分享链接复制的文档）
    original_document_id: Optional[str] = None  # 原始文档ID
    original_owner_id: Optional[str] = None  # 原始文档拥有者ID
    original_owner_name: Optional[str] = None  # 原始文档拥有者姓名
    copied_from_share: bool = False  # 是否从分享链接复制
    tags: List[str] = []
    metadata: Dict[str, Any] = {}
    children: Optional[List[str]] = Field(default_factory=list)

class DocumentCreate(BaseModel):
    title: str
    parent_id: Optional[str] = None
    content: Optional[str] = ""
    tags: Optional[List[str]] = []
    metadata: Optional[Dict[str, Any]] = {}

class DocumentUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    parent_id: Optional[str] = None
    updated_by: Optional[str] = None  # 最后修改者ID
    updated_by_name: Optional[str] = None  # 最后修改者姓名
    tags: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None

class DocumentTreeNode(BaseModel):
    id: str  # 改为字符串类型
    title: str  # 改为 title 而不是 name，与前端保持一致
    parent_id: Optional[str] = None
    has_children: bool = False  # 添加是否有子节点的标识
    created_at: Optional[datetime] = None  # 改为可选
    updated_at: Optional[datetime] = None  # 改为可选

class DocumentResponse(BaseModel):
    id: PyObjectId
    title: str
    parent_id: Optional[str] = None
    content: str
    owner_id: str
    creator_id: str
    creator_name: Optional[str] = None  # 创建者姓名
    creator_email: Optional[str] = None  # 创建者邮箱
    collaborators: List[str]
    permissions: Dict[str, List[str]]
    shared: Optional[bool] = False  # 分享状态标识
    is_folder_share: Optional[bool] = False  # 是否为文件夹分享
    created_at: datetime
    updated_at: datetime
    updated_by: Optional[str] = None  # 最后修改者ID
    updated_by_name: Optional[str] = None  # 最后修改者姓名
    # 原始文档信息
    original_document_id: Optional[str] = None
    original_owner_id: Optional[str] = None
    original_owner_name: Optional[str] = None
    copied_from_share: Optional[bool] = False
    tags: List[str]
    metadata: Dict[str, Any]
    children: Optional[List[str]] = []
    share_permissions: Optional[List[str]] = None