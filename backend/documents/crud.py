from backend.db.mongodb import MongoDBBase
from backend.documents.models import Document, DocumentCreate, DocumentUpdate
from typing import Optional, List, Dict
from bson import ObjectId
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class DocumentRepository(MongoDBBase[Document]):
    def __init__(self):
        super().__init__(collection_name="documents", model_class=Document)
    
    async def create_document(self, document: DocumentCreate, creator_id: str) -> Document:
        # 创建新文档
        doc_data = document.model_dump(exclude_none=True)
        doc_data["creator_id"] = creator_id
        doc_data["owner_id"] = creator_id
        doc_data["collaborators"] = [creator_id]
        doc_data["permissions"] = {
            "read": [creator_id],
            "write": [creator_id],
            "admin": [creator_id]
        }
        doc_data["created_at"] = datetime.utcnow()
        doc_data["updated_at"] = datetime.utcnow()
        doc_data["children"] = []
        document_obj = await self.create(doc_data)
        parent_id = doc_data.get("parent_id")
        if parent_id:
            await self._collection.update_one(
                {"_id": ObjectId(parent_id)},
                {"$push": {"children": str(document_obj.id)}}
            )
        logger.warning(f"文档创建成功，原始ID,__ID: {document_obj}")
        return document_obj
    
    async def get_document_by_id(self, id: str) -> Optional[Document]:
        try:
            logger.warning(f"开始获取文档 in get_document_by_id，ID: {id}")
            return await self.find_one({"_id": ObjectId(id)})
        except Exception as e:
            logger.error(f"获取文档失败: {str(e)}")
            return None
    
    async def get_documents_by_user_id(self, user_id: str) -> List[Document]:
        # 获取用户有权限访问的所有文档
        return await self.find_many({
            "$or": [
                {"owner_id": user_id},
                {"collaborators": user_id},
                {"permissions.read": user_id}
            ]
        }, skip=0, limit=1000)  # 按创建时间倒序排序

    async def get_documents_by_path(self, path: List[str]) -> List[Document]:
        # 使用物化路径查询
        path_str = "," + ",".join(path) + ","
        return await self.find_many({"path": path_str})

    async def update_document(self, id: str, document: DocumentUpdate) -> Optional[Document]:
        update_data = document.model_dump(exclude_none=True)
        update_data["updated_at"] = datetime.utcnow()
        return await self.update(id, update_data)
    
    async def delete_document(self, id: str) -> bool:
        doc = await self.get_document_by_id(id)
        if doc and doc.parent_id:
            await self._collection.update_one(
                {"_id": ObjectId(doc.parent_id)},
                {"$pull": {"children": id}}
            )
        return await self.delete(id)

    async def safe_delete_document(self, id: str) -> bool:
        """安全删除文档，处理循环引用的情况"""
        try:
            # 首先获取要删除的文档
            doc = await self.get_document_by_id(id)
            if not doc:
                return False

            # 新增：从父节点children移除自己
            if doc.parent_id:
                await self._collection.update_one(
                    {"_id": ObjectId(doc.parent_id)},
                    {"$pull": {"children": id}}
                )

            # 获取所有子文档
            children = await self.get_documents_by_parent_id(id)
            
            # 递归删除所有子文档
            for child in children:
                await self.safe_delete_document(str(child.id))

            # 最后删除当前文档
            return await self.delete(id)
        except Exception as e:
            logger.error(f"删除文档时发生错误: {str(e)}")
            return False

    async def delete_all_documents(self) -> bool:
        """删除所有文档，处理循环引用的情况"""
        try:
            # 获取所有文档
            all_docs = await self.find_many({})
            
            # 按创建时间排序，确保先删除子文档
            sorted_docs = sorted(all_docs, key=lambda x: x.created_at)
            
            # 逐个安全删除
            for doc in sorted_docs:
                await self.safe_delete_document(str(doc.id))
            
            return True
        except Exception as e:
            logger.error(f"删除所有文档时发生错误: {str(e)}")
            return False

    async def get_all_documents(self, user_id: str) -> List[Document]:
        """获取用户有权限访问的所有文档"""
        return await self.get_documents_by_user_id(user_id)

    async def get_documents_by_parent_id(self, parent_id: str) -> List[Document]:
        """获取指定父节点下的所有文档"""
        cursor = self._collection.find({"parent_id": parent_id})
        documents = []
        async for doc in cursor:
            documents.append(Document(**doc))
        return documents

    async def move_document(self, doc_id: str, new_parent_id: str) -> Optional[Document]:
        """移动文档到新的父节点下"""
        try:
            # 获取要移动的文档
            doc = await self.get_document_by_id(doc_id)
            if not doc:
                return None

            old_parent_id = doc.parent_id

            # 1. 从原父节点的children中移除
            if old_parent_id:
                await self._collection.update_one(
                    {"_id": ObjectId(old_parent_id)},
                    {"$pull": {"children": doc_id}}
                )

            # 2. 添加到新父节点的children中
            if new_parent_id:
                await self._collection.update_one(
                    {"_id": ObjectId(new_parent_id)},
                    {"$push": {"children": doc_id}}
                )

            # 3. 更新文档的parent_id
            update_data = {
                "parent_id": new_parent_id,
                "updated_at": datetime.utcnow()
            }
            
            return await self.update(doc_id, update_data)
        except Exception as e:
            logger.error(f"移动文档时发生错误: {str(e)}")
            return None