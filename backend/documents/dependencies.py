from fastapi import Depends, HTTPException, status
from backend.documents.crud import DocumentRepository
import logging  

logger = logging.getLogger(__name__)

async def get_document_by_id(
    document_id: str
):
    try:
        document_repo = DocumentRepository()
        document = await document_repo.get_document_by_id(document_id)
        if not document:
            raise HTTPException(status_code=404, detail="文档不存在")
        return document
    except Exception as e:
        logger.error(f"获取文档失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取文档失败: {str(e)}")