from backend.documents.crud import DocumentRepository
from backend.documents.models import Document, DocumentCreate, DocumentUpdate, DocumentResponse, DocumentTreeNode
from typing import Optional, List, Dict, Any
from bson import ObjectId
from fastapi import HTTPException, status
from datetime import datetime
import logging
import jwt
from datetime import timedelta
from backend.auth.security import SECRET_KEY  # 导入统一的SECRET_KEY
from backend.auth.models import UserDB
from backend.auth.crud import UserRepository
from backend.db.mongodb import PyObjectId
from backend.db.dependencies import get_mongodb_collection
from motor.motor_asyncio import AsyncIOMotorCollection
import secrets
import uuid

logger = logging.getLogger(__name__)

class DocumentService:
    def __init__(self):
        self.document_repo = DocumentRepository()
        self.user_repo = UserRepository()

    async def _get_creator_info(self, creator_id: str) -> Dict[str, Optional[str]]:
        """获取创建者信息"""
        try:
            user = await self.user_repo.get_by_id(creator_id)
            if user:
                result = {
                    "creator_name": user.username,
                    "creator_email": user.email
                }
                return result
            return {"creator_name": None, "creator_email": None}
        except Exception as e:
            logger.error(f"获取创建者信息失败: creator_id={creator_id}, error={e}")
            return {"creator_name": None, "creator_email": None}

    async def create_document(self, document: DocumentCreate, creator_id: str) -> DocumentResponse:
        logger.warning(f"开始创建文档，创建者ID: {creator_id}")
        # 如果提供了parent_id，验证父节点是否存在
        if document.parent_id is not None:  # 只验证非None的父节点
            logger.warning(f"验证父节点是否存在: {document.parent_id}")
            parent_doc = await self.document_repo.get_document_by_id(document.parent_id)
            if not parent_doc:
                logger.warning(f"父节点不存在: {document.parent_id}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="父节点不存在"
                )
        
        # 创建新文档
        logger.warning("开始创建新文档")
        doc = await self.document_repo.create_document(document, creator_id)
        logger.warning(f"文档创建成功，原始ID: {doc}")
        doc_dict = doc.model_dump()
        doc_dict["id"] = str(doc.id)  # 确保 id 是字符串类型
        logger.warning(f"文档数据准备完成，ID: {doc_dict['id']}")
        
        # 获取创建者信息
        creator_info = await self._get_creator_info(doc.creator_id)
        doc_dict.update(creator_info)
        
        return DocumentResponse(**doc_dict)

    async def get_document_by_id(self, document_id: str) -> DocumentResponse:
        logger.warning(f"开始获取文档，ID: {document_id}")
        # 获取单个文档
        doc = await self.document_repo.get_document_by_id(document_id)
        if not doc:
            logger.warning(f"文档不存在: {document_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )
        logger.warning(f"成功获取文档: {doc.id}")
        doc_dict = doc.model_dump()
        doc_dict["id"] = str(doc.id)  # 确保 id 是字符串类型
        
        # 获取创建者信息
        creator_info = await self._get_creator_info(doc.creator_id)
        doc_dict.update(creator_info)
        
        return DocumentResponse(**doc_dict)

    async def get_document_tree(self, user_id: str) -> List[DocumentTreeNode]:
        # 获取所有文档
        documents = await self.document_repo.get_all_documents(user_id)
        # logger.warning(f"获取到的文档列表: {documents}")
        
        # 获取用户有权访问的所有文档ID
        accessible_doc_ids = set()
        for doc in documents:
            accessible_doc_ids.add(str(doc.id))
        
        # 转换为节点列表
        nodes = []
        for doc in documents:
            parent_id = doc.parent_id
            
            # 对于协作文档（用户不是owner但是collaborator）
            if doc.owner_id != user_id and user_id in doc.collaborators:
                # 只有在以下情况下才将其显示为根节点：
                # 1. 原本就是根文档（parent_id 为 None）
                # 2. 父文档不在用户的可访问文档列表中
                if parent_id is None or parent_id not in accessible_doc_ids:
                    parent_id = None
                # 否则保持原有的层级关系
                
            node = DocumentTreeNode(
                id=str(doc.id),  # 转换为字符串
                title=doc.title,  # 使用title而不是name
                parent_id=parent_id,
                has_children=False,  # 使用has_children而不是is_folder
                created_at=doc.created_at,
                updated_at=doc.updated_at
            )
            nodes.append(node)
        
        # logger.warning(f"转换后的节点列表: {nodes}")
        return nodes

    async def update_document(self, document_id: str, document: DocumentUpdate, updated_by: Optional[str] = None) -> DocumentResponse:
        # 获取当前文档
        current_doc = await self.document_repo.get_document_by_id(document_id)
        if not current_doc:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )
        
        # 检查title是否变化并验证是否有效
        if document.title and document.title.strip() != current_doc.title:
            if not document.title.strip():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="文档标题不能为空"
                )
        
        # 检查parent_id是否变化并验证是否会造成循环引用
        if document.parent_id is not None and document.parent_id != current_doc.parent_id:
            if document.parent_id == document_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="不能将文档设置为自己的子文档"
                )
            
            # 检查是否会造成循环引用
            async def is_descendant(child_id: str, target_id: str) -> bool:
                if child_id == target_id:
                    return True
                
                descendants = await self.document_repo.get_documents_by_parent_id(child_id)
                for descendant in descendants:
                    if await is_descendant(str(descendant.id), target_id):
                        return True
                return False
            
            if await is_descendant(document_id, document.parent_id):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="不能将文档移动到自己的子文档下"
                )
        
        # 准备更新数据
        update_data = document.model_dump(exclude_unset=True)
        update_data["updated_at"] = datetime.utcnow()
        
        # 添加修改者信息
        if updated_by:
            update_data["updated_by"] = updated_by
            # 获取修改者姓名
            try:
                user_info = await self._get_creator_info(updated_by)
                update_data["updated_by_name"] = user_info.get("creator_name", "未知用户")
            except Exception as e:
                logger.error(f"获取修改者信息失败: {e}")
                update_data["updated_by_name"] = "未知用户"
        
        updated_doc = await self.document_repo.update_document(document_id, DocumentUpdate(**update_data))
        if not updated_doc:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="更新文档失败"
            )
        
        # 处理父文档变更
        if document.parent_id is not None and document.parent_id != current_doc.parent_id:
            # 从原父文档中移除
            if current_doc.parent_id:
                await self.document_repo.collection.update_one(
                    {"_id": ObjectId(current_doc.parent_id)},
                    {"$pull": {"children": document_id}}
                )
            
            # 添加到新父文档
            if document.parent_id:
                await self.document_repo.collection.update_one(
                    {"_id": ObjectId(document.parent_id)},
                    {"$push": {"children": document_id}}
                )
        
        doc_dict = updated_doc.model_dump()
        doc_dict["id"] = str(updated_doc.id)
        
        # 获取创建者信息
        creator_info = await self._get_creator_info(updated_doc.creator_id)
        doc_dict.update(creator_info)
        
        return DocumentResponse(**doc_dict)

    async def delete_document(self, document_id: str, user_id: str, force: bool = False) -> bool:
        """删除文档 - 只有文档拥有者可以删除"""
        try:
            # 获取文档并检查权限
            doc = await self.document_repo.get_document_by_id(document_id)
            if not doc:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="文档不存在"
                )
            
            # 检查权限：只有文档拥有者可以删除文档
            if doc.owner_id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有文档拥有者可以删除文档"
                )
            
            if force:
                return await self.document_repo.safe_delete_document(document_id)
            else:
                return await self.document_repo.delete_document(document_id)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"删除文档失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="删除文档失败"
            )

    async def delete_all_documents(self, force: bool = False) -> bool:
        """
        删除所有文档
        :param force: 是否强制删除（包括子文档）
        :return: 是否删除成功
        """
        if not force:
            # 检查是否存在有子文档的文档
            all_docs = await self.document_repo.find_many({})
            for doc in all_docs:
                child_docs = await self.document_repo.get_documents_by_parent_id(str(doc.id))
                if child_docs:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="存在包含子文档的文档，请先删除子文档或使用force=True参数强制删除"
                    )
        
        # 执行删除操作
        success = await self.document_repo.delete_all_documents()
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="删除文档时发生错误"
            )
        return success

    async def move_document(self, document_id: str, target_parent_id: Optional[str], position: str = "inside", reference_node_id: Optional[str] = None) -> DocumentResponse:
        """
        移动文档到新的父节点，支持精确的位置控制
        
        Args:
            document_id: 要移动的文档ID
            target_parent_id: 目标父节点ID，None表示移动到根层级
            position: 插入位置 ("inside", "before", "after")
            reference_node_id: 参考节点ID（当position为before/after时使用）
        """
        try:
            # 获取要移动的文档
            doc_to_move = await self.document_repo.get_document_by_id(document_id)
            if not doc_to_move:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="要移动的文档不存在"
                )

            # 检查目标父节点是否存在（如果不是根层级）
            if target_parent_id:
                target_parent = await self.document_repo.get_document_by_id(target_parent_id)
                if not target_parent:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="目标父节点不存在"
                    )
                
                # 检查是否会造成循环引用
                async def is_descendant(child_id: str, target_id: str) -> bool:
                    if child_id == target_id:
                        return True
                    
                    descendants = await self.document_repo.get_documents_by_parent_id(child_id)
                    for descendant in descendants:
                        if await is_descendant(str(descendant.id), target_id):
                            return True
                    return False
                
                if await is_descendant(document_id, target_parent_id):
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="不能将文档移动到自己的子文档下"
                    )

            # 检查参考节点是否存在（当position为before/after时）
            reference_node = None
            if position in ["before", "after"] and reference_node_id:
                reference_node = await self.document_repo.get_document_by_id(reference_node_id)
                if not reference_node:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="参考节点不存在"
                    )

            # 获取当前文档的原父节点
            old_parent_id = doc_to_move.parent_id

            # 处理不同的插入位置
            if position == "inside":
                # 移动到目标节点内部
                new_parent_id = target_parent_id
            elif position in ["before", "after"] and reference_node:
                # 移动到参考节点的前面或后面，使用参考节点的父节点作为新父节点
                new_parent_id = reference_node.parent_id
            else:
                # 默认情况，移动到目标父节点内部
                new_parent_id = target_parent_id

            # 执行移动操作
            update_data = {
                "parent_id": new_parent_id,
                "updated_at": datetime.utcnow()
            }

            await self.document_repo.collection.update_one(
                {"_id": ObjectId(document_id)},
                {"$set": update_data}
            )

            # 更新原父节点的children列表（如果有）
            if old_parent_id:
                await self.document_repo.collection.update_one(
                    {"_id": ObjectId(old_parent_id)},
                    {"$pull": {"children": document_id}}
                )

            # 更新新父节点的children列表（如果有）
            if new_parent_id:
                # 检查是否已经在children列表中
                parent_doc = await self.document_repo.get_document_by_id(new_parent_id)
                if parent_doc and document_id not in (parent_doc.children or []):
                    
                    # 根据position决定插入位置
                    if position == "before" and reference_node_id:
                        # 插入到参考节点之前
                        children = parent_doc.children or []
                        if reference_node_id in children:
                            insert_index = children.index(reference_node_id)
                            children.insert(insert_index, document_id)
                        else:
                            children.append(document_id)
                        
                        await self.document_repo.collection.update_one(
                            {"_id": ObjectId(new_parent_id)},
                            {"$set": {"children": children}}
                        )
                    elif position == "after" and reference_node_id:
                        # 插入到参考节点之后
                        children = parent_doc.children or []
                        if reference_node_id in children:
                            insert_index = children.index(reference_node_id) + 1
                            children.insert(insert_index, document_id)
                        else:
                            children.append(document_id)
                        
                        await self.document_repo.collection.update_one(
                            {"_id": ObjectId(new_parent_id)},
                            {"$set": {"children": children}}
                        )
                    else:
                        # 默认添加到末尾
                        await self.document_repo.collection.update_one(
                            {"_id": ObjectId(new_parent_id)},
                            {"$push": {"children": document_id}}
                        )

            # 获取更新后的文档
            updated_doc = await self.document_repo.get_document_by_id(document_id)
            if not updated_doc:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="移动文档后无法获取更新的文档"
                )
            
            # 构建返回数据
            doc_dict = updated_doc.model_dump()
            doc_dict["id"] = str(updated_doc.id)
            
            # 获取创建者信息
            creator_info = await self._get_creator_info(updated_doc.creator_id)
            doc_dict.update(creator_info)
            
            return DocumentResponse(**doc_dict)
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"移动文档失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="移动文档失败"
            )

    async def share_document(
        self, 
        document_id: str, 
        user_id: str, 
        permissions: List[str] = ["read"],
        expiry_days: Optional[int] = None
    ) -> Dict[str, Any]:
        """分享文档给其他用户"""
        # 获取文档并验证权限
        doc = await self.document_repo.get_document_by_id(document_id)
        if not doc:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )
        
        # 检查当前用户是否有管理权限（是创建者或拥有admin权限）
        if doc.owner_id != user_id and user_id not in doc.permissions.get("admin", []):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限分享此文档"
            )
        
        # 检查是否为文件夹（有子文档）
        children_count = await self.document_repo.collection.count_documents({"parent_id": document_id})
        is_folder = children_count > 0
        
        # 生成分享token，包含唯一标识符
        payload = {
            "document_id": document_id,
            "permissions": permissions,
            "shared_by": user_id,
            "created_at": datetime.utcnow().isoformat(),
            "share_id": str(uuid.uuid4()),  # 添加唯一标识符
            "timestamp": int(datetime.utcnow().timestamp() * 1000000),  # 微秒级时间戳
            "is_folder": is_folder  # 标识是否为文件夹分享
        }
        
        if expiry_days:
            payload["exp"] = datetime.utcnow() + timedelta(days=expiry_days)
        
        # 这里需要从config获取secret_key，暂时使用固定值
        secret_key = SECRET_KEY  # 实际项目中应该从配置获取
        share_token = jwt.encode(payload, secret_key, algorithm="HS256")
        
        # 更新文档的权限设置
        current_permissions = doc.permissions.copy()
        for permission in permissions:
            if permission not in current_permissions:
                current_permissions[permission] = []
            # 这里暂时不添加具体用户，因为是公开分享
        
        await self.document_repo.collection.update_one(
            {"_id": ObjectId(document_id)},
            {
                "$set": {
                    "permissions": current_permissions,
                    "shared": True,  # 添加分享状态标识
                    "share_token": share_token,  # 保存当前有效的分享token
                    "is_folder_share": is_folder,  # 保存是否为文件夹分享
                    "updated_at": datetime.utcnow()
                }
            }
        )
        
        return {
            "share_token": share_token,
            "share_url": f"/share/{share_token}",
            "permissions": permissions,
            "expiry_days": expiry_days,
            "is_folder": is_folder,
            "created_at": datetime.utcnow().isoformat()
        }
    
    async def revoke_share(self, document_id: str, user_id: str) -> bool:
        """撤销文档分享"""
        doc = await self.document_repo.get_document_by_id(document_id)
        if not doc:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )
        
        # 检查权限
        if doc.owner_id != user_id and user_id not in doc.permissions.get("admin", []):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限撤销分享"
            )
        
        # 重置权限为默认状态
        default_permissions = {
            "read": [],
            "write": [],
            "admin": [doc.owner_id]
        }
        
        await self.document_repo.collection.update_one(
            {"_id": ObjectId(document_id)},
            {
                "$set": {
                    "permissions": default_permissions,
                    "shared": False,  # 标识分享已被撤销
                    "updated_at": datetime.utcnow()
                },
                "$unset": {
                    "share_token": ""  # 移除分享token
                }
            }
        )
        
        return True
    
    async def get_shared_document(self, share_token: str) -> DocumentResponse:
        """通过分享token获取文档或文件夹"""
        try:
            # 解码token
            secret_key = SECRET_KEY  # 实际项目中应该从配置获取
            payload = jwt.decode(share_token, secret_key, algorithms=["HS256"])
            
            document_id = payload.get("document_id")
            permissions = payload.get("permissions", ["read"])
            is_folder_from_token = payload.get("is_folder", False)
            
            if not document_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的分享链接"
                )
            
            # 获取文档
            doc = await self.document_repo.get_document_by_id(document_id)
            if not doc:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="分享的文档不存在"
                )
            
            # 检查文档是否仍然处于分享状态
            if not getattr(doc, 'shared', False):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="分享链接已被撤销"
                )
            
            # 重新检查是否为文件夹（有子文档）
            children_count = await self.document_repo.collection.count_documents({"parent_id": document_id})
            is_folder_current = children_count > 0
            
            # 优先使用数据库中存储的值，如果没有则使用重新计算的值
            is_folder_share = getattr(doc, 'is_folder_share', is_folder_from_token or is_folder_current)
            
            # 返回文档，但隐藏敏感信息
            doc_dict = doc.model_dump()
            doc_dict["id"] = str(doc.id)
            doc_dict["share_permissions"] = permissions  # 添加分享权限信息
            doc_dict["is_folder_share"] = is_folder_share  # 使用正确的文件夹标识
            
            # 获取创建者信息
            creator_info = await self._get_creator_info(doc.creator_id)
            doc_dict.update(creator_info)
            
            return DocumentResponse(**doc_dict)
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="分享链接已过期"
            )
        except jwt.InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的分享链接"
            )
    
    async def get_shared_folder_tree(self, share_token: str) -> List[DocumentTreeNode]:
        """获取分享文件夹的文档树"""
        try:
            # 解码token
            secret_key = SECRET_KEY
            payload = jwt.decode(share_token, secret_key, algorithms=["HS256"])
            
            document_id = payload.get("document_id")
            is_folder = payload.get("is_folder", False)
            
            if not document_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的分享链接"
                )
            
            if not is_folder:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="此链接不是文件夹分享"
                )
            
            # 获取根文档
            root_doc = await self.document_repo.get_document_by_id(document_id)
            if not root_doc:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="分享的文档不存在"
                )
            
            # 检查文档是否仍然处于分享状态
            if not getattr(root_doc, 'shared', False):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="分享链接已被撤销"
                )
            
            # 递归获取所有相关文档（包括根文档和所有子文档）
            all_documents = []
            
            async def collect_documents(parent_id: str):
                # 如果是第一次调用（根文档），直接添加根文档
                if parent_id == document_id:
                    # 检查根文档是否有子文档
                    children_count = await self.document_repo.collection.count_documents({"parent_id": parent_id})
                    root_node = DocumentTreeNode(
                        id=str(root_doc.id),
                        title=root_doc.title,
                        parent_id=root_doc.parent_id,
                        has_children=children_count > 0,
                        created_at=root_doc.created_at,
                        updated_at=root_doc.updated_at
                    )
                    all_documents.append(root_node)
                
                # 获取子文档
                children_cursor = self.document_repo.collection.find({"parent_id": parent_id})
                async for child_doc in children_cursor:
                    child_id = str(child_doc["_id"])
                    
                    # 检查这个子文档是否还有子文档
                    grandchildren_count = await self.document_repo.collection.count_documents({"parent_id": child_id})
                    
                    child_node = DocumentTreeNode(
                        id=child_id,
                        title=child_doc.get("title", "无标题"),
                        parent_id=child_doc.get("parent_id"),
                        has_children=grandchildren_count > 0,
                        created_at=child_doc.get("created_at"),
                        updated_at=child_doc.get("updated_at")
                    )
                    all_documents.append(child_node)
                    
                    # 递归处理子文档
                    await collect_documents(child_id)
            
            # 从根文档开始收集所有文档
            await collect_documents(document_id)
            
            return all_documents
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="分享链接已过期"
            )
        except jwt.InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的分享链接"
            )

    async def update_shared_document(self, share_token: str, document: DocumentUpdate, user_id: Optional[str] = None) -> DocumentResponse:
        """通过分享token更新文档"""
        try:
            # 解码token
            secret_key = SECRET_KEY  # 实际项目中应该从配置获取
            payload = jwt.decode(share_token, secret_key, algorithms=["HS256"])
            
            document_id = payload.get("document_id")
            permissions = payload.get("permissions", ["read"])
            
            if not document_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的分享链接"
                )
            
            # 检查是否有写权限
            if "write" not in permissions and "admin" not in permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有编辑权限"
                )
            
            # 获取文档
            doc = await self.document_repo.get_document_by_id(document_id)
            if not doc:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="分享的文档不存在"
                )
            
            # 检查文档是否仍然处于分享状态
            if not getattr(doc, 'shared', False):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="分享链接已被撤销"
                )
            
            # 更新文档，包含修改者信息
            update_data = document.model_dump(exclude_unset=True)
            update_data["updated_at"] = datetime.utcnow()
            
            # 如果提供了用户ID，获取用户信息作为修改者
            if user_id:
                update_data["updated_by"] = user_id
                try:
                    user_info = await self._get_creator_info(user_id)
                    update_data["updated_by_name"] = user_info.get("creator_name", "协作用户")
                except:
                    update_data["updated_by_name"] = "协作用户"
            else:
                # 如果没有用户ID，使用分享用户标识
                update_data["updated_by"] = "shared_user"
                update_data["updated_by_name"] = "分享用户"
            
            await self.document_repo.collection.update_one(
                {"_id": ObjectId(document_id)},
                {"$set": update_data}
            )
            
            # 返回更新后的文档
            updated_doc = await self.document_repo.get_document_by_id(document_id)
            doc_dict = updated_doc.model_dump()
            doc_dict["id"] = str(updated_doc.id)
            doc_dict["share_permissions"] = permissions  # 添加分享权限信息
            
            # 获取创建者信息
            creator_info = await self._get_creator_info(updated_doc.creator_id)
            doc_dict.update(creator_info)
            
            return DocumentResponse(**doc_dict)
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="分享链接已过期"
            )
        except jwt.InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的分享链接"
            )
    
    async def copy_shared_document_to_user(self, share_token: str, user_id: str, target_parent_id: Optional[str] = None) -> DocumentResponse:
        """将分享的文档复制到用户的笔记中"""
        try:
            # 解码token
            secret_key = SECRET_KEY
            payload = jwt.decode(share_token, secret_key, algorithms=["HS256"])
            
            document_id = payload.get("document_id")
            if not document_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的分享链接"
                )
            
            # 获取原文档
            original_doc = await self.document_repo.get_document_by_id(document_id)
            if not original_doc:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="分享的文档不存在"
                )
            
            # 检查文档是否仍然处于分享状态
            if not getattr(original_doc, 'shared', False):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="分享链接已被撤销"
                )
            
            # 获取原始拥有者信息
            original_owner_info = await self._get_creator_info(original_doc.owner_id)
            
            # 创建文档副本，包含原始文档信息
            copy_data = DocumentCreate(
                title=f"{original_doc.title} (副本)",
                content=original_doc.content,
                parent_id=target_parent_id,
                tags=original_doc.tags.copy() if original_doc.tags else [],
                metadata=original_doc.metadata.copy() if original_doc.metadata else {}
            )
            
            # 先创建基础文档
            new_doc = await self.document_repo.create_document(copy_data, user_id)
            
            # 然后更新原始文档信息
            await self.document_repo.collection.update_one(
                {"_id": ObjectId(str(new_doc.id))},
                {
                    "$set": {
                        "original_document_id": document_id,
                        "original_owner_id": original_doc.owner_id,
                        "original_owner_name": original_owner_info.get("creator_name", "未知用户"),
                        "copied_from_share": True,
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            
            # 重新获取更新后的文档
            updated_doc = await self.document_repo.get_document_by_id(str(new_doc.id))
            doc_dict = updated_doc.model_dump()
            doc_dict["id"] = str(updated_doc.id)
            
            # 获取创建者信息
            creator_info = await self._get_creator_info(updated_doc.creator_id)
            doc_dict.update(creator_info)
            
            return DocumentResponse(**doc_dict)
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="分享链接已过期"
            )
        except jwt.InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的分享链接"
            )
    
    async def add_collaborator(
        self, 
        document_id: str, 
        collaborator_id: str, 
        permissions: List[str], 
        user_id: str
    ) -> DocumentResponse:
        """添加协作者"""
        doc = await self.document_repo.get_document_by_id(document_id)
        if not doc:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )
        
        # 检查权限
        if doc.owner_id != user_id and user_id not in doc.permissions.get("admin", []):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限添加协作者"
            )
        
        # 更新协作者列表和权限
        collaborators = list(set(doc.collaborators + [collaborator_id]))
        current_permissions = doc.permissions.copy()
        
        for permission in permissions:
            if permission not in current_permissions:
                current_permissions[permission] = []
            if collaborator_id not in current_permissions[permission]:
                current_permissions[permission].append(collaborator_id)
        
        await self.document_repo.collection.update_one(
            {"_id": ObjectId(document_id)},
            {
                "$set": {
                    "collaborators": collaborators,
                    "permissions": current_permissions,
                    "updated_at": datetime.utcnow()
                }
            }
        )
        
        # 返回更新后的文档
        updated_doc = await self.document_repo.get_document_by_id(document_id)
        doc_dict = updated_doc.model_dump()
        doc_dict["id"] = str(updated_doc.id)
        return DocumentResponse(**doc_dict)
    
    async def remove_collaborator(
        self, 
        document_id: str, 
        collaborator_id: str, 
        user_id: str
    ) -> DocumentResponse:
        """移除协作者"""
        doc = await self.document_repo.get_document_by_id(document_id)
        if not doc:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )
        
        # 检查权限
        if doc.owner_id != user_id and user_id not in doc.permissions.get("admin", []):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限移除协作者"
            )
        
        # 更新协作者列表和权限
        collaborators = [c for c in doc.collaborators if c != collaborator_id]
        current_permissions = doc.permissions.copy()
        
        for permission_list in current_permissions.values():
            if collaborator_id in permission_list:
                permission_list.remove(collaborator_id)
        
        await self.document_repo.collection.update_one(
            {"_id": ObjectId(document_id)},
            {
                "$set": {
                    "collaborators": collaborators,
                    "permissions": current_permissions,
                    "updated_at": datetime.utcnow()
                }
            }
        )
        
        # 返回更新后的文档
        updated_doc = await self.document_repo.get_document_by_id(document_id)
        doc_dict = updated_doc.model_dump()
        doc_dict["id"] = str(updated_doc.id)
        return DocumentResponse(**doc_dict)
    
    async def leave_collaboration(self, document_id: str, user_id: str) -> bool:
        """协作者退出协作"""
        doc = await self.document_repo.get_document_by_id(document_id)
        if not doc:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )
        
        # 检查用户是否是协作者
        if user_id not in doc.collaborators:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="您不是此文档的协作者"
            )
        
        # 不允许文档拥有者退出协作
        if doc.owner_id == user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文档拥有者不能退出协作，请删除文档或转移所有权"
            )
        
        # 从协作者列表中移除用户
        collaborators = [c for c in doc.collaborators if c != user_id]
        current_permissions = doc.permissions.copy()
        
        # 从所有权限列表中移除用户
        for permission_list in current_permissions.values():
            if user_id in permission_list:
                permission_list.remove(user_id)
        
        await self.document_repo.collection.update_one(
            {"_id": ObjectId(document_id)},
            {
                "$set": {
                    "collaborators": collaborators,
                    "permissions": current_permissions,
                    "updated_at": datetime.utcnow()
                }
            }
        )
        
        return True

    async def check_document_permission(
        self, 
        document_id: str, 
        user_id: str, 
        required_permission: str = "read"
    ) -> bool:
        """检查用户对文档的权限"""
        doc = await self.document_repo.get_document_by_id(document_id)
        if not doc:
            return False
        
        # 文档所有者拥有所有权限
        if doc.owner_id == user_id:
            return True
        
        # 检查具体权限
        return user_id in doc.permissions.get(required_permission, [])

    async def add_shared_document_to_user_notes(self, share_token: str, user_id: str) -> DocumentResponse:
        """将分享的文档添加到用户的笔记中（协作模式）"""
        try:
            # 解码token
            secret_key = SECRET_KEY
            payload = jwt.decode(share_token, secret_key, algorithms=["HS256"])
            
            document_id = payload.get("document_id")
            if not document_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的分享链接"
                )
            
            # 获取原文档
            original_doc = await self.document_repo.get_document_by_id(document_id)
            if not original_doc:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="分享的文档不存在"
                )
            
            # 检查文档是否仍然处于分享状态
            if not getattr(original_doc, 'shared', False):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="分享链接已被撤销"
                )
            
            # 检查用户是否已经是协作者
            if user_id in original_doc.collaborators:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="此文档已在您的笔记中"
                )
            
            # 将用户添加为协作者
            collaborators = list(set(original_doc.collaborators + [user_id]))
            
            # 根据分享权限设置用户权限
            permissions = payload.get("permissions", ["read"])
            current_permissions = original_doc.permissions.copy()
            
            for permission in permissions:
                if permission not in current_permissions:
                    current_permissions[permission] = []
                if user_id not in current_permissions[permission]:
                    current_permissions[permission].append(user_id)
            
            # 更新原文档的协作者和权限
            await self.document_repo.collection.update_one(
                {"_id": ObjectId(document_id)},
                {
                    "$set": {
                        "collaborators": collaborators,
                        "permissions": current_permissions,
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            
            # 返回更新后的文档
            updated_doc = await self.document_repo.get_document_by_id(document_id)
            doc_dict = updated_doc.model_dump()
            doc_dict["id"] = str(updated_doc.id)
            doc_dict["share_permissions"] = permissions  # 添加分享权限信息
            
            # 获取创建者信息
            creator_info = await self._get_creator_info(updated_doc.creator_id)
            doc_dict.update(creator_info)
            
            return DocumentResponse(**doc_dict)
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="分享链接已过期"
            )
        except jwt.InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的分享链接"
            )

    async def add_shared_folder_to_user_notes(self, share_token: str, user_id: str) -> DocumentResponse:
        """将分享的文件夹添加到用户的笔记中（复制模式）"""
        try:
            # 解码token
            secret_key = SECRET_KEY
            payload = jwt.decode(share_token, secret_key, algorithms=["HS256"])
            
            document_id = payload.get("document_id")
            is_folder = payload.get("is_folder", False)
            
            if not document_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的分享链接"
                )
            
            if not is_folder:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="此链接不是文件夹分享"
                )
            
            # 获取原文档
            original_doc = await self.document_repo.get_document_by_id(document_id)
            if not original_doc:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="分享的文档不存在"
                )
            
            # 检查文档是否仍然处于分享状态
            if not getattr(original_doc, 'shared', False):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="分享链接已被撤销"
                )
            
            # 获取原始拥有者信息
            original_owner_info = await self._get_creator_info(original_doc.owner_id)
            
            # 递归复制文件夹及其所有子文档
            async def copy_document_recursive(source_doc_id: str, target_parent_id: Optional[str] = None) -> str:
                # 获取源文档
                source_doc = await self.document_repo.get_document_by_id(source_doc_id)
                if not source_doc:
                    return None
                
                # 创建文档副本
                copy_data = DocumentCreate(
                    title=source_doc.title,
                    content=source_doc.content,
                    parent_id=target_parent_id,
                    tags=source_doc.tags.copy() if source_doc.tags else [],
                    metadata=source_doc.metadata.copy() if source_doc.metadata else {}
                )
                
                # 创建新文档
                new_doc = await self.document_repo.create_document(copy_data, user_id)
                
                # 更新原始文档信息
                await self.document_repo.collection.update_one(
                    {"_id": ObjectId(str(new_doc.id))},
                    {
                        "$set": {
                            "original_document_id": source_doc_id,
                            "original_owner_id": original_doc.owner_id,
                            "original_owner_name": original_owner_info.get("creator_name", "未知用户"),
                            "copied_from_share": True,
                            "updated_at": datetime.utcnow()
                        }
                    }
                )
                
                # 递归复制子文档
                children_cursor = self.document_repo.collection.find({"parent_id": source_doc_id})
                async for child_doc in children_cursor:
                    child_id = str(child_doc["_id"])
                    await copy_document_recursive(child_id, str(new_doc.id))
                
                return str(new_doc.id)
            
            # 开始复制
            root_copy_id = await copy_document_recursive(document_id)
            
            # 获取复制后的根文档
            copied_doc = await self.document_repo.get_document_by_id(root_copy_id)
            doc_dict = copied_doc.model_dump()
            doc_dict["id"] = str(copied_doc.id)
            
            # 获取创建者信息
            creator_info = await self._get_creator_info(copied_doc.creator_id)
            doc_dict.update(creator_info)
            
            return DocumentResponse(**doc_dict)
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="分享链接已过期"
            )
        except jwt.InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的分享链接"
            )

    async def add_shared_folder_to_user_notes_collaborative(self, share_token: str, user_id: str) -> DocumentResponse:
        """将分享的文件夹添加到用户的笔记中（协作模式）"""
        try:
            # 解码token
            secret_key = SECRET_KEY
            payload = jwt.decode(share_token, secret_key, algorithms=["HS256"])
            
            document_id = payload.get("document_id")
            is_folder = payload.get("is_folder", False)
            
            if not document_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的分享链接"
                )
            
            if not is_folder:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="此链接不是文件夹分享"
                )
            
            # 获取原文档
            original_doc = await self.document_repo.get_document_by_id(document_id)
            if not original_doc:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="分享的文档不存在"
                )
            
            # 检查文档是否仍然处于分享状态
            if not getattr(original_doc, 'shared', False):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="分享链接已被撤销"
                )
            
            # 检查用户是否已经是协作者
            if user_id in original_doc.collaborators:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="此文件夹已在您的笔记中"
                )
            
            # 获取分享权限
            permissions = payload.get("permissions", ["read"])
            
            # 递归添加协作者到文件夹及所有子文档
            async def add_collaborator_recursive(doc_id: str):
                # 获取文档
                doc = await self.document_repo.get_document_by_id(doc_id)
                if not doc:
                    return
                
                # 添加用户为协作者
                collaborators = list(set(doc.collaborators + [user_id]))
                current_permissions = doc.permissions.copy()
                
                # 设置用户权限
                for permission in permissions:
                    if permission not in current_permissions:
                        current_permissions[permission] = []
                    if user_id not in current_permissions[permission]:
                        current_permissions[permission].append(user_id)
                
                # 更新文档
                await self.document_repo.collection.update_one(
                    {"_id": ObjectId(doc_id)},
                    {
                        "$set": {
                            "collaborators": collaborators,
                            "permissions": current_permissions,
                            "updated_at": datetime.utcnow()
                        }
                    }
                )
                
                # 递归处理子文档
                children_cursor = self.document_repo.collection.find({"parent_id": doc_id})
                async for child_doc in children_cursor:
                    child_id = str(child_doc["_id"])
                    await add_collaborator_recursive(child_id)
            
            # 开始递归添加协作者
            await add_collaborator_recursive(document_id)
            
            # 返回更新后的根文档
            updated_doc = await self.document_repo.get_document_by_id(document_id)
            doc_dict = updated_doc.model_dump()
            doc_dict["id"] = str(updated_doc.id)
            doc_dict["share_permissions"] = permissions  # 添加分享权限信息
            
            # 获取创建者信息
            creator_info = await self._get_creator_info(updated_doc.creator_id)
            doc_dict.update(creator_info)
            
            return DocumentResponse(**doc_dict)
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="分享链接已过期"
            )
        except jwt.InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的分享链接"
            )