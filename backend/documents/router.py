from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Body
from fastapi.responses import JSONResponse, Response
from backend.documents.service import DocumentService
from backend.auth.models import UserDB
from backend.auth.dependencies import get_current_active_user
from backend.documents.models import DocumentCreate, DocumentUpdate, DocumentResponse, DocumentTreeNode
from backend.file.dependencies import upload_image_and_get_url
from backend.file.storage_factory import get_storage
from typing import List
import logging
import re
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/documents", tags=["documents"])

@router.post("", response_model=DocumentResponse, status_code=status.HTTP_201_CREATED)
async def create_document(
    document: DocumentCreate,
    current_user: UserDB = Depends(get_current_active_user)
):
    """创建新文档"""
    try:
        return await DocumentService().create_document(document, str(current_user.id))
    except Exception as e:
        logger.error(f"创建文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建文档失败"
        )

@router.get("/tree", response_model=List[DocumentTreeNode])
async def get_document_tree(
    current_user: UserDB = Depends(get_current_active_user)
):
    """获取用户的文档树结构"""
    try:
        return await DocumentService().get_document_tree(str(current_user.id))
    except Exception as e:
        logger.error(f"获取文档树失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取文档树失败"
        )

@router.get("/image_proxy")
async def image_proxy(url: str):
    """代理访问TOS存储的图片 - 公开接口"""
    try:
        logger.info(f"图片代理请求: URL={url}")
        
        # 验证URL是否为TOS链接
        if not url.startswith("https://tos-") or "volces.com" not in url:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只支持TOS存储链接"
            )
        
        # 解析TOS链接，提取用户ID和文件ID
        # URL格式: https://tos-ap-southeast-3.volces.com/deepcognition/users/{user_id}/{file_id}/{filename}
        pattern = r"https://[^/]+/[^/]+/users/([^/]+)/([^/]+)/([^/]+)"
        match = re.match(pattern, url)
        
        if not match:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的TOS链接格式"
            )
        
        url_user_id, file_id, filename = match.groups()
        logger.info(f"解析TOS链接: user_id={url_user_id}, file_id={file_id}, filename={filename}")
        
        # 获取存储服务
        storage = get_storage()
        logger.info(f"开始下载文件: user_id={url_user_id}, file_id={file_id}")
        
        # 下载文件内容
        file_content = await storage.download_file(
            user_id=url_user_id,
            file_id=file_id
        )
        
        if not file_content:
            logger.warning(f"文件不存在: user_id={url_user_id}, file_id={file_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="图片不存在"
            )
        
        logger.info(f"文件下载成功: 大小={len(file_content)} bytes")
        
        # 根据文件扩展名确定Content-Type
        file_ext = filename.lower().split('.')[-1] if '.' in filename else ''
        content_type_map = {
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'png': 'image/png',
            'gif': 'image/gif',
            'webp': 'image/webp',
            'svg': 'image/svg+xml'
        }
        content_type = content_type_map.get(file_ext, 'image/jpeg')
        
        # 返回图片内容
        return Response(
            content=file_content,
            media_type=content_type,
            headers={
                "Cache-Control": "public, max-age=3600",  # 缓存1小时
                "Content-Disposition": f"inline; filename={filename}"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"图片代理失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="图片代理失败"
        )

@router.get("/debug_auth")
async def debug_auth(
    current_user: UserDB = Depends(get_current_active_user)
):
    """调试认证状态"""
    return {
        "status": "authenticated",
        "user_id": str(current_user.id),
        "username": current_user.username,
        "message": "认证成功"
    }

@router.get("/{document_id}", response_model=DocumentResponse)
async def get_document(
    document_id: str,
    current_user: UserDB = Depends(get_current_active_user)
):
    """获取单个文档"""
    try:
        return await DocumentService().get_document_by_id(document_id)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取文档失败"
        )

@router.put("/{document_id}", response_model=DocumentResponse)
async def update_document(
    document_id: str,
    document: DocumentUpdate,
    current_user: UserDB = Depends(get_current_active_user)
):
    """更新文档"""
    try:
        return await DocumentService().update_document(document_id, document, str(current_user.id))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新文档失败"
        )

@router.delete("/{document_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_document(
    document_id: str,
    force: bool = False,
    current_user: UserDB = Depends(get_current_active_user)
):
    """删除文档 - 只有文档拥有者可以删除
    :param document_id: 文档ID
    :param force: 是否强制删除（包括子文档）
    :param current_user: 当前用户
    """
    try:
        await DocumentService().delete_document(document_id, str(current_user.id), force)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除文档失败"
        )

@router.post("/upload_image")
async def upload_image(
    file: UploadFile = File(...),
    current_user: UserDB = Depends(get_current_active_user)
):
    """上传图片并返回可访问的公开链接"""
    try:
        # 调用dependency时需要手动传入参数，因为FastAPI无法自动注入到普通函数调用中
        from backend.file.storage_factory import get_storage
        storage = get_storage()
        
        # 直接调用dependency的逻辑
        from backend.file.dependencies import upload_image_and_get_url
        
        # 验证文件是否为图片
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只能上传图片文件"
            )
        
        # 验证文件
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文件名不能为空"
            )
        
        # 检查文件大小（假设限制为10MB）
        if file.size and file.size > 10 * 1024 * 1024:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail="文件大小不能超过10MB"
            )
        
        # 上传文件
        file_info = await storage.upload_file(
            user_id=str(current_user.id),
            filename=file.filename,
            file_content=file.file,
            content_type=file.content_type
        )
        
        # 获取公开访问链接
        file_url = await storage.get_file_url(
            user_id=str(current_user.id),
            file_id=file_info.file_id,
            expires_in=365 * 24 * 3600  # 设置为1年过期，用于长期访问
        )
        
        if not file_url:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="无法生成文件访问链接"
            )
        
        logger.info(f"图片上传成功: {file.filename}, URL: {file_url}")
        return JSONResponse(content={"url": file_url})
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"图片上传失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="图片上传失败"
        )

@router.put("/{document_id}/move", response_model=DocumentResponse)
async def move_document(
    document_id: str,
    data: dict = Body(...),
    current_user: UserDB = Depends(get_current_active_user)
):
    """移动文档到新位置，支持精确的位置控制"""
    try:
        target_parent_id = data.get("target_parent_id")
        position = data.get("position", "inside")  # 默认为内部插入
        reference_node_id = data.get("reference_node_id")
        
        return await DocumentService().move_document(
            document_id, 
            target_parent_id, 
            position,
            reference_node_id
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"移动文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="移动文档失败"
        )

# 分享和协作相关的API端点
@router.post("/{document_id}/share")
async def share_document(
    document_id: str,
    data: dict = Body(...),
    current_user: UserDB = Depends(get_current_active_user)
):
    """分享文档"""
    try:
        permissions = data.get("permissions", ["read"])
        expiry_days = data.get("expiry_days")
        
        result = await DocumentService().share_document(
            document_id, 
            str(current_user.id), 
            permissions,
            expiry_days
        )
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分享文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="分享文档失败"
        )

@router.delete("/{document_id}/share")
async def revoke_share(
    document_id: str,
    current_user: UserDB = Depends(get_current_active_user)
):
    """撤销文档分享"""
    try:
        success = await DocumentService().revoke_share(document_id, str(current_user.id))
        return {"success": success}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"撤销分享失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="撤销分享失败"
        )

@router.get("/shared/{share_token}", response_model=DocumentResponse)
async def get_shared_document(share_token: str):
    """通过分享链接获取文档（公开接口）"""
    try:
        return await DocumentService().get_shared_document(share_token)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取分享文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取分享文档失败"
        )

@router.get("/shared/{share_token}/tree")
async def get_shared_folder_tree(share_token: str):
    """获取分享文件夹的文档树（公开接口）"""
    try:
        return await DocumentService().get_shared_folder_tree(share_token)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取分享文件夹树失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取分享文件夹树失败"
        )

@router.get("/shared/{share_token}/document/{document_id}", response_model=DocumentResponse)
async def get_document_in_shared_folder(share_token: str, document_id: str):
    """获取分享文件夹中的特定文档（公开接口）"""
    try:
        # 获取分享文件夹的文档树来验证权限和文档归属
        document_tree = await DocumentService().get_shared_folder_tree(share_token)
        
        # 检查请求的文档是否在分享的文件夹树中
        document_ids_in_tree = {doc.id for doc in document_tree}
        
        if document_id not in document_ids_in_tree:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不在分享的文件夹中"
            )
        
        # 获取并返回文档
        document = await DocumentService().get_document_by_id(document_id)
        
        return document
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取分享文件夹中的文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取文档失败"
        )

@router.put("/shared/{share_token}", response_model=DocumentResponse)
async def update_shared_document(
    share_token: str,
    document: DocumentUpdate,
    current_user: UserDB = Depends(get_current_active_user)
):
    """通过分享token更新文档"""
    try:
        return await DocumentService().update_shared_document(share_token, document, str(current_user.id))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新分享文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新分享文档失败"
        )

@router.post("/shared/{share_token}/copy", response_model=DocumentResponse)
async def copy_shared_document(
    share_token: str,
    data: dict = Body(...),
    current_user: UserDB = Depends(get_current_active_user)
):
    """将分享的文档复制到用户的笔记中"""
    try:
        target_parent_id = data.get("target_parent_id")
        return await DocumentService().copy_shared_document_to_user(
            share_token, 
            str(current_user.id),
            target_parent_id
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"复制分享文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="复制分享文档失败"
        )

@router.post("/{document_id}/collaborators")
async def add_collaborator(
    document_id: str,
    data: dict = Body(...),
    current_user: UserDB = Depends(get_current_active_user)
):
    """添加协作者"""
    try:
        collaborator_id = data.get("collaborator_id")
        permissions = data.get("permissions", ["read"])
        
        if not collaborator_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="协作者ID不能为空"
            )
        
        return await DocumentService().add_collaborator(
            document_id,
            collaborator_id,
            permissions,
            str(current_user.id)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加协作者失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="添加协作者失败"
        )

@router.delete("/{document_id}/collaborators/{collaborator_id}")
async def remove_collaborator(
    document_id: str,
    collaborator_id: str,
    current_user: UserDB = Depends(get_current_active_user)
):
    """移除协作者"""
    try:
        return await DocumentService().remove_collaborator(
            document_id,
            collaborator_id,
            str(current_user.id)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"移除协作者失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="移除协作者失败"
        )

@router.post("/{document_id}/leave")
async def leave_collaboration(
    document_id: str,
    current_user: UserDB = Depends(get_current_active_user)
):
    """退出协作"""
    try:
        success = await DocumentService().leave_collaboration(document_id, str(current_user.id))
        return {"success": success, "message": "已成功退出协作"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"退出协作失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="退出协作失败"
        )

@router.get("/{document_id}/permissions")
async def check_permissions(
    document_id: str,
    permission: str = "read",
    current_user: UserDB = Depends(get_current_active_user)
):
    """检查用户对文档的权限"""
    try:
        has_permission = await DocumentService().check_document_permission(
            document_id,
            str(current_user.id),
            permission
        )
        return {"has_permission": has_permission}
    except Exception as e:
        logger.error(f"检查权限失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="检查权限失败"
        )

@router.post("/shared/{share_token}/add_to_notes", response_model=DocumentResponse)
async def add_shared_document_to_notes(
    share_token: str,
    current_user: UserDB = Depends(get_current_active_user)
):
    """将分享的文档添加到用户的笔记中（协作模式）"""
    try:
        return await DocumentService().add_shared_document_to_user_notes(
            share_token, 
            str(current_user.id)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加分享文档到笔记失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="添加分享文档到笔记失败"
        )

@router.post("/shared/{share_token}/add_folder_to_notes", response_model=DocumentResponse)
async def add_shared_folder_to_notes(
    share_token: str,
    current_user: UserDB = Depends(get_current_active_user)
):
    """将分享的文件夹添加到用户的笔记中（复制模式）"""
    try:
        return await DocumentService().add_shared_folder_to_user_notes(
            share_token, 
            str(current_user.id)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加分享文件夹到笔记失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="添加分享文件夹到笔记失败"
        )

@router.post("/shared/{share_token}/add_folder_to_notes_collaborative", response_model=DocumentResponse)
async def add_shared_folder_to_notes_collaborative(
    share_token: str,
    current_user: UserDB = Depends(get_current_active_user)
):
    """将分享的文件夹添加到用户的笔记中（协作模式）"""
    try:
        return await DocumentService().add_shared_folder_to_user_notes_collaborative(
            share_token, 
            str(current_user.id)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加分享文件夹到笔记失败（协作模式）: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="添加分享文件夹到笔记失败"
        )