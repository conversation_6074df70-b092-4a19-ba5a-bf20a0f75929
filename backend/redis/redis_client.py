import redis.asyncio as redis
import json
import logging
import time
import inspect
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timezone, timedelta
import pickle
from backend.config import settings

logger = logging.getLogger(__name__)

def get_caller_info():
    """获取调用者信息"""
    try:
        # 获取调用堆栈
        frame = inspect.currentframe()
        # 跳过当前函数和被装饰的函数
        caller_frame = frame.f_back.f_back
        if caller_frame:
            filename = caller_frame.f_code.co_filename
            line_number = caller_frame.f_lineno
            function_name = caller_frame.f_code.co_name
            # 只显示文件名，不显示完整路径
            filename = filename.split('/')[-1]
            return f"{filename}:{function_name}:{line_number}"
        return "未知调用者"
    except:
        return "获取调用者失败"
    finally:
        del frame  # 避免内存泄漏

class DateTimeAwareJSONEncoder(json.JSONEncoder):
    """支持 datetime 序列化的 JSON 编码器"""
    def default(self, obj):
        if isinstance(obj, datetime):
            # 将 datetime 对象转换为 ISO 格式字符串，并添加类型标记
            return {
                "__datetime__": True,
                "value": obj.isoformat()
            }
        return super().default(obj)

def datetime_aware_json_hook(dct):
    """支持 datetime 反序列化的 JSON 解码钩子"""
    if isinstance(dct, dict) and dct.get("__datetime__"):
        try:
            # 将 ISO 格式字符串转换回 datetime 对象
            return datetime.fromisoformat(dct["value"])
        except (ValueError, TypeError) as e:
            logger.warning(f"无法解析 datetime: {dct['value']}, 错误: {e}")
            # 如果解析失败，返回原始字符串
            return dct["value"]
    return dct

class RedisClient:
    """Redis客户端封装类，提供基础的Redis操作接口（单例模式）"""
    # 花簇
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self.client: Optional[redis.Redis] = None
            self.app_key_prefix = "research_agent:"  # 应用相关的键前缀
            self.connect()
            RedisClient._initialized = True
    
    def connect(self):
        """建立Redis连接"""
        try:
            # 根据环境选择不同的Redis配置
            base_url = settings.REDIS_URL
            if settings.ENVIRONMENT == "development":
                redis_url = base_url  # 开发环境使用默认配置的数据库
            elif settings.ENVIRONMENT == "testing":
                # 测试环境使用不同的数据库
                redis_url = base_url.replace('/0', '/1') if '/0' in base_url else f"{base_url.rstrip('/')}/1"
            else:  # production
                redis_url = base_url
            
            self.client = redis.from_url(
                redis_url,
                decode_responses=False,  # 保持二进制数据用于pickle
                socket_connect_timeout=settings.REDIS_SOCKET_TIMEOUT,
                socket_keepalive=True,
                socket_keepalive_options={},
                health_check_interval=30,
                retry_on_timeout=True,
                max_connections=settings.REDIS_MAX_CONNECTIONS
            )
            logger.info(f"Redis连接成功: {redis_url} (环境: {settings.ENVIRONMENT})")
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            self.client = None
            raise RuntimeError(f"无法连接到Redis: {str(e)}")
    
    def _serialize_value(self, value: Any) -> bytes:
        """
        序列化值，支持 datetime 对象
        
        Args:
            value: 要序列化的值
            
        Returns:
            序列化后的字节数据
        """
        try:
            if isinstance(value, (dict, list)):
                # 使用支持 datetime 的 JSON 编码器
                return json.dumps(value, cls=DateTimeAwareJSONEncoder, ensure_ascii=False).encode('utf-8')
            elif isinstance(value, str):
                return value.encode('utf-8')
            elif isinstance(value, datetime):
                # 直接处理 datetime 对象
                datetime_dict = {
                    "__datetime__": True,
                    "value": value.isoformat()
                }
                return json.dumps(datetime_dict, ensure_ascii=False).encode('utf-8')
            else:
                # 其他复杂对象使用pickle
                return pickle.dumps(value)
        except Exception as e:
            logger.error(f"序列化失败: {type(value)} - {e}")
            # 对于复杂对象，尝试分析具体的不可序列化内容
            if hasattr(value, '__dict__'):
                logger.error(f"对象属性: {list(value.__dict__.keys()) if value.__dict__ else 'None'}")
                # 检查是否包含线程锁等不可序列化的对象
                for attr_name, attr_value in (value.__dict__.items() if value.__dict__ else []):
                    try:
                        pickle.dumps(attr_value)
                    except Exception as attr_e:
                        logger.error(f"不可序列化的属性 {attr_name}: {type(attr_value)} - {attr_e}")
            
            # 降级到 pickle
            try:
                return pickle.dumps(value)
            except Exception as pickle_e:
                logger.error(f"pickle序列化也失败: {pickle_e}")
                # 如果连pickle都失败，返回错误信息的字节形式
                error_msg = f"无法序列化对象: {type(value).__name__}"
                return error_msg.encode('utf-8')
    
    def _deserialize_value(self, value: bytes, default: Any = None) -> Any:
        """
        反序列化值，支持 datetime 对象
        
        Args:
            value: 要反序列化的字节数据
            default: 默认值
            
        Returns:
            反序列化后的值
        """
        if value is None:
            return default
        
        try:
            # 首先尝试JSON（支持datetime）
            try:
                json_str = value.decode('utf-8')
                return json.loads(json_str, object_hook=datetime_aware_json_hook)
            except (json.JSONDecodeError, UnicodeDecodeError):
                pass
            
            # 然后尝试简单字符串
            try:
                return value.decode('utf-8')
            except UnicodeDecodeError:
                pass
            
            # 最后尝试pickle
            try:
                return pickle.loads(value)
            except:
                pass
            
            # 如果所有方法都失败，返回默认值
            logger.warning(f"无法反序列化数据，返回默认值: {default}")
            return default
            
        except Exception as e:
            logger.error(f"反序列化失败: {e}")
            return default
    
    async def ping(self) -> bool:
        """测试Redis连接是否正常"""
        if self.client is None:
            return False
        try:
            await self.client.ping()
            return True
        except Exception as e:
            logger.error(f"Redis ping失败: {e}")
            return False
    
    async def set(self, key: str, value: Any, ex: Optional[int] = None) -> bool:
        """
        设置键值对
        :param key: 键
        :param value: 值（自动序列化，支持datetime）
        :param ex: 过期时间（秒）
        :return: 操作是否成功
        """
        if self.client is None:
            logger.error("Redis未连接")
            return False
        
        caller_info = get_caller_info()
        start_time = time.time()
        # logger.info(f"[Redis] 开始设置键值 - 调用者: {caller_info}, 键: {key}, 过期时间: {ex}秒")
        
        try:
            serialized_value = self._serialize_value(value)
            result = await self.client.set(key, serialized_value, ex=ex)
            elapsed_time = time.time() - start_time
            # logger.info(f"[Redis] 设置键值完成 - 调用者: {caller_info}, 键: {key}, 耗时: {elapsed_time:.3f}秒, 结果: {'成功' if result else '失败'}")
            return bool(result)
        except Exception as e:
            elapsed_time = time.time() - start_time
            # logger.error(f"[Redis] 设置键值错误 - 调用者: {caller_info}, 键: {key}, 耗时: {elapsed_time:.3f}秒, 错误: {e}")
            return False
    
    async def get(self, key: str, default: Any = None) -> Any:
        """
        获取键值
        :param key: 键
        :param default: 默认值
        :return: 反序列化后的值（支持datetime）
        """
        if self.client is None:
            logger.error("Redis未连接")
            return default
        
        caller_info = get_caller_info()
        start_time = time.time()
        # logger.info(f"[Redis] 开始获取键值 - 调用者: {caller_info}, 键: {key}")
        
        try:
            value = await self.client.get(key)
            result = self._deserialize_value(value, default)
            elapsed_time = time.time() - start_time
            # logger.info(f"[Redis] 获取键值完成 - 调用者: {caller_info}, 键: {key}, 耗时: {elapsed_time:.3f}秒, 结果: {'找到' if value is not None else '未找到'}")
            return result
        except Exception as e:
            elapsed_time = time.time() - start_time
            # logger.error(f"[Redis] 获取键值错误 - 调用者: {caller_info}, 键: {key}, 耗时: {elapsed_time:.3f}秒, 错误: {e}")
            return default
    
    async def delete(self, *keys: str) -> int:
        """
        删除一个或多个键
        :param keys: 键列表
        :return: 删除的键数量
        """
        if self.client is None:
            logger.error("Redis未连接")
            return 0
        
        caller_info = get_caller_info()
        start_time = time.time()
        # logger.info(f"[Redis] 开始删除键 - 调用者: {caller_info}, 键数量: {len(keys)}, 键: {keys}")
        
        try:
            result = await self.client.delete(*keys)
            elapsed_time = time.time() - start_time
            # logger.info(f"[Redis] 删除键完成 - 调用者: {caller_info}, 耗时: {elapsed_time:.3f}秒, 删除数量: {result}")
            return result
        except Exception as e:
            elapsed_time = time.time() - start_time
            # logger.error(f"[Redis] 删除键错误 - 调用者: {caller_info}, 键: {keys}, 耗时: {elapsed_time:.3f}秒, 错误: {e}")
            return 0
    
    async def exists(self, key: str) -> bool:
        """
        检查键是否存在
        :param key: 键
        :return: 是否存在
        """
        if self.client is None:
            return False
        
        caller_info = get_caller_info()
        start_time = time.time()
        # logger.info(f"[Redis] 开始检查键存在 - 调用者: {caller_info}, 键: {key}")
        
        try:
            result = bool(await self.client.exists(key))
            elapsed_time = time.time() - start_time
            # logger.info(f"[Redis] 检查键存在完成 - 调用者: {caller_info}, 键: {key}, 耗时: {elapsed_time:.3f}秒, 结果: {'存在' if result else '不存在'}")
            return result
        except Exception as e:
            elapsed_time = time.time() - start_time
            # logger.error(f"[Redis] 检查键存在错误 - 调用者: {caller_info}, 键: {key}, 耗时: {elapsed_time:.3f}秒, 错误: {e}")
            return False
    
    async def expire(self, key: str, seconds: int) -> bool:
        """
        设置键的过期时间
        :param key: 键
        :param seconds: 过期时间（秒）
        :return: 操作是否成功
        """
        if self.client is None:
            return False
        
        caller_info = get_caller_info()
        start = time.time()
        # logger.info(f"[Redis] 开始设置过期时间 - 调用者: {caller_info}, 键: {key}, 时间: {seconds}秒")
        
        try:
            result = bool(await self.client.expire(key, seconds))
            elapsed_time = time.time() - start
            # logger.info(f"[Redis] 设置过期时间完成 - 调用者: {caller_info}, 键: {key}, 耗时: {elapsed_time:.3f}秒, 结果: {'成功' if result else '失败'}")
            return result
        except Exception as e:
            elapsed_time = time.time() - start
            # logger.error(f"[Redis] 设置过期时间错误 - 调用者: {caller_info}, 键: {key}, 耗时: {elapsed_time:.3f}秒, 错误: {e}")
            return False
    
    async def ttl(self, key: str) -> int:
        """
        获取键的剩余生存时间
        :param key: 键
        :return: 剩余时间（秒），-1表示无过期时间，-2表示键不存在
        """
        if self.client is None:
            return -2
        
        try:
            return await self.client.ttl(key)
        except Exception as e:
            logger.error(f"Redis获取TTL失败 {key}: {e}")
            return -2
    
    async def keys(self, pattern: str = "*") -> List[str]:
        """
        获取匹配模式的所有键
        :param pattern: 匹配模式
        :return: 键列表
        """
        if self.client is None:
            return []
        
        try:
            keys = await self.client.keys(pattern)
            return [key.decode('utf-8') if isinstance(key, bytes) else key for key in keys]
        except Exception as e:
            logger.error(f"Redis获取键列表失败 {pattern}: {e}")
            return []
    
    async def hset(self, name: str, mapping: Dict[str, Any]) -> int:
        """
        设置哈希表字段
        :param name: 哈希表名
        :param mapping: 字段映射
        :return: 新增字段数量
        """
        if self.client is None:
            return 0
        
        caller_info = get_caller_info()
        start_time = time.time()
        # logger.info(f"[Redis] 开始设置哈希表 - 调用者: {caller_info}, 哈希表: {name}, 字段数量: {len(mapping)}")
        
        try:
            # 序列化所有值，支持 datetime
            serialized_mapping = {}
            for key, value in mapping.items():
                # 使用统一的序列化方法
                serialized_value = self._serialize_value(value)
                # 哈希表需要字符串值，所以解码为字符串
                if isinstance(serialized_value, bytes):
                    try:
                        serialized_mapping[key] = serialized_value.decode('utf-8')
                    except UnicodeDecodeError:
                        # 如果不能解码为字符串，使用base64编码
                        import base64
                        serialized_mapping[key] = f"__pickle_base64__{base64.b64encode(serialized_value).decode('ascii')}"
                else:
                    serialized_mapping[key] = str(serialized_value)
            
            result = await self.client.hset(name, mapping=serialized_mapping)
            elapsed_time = time.time() - start_time
            # logger.info(f"[Redis] 设置哈希表完成 - 调用者: {caller_info}, 哈希表: {name}, 耗时: {elapsed_time:.3f}秒, 新增字段: {result}")
            return result
        except Exception as e:
            elapsed_time = time.time() - start_time
            # logger.error(f"[Redis] 设置哈希表错误 - 调用者: {caller_info}, 哈希表: {name}, 耗时: {elapsed_time:.3f}秒, 错误: {e}")
            return 0
    
    async def hget(self, name: str, key: str, default: Any = None) -> Any:
        """
        获取哈希表字段值
        :param name: 哈希表名
        :param key: 字段名
        :param default: 默认值
        :return: 字段值
        """
        if self.client is None:
            return default
        
        caller_info = get_caller_info()
        start_time = time.time()
        # logger.info(f"[Redis] 开始获取哈希表字段 - 调用者: {caller_info}, 哈希表: {name}, 字段: {key}")
        
        try:
            value = await self.client.hget(name, key)
            if value is None:
                elapsed_time = time.time() - start_time
                # logger.info(f"[Redis] 获取哈希表字段完成 - 调用者: {caller_info}, 哈希表: {name}, 字段: {key}, 耗时: {elapsed_time:.3f}秒, 结果: 未找到")
                return default
            
            # 处理特殊的pickle base64编码
            if isinstance(value, (str, bytes)):
                value_str = value.decode('utf-8') if isinstance(value, bytes) else value
                if value_str.startswith("__pickle_base64__"):
                    import base64
                    try:
                        pickle_data = base64.b64decode(value_str[17:])  # 移除前缀
                        result = pickle.loads(pickle_data)
                        elapsed_time = time.time() - start_time
                        # logger.info(f"[Redis] 获取哈希表字段完成 - 调用者: {caller_info}, 哈希表: {name}, 字段: {key}, 耗时: {elapsed_time:.3f}秒, 结果: 找到(pickle)")
                        return result
                    except Exception as e:
                        logger.error(f"无法解码pickle base64数据: {e}")
                        elapsed_time = time.time() - start_time
                        # logger.info(f"[Redis] 获取哈希表字段完成 - 调用者: {caller_info}, 哈希表: {name}, 字段: {key}, 耗时: {elapsed_time:.3f}秒, 结果: 解码失败")
                        return default
                
                # 使用统一的反序列化方法
                value_bytes = value_str.encode('utf-8')
                result = self._deserialize_value(value_bytes, default)
                elapsed_time = time.time() - start_time
                # logger.info(f"[Redis] 获取哈希表字段完成 - 调用者: {caller_info}, 哈希表: {name}, 字段: {key}, 耗时: {elapsed_time:.3f}秒, 结果: 找到")
                return result
            
            elapsed_time = time.time() - start_time
            # logger.info(f"[Redis] 获取哈希表字段完成 - 调用者: {caller_info}, 哈希表: {name}, 字段: {key}, 耗时: {elapsed_time:.3f}秒, 结果: 找到(原始)")
            return value
        except Exception as e:
            elapsed_time = time.time() - start_time
            # logger.error(f"[Redis] 获取哈希表字段错误 - 调用者: {caller_info}, 哈希表: {name}, 字段: {key}, 耗时: {elapsed_time:.3f}秒, 错误: {e}")
            return default
    
    async def hgetall(self, name: str) -> Dict[str, Any]:
        """
        获取哈希表所有字段
        :param name: 哈希表名
        :return: 字段映射
        """
        if self.client is None:
            return {}
        
        try:
            result = await self.client.hgetall(name)
            if not result:
                return {}
            
            # 反序列化所有值，支持 datetime
            deserialized_result = {}
            for key, value in result.items():
                key_str = key.decode('utf-8') if isinstance(key, bytes) else key
                
                # 处理特殊的pickle base64编码
                if isinstance(value, (str, bytes)):
                    value_str = value.decode('utf-8') if isinstance(value, bytes) else value
                    if value_str.startswith("__pickle_base64__"):
                        import base64
                        try:
                            pickle_data = base64.b64decode(value_str[17:])  # 移除前缀
                            deserialized_result[key_str] = pickle.loads(pickle_data)
                            continue
                        except Exception as e:
                            logger.error(f"无法解码pickle base64数据: {e}")
                            deserialized_result[key_str] = value_str
                            continue
                    
                    # 使用统一的反序列化方法
                    value_bytes = value_str.encode('utf-8')
                    deserialized_result[key_str] = self._deserialize_value(value_bytes, value_str)
                else:
                    deserialized_result[key_str] = value
            
            return deserialized_result
        except Exception as e:
            logger.error(f"Redis获取哈希表失败 {name}: {e}")
            return {}
    
    async def hdel(self, name: str, *keys: str) -> int:
        """
        删除哈希表字段
        :param name: 哈希表名
        :param keys: 字段名列表
        :return: 删除的字段数量
        """
        if self.client is None:
            return 0
        
        try:
            return await self.client.hdel(name, *keys)
        except Exception as e:
            logger.error(f"Redis删除哈希表字段失败 {name}: {e}")
            return 0
    
    async def close(self):
        """关闭Redis连接"""
        if self.client:
            await self.client.close()
            self.client = None
            logger.info("Redis连接已关闭")
    
    async def clear_app_data(self, app_prefixes: Optional[List[str]] = None) -> int:
        """
        清理应用相关的Redis数据（应用重启时调用）
        
        Args:
            app_prefixes: 应用键前缀列表，如果为None则使用默认前缀
            
        Returns:
            删除的键数量
        """
        if self.client is None:
            logger.error("Redis未连接，无法清理应用数据")
            return 0
        
        try:
            # 设置默认前缀
            if app_prefixes is None:
                app_prefixes = [
                    self.app_key_prefix,           # 通用应用前缀
                    "research_memory:",            # 研究内存缓存
                    "core:",                       # 核心数据缓存
                    "messages:",                   # 消息缓存
                    "contexts:",                   # 上下文缓存
                    "history:",                    # 历史数据缓存
                ]
            
            deleted_count = 0
            for prefix in app_prefixes:
                pattern = f"{prefix}*"
                keys_to_delete = await self.keys(pattern)
                
                if keys_to_delete:
                    deleted_count += await self.delete(*keys_to_delete)
                    logger.info(f"清理Redis键前缀 '{prefix}': 删除了 {len(keys_to_delete)} 个键")
                else:
                    logger.debug(f"Redis键前缀 '{prefix}' 没有找到需要删除的键")
            
            logger.info(f"Redis应用数据清理完成，总共删除了 {deleted_count} 个键")
            return deleted_count
            
        except Exception as e:
            logger.error(f"清理Redis应用数据失败: {e}")
            return 0
    
    async def reset_for_restart(self) -> bool:
        """
        应用重启时的Redis重置操作
        
        Returns:
            重置是否成功
        """
        try:
            # 清理应用相关数据
            deleted_count = await self.clear_app_data()
            
            # 可以在这里添加其他重启时需要的操作
            # 比如重置某些计数器、清理会话锁等
            
            logger.info(f"Redis重启重置完成，清理了 {deleted_count} 个键")
            return True
            
        except Exception as e:
            logger.error(f"Redis重启重置失败: {e}")
            return False
    
    @classmethod
    def get_instance(cls) -> 'RedisClient':
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    @classmethod
    async def reset_instance(cls) -> bool:
        """重置单例实例（用于测试或完全重启）"""
        try:
            if cls._instance and cls._instance.client:
                await cls._instance.close()
            cls._instance = None
            cls._initialized = False
            logger.info("Redis单例实例已重置")
            return True
        except Exception as e:
            logger.error(f"重置Redis单例实例失败: {e}")
            return False
    
    # ==================== 列表操作（用于消息队列） ====================
    
    async def lpush(self, key: str, *values: Any) -> int:
        """
        从列表左侧推入一个或多个值
        :param key: 列表键名
        :param values: 要推入的值
        :return: 推入后列表的长度
        """
        if self.client is None:
            logger.error("Redis未连接")
            return 0
        
        caller_info = get_caller_info()
        start_time = time.time()
        # logger.info(f"[Redis] 开始列表左推入 - 调用者: {caller_info}, 键: {key}, 值数量: {len(values)}")
        
        try:
            # 使用统一的序列化方法
            serialized_values = []
            for value in values:
                serialized_value = self._serialize_value(value)
                # 列表需要字符串或bytes值
                if isinstance(serialized_value, bytes):
                    serialized_values.append(serialized_value)
                else:
                    serialized_values.append(str(serialized_value).encode('utf-8'))
            
            result = await self.client.lpush(key, *serialized_values)
            elapsed_time = time.time() - start_time
            # logger.info(f"[Redis] 列表左推入完成 - 调用者: {caller_info}, 键: {key}, 耗时: {elapsed_time:.3f}秒, 列表长度: {result}")
            return result
        except Exception as e:
            elapsed_time = time.time() - start_time
            # logger.error(f"[Redis] 列表左推入错误 - 调用者: {caller_info}, 键: {key}, 耗时: {elapsed_time:.3f}秒, 错误: {e}")
            return 0
    
    async def rpush(self, key: str, *values: Any) -> int:
        """
        从列表右侧推入一个或多个值
        :param key: 列表键名
        :param values: 要推入的值
        :return: 推入后列表的长度
        """
        if self.client is None:
            logger.error("Redis未连接")
            return 0
        
        try:
            # 使用统一的序列化方法
            serialized_values = []
            for value in values:
                serialized_value = self._serialize_value(value)
                # 列表需要字符串或bytes值
                if isinstance(serialized_value, bytes):
                    serialized_values.append(serialized_value)
                else:
                    serialized_values.append(str(serialized_value).encode('utf-8'))
            
            result = await self.client.rpush(key, *serialized_values)
            return result
        except Exception as e:
            logger.error(f"Redis rpush失败 {key}: {e}")
            return 0
    
    async def lpop(self, key: str, count: Optional[int] = None) -> Union[Any, List[Any], None]:
        """
        从列表左侧弹出一个或多个值
        :param key: 列表键名
        :param count: 弹出数量，None表示弹出一个
        :return: 弹出的值或值列表
        """
        if self.client is None:
            logger.error("Redis未连接")
            return None
        
        try:
            if count is None:
                # 弹出单个值
                value = await self.client.lpop(key)
                return self._deserialize_value(value) if value else None
            else:
                # 弹出多个值
                values = await self.client.lpop(key, count)
                if values:
                    return [self._deserialize_value(v) for v in values]
                return []
        except Exception as e:
            logger.error(f"Redis lpop失败 {key}: {e}")
            return None if count is None else []
    
    async def rpop(self, key: str, count: Optional[int] = None) -> Union[Any, List[Any], None]:
        """
        从列表右侧弹出一个或多个值
        :param key: 列表键名
        :param count: 弹出数量，None表示弹出一个
        :return: 弹出的值或值列表
        """
        if self.client is None:
            logger.error("Redis未连接")
            return None
        
        try:
            if count is None:
                # 弹出单个值
                value = await self.client.rpop(key)
                return self._deserialize_value(value) if value else None
            else:
                # 弹出多个值
                values = await self.client.rpop(key, count)
                if values:
                    return [self._deserialize_value(v) for v in values]
                return []
        except Exception as e:
            logger.error(f"Redis rpop失败 {key}: {e}")
            return None if count is None else []
    
    async def llen(self, key: str) -> int:
        """
        获取列表长度
        :param key: 列表键名
        :return: 列表长度
        """
        if self.client is None:
            logger.error("Redis未连接")
            return 0
        
        try:
            return await self.client.llen(key)
        except Exception as e:
            logger.error(f"Redis llen失败 {key}: {e}")
            return 0
    
    async def lrange(self, key: str, start: int, stop: int) -> List[Any]:
        """
        获取列表指定范围的元素
        :param key: 列表键名
        :param start: 开始索引
        :param stop: 结束索引
        :return: 元素列表
        """
        if self.client is None:
            logger.error("Redis未连接")
            return []
        
        try:
            values = await self.client.lrange(key, start, stop)
            if values:
                return [self._deserialize_value(v) for v in values]
            return []
        except Exception as e:
            logger.error(f"Redis lrange失败 {key}: {e}")
            return []
    
    async def ltrim(self, key: str, start: int, end: int) -> bool:
        """
        修剪列表，只保留指定范围的元素
        :param key: 列表键名
        :param start: 开始位置
        :param end: 结束位置
        :return: 操作是否成功
        """
        if self.client is None:
            logger.error("Redis未连接")
            return False
        
        try:
            await self.client.ltrim(key, start, end)
            return True
        except Exception as e:
            logger.error(f"Redis ltrim失败 {key}: {e}")
            return False 