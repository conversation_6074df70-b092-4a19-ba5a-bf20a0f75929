import logging
import asyncio
from .redis_client import RedisClient
from backend.config import settings

logger = logging.getLogger(__name__)

def get_redis_client() -> RedisClient:
    """
    获取 Redis 客户端实例（单例模式）
    :return: Redis 客户端
    """
    return RedisClient.get_instance()

async def shutdown_redis_client():
    """
    关闭 Redis 客户端
    """
    try:
        redis_client = RedisClient.get_instance()
        await redis_client.close()
        logger.info("Redis 客户端已关闭")
    except Exception as e:
        logger.error(f"关闭 Redis 客户端失败: {e}")

async def reset_redis_for_restart():
    """
    应用重启时重置Redis相关数据
    """
    try:
        redis_client = RedisClient.get_instance()
        success = await redis_client.reset_for_restart()
        if success:
            logger.info("Redis应用数据重置成功")
        else:
            logger.warning("Redis应用数据重置失败")
        return success
    except Exception as e:
        logger.error(f"Redis重启重置失败: {e}")
        return False 