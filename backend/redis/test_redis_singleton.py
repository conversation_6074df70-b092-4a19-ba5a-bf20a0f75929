#!/usr/bin/env python3
"""
Redis单例模式测试脚本
用于验证RedisClient的单例实现是否正常工作
"""

import asyncio
import logging
from redis_client import RedisClient
from dependencies import get_redis_client, reset_redis_for_restart

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_redis_singleton():
    """测试Redis单例模式"""
    logger.info("开始测试Redis单例模式...")
    
    # 测试1: 多次获取实例，应该是同一个对象
    print("\n=== 测试1: 单例模式验证 ===")
    client1 = RedisClient()
    client2 = RedisClient.get_instance()
    client3 = get_redis_client()
    
    print(f"client1 id: {id(client1)}")
    print(f"client2 id: {id(client2)}")
    print(f"client3 id: {id(client3)}")
    print(f"是否为同一实例: {client1 is client2 is client3}")
    
    # 测试2: 基本Redis操作
    print("\n=== 测试2: 基本Redis操作 ===")
    redis_client = get_redis_client()
    
    # 测试连接
    is_connected = await redis_client.ping()
    print(f"Redis连接状态: {is_connected}")
    
    if is_connected:
        # 测试基本的set/get操作
        test_key = "test:singleton:key"
        test_value = {"message": "Hello Redis Singleton!", "timestamp": "2024-01-01"}
        
        # 设置值
        set_result = await redis_client.set(test_key, test_value, ex=60)
        print(f"设置值结果: {set_result}")
        
        # 获取值
        get_result = await redis_client.get(test_key)
        print(f"获取值结果: {get_result}")
        
        # 测试3: 应用数据清理
        print("\n=== 测试3: 应用数据清理 ===")
        
        # 添加一些测试数据
        test_keys = [
            "research_memory:test1",
            "core:test2", 
            "messages:test3",
            "contexts:test4",
            "other:test5"  # 这个不应该被清理
        ]
        
        for key in test_keys:
            await redis_client.set(key, f"test_data_for_{key}", ex=3600)
        
        print("添加了测试数据，键列表:")
        all_keys = await redis_client.keys("*test*")
        for key in all_keys:
            print(f"  - {key}")
        
        # 执行应用数据清理
        deleted_count = await redis_client.clear_app_data()
        print(f"清理了 {deleted_count} 个应用相关的键")
        
        # 检查剩余的键
        remaining_keys = await redis_client.keys("*test*")
        print("清理后剩余的键:")
        for key in remaining_keys:
            print(f"  - {key}")
        
        # 测试4: 重启重置
        print("\n=== 测试4: 重启重置 ===")
        success = await reset_redis_for_restart()
        print(f"重启重置结果: {success}")
        
        # 清理测试数据
        await redis_client.delete(*remaining_keys)
        print("清理了剩余的测试数据")
    
    print("\n=== 测试完成 ===")

async def main():
    """主函数"""
    try:
        await test_redis_singleton()
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        raise
    finally:
        # 清理资源
        try:
            redis_client = get_redis_client()
            await redis_client.close()
            print("Redis连接已关闭")
        except Exception as e:
            logger.error(f"关闭Redis连接时出错: {e}")

if __name__ == "__main__":
    asyncio.run(main()) 