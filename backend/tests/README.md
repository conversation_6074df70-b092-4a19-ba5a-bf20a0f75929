# DAIR 测试系统

本测试系统基于pytest，提供了完整的单元测试和集成测试支持。

## 目录结构

```
tests/
├── __init__.py                          # 测试包初始化
├── conftest.py                         # pytest全局配置和fixtures
├── test_infrastructure.py             # 测试基础设施
├── README.md                           # 本文档
├── unit/                               # 单元测试目录
│   ├── __init__.py
│   └── conversation/                   # conversation模块单元测试
│       ├── __init__.py
│       ├── test_models.py             # 模型测试
│       └── test_session_cache.py      # session缓存单元测试
└── integration/                        # 集成测试目录
    ├── __init__.py
    └── conversation/                   # conversation模块集成测试
        ├── __init__.py
        └── test_session_cache_integration.py  # session缓存集成测试
```

## 测试类型

### 单元测试 (Unit Tests)
- 位置: `tests/unit/`
- 特点: 使用Mock对象，不依赖外部服务
- 标记: `@pytest.mark.unit`
- 执行速度: 快速

### 集成测试 (Integration Tests)
- 位置: `tests/integration/`
- 特点: 使用真实的数据库和服务连接
- 标记: `@pytest.mark.integration`
- 执行速度: 较慢，但测试真实功能

## 数据库测试支持

### MongoDB测试
- **测试数据库**: `dair_test` (与生产数据库隔离)
- **自动清理**: 每次测试前后自动清理数据
- **Fixtures**: `test_mongo_client`, `clean_mongo_db`, `test_mongo_base`

### Redis测试
- **测试数据库**: Redis数据库15 (与生产数据库隔离)
- **自动清理**: 每次测试前后自动清理数据
- **Fixtures**: `test_redis_client`, `clean_redis_db`

### TOS存储测试
- **测试存储桶**: `dair-test` (与生产存储隔离)
- **Mock支持**: 提供完整的mock TOS客户端

## 运行测试

### 使用测试运行器（推荐）

```bash
# 运行所有测试
python run_tests.py --all

# 只运行单元测试
python run_tests.py --unit

# 只运行集成测试
python run_tests.py --integration

# 运行特定模块的测试
python run_tests.py --module conversation

# 跳过慢速测试
python run_tests.py --unit --no-slow

# 生成覆盖率报告
python run_tests.py --unit --coverage

# 生成HTML测试报告
python run_tests.py --all --html-report

# 详细输出
python run_tests.py --unit --verbose
```

### 直接使用pytest

```bash
# 运行所有测试
pytest

# 只运行单元测试
pytest -m unit

# 只运行集成测试  
pytest -m integration

# 运行特定模块
pytest -m conversation

# 跳过慢速测试
pytest -m "not slow"

# 组合标记
pytest -m "unit and conversation"

# 详细输出
pytest -v

# 覆盖率报告
pytest --cov=backend --cov-report=html
```

## 测试标记 (Markers)

- `@pytest.mark.unit` - 单元测试
- `@pytest.mark.integration` - 集成测试
- `@pytest.mark.slow` - 慢速测试
- `@pytest.mark.conversation` - conversation模块测试
- `@pytest.mark.auth` - 认证模块测试
- `@pytest.mark.document` - 文档模块测试
- `@pytest.mark.redis` - Redis相关测试
- `@pytest.mark.mongodb` - MongoDB相关测试
- `@pytest.mark.tos` - TOS存储相关测试

## 编写测试

### 单元测试示例

```python
import pytest
from unittest.mock import AsyncMock, patch
from backend.tests.test_infrastructure import unit_test

@pytest.mark.unit
@pytest.mark.conversation
class TestMyService:
    
    @unit_test
    async def test_my_function(self, mock_redis_client):
        """测试我的函数"""
        # 配置mock
        mock_redis_client.get.return_value = "test_value"
        
        # 执行测试
        result = await my_service.get_data("test_key")
        
        # 断言
        assert result == "test_value"
        mock_redis_client.get.assert_called_once_with("test_key")
```

### 集成测试示例

```python
import pytest
from backend.tests.test_infrastructure import (
    integration_test, 
    test_redis_context,
    requires_redis
)

@pytest.mark.integration
@pytest.mark.redis
class TestMyServiceIntegration:
    
    @integration_test
    @requires_redis
    async def test_real_redis_operations(self, test_settings):
        """测试真实Redis操作"""
        async with test_redis_context(test_settings) as redis_client:
            # 使用真实Redis进行测试
            await redis_client.set("test_key", "test_value")
            result = await redis_client.get("test_key")
            assert result == "test_value"
```

## 环境配置

### 测试环境变量

测试会自动使用以下测试配置：

```python
ENVIRONMENT=testing
MONGO_DB_NAME=dair_test
REDIS_URL=redis://localhost:6379/15
TOS_BUCKET_NAME=dair-test
LOG_LEVEL=DEBUG
```

### 依赖服务

运行集成测试前，确保以下服务可用：

1. **MongoDB**: 运行在默认端口27017
2. **Redis**: 运行在默认端口6379
3. **TOS**: 配置正确的访问密钥（如需要）

## CI/CD集成

测试系统设计支持CI/CD流水线：

### 快速检查（单元测试）
```bash
python run_tests.py --unit --no-slow
```

### 完整验证（所有测试）
```bash
python run_tests.py --all --coverage --html-report
```

## 最佳实践

1. **测试隔离**: 每个测试都应该独立，不依赖其他测试的执行结果
2. **数据清理**: 使用fixtures自动清理测试数据
3. **Mock使用**: 单元测试中使用Mock，集成测试中使用真实服务
4. **测试命名**: 使用描述性的测试函数名
5. **文档化**: 为复杂测试添加详细的docstring
6. **标记分类**: 正确使用pytest标记分类测试

## 故障排除

### 常见问题

1. **MongoDB连接失败**
   - 检查MongoDB服务是否运行
   - 确认连接字符串正确
   - 检查测试数据库权限

2. **Redis连接失败**
   - 检查Redis服务是否运行
   - 确认使用了正确的数据库编号(15)
   - 检查Redis配置

3. **测试数据污染**
   - 确保使用了正确的fixtures
   - 检查测试数据库名称
   - 手动清理测试数据

### 调试技巧

```bash
# 运行单个测试文件
pytest tests/unit/conversation/test_models.py -v

# 运行单个测试函数
pytest tests/unit/conversation/test_models.py::TestResearchRequest::test_valid_research_request -v

# 显示更多输出
pytest -s -v

# 在第一个失败时停止
pytest -x
``` 