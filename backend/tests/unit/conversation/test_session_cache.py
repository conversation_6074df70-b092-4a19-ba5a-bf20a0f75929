"""
SessionCache单元测试 - 使用Mock Redis
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone
import json

from backend.conversation.session_cache import SessionCache
from backend.tests.test_infrastructure import unit_test


@pytest.mark.unit
@pytest.mark.redis
class TestSessionCache:
    """测试SessionCache类"""
    
    @pytest.fixture
    def mock_redis_client(self):
        """Mock Redis客户端fixture"""
        mock_client = AsyncMock()
        mock_client.ping.return_value = True
        mock_client.get.return_value = None
        mock_client.set.return_value = True
        mock_client.delete.return_value = 1
        mock_client.hget.return_value = None
        mock_client.hset.return_value = 1
        mock_client.hgetall.return_value = {}
        mock_client.exists.return_value = 0
        mock_client.ttl.return_value = 3600
        mock_client.keys.return_value = []
        return mock_client
    
    @pytest.fixture
    def session_cache(self, mock_redis_client):
        """SessionCache实例fixture"""
        with patch('backend.conversation.session_cache.get_redis_client', return_value=mock_redis_client):
            cache = SessionCache()
            return cache
    
    @pytest.fixture
    def sample_session_data(self):
        """示例会话数据"""
        return {
            "session_id": "test_session_123",
            "user_id": "test_user",
            "agent": "research_agent",
            "status": "running",
            "messages": [
                {"role": "user", "content": "Hello"},
                {"role": "assistant", "content": "Hi there!"}
            ],
            "created_at": datetime.now(timezone.utc).isoformat(),
            "updated_at": datetime.now(timezone.utc).isoformat()
        }
    
    @unit_test
    async def test_set_session_success(self, session_cache, mock_redis_client, sample_session_data):
        """测试成功设置会话"""
        session_id = sample_session_data["session_id"]
        
        # 配置mock返回值
        mock_redis_client.hset.return_value = len(sample_session_data)
        mock_redis_client.expire.return_value = True
        mock_redis_client.sadd.return_value = 1
        
        # 执行测试
        result = await session_cache.set_session(session_id, sample_session_data, expire=300)
        
        # 断言
        assert result is True
        mock_redis_client.hset.assert_called_once()
        mock_redis_client.expire.assert_called_with(f"session:{session_id}", 300)
        mock_redis_client.sadd.assert_called_with("active_sessions", session_id)
    
    @unit_test
    async def test_get_session_success(self, session_cache, mock_redis_client, sample_session_data):
        """测试成功获取会话"""
        session_id = sample_session_data["session_id"]
        
        # 配置mock返回值 - 模拟Redis哈希返回
        mock_redis_data = {}
        for key, value in sample_session_data.items():
            if isinstance(value, (dict, list)):
                mock_redis_data[key] = json.dumps(value)
            else:
                mock_redis_data[key] = str(value)
        
        mock_redis_client.hgetall.return_value = mock_redis_data
        
        # 执行测试
        result = await session_cache.get_session(session_id)
        
        # 断言
        assert result is not None
        assert result["session_id"] == session_id
        assert result["user_id"] == sample_session_data["user_id"]
        assert isinstance(result["messages"], list)
        mock_redis_client.hgetall.assert_called_once_with(f"session:{session_id}")
    
    @unit_test
    async def test_get_session_not_found(self, session_cache, mock_redis_client):
        """测试获取不存在的会话"""
        session_id = "non_existent_session"
        
        # 配置mock返回值
        mock_redis_client.hgetall.return_value = {}
        
        # 执行测试
        result = await session_cache.get_session(session_id)
        
        # 断言
        assert result is None
        mock_redis_client.hgetall.assert_called_once_with(f"session:{session_id}")
    
    @unit_test
    async def test_exists_session_true(self, session_cache, mock_redis_client):
        """测试会话存在检查 - 存在"""
        session_id = "existing_session"
        
        # 配置mock返回值
        mock_redis_client.exists.return_value = 1
        
        # 执行测试
        result = await session_cache.exists_session(session_id)
        
        # 断言
        assert result is True
        mock_redis_client.exists.assert_called_once_with(f"session:{session_id}")
    
    @unit_test
    async def test_exists_session_false(self, session_cache, mock_redis_client):
        """测试会话存在检查 - 不存在"""
        session_id = "non_existent_session"
        
        # 配置mock返回值
        mock_redis_client.exists.return_value = 0
        
        # 执行测试
        result = await session_cache.exists_session(session_id)
        
        # 断言
        assert result is False
        mock_redis_client.exists.assert_called_once_with(f"session:{session_id}")
    
    @unit_test
    async def test_update_session_field(self, session_cache, mock_redis_client):
        """测试更新会话字段"""
        session_id = "test_session"
        field = "status"
        value = "completed"
        
        # 配置mock返回值
        mock_redis_client.hset.return_value = 1
        
        # 执行测试
        result = await session_cache.update_session_field(session_id, field, value)
        
        # 断言
        assert result is True
        mock_redis_client.hset.assert_called_once_with(f"session:{session_id}", field, value)
    
    @unit_test
    async def test_delete_session(self, session_cache, mock_redis_client):
        """测试删除会话"""
        session_id = "test_session"
        
        # 配置mock返回值
        mock_redis_client.delete.return_value = 1
        mock_redis_client.srem.return_value = 1
        
        # 执行测试
        result = await session_cache.delete_session(session_id)
        
        # 断言
        assert result is True
        mock_redis_client.delete.assert_called_once_with(f"session:{session_id}")
        mock_redis_client.srem.assert_called_once_with("active_sessions", session_id)
    
    @unit_test
    async def test_get_active_sessions(self, session_cache, mock_redis_client):
        """测试获取活跃会话列表"""
        active_sessions = ["session_1", "session_2", "session_3"]
        
        # 配置mock返回值
        mock_redis_client.smembers.return_value = {s.encode() for s in active_sessions}
        
        # 执行测试
        result = await session_cache.get_active_sessions()
        
        # 断言
        assert len(result) == 3
        assert all(session in result for session in active_sessions)
        mock_redis_client.smembers.assert_called_once_with("active_sessions")
    
    @unit_test
    async def test_get_session_info(self, session_cache, mock_redis_client):
        """测试获取会话信息"""
        session_id = "test_session"
        
        # 配置mock返回值
        mock_redis_client.exists.return_value = 1
        mock_redis_client.ttl.return_value = 1800  # 30分钟
        
        # 执行测试
        result = await session_cache.get_session_info(session_id)
        
        # 断言
        assert result is not None
        assert result["session_id"] == session_id
        assert result["exists"] is True
        assert result["ttl"] == 1800
        mock_redis_client.exists.assert_called_once_with(f"session:{session_id}")
        mock_redis_client.ttl.assert_called_once_with(f"session:{session_id}")
    
    @unit_test
    async def test_migrate_from_memory(self, session_cache, mock_redis_client):
        """测试从内存迁移会话"""
        memory_sessions = {
            "session_1": {"user_id": "user_1", "status": "running"},
            "session_2": {"user_id": "user_2", "status": "paused"}
        }
        
        # 配置mock返回值
        mock_redis_client.hset.return_value = 2
        mock_redis_client.expire.return_value = True
        mock_redis_client.sadd.return_value = 1
        
        # 执行测试
        result = await session_cache.migrate_from_memory(memory_sessions)
        
        # 断言
        assert result == 2  # 迁移了2个会话
        assert mock_redis_client.hset.call_count == 2
        assert mock_redis_client.expire.call_count == 2
        assert mock_redis_client.sadd.call_count == 1
    
    @unit_test
    async def test_redis_connection_error(self, mock_redis_client):
        """测试Redis连接错误处理"""
        # 配置mock抛出异常
        mock_redis_client.ping.side_effect = Exception("Connection failed")
        
        with patch('backend.conversation.session_cache.get_redis_client', return_value=mock_redis_client):
            cache = SessionCache()
            
            # 测试在连接失败时的行为
            result = await cache.set_session("test", {"data": "test"})
            assert result is False
