"""
Conversation模块Models单元测试
"""

import pytest
from datetime import datetime
from pydantic import ValidationError

from backend.conversation.models import (
    ResearchRequest,
    FeedbackRequest,
    ClarificationAnswerRequest,
    ResearchResponse,
    CreateSessionResponse,
    CreateSessionRequest,
    StartResearchRequest,
    UserModelPreferences,
    EditRecordRequest,
    ReportFeedbackRequest,
    DeleteContextRequest,
    MarkdownToPdfRequest
)


@pytest.mark.unit
class TestResearchRequest:
    """测试ResearchRequest模型"""
    
    def test_valid_research_request(self):
        """测试有效的研究请求"""
        data = {
            "question": "What is machine learning?",
            "benchmark_id": 1,
            "enable_cognition_search": True
        }
        request = ResearchRequest(**data)
        
        assert request.question == "What is machine learning?"
        assert request.benchmark_id == 1
        assert request.enable_cognition_search is True
    
    def test_minimal_research_request(self):
        """测试最小化的研究请求"""
        data = {"question": "What is AI?"}
        request = ResearchRequest(**data)
        
        assert request.question == "What is AI?"
        assert request.benchmark_id is None
        assert request.enable_cognition_search is False
    
    def test_invalid_research_request(self):
        """测试无效的研究请求"""
        with pytest.raises(ValidationError):
            ResearchRequest()  # 缺少必需的question字段


@pytest.mark.unit
class TestFeedbackRequest:
    """测试FeedbackRequest模型"""
    
    def test_valid_feedback_request(self):
        """测试有效的反馈请求"""
        data = {"feedback": "This is helpful feedback"}
        request = FeedbackRequest(**data)
        
        assert request.feedback == "This is helpful feedback"
    
    def test_invalid_feedback_request(self):
        """测试无效的反馈请求"""
        with pytest.raises(ValidationError):
            FeedbackRequest()  # 缺少必需的feedback字段


@pytest.mark.unit
class TestClarificationAnswerRequest:
    """测试ClarificationAnswerRequest模型"""
    
    def test_string_list_answers(self):
        """测试字符串列表答案"""
        data = {"answers": ["answer1", "answer2", "answer3"]}
        request = ClarificationAnswerRequest(**data)
        
        assert request.answers == ["answer1", "answer2", "answer3"]
    
    def test_dict_list_answers(self):
        """测试字典列表答案"""
        data = {
            "answers": [
                {"question": "Q1", "answer": "A1"},
                {"question": "Q2", "answer": "A2"}
            ]
        }
        request = ClarificationAnswerRequest(**data)
        
        assert len(request.answers) == 2
        assert request.answers[0]["question"] == "Q1"


@pytest.mark.unit
class TestResponseModels:
    """测试响应模型"""
    
    def test_research_response(self):
        """测试研究响应模型"""
        data = {
            "session_id": "session_123",
            "message": "Research completed"
        }
        response = ResearchResponse(**data)
        
        assert response.session_id == "session_123"
        assert response.message == "Research completed"
    
    def test_create_session_response(self):
        """测试创建会话响应模型"""
        data = {
            "session_id": "new_session_456",
            "message": "Session created successfully"
        }
        response = CreateSessionResponse(**data)
        
        assert response.session_id == "new_session_456"
        assert response.message == "Session created successfully"


@pytest.mark.unit
class TestV2RequestModels:
    """测试V2版本请求模型"""
    
    def test_create_session_request(self):
        """测试创建会话请求模型"""
        data = {
            "user_id": "user_123",
            "metadata": {"key": "value"}
        }
        request = CreateSessionRequest(**data)
        
        assert request.user_id == "user_123"
        assert request.metadata == {"key": "value"}
    
    def test_create_session_request_minimal(self):
        """测试最小化创建会话请求"""
        data = {"user_id": "user_456"}
        request = CreateSessionRequest(**data)
        
        assert request.user_id == "user_456"
        assert request.metadata is None
    
    def test_start_research_request(self):
        """测试启动研究请求模型"""
        data = {
            "session_id": "session_789",
            "question": "How does quantum computing work?",
            "benchmark_id": 5,
            "enable_cognition_search": True
        }
        request = StartResearchRequest(**data)
        
        assert request.session_id == "session_789"
        assert request.question == "How does quantum computing work?"
        assert request.benchmark_id == 5
        assert request.enable_cognition_search is True


@pytest.mark.unit
class TestUserModelPreferences:
    """测试用户模型偏好"""
    
    def test_empty_preferences(self):
        """测试空偏好设置"""
        preferences = UserModelPreferences()
        
        assert preferences.action_selection_model is None
        assert preferences.report_editing_model is None
        assert preferences.url_selection_model is None
    
    def test_full_preferences(self):
        """测试完整偏好设置"""
        data = {
            "action_selection_model": {"model": "gpt-4", "temperature": 0.7},
            "report_editing_model": {"model": "gpt-3.5", "max_tokens": 2000},
            "url_selection_model": {"model": "gpt-4", "threshold": 0.8}
        }
        preferences = UserModelPreferences(**data)
        
        assert preferences.action_selection_model["model"] == "gpt-4"
        assert preferences.report_editing_model["max_tokens"] == 2000
        assert preferences.url_selection_model["threshold"] == 0.8


@pytest.mark.unit
class TestOperationalModels:
    """测试操作相关模型"""
    
    def test_edit_record_request(self):
        """测试编辑记录请求"""
        data = {
            "type": "text_edit",
            "timestamp": "2024-01-01T12:00:00Z",
            "original_content": "Original text",
            "edited_content": "Edited text"
        }
        request = EditRecordRequest(**data)
        
        assert request.type == "text_edit"
        assert request.timestamp == "2024-01-01T12:00:00Z"
        assert request.original_content == "Original text"
        assert request.edited_content == "Edited text"
    
    def test_report_feedback_request(self):
        """测试报告反馈请求"""
        data = {
            "sessionId": "session_123",
            "rating": "excellent",
            "comment": "Very helpful research",
            "timestamp": "2024-01-01T12:00:00Z"
        }
        request = ReportFeedbackRequest(**data)
        
        assert request.sessionId == "session_123"
        assert request.rating == "excellent"
        assert request.comment == "Very helpful research"
    
    def test_delete_context_request(self):
        """测试删除上下文请求"""
        data = {
            "conversation_id": "conv_123",
            "context_id": "ctx_456"
        }
        request = DeleteContextRequest(**data)
        
        assert request.conversation_id == "conv_123"
        assert request.context_id == "ctx_456"
    
    def test_markdown_to_pdf_request(self):
        """测试Markdown转PDF请求"""
        data = {
            "markdown_content": "# Title\nContent here",
            "filename": "custom.pdf"
        }
        request = MarkdownToPdfRequest(**data)
        
        assert request.markdown_content == "# Title\nContent here"
        assert request.filename == "custom.pdf"
    
    def test_markdown_to_pdf_request_default_filename(self):
        """测试默认文件名的Markdown转PDF请求"""
        data = {"markdown_content": "# Title\nContent"}
        request = MarkdownToPdfRequest(**data)
        
        assert request.filename == "document.pdf" 