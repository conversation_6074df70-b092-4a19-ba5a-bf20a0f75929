"""
Conversation Router 集成测试

测试对话路由器的主要接口功能，包括：
- 创建会话
- 开始研究  
- 获取状态
- 用户反馈
- 模型配置
- 会话管理
"""

import pytest
import asyncio
import uuid
from typing import Dict, Any
from datetime import datetime
from unittest.mock import patch, AsyncMock

from fastapi.testclient import TestClient
from httpx import AsyncClient
import logging

from backend.main import app
from backend.auth.models import UserDB
from backend.conversation.models import (
    CreateSessionRequest, StartResearchRequest, FeedbackRequest,
    UserModelPreferences, ClarificationAnswerRequest
)
logger = logging.getLogger(__name__)

@pytest.mark.integration
@pytest.mark.mongodb
@pytest.mark.redis
class TestConversationRouter:
    """对话路由器集成测试"""
    
    @pytest.fixture
    def test_user(self):
        """测试用户fixture"""
        return UserDB(
            id="test_user_id_123",
            username="test_user",
            email="<EMAIL>",
            hashed_password="fake_hashed_password",
            is_active=True,
            is_admin=False,
            token_budget=100.0,
            current_token_usage=0.0
        )
    
    @pytest.fixture
    def test_session_id(self):
        """测试会话ID"""
        return str(uuid.uuid4())
    
    @pytest.fixture
    def auth_headers(self, test_user):
        """认证头部"""
        # 在实际测试中，这里需要生成有效的JWT token
        # 现在先mock一个简单的token
        return {"Authorization": "Bearer test_token_123"}
    
    @pytest.fixture
    async def client(self):
        """异步HTTP客户端"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            yield ac
    
    @pytest.fixture(autouse=True)
    async def setup_test_environment(self, test_user):
        """设置测试环境"""
        # Mock认证依赖
        def mock_get_current_active_user():
            return test_user
        
        def mock_check_user_token_budget():
            return test_user
        
        # 应用Mock
        with patch("backend.auth.dependencies.get_current_active_user", return_value=test_user):
            with patch("backend.auth.dependencies.check_user_token_budget", return_value=test_user):
                yield
    
    @pytest.mark.integration
    async def test_create_session_v2(self, client: AsyncClient, auth_headers: Dict[str, str]):
        """测试创建会话V2接口"""
        # 准备请求数据
        request_data = {
            "language": "zh"
        }
        
        # 发送请求
        response = await client.post(
            "/conversation/api/v2/research/create_session",
            json=request_data,
            headers=auth_headers
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert "session_id" in data
        assert "message" in data
        assert data["message"] == "研究会话已创建"
        
        # 验证session_id格式
        session_id = data["session_id"]
        assert len(session_id) > 0
        assert isinstance(session_id, str)
        
        logger.info(f"✅ 创建会话成功: {session_id}")
        return session_id
    
    @pytest.mark.integration
    async def test_start_research_v2(self, client: AsyncClient, auth_headers: Dict[str, str]):
        """测试启动研究V2接口"""
        # 先创建会话
        session_id = await self.test_create_session_v2(client, auth_headers)
        
        # 准备研究请求数据
        request_data = {
            "session_id": session_id,
            "question": "人工智能的发展趋势是什么？",
            "enable_cognition_search": False
        }
        
        # 发送启动研究请求
        response = await client.post(
            "/conversation/api/v2/research/start",
            json=request_data,
            headers=auth_headers
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert "session_id" in data
        assert data["session_id"] == session_id
        assert "message" in data
        
        logger.info(f"✅ 启动研究成功: {session_id}")
        return session_id
    
    @integration_test
    async def test_get_research_status(self, client: AsyncClient, auth_headers: Dict[str, str]):
        """测试获取研究状态接口"""
        # 先创建并启动研究
        session_id = await self.test_start_research_v2(client, auth_headers)
        
        # 获取研究状态
        response = await client.get(
            f"/conversation/research/{session_id}/status",
            headers=auth_headers
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert "session_id" in data
        assert data["session_id"] == session_id
        assert "status" in data
        assert "question" in data
        assert "last_activity" in data
        
        logger.info(f"✅ 获取研究状态成功: {data['status']}")
        return data
    
    @integration_test
    async def test_submit_feedback(self, client: AsyncClient, auth_headers: Dict[str, str]):
        """测试提交用户反馈接口"""
        # 先创建并启动研究
        session_id = await self.test_start_research_v2(client, auth_headers)
        
        # 准备反馈数据
        feedback_data = {
            "feedback": "研究内容很有帮助，但希望能更深入一些",
            "rating": 4
        }
        
        # 提交反馈
        response = await client.post(
            f"/conversation/research/{session_id}/feedback",
            json=feedback_data,
            headers=auth_headers
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert data["status"] == "success"
        
        logger.info("✅ 提交反馈成功")
        return data
    
    @integration_test
    async def test_get_my_chats(self, client: AsyncClient, auth_headers: Dict[str, str]):
        """测试获取我的聊天记录接口"""
        # 先创建几个会话
        session_ids = []
        for i in range(2):
            session_id = await self.test_create_session_v2(client, auth_headers)
            session_ids.append(session_id)
        
        # 获取聊天记录
        response = await client.get(
            "/conversation/api/chats/my?skip=0&limit=10",
            headers=auth_headers
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert "chats" in data
        assert "total" in data
        assert isinstance(data["chats"], list)
        
        logger.info(f"✅ 获取聊天记录成功: {data['total']} 条记录")
        return data
    
    @integration_test
    async def test_get_available_models(self, client: AsyncClient, auth_headers: Dict[str, str]):
        """测试获取可用模型接口"""
        response = await client.get(
            "/conversation/api/models/available",
            headers=auth_headers
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert "available_models" in data
        assert "current_models" in data
        assert "default_models" in data
        
        logger.info("✅ 获取可用模型成功")
        return data
    
    @integration_test
    async def test_set_models_preferences(self, client: AsyncClient, auth_headers: Dict[str, str]):
        """测试设置模型偏好接口"""
        # 准备模型偏好数据
        preferences_data = {
            "action_selection_model": {
                "name": "gpt-4",
                "temperature": 0.7,
                "max_tokens": 8000
            },
            "report_editing_model": {
                "name": "gpt-4",
                "temperature": 0.6,
                "max_tokens": 20000
            },
            "url_selection_model": {
                "name": "gpt-3.5-turbo",
                "temperature": 0.5,
                "max_tokens": 500
            }
        }
        
        # 设置模型偏好
        response = await client.post(
            "/conversation/api/models/preferences",
            json=preferences_data,
            headers=auth_headers
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert data["status"] == "success"
        assert "models_config" in data
        
        logger.info("✅ 设置模型偏好成功")
        return data
    
    @integration_test
    async def test_pause_and_resume_research(self, client: AsyncClient, auth_headers: Dict[str, str]):
        """测试暂停和恢复研究接口"""
        # 先创建并启动研究
        session_id = await self.test_start_research_v2(client, auth_headers)
        
        # 暂停研究
        response = await client.post(
            f"/conversation/research/{session_id}/pause",
            headers=auth_headers
        )
        
        # 验证暂停响应
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert data["status"] == "success"
        
        logger.info("✅ 暂停研究成功")
        
        # 恢复研究
        response = await client.post(
            f"/conversation/research/{session_id}/resume",
            headers=auth_headers
        )
        
        # 验证恢复响应
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert data["status"] == "success"
        
        logger.info("✅ 恢复研究成功")
        return data
    
    @integration_test
    async def test_delete_research_session(self, client: AsyncClient, auth_headers: Dict[str, str]):
        """测试删除研究会话接口"""
        # 先创建会话
        session_id = await self.test_create_session_v2(client, auth_headers)
        
        # 删除会话
        response = await client.delete(
            f"/conversation/research/{session_id}",
            headers=auth_headers
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert data["status"] == "success"
        assert "message" in data
        
        logger.info("✅ 删除研究会话成功")
        
        # 验证会话已被删除 - 尝试获取状态应该返回404
        response = await client.get(
            f"/conversation/research/{session_id}/status",
            headers=auth_headers
        )
        assert response.status_code == 404
        
        logger.info("✅ 验证会话已删除")
        return data
    
    @integration_test
    async def test_get_benchmark_list(self, client: AsyncClient):
        """测试获取benchmark列表接口"""
        response = await client.get("/conversation/benchmark/list")
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        
        logger.info(f"✅ 获取benchmark列表成功: {len(data)} 个项目")
        return data
    
    @integration_test
    async def test_submit_clarification_answer(self, client: AsyncClient, auth_headers: Dict[str, str]):
        """测试提交澄清问题回答接口"""
        # 先创建并启动研究
        session_id = await self.test_start_research_v2(client, auth_headers)
        
        # 准备澄清回答数据
        answer_data = {
            "answer": "我希望重点关注机器学习和深度学习的最新发展",
            "question_id": "clarification_1"
        }
        
        # 提交澄清回答
        response = await client.post(
            f"/conversation/research/{session_id}/clarification_answer",
            json=answer_data,
            headers=auth_headers
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert data["status"] == "success"
        
        logger.info("✅ 提交澄清回答成功")
        return data
    
    @integration_test
    async def test_get_pending_questions(self, client: AsyncClient, auth_headers: Dict[str, str]):
        """测试获取待处理澄清问题接口"""
        # 先创建并启动研究
        session_id = await self.test_start_research_v2(client, auth_headers)
        
        # 获取待处理问题
        response = await client.get(
            f"/conversation/research/{session_id}/pending_questions",
            headers=auth_headers
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert "questions" in data
        assert isinstance(data["questions"], list)
        
        logger.info(f"✅ 获取待处理问题成功: {len(data['questions'])} 个问题")
        return data
    
    @integration_test
    async def test_update_research_status(self, client: AsyncClient, auth_headers: Dict[str, str]):
        """测试更新研究状态接口"""
        # 先创建并启动研究
        session_id = await self.test_start_research_v2(client, auth_headers)
        
        # 准备更新数据
        update_data = {
            "session_id": session_id,
            "enable_cognition_search": True
        }
        
        # 更新研究状态
        response = await client.post(
            "/conversation/api/v2/research/update_status",
            json=update_data,
            headers=auth_headers
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert data["status"] == "success"
        
        logger.info("✅ 更新研究状态成功")
        return data
    
    @integration_test
    async def test_error_handling_invalid_session(self, client: AsyncClient, auth_headers: Dict[str, str]):
        """测试错误处理 - 无效会话ID"""
        invalid_session_id = "invalid_session_id_123"
        
        # 尝试获取无效会话的状态
        response = await client.get(
            f"/conversation/research/{invalid_session_id}/status",
            headers=auth_headers
        )
        
        # 验证错误响应
        assert response.status_code == 404
        data = response.json()
        assert "detail" in data
        
        logger.info("✅ 错误处理测试成功 - 无效会话ID")
        return data
    
    @integration_test
    async def test_comprehensive_workflow(self, client: AsyncClient, auth_headers: Dict[str, str]):
        """测试完整的工作流程"""
        logger.info("🚀 开始完整工作流程测试")
        
        # 1. 创建会话
        session_id = await self.test_create_session_v2(client, auth_headers)
        logger.info(f"1. ✅ 创建会话: {session_id}")
        
        # 2. 启动研究
        await self.test_start_research_v2(client, auth_headers)
        logger.info("2. ✅ 启动研究")
        
        # 3. 获取状态
        status = await self.test_get_research_status(client, auth_headers)
        logger.info(f"3. ✅ 获取状态: {status['status']}")
        
        # 4. 提交反馈
        await self.test_submit_feedback(client, auth_headers)
        logger.info("4. ✅ 提交反馈")
        
        # 5. 暂停研究
        await self.test_pause_and_resume_research(client, auth_headers)
        logger.info("5. ✅ 暂停和恢复研究")
        
        # 6. 获取聊天记录
        chats = await self.test_get_my_chats(client, auth_headers)
        logger.info(f"6. ✅ 获取聊天记录: {chats['total']} 条")
        
        # 7. 设置模型偏好
        await self.test_set_models_preferences(client, auth_headers)
        logger.info("7. ✅ 设置模型偏好")
        
        # 8. 最终删除会话
        await self.test_delete_research_session(client, auth_headers)
        logger.info("8. ✅ 删除会话")
        
        logger.info("🎉 完整工作流程测试成功!")
        return {"status": "success", "message": "完整工作流程测试通过"}


# 性能测试类
@pytest.mark.integration
@pytest.mark.slow
class TestConversationPerformance:
    """对话接口性能测试"""
    
    @pytest.fixture
    def test_user(self):
        """测试用户fixture"""
        return UserDB(
            id="perf_test_user_123",
            username="perf_test_user",
            email="<EMAIL>",
            hashed_password="fake_hashed_password",
            is_active=True,
            is_admin=False,
            token_budget=1000.0,
            current_token_usage=0.0
        )
    
    @pytest.fixture
    def auth_headers(self, test_user):
        """认证头部"""
        return {"Authorization": "Bearer perf_test_token_123"}
    
    @pytest.fixture
    async def client(self):
        """异步HTTP客户端"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            yield ac
    
    @pytest.fixture(autouse=True)
    async def setup_test_environment(self, test_user):
        """设置测试环境"""
        def mock_get_current_active_user():
            return test_user
        
        def mock_check_user_token_budget():
            return test_user
        
        with patch("backend.auth.dependencies.get_current_active_user", return_value=test_user):
            with patch("backend.auth.dependencies.check_user_token_budget", return_value=test_user):
                yield
    
    @integration_test
    async def test_concurrent_session_creation(self, client: AsyncClient, auth_headers: Dict[str, str]):
        """测试并发创建会话的性能"""
        import time
        import asyncio
        
        concurrent_count = 5
        start_time = time.time()
        
        # 创建并发任务
        async def create_single_session():
            request_data = {"language": "zh"}
            response = await client.post(
                "/conversation/api/v2/research/create_session",
                json=request_data,
                headers=auth_headers
            )
            return response.status_code == 200
        
        # 并发执行
        tasks = [create_single_session() for _ in range(concurrent_count)]
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 验证结果
        success_count = sum(results)
        assert success_count == concurrent_count
        
        # 性能断言（5个并发会话创建应该在10秒内完成）
        assert duration < 10.0
        
        logger.info(f"✅ 并发会话创建测试: {concurrent_count} 个会话在 {duration:.2f} 秒内创建成功")
        return {
            "concurrent_count": concurrent_count,
            "duration": duration,
            "success_rate": success_count / concurrent_count
        }
    
    @integration_test
    async def test_api_response_time(self, client: AsyncClient, auth_headers: Dict[str, str]):
        """测试API响应时间"""
        import time
        
        # 测试不同接口的响应时间
        endpoints = [
            ("GET", "/conversation/api/models/available"),
            ("GET", "/conversation/benchmark/list"),
            ("GET", "/conversation/api/chats/my?limit=5"),
        ]
        
        results = {}
        
        for method, endpoint in endpoints:
            start_time = time.time()
            
            if method == "GET":
                response = await client.get(endpoint, headers=auth_headers)
            else:
                response = await client.post(endpoint, headers=auth_headers)
            
            end_time = time.time()
            duration = end_time - start_time
            
            results[endpoint] = {
                "status_code": response.status_code,
                "duration": duration,
                "success": response.status_code == 200
            }
            
            # 响应时间应该在2秒内
            assert duration < 2.0
            
            logger.info(f"✅ {method} {endpoint}: {duration:.3f}s")
        
        return results
