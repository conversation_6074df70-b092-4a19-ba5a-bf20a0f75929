#!/usr/bin/env python3
"""
Conversation 接口集成测试运行脚本

专门用于测试 conversation 模块的所有接口，包括：
- 时间性能记录
- 连通性测试
- 使用测试数据库
- 生成详细的测试报告
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path
from datetime import datetime
import json

# 添加backend路径到sys.path
backend_path = Path(__file__).parent.parent
sys.path.insert(0, str(backend_path))

def run_conversation_tests(verbose: bool = True, 
                          save_report: bool = True,
                          test_filter: str = "",
                          timeout: int = 300):
    """
    运行conversation接口测试
    
    Args:
        verbose: 是否显示详细输出
        save_report: 是否保存测试报告
        test_filter: 测试过滤器（例如："test_benchmark"）
        timeout: 测试超时时间（秒）
    """
    
    print("="*60)
    print("CONVERSATION 接口集成测试")
    print("="*60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试数据库: dair_test")
    print(f"Redis测试库: 15")
    print("-"*60)
    
    # 确保测试环境变量
    env = os.environ.copy()
    env.update({
        "TESTING": "true",
        "MONGO_DB_NAME": "dair_test", 
        "REDIS_URL": "redis://localhost:6379/15",
        "LOG_LEVEL": "INFO"
    })
    
    # 构建pytest命令
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/integration/conversation/test_router.py",
        "-v" if verbose else "",
        "--tb=short",  # 简短的错误信息
        "--durations=10",  # 显示最慢的10个测试
        "-x",  # 遇到第一个失败就停止
        f"--timeout={timeout}",  # 设置超时
        "-m", "integration",  # 只运行集成测试
        "--disable-warnings",  # 禁用警告输出
    ]
    
    # 添加测试过滤器
    if test_filter:
        cmd.extend(["-k", test_filter])
    
    # 添加报告生成
    if save_report:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_dir = Path("test_reports")
        report_dir.mkdir(exist_ok=True)
        
        # HTML报告
        html_report = report_dir / f"conversation_test_report_{timestamp}.html"
        cmd.extend(["--html", str(html_report), "--self-contained-html"])
        
        # JUnit XML报告
        xml_report = report_dir / f"conversation_test_results_{timestamp}.xml"
        cmd.extend(["--junit-xml", str(xml_report)])
    
    # 过滤空字符串
    cmd = [arg for arg in cmd if arg]
    
    print(f"执行命令: {' '.join(cmd)}")
    print("-"*60)
    
    # 运行测试
    try:
        result = subprocess.run(
            cmd,
            env=env,
            cwd=backend_path,
            timeout=timeout + 60,  # 给subprocess额外的时间
            capture_output=True,
            text=True
        )
        
        # 输出结果
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("\nSTDERR:")
            print(result.stderr)
        
        print("-"*60)
        print(f"测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"退出码: {result.returncode}")
        
        if result.returncode == 0:
            print("✅ 所有测试通过!")
        else:
            print("❌ 测试失败!")
        
        if save_report:
            print(f"\n📊 测试报告已保存到:")
            if html_report.exists():
                print(f"  HTML: {html_report}")
            if xml_report.exists():
                print(f"  XML:  {xml_report}")
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print(f"❌ 测试超时 ({timeout}秒)")
        return False
    except KeyboardInterrupt:
        print("❌ 测试被用户中断")
        return False
    except Exception as e:
        print(f"❌ 运行测试时发生错误: {e}")
        return False


def check_dependencies():
    """检查测试依赖"""
    print("检查测试依赖...")
    
    required_packages = [
        "pytest",
        "pytest-asyncio", 
        "pytest-html",
        "pytest-timeout",
        "httpx"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少以下依赖包: {', '.join(missing_packages)}")
        print("请安装: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ 所有依赖都已安装")
    return True


def show_test_info():
    """显示测试信息"""
    print("\n📋 测试信息:")
    print("- 测试文件: tests/integration/conversation/test_router.py")
    print("- 测试类型: 集成测试")
    print("- 数据库: dair_test (测试专用)")
    print("- Redis: 数据库15 (测试专用)")
    print("- 测试内容:")
    print("  • Benchmark接口")
    print("  • 聊天管理接口")
    print("  • 研究V1/V2接口")
    print("  • 上下文管理接口")
    print("  • 会话管理接口")
    print("  • 模型配置接口")
    print("  • 高级功能接口")
    print("  • 连通性和错误处理")
    print("- 每个接口都会记录响应时间")
    print("- 自动生成性能摘要报告")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="运行 Conversation 接口集成测试",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python run_conversation_tests.py                    # 运行所有测试
  python run_conversation_tests.py -f test_benchmark  # 只运行benchmark测试
  python run_conversation_tests.py --no-report        # 不生成报告
  python run_conversation_tests.py --timeout 600      # 设置10分钟超时
  python run_conversation_tests.py --quiet           # 简化输出
        """
    )
    
    parser.add_argument(
        "-f", "--filter",
        help="测试过滤器，例如: test_benchmark",
        default=""
    )
    parser.add_argument(
        "--no-report",
        action="store_true",
        help="不生成测试报告"
    )
    parser.add_argument(
        "--timeout",
        type=int,
        default=300,
        help="测试超时时间（秒），默认300秒"
    )
    parser.add_argument(
        "--quiet",
        action="store_true",
        help="简化输出"
    )
    parser.add_argument(
        "--info",
        action="store_true",
        help="显示测试信息"
    )
    parser.add_argument(
        "--check-deps",
        action="store_true",
        help="只检查依赖，不运行测试"
    )
    
    args = parser.parse_args()
    
    if args.info:
        show_test_info()
        return
    
    if args.check_deps:
        success = check_dependencies()
        sys.exit(0 if success else 1)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    if not args.quiet:
        show_test_info()
    
    # 运行测试
    success = run_conversation_tests(
        verbose=not args.quiet,
        save_report=not args.no_report,
        test_filter=args.filter,
        timeout=args.timeout
    )
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main() 