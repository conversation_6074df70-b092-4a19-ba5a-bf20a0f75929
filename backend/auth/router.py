from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.security import OAuth2PasswordRequestForm
from datetime import timedelta, datetime
import logging
from functools import lru_cache
from typing import Optional, Dict, Any, List
from pydantic import BaseModel

from backend.auth.models import UserCreate, UserResponse, Token, UserDB, AdminUser, InviteCodeCreate, InviteCodeResponse
from backend.auth.crud import UserRepository, InviteCodeRepository
from backend.auth.security import create_access_token, ACCESS_TOKEN_EXPIRE_MINUTES, get_cn_time
from backend.auth.dependencies import get_current_active_user, get_current_user_info_required
from backend.config import settings
from dc_agents.src.agents.research_memory import ResearchMemory
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/auth", tags=["auth"])

async def init_admin_user():
    """初始化管理员用户"""
    try:
        user_repo = UserRepository()
        # 管理员默认配置
        admin_config = {
            "username": settings.ADMIN_USERNAME,
            "email": settings.ADMIN_EMAIL,
            "password": settings.ADMIN_PASSWORD,
            "is_admin": True
        }
        
        # 检查是否已存在管理员用户
        admin = await user_repo.get_by_username(admin_config["username"])
        logger.info(f"初始化管理员用户: {admin}")
        
        if admin:
            # 如果管理员已存在，更新信息
            update_data = {
                "email": admin_config["email"],
                "password": admin_config["password"],
                "is_admin": True
            }
            await user_repo.update_user(admin_config["username"], update_data)
            logger.info("管理员用户信息已更新")
        else:
            # 创建默认管理员用户
            admin_data = AdminUser(**admin_config)
            await user_repo.create_user(admin_data)
            logger.info("管理员用户创建成功")
    except Exception as e:
        logger.error(f"初始化管理员用户失败: {str(e)}")

@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register(user_data: UserCreate):
    """
    注册新用户
    """
    # 实例化仓库
    invite_repo = InviteCodeRepository()
    user_repo = UserRepository()
    
    try:
        # 先验证邀请码是否有效（不占用）
        if not await invite_repo.validate_code(user_data.invite_code):
            logger.warning(f"注册失败：无效的邀请码 - {user_data.invite_code}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的邀请码"
            )
        
        # 创建用户
        try:
            user = await user_repo.create_user(user_data)
        except ValueError as e:
            # 用户创建失败(用户名或邮箱已存在等)，不占用邀请码
            logger.warning(f"用户注册失败 - 验证错误: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
        
        # 用户创建成功，才占用邀请码
        if not await invite_repo.use_code(user_data.invite_code, str(user.id)):
            # 如果邀请码占用失败（可能在验证后被其他用户占用）
            # 删除已创建的用户
            logger.warning(f"邀请码占用失败，可能已被其他用户使用 - {user_data.invite_code}")
            await user_repo.collection.delete_one({"_id": user.id})
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邀请码已失效，请尝试使用其他邀请码"
            )
        
        # 准备响应数据
        return {
            "_id": str(user.id),
            "username": user.username,
            "email": user.email,
            "is_active": user.is_active,
            "created_at": user.created_at
        }
    except HTTPException:
        # 直接抛出HTTP异常
        raise
    except Exception as e:
        # 其他意外错误
        import traceback
        traceback.print_exc()
        logger.error(f"用户注册失败 - 意外错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="注册过程中发生错误，请稍后再试"
        )


@router.post("/login", response_model=Token)
async def login(form_data: OAuth2PasswordRequestForm = Depends()):
    """
    用户登录
    """
    try:
        user_repo = UserRepository()
        user = await user_repo.authenticate_user(form_data.username, form_data.password)
        
        if not user:
            logger.warning(f"登录失败: 用户名或密码错误 - {form_data.username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 创建访问令牌
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.username},
            expires_delta=access_token_expires
        )
        
        logger.info(f"用户 {user.username} 登录成功")
        return {"access_token": access_token, "token_type": "bearer"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登录过程中发生错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录过程中发生错误，请稍后再试"
        )

@router.post("/admin/login", response_model=Token)
async def admin_login(form_data: OAuth2PasswordRequestForm = Depends()):
    """
    管理员登录
    """
    try:
        user_repo = UserRepository()
        user = await user_repo.authenticate_user(form_data.username, form_data.password)
        logger.warning(f"管理员登录用户: {user}")
        if not user or not getattr(user, 'is_admin', False):
            logger.warning(f"管理员登录失败: 用户名或密码错误 - {form_data.username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"管理员用户名或密码错误: {form_data.username}, {form_data.password}",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 创建访问令牌
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.username, "is_admin": True},
            expires_delta=access_token_expires
        )
        
        logger.info(f"管理员 {user.username} 登录成功")
        return {"access_token": access_token, "token_type": "bearer"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"管理员登录过程中发生错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录过程中发生错误，请稍后再试"
        )
    
@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: UserDB = Depends(get_current_active_user)):
    """
    获取当前用户信息
    """
    try:
        # 转换为响应模型
        return {
            "_id": str(current_user.id),
            "username": current_user.username,
            "email": current_user.email,
            "is_active": current_user.is_active,
            "is_admin": current_user.is_admin,
            "created_at": current_user.created_at
        }
    except Exception as e:
        logger.error(f"获取用户信息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户信息失败"
        )


@router.get("/my-chats")
async def get_my_chats(
    skip: int = 0,
    limit: int = 20,
    current_user: UserDB = Depends(get_current_active_user)
):
    """
    获取当前用户的所有对话历史
    
    Args:
        skip: 跳过数量，默认0
        limit: 返回数量限制，默认20个，最大100个
    
    返回用户的所有历史对话记录摘要信息
    """
    try:
        # 限制最大返回数量
        limit = min(limit, 100)
        
        # 创建ResearchMemory实例
        research_memory = ResearchMemory("", "Researcher")  # 需要提供必要的参数
        
        # 获取当前用户的所有对话
        user_id = str(current_user.id)
        chats = await research_memory.get_all_conversations(user_id=user_id, skip=skip, limit=limit)
        
        # 格式化返回结果
        for chat in chats:
            # 确保created_at是ISO格式的字符串
            if isinstance(chat.get("created_at"), datetime):
                chat["created_at"] = chat["created_at"].isoformat()
                
            # 截断提示文本(如果过长)
            if len(chat.get("prompt", "")) > 100:
                chat["prompt"] = chat["prompt"][:100] + "..."
        
        return {
            "chats": chats,
            "total": len(chats),
            "skip": skip,
            "limit": limit
        }
    except Exception as e:
        logger.error(f"获取用户对话历史失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取对话历史失败，请稍后再试"
        )


@router.post("/auth/get_user_conversations")
async def get_user_conversations(user_info_required: Dict = Depends(get_current_user_info_required)):
    """
    获取用户所有的对话会话
    """
    try:
        research_memory = ResearchMemory()
        user_id = user_info_required.get("user_id")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户ID未提供"
            )
        # 获取用户对话
        conversations = research_memory.get_user_conversations(user_id)
        return {"conversations": conversations}
    except Exception as e:
        logger.error(f"获取用户对话失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户对话失败，请稍后再试"
        )

@router.post("/admin/invite-codes/generate", response_model=List[InviteCodeResponse])
async def generate_invite_codes(
    request: InviteCodeCreate,
    current_user: UserDB = Depends(get_current_active_user)
):
    """
    生成邀请码（仅管理员）
    """
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    
    try:
        invite_repo = InviteCodeRepository()
        codes = await invite_repo.generate_codes(request.count, str(current_user.id))
        logger.info(f"生成邀请码: {codes}")
        return [{"_id": str(code.code), **code.model_dump()} for code in codes]
    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"生成邀请码失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="生成邀请码失败，请稍后再试"
        )

class InviteCodePage(BaseModel):
    items: List[InviteCodeResponse]
    total: int

@router.get("/admin/invite-codes", response_model=InviteCodePage)
async def get_invite_codes(
    skip: int = 0,
    limit: int = 10,
    status: str = Query("all", enum=["all", "used", "unused"]),
    current_user: UserDB = Depends(get_current_active_user)
):
    """
    获取所有邀请码（仅管理员）
    """
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    
    try:
        invite_repo = InviteCodeRepository()
        codes_from_db, total = await invite_repo.get_all_codes_paginated(
            skip=skip, limit=limit, status=status
        )
        
        codes = []
        for code in codes_from_db:
            code["_id"] = str(code["_id"])
            codes.append(code)

        return {"items": codes, "total": total}
    except Exception as e:
        logger.error(f"获取邀请码失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取邀请码失败，请稍后再试"
        )

 