from pydantic import BaseModel, EmailStr, Field
from typing import Optional, List
from datetime import datetime
from bson import ObjectId

from backend.db.mongodb import PyObjectId
from backend.auth.security import get_cn_time


class UserBase(BaseModel):
    """用户基础模型"""
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr


class UserCreate(UserBase):
    """用户创建模型"""
    password: str = Field(..., min_length=6)
    invite_code: str = Field(..., min_length=8, max_length=8)


class UserLogin(BaseModel):
    """用户登录模型"""
    username: str
    password: str


class UserDB(UserBase):
    """用户数据库模型"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    hashed_password: str
    is_active: bool = True
    created_at: datetime = Field(default_factory=get_cn_time)
    updated_at: datetime = Field(default_factory=get_cn_time)
    is_admin: bool = False
    # Token使用量限制字段
    token_budget: float = Field(default=10.0, description="用户token使用预算上限")
    current_token_usage: float = Field(default=0.0, description="用户当前token使用量")
    
    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
        "json_encoders": {ObjectId: str}
    }


class UserResponse(UserBase):
    """用户响应模型"""
    id: str = Field(..., alias="_id")
    is_active: bool
    is_admin: bool = False
    created_at: datetime

    model_config = {
        "populate_by_name": True
    }


class Token(BaseModel):
    """令牌模型"""
    access_token: str
    token_type: str


class TokenData(BaseModel):
    """令牌数据模型"""
    username: Optional[str] = None 

class AdminUser(UserBase):
    is_admin: bool = True
    password: str = Field(..., min_length=6)

class InviteCodeBase(BaseModel):
    """邀请码基础模型"""
    code: str = Field(..., min_length=8, max_length=8)
    is_used: bool = False
    used_by: Optional[str] = None
    used_at: Optional[datetime] = None
    created_by: str
    created_at: datetime = Field(default_factory=get_cn_time)

    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
        "json_encoders": {ObjectId: str}
    }

class InviteCodeCreate(BaseModel):
    """创建邀请码请求模型"""
    count: int = Field(..., ge=1, le=100)  # 一次最多生成100个邀请码

class InviteCodeResponse(InviteCodeBase):
    """邀请码响应模型"""
    id: str = Field(..., alias="_id")

# Token管理相关模型
class TokenLimitUpdateRequest(BaseModel):
    """更新用户token预算请求模型"""
    token_budget: float = Field(..., ge=0, description="用户token使用预算上限")

class TokenUsageUpdateRequest(BaseModel):
    """更新用户token使用量请求模型"""
    current_token_usage: float = Field(..., ge=0, description="用户当前token使用量")

class BatchTokenBudgetUpdateRequest(BaseModel):
    """批量更新用户token预算请求模型"""
    user_ids: List[str] = Field(..., min_items=1, description="用户ID列表")
    token_budget: float = Field(..., ge=0, description="新的token预算上限")

class BatchTokenUsageUpdateRequest(BaseModel):
    """批量更新用户token使用量请求模型"""
    user_ids: List[str] = Field(..., min_items=1, description="用户ID列表")
    current_token_usage: float = Field(..., ge=0, description="新的token使用量")

class UserTokenInfoResponse(BaseModel):
    """用户token信息响应模型"""
    user_id: str
    username: str
    token_budget: float
    current_token_usage: float
    remaining_budget: float
    usage_percentage: float

class EnvironmentConfig(BaseModel):
    """环境配置模型"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    environment: str = Field(default="dev", description="环境类型：dev或product")
    updated_at: datetime = Field(default_factory=get_cn_time)
    updated_by: str = Field(description="最后更新人ID")
    
    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
        "json_encoders": {ObjectId: str}
    }