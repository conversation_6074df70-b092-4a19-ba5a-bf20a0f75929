from fastapi import HTTPException, status


class TokenBudgetExceededException(HTTPException):
    """Token预算用尽异常"""
    def __init__(self, remaining_budget: float = 0.0, required_budget: float = 0.0):
        detail = f"Token使用额度已用尽。剩余额度: {remaining_budget:.4f}, 需要额度: {required_budget:.4f}。请联系管理员进行充值。"
        super().__init__(
            status_code=status.HTTP_402_PAYMENT_REQUIRED,
            detail=detail
        )


class InsufficientTokenBudgetException(HTTPException):
    """Token预算不足异常"""
    def __init__(self, remaining_budget: float, required_budget: float):
        detail = f"Token预算不足。剩余额度: {remaining_budget:.4f}, 需要额度: {required_budget:.4f}。请联系管理员进行充值。"
        super().__init__(
            status_code=status.HTTP_402_PAYMENT_REQUIRED,
            detail=detail
        ) 