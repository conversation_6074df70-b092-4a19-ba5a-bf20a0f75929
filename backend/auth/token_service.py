from typing import Optional, List, Dict
import logging
from bson import ObjectId

from backend.auth.models import UserDB, UserTokenInfoResponse
from backend.auth.crud import UserRepository
from backend.auth.exceptions import TokenBudgetExceededException, InsufficientTokenBudgetException

logger = logging.getLogger(__name__)


class TokenLimitService:
    """Token限制管理服务"""
    
    def __init__(self):
        self.user_repo = UserRepository()
    
    async def check_user_budget(self, user_id: str, required_budget: float = 0.0) -> bool:
        """
        检查用户是否有足够的token预算
        
        Args:
            user_id: 用户ID
            required_budget: 需要的预算金额（可选，用于预检查）
            
        Returns:
            bool: True如果有足够预算，False如果预算不足
            
        Raises:
            TokenBudgetExceededException: 如果预算已用尽
            InsufficientTokenBudgetException: 如果预算不足以进行当前操作
        """
        user = await self.user_repo.get_by_id(user_id)
        if not user:
            raise ValueError(f"找不到用户: {user_id}")
        
        remaining_budget = user.token_budget - user.current_token_usage
        
        # 如果剩余预算已经为负数或零，抛出预算用尽异常
        if remaining_budget <= 0:
            logger.warning(f"用户 {user_id} token预算已用尽: {remaining_budget}")
            raise TokenBudgetExceededException(remaining_budget, required_budget)
        
        # 如果提供了required_budget并且不足，抛出预算不足异常
        if required_budget > 0 and remaining_budget < required_budget:
            logger.warning(f"用户 {user_id} token预算不足: 剩余 {remaining_budget}, 需要 {required_budget}")
            raise InsufficientTokenBudgetException(remaining_budget, required_budget)
        
        return True
    
    async def get_user_token_info(self, user_id: str) -> UserTokenInfoResponse:
        """获取用户token使用信息"""
        user = await self.user_repo.get_by_id(user_id)
        if not user:
            raise ValueError(f"找不到用户: {user_id}")
        
        remaining_budget = user.token_budget - user.current_token_usage
        usage_percentage = (user.current_token_usage / user.token_budget * 100) if user.token_budget > 0 else 0
        
        return UserTokenInfoResponse(
            user_id=str(user.id),
            username=user.username,
            token_budget=user.token_budget,
            current_token_usage=user.current_token_usage,
            remaining_budget=max(0, remaining_budget),
            usage_percentage=min(100, usage_percentage)
        )
    
    async def update_user_budget(self, user_id: str, new_budget: float) -> bool:
        """更新用户token预算"""
        try:
            update_data = {"token_budget": new_budget}
            user = await self.user_repo.update_user_by_id(user_id, update_data)
            if user:
                logger.info(f"更新用户 {user_id} token预算为: {new_budget}")
                return True
            return False
        except Exception as e:
            logger.error(f"更新用户token预算失败: {str(e)}")
            raise
    
    async def update_user_usage(self, user_id: str, new_usage: float) -> bool:
        """更新用户token使用量"""
        try:
            update_data = {"current_token_usage": new_usage}
            user = await self.user_repo.update_user_by_id(user_id, update_data)
            if user:
                logger.info(f"更新用户 {user_id} token使用量为: {new_usage}")
                return True
            return False
        except Exception as e:
            logger.error(f"更新用户token使用量失败: {str(e)}")
            raise
    
    async def increment_user_usage(self, user_id: str, usage_increment: float) -> bool:
        """增加用户token使用量"""
        try:
            user = await self.user_repo.get_by_id(user_id)
            if not user:
                raise ValueError(f"找不到用户: {user_id}")
            
            new_usage = user.current_token_usage + usage_increment
            return await self.update_user_usage(user_id, new_usage)
        except Exception as e:
            logger.error(f"增加用户token使用量失败: {str(e)}")
            raise
    
    async def reset_user_usage(self, user_id: str) -> bool:
        """重置用户token使用量为0"""
        return await self.update_user_usage(user_id, 0.0)

    async def sync_user_usage_from_sessions(self, user_id: str) -> Dict[str, any]:
        """从session记录同步用户token使用量到用户表
        
        这个方法确保用户表中的token使用量与session记录保持一致
        
        Args:
            user_id: 用户ID
            
        Returns:
            同步结果，包含同步前后的使用量和差异
        """
        try:
            # 导入SessionStore来查询session数据
            from backend.conversation.session_store import SessionStore
            session_store = SessionStore()
            
            # 获取用户当前在用户表中的使用量
            user = await self.user_repo.get_by_id(user_id)
            if not user:
                raise ValueError(f"找不到用户: {user_id}")
            
            old_usage = user.current_token_usage
            
            # 从session记录中计算实际使用量
            session_usage = await session_store.get_user_token_usage(user_id)
            actual_usage = session_usage.get("total_cost", 0.0)
            
            # 更新用户表中的使用量
            success = await self.update_user_usage(user_id, actual_usage)
            
            if success:
                logger.info(f"用户 {user_id} token使用量同步完成: {old_usage} -> {actual_usage}")
                return {
                    "user_id": user_id,
                    "old_usage": old_usage,
                    "new_usage": actual_usage,
                    "difference": actual_usage - old_usage,
                    "synced": True
                }
            else:
                raise Exception("更新用户使用量失败")
                
        except Exception as e:
            logger.error(f"同步用户token使用量失败: {str(e)}")
            return {
                "user_id": user_id,
                "error": str(e),
                "synced": False
            }

    async def sync_all_users_usage_from_sessions(self) -> Dict[str, any]:
        """同步所有用户的token使用量从session记录到用户表
        
        Returns:
            批量同步结果
        """
        try:
            # 获取所有用户
            users = await self.user_repo.get_all_users(limit=10000)
            
            success_count = 0
            failed_users = []
            sync_results = []
            
            for user in users:
                user_id = str(user.id)
                sync_result = await self.sync_user_usage_from_sessions(user_id)
                
                if sync_result.get("synced"):
                    success_count += 1
                    sync_results.append(sync_result)
                else:
                    failed_users.append({
                        "user_id": user_id,
                        "username": user.username,
                        "error": sync_result.get("error", "未知错误")
                    })
            
            logger.info(f"批量同步token使用量完成: 成功 {success_count}/{len(users)} 个用户")
            
            return {
                "total_users": len(users),
                "success_count": success_count,
                "failed_count": len(failed_users),
                "failed_users": failed_users,
                "sync_results": sync_results
            }
            
        except Exception as e:
            logger.error(f"批量同步用户token使用量失败: {str(e)}")
            raise

    async def record_session_token_usage(self, user_id: str, session_id: str, token_cost: float) -> bool:
        """记录session的token使用量并实时更新用户总使用量
        
        这是核心方法，确保session记录和用户表的一致性
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            token_cost: 本次token使用成本
            
        Returns:
            是否成功记录和更新
        """
        try:
            # 增加用户的token使用量
            success = await self.increment_user_usage(user_id, token_cost)
            
            if success:
                logger.info(f"用户 {user_id} session {session_id} token使用量已记录: +{token_cost}")
                
                # 检查是否超出预算
                try:
                    await self.check_user_budget(user_id)
                except (TokenBudgetExceededException, InsufficientTokenBudgetException) as e:
                    logger.warning(f"用户 {user_id} token预算警告: {str(e)}")
                    # 不阻止记录，只是记录警告
                
                return True
            else:
                logger.error(f"记录用户 {user_id} token使用量失败")
                return False
                
        except Exception as e:
            logger.error(f"记录session token使用量失败: {str(e)}")
            return False

    async def batch_update_user_budget(self, user_ids: List[str], new_budget: float) -> Dict[str, any]:
        """批量更新用户token预算"""
        try:
            success_count = 0
            failed_users = []
            updated_users = []
            
            for user_id in user_ids:
                try:
                    success = await self.update_user_budget(user_id, new_budget)
                    if success:
                        success_count += 1
                        # 获取更新后的用户信息
                        token_info = await self.get_user_token_info(user_id)
                        updated_users.append(token_info)
                    else:
                        failed_users.append({"user_id": user_id, "reason": "用户不存在"})
                except Exception as e:
                    logger.error(f"批量更新用户 {user_id} token预算失败: {str(e)}")
                    failed_users.append({"user_id": user_id, "reason": str(e)})
            
            logger.info(f"批量更新token预算完成: 成功 {success_count}/{len(user_ids)} 个用户")
            
            return {
                "success_count": success_count,
                "total_count": len(user_ids),
                "failed_users": failed_users,
                "updated_users": updated_users
            }
        except Exception as e:
            logger.error(f"批量更新用户token预算失败: {str(e)}")
            raise

    async def batch_update_user_usage(self, user_ids: List[str], new_usage: float) -> Dict[str, any]:
        """批量更新用户token使用量"""
        try:
            success_count = 0
            failed_users = []
            updated_users = []
            
            for user_id in user_ids:
                try:
                    success = await self.update_user_usage(user_id, new_usage)
                    if success:
                        success_count += 1
                        # 获取更新后的用户信息
                        token_info = await self.get_user_token_info(user_id)
                        updated_users.append(token_info)
                    else:
                        failed_users.append({"user_id": user_id, "reason": "用户不存在"})
                except Exception as e:
                    logger.error(f"批量更新用户 {user_id} token使用量失败: {str(e)}")
                    failed_users.append({"user_id": user_id, "reason": str(e)})
            
            logger.info(f"批量更新token使用量完成: 成功 {success_count}/{len(user_ids)} 个用户")
            
            return {
                "success_count": success_count,
                "total_count": len(user_ids),
                "failed_users": failed_users,
                "updated_users": updated_users
            }
        except Exception as e:
            logger.error(f"批量更新用户token使用量失败: {str(e)}")
            raise 