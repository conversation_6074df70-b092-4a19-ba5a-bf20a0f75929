from fastapi import Depends, HTTPException, status
from fastapi.security import OA<PERSON>2<PERSON><PERSON>wordBearer
from jose import JW<PERSON>rror, jwt
from typing import Optional
import logging
from backend.auth.token_service import TokenLimitService
from backend.auth.models import TokenData, UserDB
from backend.auth.security import SECRET_KEY, ALGORITHM
from backend.auth.crud import UserRepository
from backend.auth.exceptions import TokenBudgetExceededException, InsufficientTokenBudgetException

logger = logging.getLogger(__name__)

# OAuth2密码承载令牌
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/auth/login")

async def get_current_user(token: str = Depends(oauth2_scheme)) -> UserDB:
    """
    获取当前用户
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # 解码JWT令牌
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        
        if username is None:
            logger.warning("令牌缺少用户名")
            raise credentials_exception
        
        token_data = TokenData(username=username)
    except JWTError as e:
        logger.warning(f"JWT令牌验证失败: {str(e)}")
        raise credentials_exception
    
    try:
        # 从数据库获取用户
        user_repo = UserRepository()
        user = await user_repo.get_by_username(token_data.username)
        
        if user is None:
            logger.warning(f"找不到令牌中的用户: {token_data.username}")
            raise credentials_exception
        
        return user
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取当前用户时出错: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="验证用户身份时发生错误"
        )


async def get_current_active_user(current_user: UserDB = Depends(get_current_user)) -> UserDB:
    """
    获取当前活跃用户
    """
    if not current_user.is_active:
        logger.warning(f"尝试使用非活跃用户账号访问: {current_user.username}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户已被禁用"
        )
    
    return current_user

async def get_current_user_info_required(current_user: UserDB = Depends(get_current_active_user)) -> dict:
    """
    获取当前活跃用户的基本信息，返回包含user_id的字典
    """
    return {
        "user_id": str(current_user.id),
        "username": current_user.username
    } 

async def get_current_admin_user(current_user: UserDB = Depends(get_current_active_user)) -> UserDB:
    """
    获取当前管理员用户，非管理员将被拒绝访问
    """
    if not current_user.is_admin:
        logger.warning(f"非管理员用户尝试访问管理员资源: {current_user.username}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要管理员权限"
        )
    
    return current_user


async def check_user_token_budget(current_user: UserDB = Depends(get_current_active_user)) -> UserDB:
    """
    检查用户是否还有token预算，主要用于研究开始和恢复时的检查
    """
    try:
        token_service = TokenLimitService()
        # 检查用户基本预算状态，如果预算用尽会抛出异常
        await token_service.check_user_budget(str(current_user.id))
        
        return current_user
    except (TokenBudgetExceededException, InsufficientTokenBudgetException) as e:
        logger.warning(f"用户 {current_user.id} token预算不足: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_402_PAYMENT_REQUIRED,
            detail=f"Token预算不足: {str(e)}"
        )
    except Exception as e:
        logger.error(f"检查用户token预算失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="检查token预算失败"
        )


def get_token_service() -> TokenLimitService:
    """
    获取token限制服务实例 - dependency injection
    """
    
    return TokenLimitService() 

async def record_token_usage_for_session(user_id: str, session_id: str, token_cost: float) -> bool:
    """记录session的token使用量并实时更新用户总使用量
    
    这是一个依赖函数，供其他模块调用来记录token使用量
    
    Args:
        user_id: 用户ID
        session_id: 会话ID
        token_cost: 本次token使用成本
        
    Returns:
        是否成功记录和更新
    """
    try:
        token_service = TokenLimitService()
        success = await token_service.record_session_token_usage(
            user_id=user_id,
            session_id=session_id,
            token_cost=token_cost
        )
        
        if success:
            logger.info(f"用户 {user_id} session {session_id} token使用量已记录: +{token_cost:.4f}")
        else:
            logger.error(f"记录用户 {user_id} token使用量失败")
            
        return success
        
    except Exception as e:
        logger.error(f"记录session token使用量失败: {str(e)}")
        return False 