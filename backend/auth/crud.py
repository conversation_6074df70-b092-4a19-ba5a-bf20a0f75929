from typing import Optional, List, Dict, Any
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorCollection
from fastapi import Depends
import logging
import random
import string

from backend.db.mongodb import MongoDBBase
from backend.db.dependencies import get_mongodb_collection
from backend.db.repository import BaseRepository
from backend.auth.models import UserDB, UserCreate, InviteCodeBase
from backend.auth.security import get_password_hash, verify_password, get_cn_time
from datetime import datetime, timezone, timedelta

logger = logging.getLogger(__name__)
def get_cn_time():
    """返回东八区（北京时间）的当前时间"""
    return datetime.now(timezone(timedelta(hours=8)))
class UserRepository(MongoDBBase[UserDB]):
    """用户数据仓库"""
    
    def __init__(self, collection: Optional[AsyncIOMotorCollection] = None):
        if collection:
            # 使用依赖注入的集合
            super().__init__(collection_name="users", model_class=UserDB, injected_collection=collection)
        else:
            # 向后兼容，使用原有方式
            super().__init__(collection_name="users", model_class=UserDB)
    
    async def get_by_id(self, id: str) -> Optional[UserDB]:
        """根据ID获取用户"""
        try:
            object_id = ObjectId(id)
            result = await self.find_one({"_id": object_id})
            return result
        except Exception as e:
            logger.error(f"查询用户失败 id={id}, error={e}")
            return None
    
    async def get_by_username(self, username: str) -> Optional[UserDB]:
        """根据用户名获取用户"""
        return await self.find_one({"username": username})
    
    async def get_by_email(self, email: str) -> Optional[UserDB]:
        """根据邮箱获取用户"""
        return await self.find_one({"email": email})
    
    async def create_user(self, user_data: UserCreate) -> UserDB:
        """创建新用户"""
        try:
            # 检查用户名是否已存在
            existing_user = await self.get_by_username(user_data.username)
            if existing_user:
                raise ValueError(f"用户名已存在 '{user_data.username}' ")
            
            # 检查邮箱是否已存在
            existing_email = await self.get_by_email(user_data.email)
            if existing_email:
                raise ValueError(f"邮箱已被注册 '{user_data.email}' ")
            
            # 创建用户数据模型
            user_dict = user_data.model_dump()
            hashed_password = get_password_hash(user_data.password)
            is_admin = getattr(user_data, 'is_admin', False)
            
            # 删除明文密码并添加哈希密码
            del user_dict["password"]
            user_dict["hashed_password"] = hashed_password
            user_dict["is_admin"] = is_admin

            # 保存到数据库
            return await self.create(user_dict)
        except Exception as e:
            logger.error(f"创建用户失败: {str(e)}")
            raise
    
    async def authenticate_user(self, username: str, password: str) -> Optional[UserDB]:
        """验证用户"""
        try:
            user = await self.get_by_username(username)
            if not user:
                logger.warning(f"认证失败: 用户名 {username} 不存在")
                return None
                
            if not verify_password(password, user.hashed_password):
                logger.warning(f"认证失败: 用户 {username} 密码错误")
                return None
                
            logger.info(f"用户 {username} 认证成功")
            return user
        except Exception as e:
            logger.error(f"用户认证过程中出错: {str(e)}")
            # 返回None而不是引发异常，以便登录接口可以返回适当的错误信息
            return None
    
    async def update_user(self, username: str, update_data: dict) -> Optional[UserDB]:
        """
        更新用户信息
        
        如果提供了密码，会自动转换为哈希密码
        """
        try:
            if "password" in update_data:
                # 如果提供了密码，转换为哈希密码
                hashed_password = get_password_hash(update_data["password"])
                # 移除原始密码并添加哈希密码
                del update_data["password"]
                update_data["hashed_password"] = hashed_password
            
            # 更新时间戳
            update_data["updated_at"] = get_cn_time()
            
            # 执行更新操作
            result = await self._collection.update_one(
                {"username": username},
                {"$set": update_data}
            )
            
            if result.modified_count == 0:
                logger.warning(f"更新用户失败: 未找到用户 '{username}'")
                return None
            
            # 返回更新后的用户
            return await self.get_by_username(username)
        except Exception as e:
            logger.error(f"更新用户失败: {str(e)}")
            raise
    
    async def update_user_by_id(self, user_id: str, update_data: dict) -> Optional[UserDB]:
        """
        通过用户ID更新用户信息
        
        如果提供了密码，会自动转换为哈希密码
        """
        try:
            if "password" in update_data:
                # 如果提供了密码，转换为哈希密码
                hashed_password = get_password_hash(update_data["password"])
                # 移除原始密码并添加哈希密码
                del update_data["password"]
                update_data["hashed_password"] = hashed_password
            
            # 更新时间戳
            update_data["updated_at"] = get_cn_time()
            
            # 执行更新操作
            result = await self._collection.update_one(
                {"_id": ObjectId(user_id)},
                {"$set": update_data}
            )
            
            if result.modified_count == 0:
                logger.warning(f"更新用户失败: 未找到用户ID '{user_id}'")
                return None
            
            # 返回更新后的用户
            return await self.get_by_id(user_id)
        except Exception as e:
            logger.error(f"通过ID更新用户失败: {str(e)}")
            raise
    
    async def get_all_users(self, skip: int = 0, limit: int = 100) -> List[UserDB]:
        """获取所有用户"""
        try:
            return await self.find_many({}, skip=skip, limit=limit)
        except Exception as e:
            logger.error(f"获取所有用户失败: {str(e)}")
            raise 

class InviteCodeRepository(MongoDBBase[InviteCodeBase]):
    """邀请码数据仓库"""
    
    def __init__(self, collection: Optional[AsyncIOMotorCollection] = None):
        if collection:
            # 使用依赖注入的集合
            super().__init__(collection_name="invite_codes", model_class=InviteCodeBase, injected_collection=collection)
        else:
            # 向后兼容，使用原有方式
            super().__init__(collection_name="invite_codes", model_class=InviteCodeBase)
    
    def _generate_invite_code(self) -> str:
        """生成8位邀请码"""
        chars = string.ascii_uppercase + string.digits
        return ''.join(random.choices(chars, k=8))
    
    async def generate_codes(self, count: int, created_by: str) -> List[InviteCodeBase]:
        """生成指定数量的邀请码"""
        codes = []
        for _ in range(count):
            code = self._generate_invite_code()
            # 确保生成的邀请码不重复
            while await self.find_one({"code": code}):
                code = self._generate_invite_code()
            
            invite_code = InviteCodeBase(
                code=code,
                created_by=created_by
            )
            codes.append(invite_code)
        
        # 批量插入邀请码
        await self._collection.insert_many([code.model_dump() for code in codes])
        return codes
    
    async def get_all_codes(self, skip: int = 0, limit: int = 100) -> List[InviteCodeBase]:
        """获取所有邀请码"""
        codes = await self.find_many({}, skip=skip, limit=limit)
        logger.info(f"获取所有邀请码: {codes}")
        return codes
    
    async def get_unused_codes(self) -> List[InviteCodeBase]:
        """获取所有未使用的邀请码"""
        return await self.find_many({"is_used": False})
    
    async def validate_code(self, code: str) -> bool:
        """只验证邀请码是否有效（未被使用）
        
        Args:
            code: 邀请码
        """
        logger.info(f"验证邀请码: {code}")
        invite_code = await self.find_one({"code": code, "is_used": False})
        logger.info(f"验证邀请码结果: {invite_code}")
        return invite_code is not None
        
    async def use_code(self, code: str, user_id: str) -> bool:
        """占用邀请码并设置使用者
        
        Args:
            code: 邀请码
            user_id: 用户ID
        """
        # 查找未使用的邀请码
        logger.info(f"使用邀请码: {code} by {user_id}")
        
        update_data = {
            "is_used": True,
            "used_by": user_id,
            "used_at": get_cn_time()
        }
        
        result = await self._collection.update_one(
            {"code": code, "is_used": False},  # 确保只更新未使用的邀请码
            {"$set": update_data}
        )
        
        logger.info(f"使用邀请码结果: {result.modified_count}")
        return result.modified_count > 0
        
    async def validate_and_use_code(self, code: str, user_id: Optional[str] = None) -> bool:
        """验证并使用邀请码 (兼容旧代码)
        
        Args:
            code: 邀请码
            user_id: 用户ID，如果为None则只验证不更新使用者信息
        """
        # 查找未使用的邀请码
        logger.info(f"验证邀请码: {code}")
        invite_code = await self.find_one({"code": code, "is_used": False})
        logger.info(f"验证邀请码结果: {invite_code}")
        if not invite_code:
            return False
        
        # 更新邀请码状态
        update_data = {
            "is_used": True,
            "used_at": get_cn_time()
        }
        
        # 如果提供了用户ID，则更新使用者信息
        if user_id:
            update_data["used_by"] = user_id
        
        result = await self._collection.update_one(
            {"code": code},
            {"$set": update_data}
        )
        logger.info(f"验证邀请码结果: {result}")
        return result.modified_count > 0

    async def get_all_codes_paginated(self, skip: int = 0, limit: int = 100, status: str = "all"):
        query = {}
        if status == "used":
            query["is_used"] = True
        elif status == "unused":
            query["is_used"] = False
        
        total = await self.collection.count_documents(query)
        cursor = self.collection.find(query).skip(skip).limit(limit)
        return await cursor.to_list(length=limit), total


# 依赖注入函数
async def get_user_repository(
    collection: AsyncIOMotorCollection = Depends(get_mongodb_collection("users"))
) -> UserRepository:
    """获取用户Repository实例（依赖注入）"""
    return UserRepository(collection)


async def get_invite_code_repository(
    collection: AsyncIOMotorCollection = Depends(get_mongodb_collection("invite_codes"))
) -> InviteCodeRepository:
    """获取邀请码Repository实例（依赖注入）"""
    return InviteCodeRepository(collection) 