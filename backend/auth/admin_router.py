from fastapi import APIRouter, Depends, HTTPException, status
import logging
from typing import List, Dict
from pydantic import BaseModel, Field

from backend.auth.models import UserDB, TokenLimitUpdateRequest, TokenUsageUpdateRequest, UserTokenInfoResponse, BatchTokenBudgetUpdateRequest, BatchTokenUsageUpdateRequest, EnvironmentConfig
from backend.auth.dependencies import get_current_admin_user
from backend.auth.crud import UserRepository
from backend.auth.token_service import TokenLimitService
from backend.db.mongodb import MongoDBBase

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/auth/admin", tags=["admin-token-management"])

@router.get("/users/{user_id}/token-info", response_model=UserTokenInfoResponse)
async def get_user_token_info(
    user_id: str,
    current_admin: UserDB = Depends(get_current_admin_user)
):
    """获取用户token使用信息（仅管理员）"""
    try:
        token_service = TokenLimitService()
        token_info = await token_service.get_user_token_info(user_id)
        return token_info
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"获取用户token信息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户token信息失败，请稍后再试"
        )

@router.put("/users/{user_id}/token-budget")
async def update_user_token_budget(
    user_id: str,
    request: TokenLimitUpdateRequest,
    current_admin: UserDB = Depends(get_current_admin_user)
):
    """更新用户token预算（仅管理员）"""
    try:
        token_service = TokenLimitService()
        success = await token_service.update_user_budget(user_id, request.token_budget)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 获取更新后的信息
        token_info = await token_service.get_user_token_info(user_id)
        
        return {
            "status": "success",
            "message": f"用户token预算已更新为 {request.token_budget}",
            "data": token_info
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新用户token预算失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用户token预算失败，请稍后再试"
        )

@router.put("/users/{user_id}/token-usage")
async def update_user_token_usage(
    user_id: str,
    request: TokenUsageUpdateRequest,
    current_admin: UserDB = Depends(get_current_admin_user)
):
    """更新用户token使用量（仅管理员）"""
    try:
        token_service = TokenLimitService()
        success = await token_service.update_user_usage(user_id, request.current_token_usage)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 获取更新后的信息
        token_info = await token_service.get_user_token_info(user_id)
        
        return {
            "status": "success",
            "message": f"用户token使用量已更新为 {request.current_token_usage}",
            "data": token_info
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新用户token使用量失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用户token使用量失败，请稍后再试"
        )

@router.post("/users/{user_id}/reset-token-usage")
async def reset_user_token_usage(
    user_id: str,
    current_admin: UserDB = Depends(get_current_admin_user)
):
    """重置用户token使用量为0（仅管理员）"""
    try:
        token_service = TokenLimitService()
        success = await token_service.reset_user_usage(user_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 获取更新后的信息
        token_info = await token_service.get_user_token_info(user_id)
        
        return {
            "status": "success",
            "message": "用户token使用量已重置为0",
            "data": token_info
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重置用户token使用量失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="重置用户token使用量失败，请稍后再试"
        )

@router.get("/users/token-overview")
async def get_all_users_token_overview(
    current_admin: UserDB = Depends(get_current_admin_user)
):
    """获取所有用户的token使用概览（仅管理员）"""
    try:
        user_repo = UserRepository()
        users = await user_repo.get_all_users(limit=1000)  # 获取所有用户
        
        token_service = TokenLimitService()
        
        overview = []
        for user in users:
            try:
                token_info = await token_service.get_user_token_info(str(user.id))
                overview.append(token_info)
            except Exception as e:
                logger.warning(f"获取用户 {user.id} token信息失败: {str(e)}")
                continue
        
        return {
            "status": "success",
            "data": overview,
            "total_users": len(overview)
        }
    except Exception as e:
        logger.error(f"获取用户token概览失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户token概览失败，请稍后再试"
        )

@router.put("/users/batch-token-budget")
async def batch_update_user_token_budget(
    request: BatchTokenBudgetUpdateRequest,
    current_admin: UserDB = Depends(get_current_admin_user)
):
    """批量更新用户token预算（仅管理员）"""
    try:
        token_service = TokenLimitService()
        result = await token_service.batch_update_user_budget(request.user_ids, request.token_budget)
        
        return {
            "status": "success",
            "message": f"批量更新完成: 成功更新 {result['success_count']}/{result['total_count']} 个用户的token预算为 {request.token_budget}",
            "data": {
                "success_count": result["success_count"],
                "total_count": result["total_count"],
                "failed_users": result["failed_users"],
                "updated_users": result["updated_users"]
            }
        }
    except Exception as e:
        logger.error(f"批量更新用户token预算失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="批量更新用户token预算失败，请稍后再试"
        )

@router.put("/users/batch-token-usage")
async def batch_update_user_token_usage(
    request: BatchTokenUsageUpdateRequest,
    current_admin: UserDB = Depends(get_current_admin_user)
):
    """批量更新用户token使用量（仅管理员）"""
    try:
        token_service = TokenLimitService()
        result = await token_service.batch_update_user_usage(request.user_ids, request.current_token_usage)
        
        return {
            "status": "success",
            "message": f"批量更新完成: 成功更新 {result['success_count']}/{result['total_count']} 个用户的token使用量为 {request.current_token_usage}",
            "data": {
                "success_count": result["success_count"],
                "total_count": result["total_count"],
                "failed_users": result["failed_users"],
                "updated_users": result["updated_users"]
            }
        }
    except Exception as e:
        logger.error(f"批量更新用户token使用量失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="批量更新用户token使用量失败，请稍后再试"
        )

@router.post("/users/{user_id}/sync-token-usage")
async def sync_user_token_usage_from_sessions(
    user_id: str,
    current_admin: UserDB = Depends(get_current_admin_user)
):
    """从session记录同步用户token使用量到用户表（仅管理员）"""
    try:
        token_service = TokenLimitService()
        sync_result = await token_service.sync_user_usage_from_sessions(user_id)
        
        if sync_result.get("synced"):
            return {
                "status": "success",
                "message": f"用户token使用量同步完成: {sync_result['old_usage']:.4f} -> {sync_result['new_usage']:.4f} (差异: {sync_result['difference']:.4f})",
                "data": sync_result
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"同步失败: {sync_result.get('error', '未知错误')}"
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"同步用户token使用量失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="同步用户token使用量失败，请稍后再试"
        )

@router.post("/users/sync-all-token-usage")
async def sync_all_users_token_usage_from_sessions(
    current_admin: UserDB = Depends(get_current_admin_user)
):
    """批量同步所有用户的token使用量从session记录到用户表（仅管理员）"""
    try:
        token_service = TokenLimitService()
        sync_result = await token_service.sync_all_users_usage_from_sessions()
        
        return {
            "status": "success",
            "message": f"批量同步完成: 成功同步 {sync_result['success_count']}/{sync_result['total_users']} 个用户的token使用量",
            "data": {
                "total_users": sync_result["total_users"],
                "success_count": sync_result["success_count"],
                "failed_count": sync_result["failed_count"],
                "failed_users": sync_result["failed_users"],
                "sync_results": sync_result["sync_results"]
            }
        }
    except Exception as e:
        logger.error(f"批量同步用户token使用量失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="批量同步用户token使用量失败，请稍后再试"
        )

@router.get("/environment")
async def get_environment_config(current_user: UserDB = Depends(get_current_admin_user)) -> Dict:
    """获取当前环境配置"""
    try:
        env_config_repo = MongoDBBase("environment_config", EnvironmentConfig)
        config = await env_config_repo.find_one({})
        if not config:
            # 如果没有配置，创建默认配置
            config = await env_config_repo.create({
                "environment": "dev",
                "updated_by": str(current_user.id)
            })
        return {"environment": config.environment}
    except Exception as e:
        logger.error(f"获取环境配置失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取环境配置失败，请稍后再试"
        )

class EnvironmentUpdateRequest(BaseModel):
    """环境更新请求模型"""
    environment: str = Field(..., description="环境类型：dev或product")

@router.post("/environment")
async def update_environment_config(
    request: EnvironmentUpdateRequest,
    current_user: UserDB = Depends(get_current_admin_user)
) -> Dict:
    """更新环境配置"""
    try:
        if request.environment not in ["dev", "product"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="环境类型必须是dev或product"
            )
        
        env_config_repo = MongoDBBase("environment_config", EnvironmentConfig)
        config = await env_config_repo.find_one({})
        if config:
            await env_config_repo.update(str(config.id), {
                "environment": request.environment,
                "updated_by": str(current_user.id)
            })
        else:
            await env_config_repo.create({
                "environment": request.environment,
                "updated_by": str(current_user.id)
            })
        
        return {"environment": request.environment}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新环境配置失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新环境配置失败，请稍后再试"
        )

@router.get("/environment/all")
async def get_all_environment_configs(current_user: UserDB = Depends(get_current_admin_user)):
    """获取所有环境配置文档（调试用）"""
    try:
        env_config_repo = MongoDBBase("environment_config", EnvironmentConfig)
        configs = await env_config_repo.find_many({})
        # 转为dict方便前端查看
        return {"configs": [c.model_dump() if hasattr(c, 'model_dump') else dict(c) for c in configs]}
    except Exception as e:
        logger.error(f"获取所有环境配置失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取所有环境配置失败，请稍后再试"
        ) 