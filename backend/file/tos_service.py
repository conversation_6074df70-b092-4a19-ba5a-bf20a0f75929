import uuid
import asyncio
import os
from datetime import datetime, timezone
from typing import Optional, List, BinaryIO
import tempfile

from tosfs.core import TosFileSystem
from tos import EnvCredentialsProvider
from backend.file.base_storage import BaseFileStorage, FileInfo
from backend.config import settings


class TOSFileStorage(BaseFileStorage):
    """TOS对象存储文件服务，基于tosfs接口"""
    
    def __init__(self):
        super().__init__()
        self.endpoint = settings.TOS_ENDPOINT
        self.access_key = settings.TOS_ACCESS_KEY
        self.secret_key = settings.TOS_SECRET_KEY
        self.bucket_name = settings.TOS_BUCKET_NAME
        self.region = settings.TOS_REGION
        
        # 初始化TOS文件系统
        if self.access_key and self.secret_key:
            # 使用配置的密钥
            self.fs = TosFileSystem(
                key=self.access_key,
                secret=self.secret_key,
                endpoint_url=self.endpoint,
                region=self.region,
            )
        else:
            # 尝试使用环境变量
            try:
                self.fs = TosFileSystem(
                    endpoint_url=self.endpoint or os.environ.get("TOS_ENDPOINT"),
                    region=self.region or os.environ.get("TOS_REGION"),
                    credentials_provider=EnvCredentialsProvider,
                )
            except Exception as e:
                raise ValueError(f"TOS配置失败，请检查配置文件或环境变量: {e}")
        
        if not self.bucket_name:
            raise ValueError("TOS存储桶名称未配置")
    
    def _get_user_prefix(self, user_id: str) -> str:
        """获取用户文件前缀路径"""
        return f"users/{user_id}/"
    
    def _generate_file_id(self) -> str:
        """生成文件ID"""
        return str(uuid.uuid4())
    
    def _get_object_path(self, user_id: str, file_id: str, filename: str) -> str:
        """获取对象存储完整路径"""
        return f"{self.bucket_name}/users/{user_id}/{file_id}/{filename}"
    
    def _get_bucket_path(self, path: str) -> str:
        """获取带存储桶的完整路径"""
        if path.startswith(f"{self.bucket_name}/"):
            return path
        return f"{self.bucket_name}/{path.lstrip('/')}"
    
    async def upload_file(
        self, 
        user_id: str, 
        filename: str, 
        file_content: BinaryIO,
        content_type: Optional[str] = None
    ) -> FileInfo:
        """上传文件到TOS"""
        file_id = self._generate_file_id()
        object_path = self._get_object_path(user_id, file_id, filename)
        
        # 读取文件内容
        file_data = file_content.read()
        file_size = len(file_data)
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(file_data)
            temp_file_path = temp_file.name
        
        try:
            # 使用tosfs上传文件
            def _upload():
                with open(temp_file_path, 'rb') as f:
                    with self.fs.open(object_path, 'wb') as remote_file:
                        remote_file.write(f.read())
            
            # 在线程池中执行同步操作
            await asyncio.get_event_loop().run_in_executor(None, _upload)
            
            # 返回文件信息
            now = datetime.now(timezone.utc)
            return FileInfo(
                file_id=file_id,
                filename=filename,
                file_path=object_path,
                file_size=file_size,
                content_type=content_type or 'application/octet-stream',
                user_id=user_id,
                created_at=now,
                updated_at=now
            )
            
        except Exception as e:
            raise Exception(f"上传文件失败: {e}")
        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
    
    async def download_file(self, user_id: str, file_id: str) -> Optional[bytes]:
        """从TOS下载文件"""
        try:
            # 列出用户目录下的文件，找到对应的文件
            user_prefix = self._get_bucket_path(f"users/{user_id}/")
            file_prefix = f"{user_prefix}{file_id}/"
            
            def _list_and_download():
                try:
                    # 列出文件
                    files = self.fs.ls(file_prefix, detail=False)
                    if not files:
                        return None
                    
                    # 取第一个文件（假设每个file_id目录下只有一个文件）
                    file_path = files[0]
                    
                    # 下载文件
                    with self.fs.open(file_path, 'rb') as f:
                        return f.read()
                except Exception:
                    return None
            
            content = await asyncio.get_event_loop().run_in_executor(None, _list_and_download)
            return content
            
        except Exception as e:
            print(f"下载文件失败: {e}")
            return None
    
    async def delete_file(self, user_id: str, file_id: str) -> bool:
        """从TOS删除文件"""
        try:
            # 列出用户目录下的文件，找到对应的文件
            user_prefix = self._get_bucket_path(f"users/{user_id}/")
            file_prefix = f"{user_prefix}{file_id}/"
            
            def _delete():
                try:
                    files = self.fs.ls(file_prefix, detail=False)
                    for file_path in files:
                        self.fs.rm(file_path)
                    
                    # 尝试删除空目录
                    try:
                        self.fs.rmdir(file_prefix.rstrip('/'))
                    except:
                        pass  # 目录可能不为空或不存在
                    
                    return True
                except Exception:
                    return False
            
            result = await asyncio.get_event_loop().run_in_executor(None, _delete)
            return result
            
        except Exception as e:
            print(f"删除文件失败: {e}")
            return False
    
    async def list_files(self, user_id: str, limit: int = 100, offset: int = 0) -> List[FileInfo]:
        """列出用户文件"""
        try:
            user_prefix = self._get_bucket_path(f"users/{user_id}/")
            print(f"DEBUG: 尝试列出目录: {user_prefix}")
            
            def _list():
                try:
                    # 列出用户目录下的所有文件（递归）
                    print(f"DEBUG: 开始列出文件，路径: {user_prefix}")
                    items = self.fs.ls(user_prefix, detail=True)
                    print(f"DEBUG: 找到 {len(items)} 个项目")
                    
                    file_infos = []
                    
                    for i, item in enumerate(items):
                        print(f"DEBUG: 处理项目 {i}: {item}")
                        
                        item_type = item.get('type', '').lower()
                        item_path = item['name']
                        
                        if item_type == 'directory':
                            # 这是一个file_id目录，需要进入其中列出文件
                            print(f"DEBUG: 发现目录，进入列出文件: {item_path}")
                            try:
                                sub_items = self.fs.ls(item_path + '/', detail=True)
                                print(f"DEBUG: 目录 {item_path} 中找到 {len(sub_items)} 个项目")
                                
                                for sub_item in sub_items:
                                    print(f"DEBUG: 处理子项目: {sub_item}")
                                    sub_type = sub_item.get('type', '').lower()
                                    
                                    if sub_type in ['file', 'object']:
                                        # 这是实际的文件
                                        file_path = sub_item['name']
                                        print(f"DEBUG: 找到文件: {file_path}")
                                        
                                        # 解析路径获取file_id和filename
                                        relative_path = file_path.replace(user_prefix, '')
                                        path_parts = relative_path.split('/')
                                        print(f"DEBUG: 文件路径部分: {path_parts}")
                                        
                                        if len(path_parts) >= 2:
                                            file_id = path_parts[0]
                                            filename = path_parts[1]
                                            
                                            # 处理时间戳
                                            last_modified = sub_item.get('LastModified', 0)
                                            if isinstance(last_modified, (int, float)):
                                                created_at = datetime.fromtimestamp(last_modified, timezone.utc)
                                            else:
                                                created_at = datetime.now(timezone.utc)
                                            
                                            file_info_obj = FileInfo(
                                                file_id=file_id,
                                                filename=filename,
                                                file_path=file_path,
                                                file_size=sub_item.get('size', 0),
                                                content_type='application/octet-stream',
                                                user_id=user_id,
                                                created_at=created_at,
                                                updated_at=created_at
                                            )
                                            file_infos.append(file_info_obj)
                                            print(f"DEBUG: 添加文件信息: {filename}")
                                            
                            except Exception as e:
                                print(f"DEBUG: 列出子目录失败: {e}")
                                
                        elif item_type in ['file', 'object']:
                            # 直接是文件（向后兼容）
                            print(f"DEBUG: 直接文件: {item_path}")
                            relative_path = item_path.replace(user_prefix, '')
                            path_parts = relative_path.split('/')
                            
                            if len(path_parts) >= 2:
                                file_id = path_parts[0]
                                filename = path_parts[1]
                                
                                last_modified = item.get('LastModified', 0)
                                if isinstance(last_modified, (int, float)):
                                    created_at = datetime.fromtimestamp(last_modified, timezone.utc)
                                else:
                                    created_at = datetime.now(timezone.utc)
                                
                                file_info_obj = FileInfo(
                                    file_id=file_id,
                                    filename=filename,
                                    file_path=item_path,
                                    file_size=item.get('size', 0),
                                    content_type='application/octet-stream',
                                    user_id=user_id,
                                    created_at=created_at,
                                    updated_at=created_at
                                )
                                file_infos.append(file_info_obj)
                                print(f"DEBUG: 添加文件信息: {filename}")
                    
                    print(f"DEBUG: 最终找到 {len(file_infos)} 个有效文件")
                    # 应用分页
                    return file_infos[offset:offset+limit]
                    
                except Exception as e:
                    print(f"DEBUG: 列出文件时出错: {e}")
                    import traceback
                    traceback.print_exc()
                    return []
            
            files = await asyncio.get_event_loop().run_in_executor(None, _list)
            return files
            
        except Exception as e:
            print(f"列出文件失败: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    async def get_file_info(self, user_id: str, file_id: str) -> Optional[FileInfo]:
        """获取文件信息"""
        try:
            user_prefix = self._get_bucket_path(f"users/{user_id}/")
            file_prefix = f"{user_prefix}{file_id}/"
            
            def _get_info():
                try:
                    files = self.fs.ls(file_prefix, detail=True)
                    if files:
                        file_info = files[0]
                        relative_path = file_info['name'].replace(user_prefix, '')
                        path_parts = relative_path.split('/')
                        
                        if len(path_parts) >= 2:
                            filename = path_parts[1]
                            
                            # 处理时间戳
                            last_modified = file_info.get('LastModified', 0)
                            if isinstance(last_modified, (int, float)):
                                created_at = datetime.fromtimestamp(last_modified, timezone.utc)
                            else:
                                created_at = datetime.now(timezone.utc)
                            
                            return FileInfo(
                                file_id=file_id,
                                filename=filename,
                                file_path=file_info['name'],
                                file_size=file_info.get('size', 0),
                                content_type='application/octet-stream',
                                user_id=user_id,
                                created_at=created_at,
                                updated_at=created_at
                            )
                    return None
                except Exception:
                    return None
            
            file_info = await asyncio.get_event_loop().run_in_executor(None, _get_info)
            return file_info
            
        except Exception as e:
            print(f"获取文件信息失败: {e}")
            return None
    
    async def get_file_url(self, user_id: str, file_id: str, expires_in: int = 3600) -> Optional[str]:
        """获取文件访问URL"""
        try:
            # 首先获取文件信息
            file_info = await self.get_file_info(user_id, file_id)
            if not file_info:
                return None
            
            def _get_url():
                try:
                    # 构建正确的TOS公开访问URL
                    # file_info.file_path 格式为: deepcognition/users/{user_id}/{file_id}/{filename}
                    # 需要移除开头的bucket名称，构建正确的URL
                    if file_info.file_path.startswith(f"{self.bucket_name}/"):
                        file_path_without_bucket = file_info.file_path[len(f"{self.bucket_name}/"):]
                    else:
                        file_path_without_bucket = file_info.file_path
                    
                    # 返回标准的TOS访问URL格式
                    url = f"{self.endpoint}/{self.bucket_name}/{file_path_without_bucket}"
                    print(f"DEBUG: 生成的文件URL: {url}")
                    return url
                except Exception as e:
                    print(f"DEBUG: 生成URL失败: {e}")
                    return None
            
            url = await asyncio.get_event_loop().run_in_executor(None, _get_url)
            return url
            
        except Exception as e:
            print(f"获取文件URL失败: {e}")
            return None 