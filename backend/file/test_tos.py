import asyncio
import io
from .storage_factory import get_storage


async def test_tos_storage():
    """测试TOS存储功能"""
    try:
        # 获取存储实例
        storage = get_storage()
        print(f"存储类型: {type(storage).__name__}")
        
        # 测试数据
        user_id = "test_user_123"
        filename = "test_file.txt"
        file_content = io.BytesIO(b"Hello, TOS Storage with tosfs!")
        
        print(f"开始测试用户 {user_id} 的文件操作...")
        
        # 测试上传文件
        print("1. 测试上传文件...")
        file_info = await storage.upload_file(
            user_id=user_id,
            filename=filename,
            file_content=file_content,
            content_type="text/plain"
        )
        print(f"上传成功: {file_info.file_id}")
        print(f"文件路径: {file_info.file_path}")
        
        # 测试获取文件信息
        print("2. 测试获取文件信息...")
        retrieved_info = await storage.get_file_info(user_id, file_info.file_id)
        if retrieved_info:
            print(f"文件信息: {retrieved_info.filename}, 大小: {retrieved_info.file_size}")
        else:
            print("获取文件信息失败")
        
        # 测试获取文件URL
        print("3. 测试获取文件URL...")
        file_url = await storage.get_file_url(user_id, file_info.file_id)
        print(f"文件URL: {file_url}")
        
        # 测试下载文件
        print("4. 测试下载文件...")
        downloaded_content = await storage.download_file(user_id, file_info.file_id)
        if downloaded_content:
            print(f"下载成功，内容: {downloaded_content.decode('utf-8')}")
        else:
            print("下载失败")
        
        # 测试列出文件
        print("5. 测试列出文件...")
        files = await storage.list_files(user_id)
        print(f"用户文件数量: {len(files)}")
        for file in files:
            print(f"  - {file.filename} ({file.file_size} bytes)")
        
        # 测试删除文件
        print("6. 测试删除文件...")
        deleted = await storage.delete_file(user_id, file_info.file_id)
        print(f"删除结果: {deleted}")
        
        print("测试完成!")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 运行TOS存储测试
    asyncio.run(test_tos_storage()) 