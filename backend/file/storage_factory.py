from typing import Type
from backend.file.base_storage import BaseFileStorage
from backend.file.tos_service import TOSFileStorage
from backend.config import settings


class StorageFactory:
    """文件存储工厂类"""
    
    @staticmethod
    def create_storage() -> BaseFileStorage:
        """
        根据配置创建存储实例
        
        Returns:
            BaseFileStorage: 存储实例
        """
        if settings.TOS_ENDPOINT:
            return TOSFileStorage()
        else:
            raise ValueError("未配置有效的存储服务")
    
    @staticmethod
    def get_storage_type() -> str:
        """
        获取当前配置的存储类型
        
        Returns:
            str: 存储类型名称
        """
        if settings.TOS_ENDPOINT:
            return "TOS"
        else:
            return "未配置"


# 全局存储实例
storage_instance: BaseFileStorage = None


def get_storage() -> BaseFileStorage:
    """
    获取存储实例（单例模式）
    
    Returns:
        BaseFileStorage: 存储实例
    """
    global storage_instance
    if storage_instance is None:
        storage_instance = StorageFactory.create_storage()
    return storage_instance 