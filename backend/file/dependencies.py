import re
from fastapi import Depends, UploadFile, HTTPException, status
from typing import Optional
from backend.file.storage_factory import get_storage
from backend.file.base_storage import BaseFileStorage
from backend.auth.dependencies import get_current_active_user
from backend.auth.models import UserDB
import logging

logger = logging.getLogger(__name__)


def get_file_storage() -> BaseFileStorage:
    """获取文件存储服务依赖"""
    return get_storage()


async def upload_file_and_get_url(
    file: UploadFile,
    user: UserDB = Depends(get_current_active_user),
    storage: BaseFileStorage = Depends(get_file_storage)
) -> str:
    """
    上传文件并返回公开访问链接的dependency
    
    Args:
        file: 上传的文件
        user: 当前用户
        storage: 文件存储服务
        
    Returns:
        str: 文件的公开访问链接
        
    Raises:
        HTTPException: 当上传失败时
    """
    try:
        # 验证文件
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文件名不能为空"
            )
        
        # 检查文件大小（假设限制为10MB）
        if file.size and file.size > 10 * 1024 * 1024:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail="文件大小不能超过10MB"
            )
        
        # 上传文件
        file_info = await storage.upload_file(
            user_id=str(user.id),
            filename=file.filename,
            file_content=file.file,
            content_type=file.content_type
        )
        
        # 获取公开访问链接
        file_url = await storage.get_file_url(
            user_id=str(user.id),
            file_id=file_info.file_id,
            expires_in=365 * 24 * 3600  # 设置为1年过期，用于长期访问
        )
        
        if not file_url:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="无法生成文件访问链接"
            )
        
        logger.info(f"文件上传成功: {file.filename}, URL: {file_url}")
        return file_url
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件上传失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文件上传失败: {str(e)}"
        )


async def upload_image_and_get_url(
    file: UploadFile,
    user: UserDB = Depends(get_current_active_user),
    storage: BaseFileStorage = Depends(get_file_storage)
) -> str:
    """
    专门用于上传图片并返回公开访问链接的dependency
    
    Args:
        file: 上传的图片文件
        user: 当前用户
        storage: 文件存储服务
        
    Returns:
        str: 图片的公开访问链接
        
    Raises:
        HTTPException: 当上传失败或文件不是图片时
    """
    try:
        # 验证文件是否为图片
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只能上传图片文件"
            )
        
        # 使用通用的文件上传功能
        return await upload_file_and_get_url(file, user, storage)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"图片上传失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"图片上传失败: {str(e)}"
        )