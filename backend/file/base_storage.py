from abc import ABC, abstractmethod
from typing import Optional, List, BinaryIO
from pydantic import BaseModel
from datetime import datetime


class FileInfo(BaseModel):
    """文件信息模型"""
    file_id: str
    filename: str
    file_path: str
    file_size: int
    content_type: str
    user_id: str
    created_at: datetime
    updated_at: datetime


class BaseFileStorage(ABC):
    """文件存储抽象基类"""
    
    def __init__(self):
        """初始化存储服务"""
        pass
    
    @abstractmethod
    async def upload_file(
        self, 
        user_id: str, 
        filename: str, 
        file_content: BinaryIO,
        content_type: Optional[str] = None
    ) -> FileInfo:
        """
        上传文件
        
        Args:
            user_id: 用户ID
            filename: 文件名
            file_content: 文件内容
            content_type: 文件类型
            
        Returns:
            FileInfo: 文件信息
        """
        pass
    
    @abstractmethod
    async def download_file(self, user_id: str, file_id: str) -> Optional[bytes]:
        """
        下载文件
        
        Args:
            user_id: 用户ID
            file_id: 文件ID
            
        Returns:
            bytes: 文件内容，如果文件不存在返回None
        """
        pass
    
    @abstractmethod
    async def delete_file(self, user_id: str, file_id: str) -> bool:
        """
        删除文件
        
        Args:
            user_id: 用户ID
            file_id: 文件ID
            
        Returns:
            bool: 删除是否成功
        """
        pass
    
    @abstractmethod
    async def list_files(self, user_id: str, limit: int = 100, offset: int = 0) -> List[FileInfo]:
        """
        列出用户文件
        
        Args:
            user_id: 用户ID
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            List[FileInfo]: 文件信息列表
        """
        pass
    
    @abstractmethod
    async def get_file_info(self, user_id: str, file_id: str) -> Optional[FileInfo]:
        """
        获取文件信息
        
        Args:
            user_id: 用户ID
            file_id: 文件ID
            
        Returns:
            FileInfo: 文件信息，如果文件不存在返回None
        """
        pass
    
    @abstractmethod
    async def get_file_url(self, user_id: str, file_id: str, expires_in: int = 3600) -> Optional[str]:
        """
        获取文件访问URL
        
        Args:
            user_id: 用户ID
            file_id: 文件ID
            expires_in: 过期时间（秒）
            
        Returns:
            str: 文件访问URL，如果文件不存在返回None
        """
        pass 