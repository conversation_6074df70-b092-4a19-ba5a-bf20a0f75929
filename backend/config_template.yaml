# DAIR 项目配置文件模板
# 这个文件不应该被提交到代码库，请复制到安全位置使用

# 环境设置
environment: "production"  # development, testing, production

# 数据库配置
mongo_uri: "mongodb://your-mongo-host:27017"
mongo_db_name: "dair_prod"

# API配置
api_host: "0.0.0.0"
api_port: 8000

# 日志配置
log_level: "INFO"
log_file_dir: "/var/log/dair"
log_file: "/var/log/dair/dair.log"

# 跨域配置
cors_origins:
  - "https://your-frontend-domain.com"
  - "https://api.your-domain.com"

# 管理员配置
admin_username: "admin"
admin_email: "<EMAIL>" 
admin_password: "your-secure-password"

# 文件上传配置
upload_path: "/app/dair/uploads"

# TOS对象存储配置
tos_endpoint: "your-tos-endpoint"
tos_access_key: "your-access-key"
tos_secret_key: "your-secret-key"
tos_bucket_name: "your-bucket-name"
tos_region: "your-region" 