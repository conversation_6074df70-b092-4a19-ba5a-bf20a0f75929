#!/usr/bin/env python3
"""
DAIR 测试运行器

提供便捷的测试运行方式，支持不同类型的测试过滤和执行
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd: list, description: str):
    """运行命令并处理结果"""
    print(f"\n{'='*60}")
    print(f"运行: {description}")
    print(f"命令: {' '.join(cmd)}")
    print('='*60)
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"\n✅ {description} 成功完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n❌ {description} 失败，退出码: {e.returncode}")
        return False
    except KeyboardInterrupt:
        print(f"\n⏹️ {description} 被用户中断")
        return False


def main():
    parser = argparse.ArgumentParser(description="DAIR 测试运行器")
    
    # 测试类型选择
    test_group = parser.add_mutually_exclusive_group()
    test_group.add_argument(
        "--unit", 
        action="store_true", 
        help="只运行单元测试（快速，使用mock）"
    )
    test_group.add_argument(
        "--integration", 
        action="store_true", 
        help="只运行集成测试（需要真实服务）"
    )
    test_group.add_argument(
        "--all", 
        action="store_true", 
        help="运行所有测试"
    )
    
    # 模块选择
    parser.add_argument(
        "--module", 
        choices=["conversation", "auth", "document", "redis", "mongodb", "tos"],
        help="只测试特定模块"
    )
    
    # 其他选项
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="详细输出"
    )
    parser.add_argument(
        "--no-slow",
        action="store_true",
        help="跳过慢速测试"
    )
    parser.add_argument(
        "--coverage",
        action="store_true",
        help="生成覆盖率报告"
    )
    parser.add_argument(
        "--html-report",
        action="store_true",
        help="生成HTML测试报告"
    )
    
    args = parser.parse_args()
    
    # 构建基础命令
    base_cmd = ["python", "-m", "pytest"]
    
    # 添加详细输出
    if args.verbose:
        base_cmd.append("-v")
    
    # 添加覆盖率
    if args.coverage:
        base_cmd.extend([
            "--cov=backend",
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov"
        ])
    
    # 添加HTML报告
    if args.html_report:
        base_cmd.extend(["--html=test_report.html", "--self-contained-html"])
    
    # 根据选择构建测试命令
    test_commands = []
    
    if args.unit:
        cmd = base_cmd + ["-m", "unit"]
        if args.no_slow:
            cmd.extend(["-m", "not slow"])
        if args.module:
            cmd.extend(["-m", args.module])
        test_commands.append((cmd, "单元测试"))
        
    elif args.integration:
        cmd = base_cmd + ["-m", "integration"]
        if args.no_slow:
            cmd.extend(["-m", "not slow"])
        if args.module:
            cmd.extend(["-m", args.module])
        test_commands.append((cmd, "集成测试"))
        
    elif args.all:
        # 先运行单元测试
        unit_cmd = base_cmd + ["-m", "unit"]
        if args.no_slow:
            unit_cmd.extend(["-m", "not slow"])
        if args.module:
            unit_cmd.extend(["-m", args.module])
        test_commands.append((unit_cmd, "单元测试"))
        
        # 再运行集成测试
        integration_cmd = base_cmd + ["-m", "integration"]
        if args.no_slow:
            integration_cmd.extend(["-m", "not slow"])
        if args.module:
            integration_cmd.extend(["-m", args.module])
        test_commands.append((integration_cmd, "集成测试"))
        
    else:
        # 默认运行所有测试
        cmd = base_cmd.copy()
        if args.no_slow:
            cmd.extend(["-m", "not slow"])
        if args.module:
            cmd.extend(["-m", args.module])
        test_commands.append((cmd, "所有测试"))
    
    # 执行测试命令
    success_count = 0
    total_count = len(test_commands)
    
    for cmd, description in test_commands:
        if run_command(cmd, description):
            success_count += 1
    
    # 总结
    print(f"\n{'='*60}")
    print(f"测试总结: {success_count}/{total_count} 组测试成功")
    print('='*60)
    
    if success_count == total_count:
        print("🎉 所有测试都通过了！")
        sys.exit(0)
    else:
        print("❌ 有测试失败")
        sys.exit(1)


if __name__ == "__main__":
    # 确保在正确的目录中运行
    backend_dir = Path(__file__).parent
    if backend_dir.name != "backend":
        print("错误: 请在backend目录中运行此脚本")
        sys.exit(1)
    
    main() 