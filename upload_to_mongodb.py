import asyncio
import json
from motor.motor_asyncio import AsyncIOMotorClient
from bson import ObjectId
from datetime import datetime
import os

# --- 配置 ---
# 这些信息是根据您的 backend/config.py 和 backend/db/dependencies.py 文件推断出来的
MONGO_URI = "mongodb://dair:<EMAIL>:3717,mongoreplica98081f54cfd51.mongodb.ap-southeast-1.ivolces.com:3717"
MONGO_DB_NAME = "dair"
COLLECTION_NAME = "cognitions_new"
# 包含了 cover_url 的 JSON 文件路径
INPUT_FILE = "/root/xjcai/DAIR/cognitions_export_with_cover.json"



def json_serializer(obj):
    """为json.dump提供自定义序列化方法，处理datetime和ObjectId等特殊类型"""
    if isinstance(obj, datetime):
        return obj.isoformat()
    if isinstance(obj, ObjectId):
        return str(obj)
    
    # 为其他无法序列化的类型提供一个备选方案
    try:
        return str(obj)
    except Exception:
        raise TypeError(f"Type {type(obj)} not serializable")

async def update_cognitions_with_cover_url():
    """连接到 MongoDB，读取 JSON 文件，并使用 cover_url 更新集合中的文档。"""
    print("开始更新认知数据中的 cover_url...")
    
    if not os.path.exists(INPUT_FILE):
        print(f"错误：输入文件不存在于 '{INPUT_FILE}'")
        return

    print(f"正在从 '{INPUT_FILE}' 读取数据...")
    with open(INPUT_FILE, 'r', encoding='utf-8') as f:
        documents_to_update = json.load(f)
    print(f"成功读取 {len(documents_to_update)} 条文档。")

    client = None
    try:
        print("正在连接到 MongoDB...")
        client = AsyncIOMotorClient(MONGO_URI)
        db = client[MONGO_DB_NAME]
        collection = db[COLLECTION_NAME]
        
        await client.admin.command('ping')
        print(f"成功连接到数据库 '{MONGO_DB_NAME}' 和集合 '{COLLECTION_NAME}'")

        updated_count = 0
        not_found_count = 0

        print("正在更新数据库中的文档...")
        for doc in documents_to_update:
            if '_id' not in doc or 'cover_url' not in doc:
                print(f"跳过一个文档，因为它缺少 '_id' 或 'cover_url' 字段: {doc.get('_id', 'N/A')}")
                continue

            try:
                # _id 在 JSON 文件中是字符串，需要转换回 ObjectId
                doc_id = ObjectId(doc['_id'])
                cover_url = doc['cover_url']

                result = await collection.update_one(
                    {'_id': doc_id},
                    {'$set': {'cover_url': cover_url}}
                )

                if result.matched_count > 0:
                    updated_count += 1
                else:
                    not_found_count += 1
                    print(f"警告：在数据库中未找到 ID 为 '{doc_id}' 的文档。")
            except Exception as e:
                print(f"处理文档 ID {doc.get('_id')} 时出错: {e}")

        
        print("\n更新操作完成！")
        print(f"成功更新 {updated_count} 个文档。")
        if not_found_count > 0:
            print(f"{not_found_count} 个文档在数据库中未找到。")

    except Exception as e:
        print(f"操作失败，发生错误: {e}")
        print("请检查您的网络连接、MongoDB URI、数据库名称和 replicaSet 名称是否正确。")
    finally:
        if client:
            client.close()
            print("数据库连接已关闭。")

if __name__ == "__main__":
    # 在运行此脚本之前，请确保您已经安装了必要的 Python 库。
    # 您可以通过以下命令安装：
    # pip install motor pymongo
    asyncio.run(update_cognitions_with_cover_url()) 