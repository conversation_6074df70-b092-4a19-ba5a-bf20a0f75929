name: Simple Activate

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: self-hosted
    env:
      HOSTNAME: ${{ secrets.ALIYUN_DEV_HOSTNAME }}
      PORT: ${{ secrets.ALIYUN_DEV_SSH_PORT }}
      PRIV_KEY: ${{ secrets.ALIYUN_DEV_SSH_PRIV_KEY }}
      USERNAME: ${{ secrets.ALIYUN_DEV_USERNAME }}
      REMOTE_TARGET: ${{ secrets.REMOTE_TARGET }}
      REGISTRY: ${{ secrets.ALIYUN_REGISTRY }}
      BACKEND_IMAGE: deepcognition/dc-backend
      FRONTEND_IMAGE: deepcognition/dc-frontend
      NGINX_IMAGE: deepcognition/dc-nginx

    steps:
      - name: Deploy Application via SSH
        uses: easingthemes/ssh-deploy@main
        with:
          REMOTE_HOST: ${{ env.HOSTNAME }}
          REMOTE_USER: ${{ env.USERNAME }}
          SSH_PRIVATE_KEY: ${{ env.PRIV_KEY }}
          REMOTE_PORT: ${{ env.PORT }}
          TARGET: ${{ env.REMOTE_TARGET }}
          SOURCE: "./"
          SCRIPT_BEFORE: |
            cd ${{ env.REMOTE_TARGET }}
            echo "${{ secrets.ALIYUN_PASSWD }}" | docker login --username ${{ secrets.ALIYUN_RAM_USER }} --password-stdin ${{ secrets.ALIYUN_REGISTRY }}
            export ALI_DOCKER_REGISTRY=${{ secrets.ALIYUN_REGISTRY }}
          SCRIPT_AFTER: |
            cd ${{ env.REMOTE_TARGET }}
            echo "${{ secrets.ALIYUN_PASSWD }}" | docker login --username ${{ secrets.ALIYUN_RAM_USER }} --password-stdin ${{ secrets.ALIYUN_REGISTRY }}
            export ALI_DOCKER_REGISTRY=${{ secrets.ALIYUN_REGISTRY }}
            bash deploy.sh
