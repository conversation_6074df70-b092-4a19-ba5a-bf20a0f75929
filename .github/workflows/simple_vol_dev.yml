name: Simple Activate To Vol DEV ECS through SSH

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: self-hosted
    env:
      HOSTNAME: ${{ secrets.VOL_DEV_HOSTNAME }}
      PORT: ${{ secrets.VOL_PORT }}
      PRIV_KEY: ${{ secrets.ALIYUN_DEV_SSH_PRIV_KEY }}
      USERNAME: ${{ secrets.VOL_USERNAME }}
      REMOTE_TARGET: ${{ secrets.VOL_TARGET }}
      REGISTRY: ${{ secrets.VOLCE_REGISTRY  }}
      BACKEND_IMAGE: deepcognition-build/dc-backend
      FRONTEND_IMAGE: deepcognition-build/dc-frontend
      NGINX_IMAGE: deepcognition-build/dc-nginx

    steps:
      - name: Deploy Application via SSH
        uses: easingthemes/ssh-deploy@main
        with:
          REMOTE_HOST: ${{ env.HOSTNAME }}
          REMOTE_USER: ${{ env.USERNAME }}
          SSH_PRIVATE_KEY: ${{ env.PRIV_KEY }}
          REMOTE_PORT: ${{ env.PORT }}
          TARGET: ${{ env.REMOTE_TARGET }}
          SOURCE: "./"
          SCRIPT_BEFORE: |
            cd ${{ env.REMOTE_TARGET }}
            echo "${{ secrets.VOLCE_IAM_PASSWD  }}" | docker login --username ${{ secrets.VOLCE_IAM_USER  }} --password-stdin ${{ secrets.VOLCE_REGISTRY  }}
            export ALI_DOCKER_REGISTRY=${{ secrets.VOLCE_REGISTRY  }}
          SCRIPT_AFTER: |
            cd ${{ env.REMOTE_TARGET }}
            echo "${{ secrets.VOLCE_IAM_PASSWD  }}" | docker login --username ${{ secrets.VOLCE_IAM_USER  }} --password-stdin ${{ secrets.VOLCE_REGISTRY  }}
            export ALI_DOCKER_REGISTRY=${{ secrets.VOLCE_REGISTRY  }}
            bash deploy.sh
