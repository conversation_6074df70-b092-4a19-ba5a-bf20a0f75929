name: Deploy To Vol DEV ECS through SSH

on:
  push: 
    branches: [ "main" ]
  workflow_dispatch:

jobs:
  build:
    runs-on: self-hosted
    env:
      HOSTNAME: ${{ secrets.VOL_DEV_HOSTNAME }}
      PORT: ${{ secrets.VOL_PORT }}
      PRIV_KEY: ${{ secrets.ALIYUN_DEV_SSH_PRIV_KEY }}
      USERNAME: ${{ secrets.VOL_USERNAME }}
      REMOTE_TARGET: ${{ secrets.VOL_TARGET }}
      REGISTRY: ${{ secrets.VOLCE_REGISTRY }}
      BACKEND_IMAGE: deepcognition-build/dc-backend
      FRONTEND_IMAGE: deepcognition-build/dc-frontend
      NGINX_IMAGE: deepcognition-build/dc-nginx

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      
      - name: Log in to the Container registry
        uses: docker/login-action@v1
        with:
          registry: ${{ secrets.VOLCE_REGISTRY }}
          username: ${{ secrets.VOLCE_IAM_USER }}
          password: ${{ secrets.VOLCE_IAM_PASSWD }}
        
      - name: Setup Node.js environment
        uses: actions/setup-node@v3.9.1
        with:
          node-version: 22.14.0
          
      - name: Setup Python
        uses: actions/setup-python@v5.6.0
        with:
          python-version: 3.12.3

      - name: Build and push frontend image
        uses: docker/build-push-action@v4
        with:
          context: ./frontend
          file: ./frontend/Dockerfile
          push: true
          tags: ${{ secrets.VOLCE_REGISTRY }}/${{ env.FRONTEND_IMAGE }}:latest, ${{ secrets.VOLCE_REGISTRY }}/${{ env.FRONTEND_IMAGE }}:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha, mode=max
          builder-debug: true

      - name: Build and push backend image
        uses: docker/build-push-action@v4
        with: 
          context: .
          file: ./backend/Dockerfile
          push: true
          tags: ${{ secrets.VOLCE_REGISTRY }}/${{ env.BACKEND_IMAGE }}:latest, ${{ secrets.VOLCE_REGISTRY }}/${{ env.BACKEND_IMAGE }}:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha, mode=max
          builder-debug: true

      - name: Build and push nginx image
        uses: docker/build-push-action@v4
        with:
          context: ./nginx
          file: ./nginx/Dockerfile
          push: true
          tags: ${{ secrets.VOLCE_REGISTRY }}/${{ env.NGINX_IMAGE }}:latest, ${{ secrets.VOLCE_REGISTRY }}/${{ env.NGINX_IMAGE }}:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
      
      - name: Deploy Application via SSH
        uses: easingthemes/ssh-deploy@main
        with:
          REMOTE_HOST: ${{ env.HOSTNAME }}
          REMOTE_USER: ${{ env.USERNAME }}
          SSH_PRIVATE_KEY: ${{ env.PRIV_KEY }}
          REMOTE_PORT: ${{ env.PORT }}
          TARGET: ${{ env.REMOTE_TARGET }}
          SOURCE: "./"
          SCRIPT_BEFORE: |
            cd ${{ env.REMOTE_TARGET }}
            echo "${{ secrets.VOLCE_IAM_PASSWD }}" | docker login --username ${{ secrets.VOLCE_IAM_USER }} --password-stdin ${{ secrets.VOLCE_REGISTRY }}
            export ALI_DOCKER_REGISTRY=${{ secrets.VOLCE_REGISTRY }}
            # 安装必要的工具
            command -v openssl >/dev/null 2>&1 || { echo "安装openssl..."; apt-get update && apt-get install -y openssl; }
            command -v curl >/dev/null 2>&1 || { echo "安装curl..."; apt-get update && apt-get install -y curl; }
          SCRIPT_AFTER: |
            cd ${{ env.REMOTE_TARGET }}
            echo "${{ secrets.VOLCE_IAM_PASSWD }}" | docker login --username ${{ secrets.VOLCE_IAM_USER }} --password-stdin ${{ secrets.VOLCE_REGISTRY }}
            export ALI_DOCKER_REGISTRY=${{ secrets.VOLCE_REGISTRY }}
            # 确保脚本有执行权限
            chmod +x deploy.sh
            chmod +x ssl-auto-setup.sh 2>/dev/null || true
            # 执行部署（包含SSL自动化）
            bash deploy.sh
