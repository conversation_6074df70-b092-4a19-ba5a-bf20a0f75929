name: build-and-integrate-test

on:
  pull_request:
    branches: ["main"]
  push:
    branches: ["main"]
  workflow_dispatch:
  
jobs:
  build:
    runs-on: self-hosted
    env:
      HOSTNAME: ${{ secrets.VOL_DEV_HOSTNAME }}
      PORT: ${{ secrets.VOL_PORT }}
      PRIV_KEY: ${{ secrets.ALIYUN_DEV_SSH_PRIV_KEY }}
      USERNAME: ${{ secrets.VOL_USERNAME }}
      REMOTE_TARGET: ${{ secrets.VOL_TARGET }}
      REGISTRY: ${{ secrets.ALIYUN_REGISTRY }}
      BACKEND_IMAGE: deepcognition/dc-backend
      FRONTEND_IMAGE: deepcognition/dc-frontend
      NGINX_IMAGE: deepcognition/dc-nginx
  
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

        
  test:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
      
      - name: Setup Python
        uses: actions/setup-python@v5.6.0
        with:
          python-version: 3.12.3

      - name: Make test script executable
        run: chmod +x integrate_test.sh

      - name: Run integration tests
        run: ./integrate_test.sh
