name: DeepCognition Production build and Deploy

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      REGISTRY: ${{ secrets.ALIYUN_REGISTRY }}
      BACKEND_IMAGE: deepcognition/dc-backend
      FRONTEND_IMAGE: deepcognition/dc-frontend
      NGINX_IMAGE: deepcognition/dc-nginx

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      
      - name: Log in to the Container registry
        uses: docker/login-action@v1
        with:
          registry: ${{ secrets.ALIYUN_REGISTRY }}
          username: ${{ secrets.ALIYUN_ACTOR }}
          password: ${{ secrets.ALIYUN_PASSWD }}
          
      - name: Setup Node.js environment
        uses: actions/setup-node@v3.9.1
        with:
          node-version: 22.14.0
          
      - name: Setup Python
        uses: actions/setup-python@v5.6.0
        with:
          python-version: 3.12.7
          
      - name: Build and push frontend image
        uses: docker/build-push-action@v4
        with:
          context: ./frontend
          file: ./frontend/Dockerfile
          push: true
          tags: ${{ secrets.ALIYUN_REGISTRY }}/${{ env.FRONTEND_IMAGE }}:latest, ${{ secrets.ALIYUN_REGISTRY }}/${{ env.FRONTEND_IMAGE }}:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha, mode=max

      - name: Build and push backend image
        uses: docker/build-push-action@v4
        with: 
          context: ./backend
          file: ./backend/Dockerfile
          push: true
          tags: ${{ secrets.ALIYUN_REGISTRY }}/${{ env.BACKEND_IMAGE }}:latest, ${{ secrets.ALIYUN_REGISTRY }}/${{ env.BACKEND_IMAGE }}:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha, mode=max

      - name: Build and push nginx image
        uses: docker/build-push-action@v4
        with:
          context: ./nginx
          file: ./nginx/Dockerfile
          push: true
          tags: ${{ secrets.ALIYUN_REGISTRY }}/${{ env.NGINX_IMAGE }}:latest, ${{ secrets.ALIYUN_REGISTRY }}/${{ env.NGINX_IMAGE }}:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
    
      
          
