#!/usr/bin/env python3
"""
测试新的时间筛选功能
"""

import requests
import json
from datetime import datetime, timedelta

# 测试API端点
BASE_URL = "http://localhost:8257"
API_ENDPOINT = f"{BASE_URL}/api/cognition/trend-stats"

def test_time_filter(time_filter, description):
    """测试特定的时间筛选"""
    print(f"\n=== 测试 {description} (time_filter={time_filter}) ===")
    
    try:
        # 这里需要添加认证token，实际使用时需要登录获取
        headers = {
            "Authorization": "Bearer YOUR_TOKEN_HERE",  # 需要替换为实际token
            "Content-Type": "application/json"
        }
        
        params = {
            "time_filter": time_filter,
            "search_scope": "all",
            "source_filter": "all"
        }
        
        response = requests.get(API_ENDPOINT, params=params, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 成功获取数据")
            print(f"   - 话题数量: {len(data.get('data', []))}")
            print(f"   - 认知总数: {data.get('total', 0)}")
            print(f"   - 原始认知数量: {len(data.get('raw_cognitions', []))}")
            
            # 显示前3个话题
            topics = data.get('data', [])[:3]
            if topics:
                print("   - 前3个热门话题:")
                for i, topic in enumerate(topics, 1):
                    print(f"     {i}. {topic['topic']}: {topic['count']}条 ({topic['percentage']:.1f}%)")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")

def main():
    """主测试函数"""
    print("🧪 开始测试新的时间筛选功能")
    print("=" * 50)
    
    # 测试原有的时间筛选
    test_time_filter("all", "所有时间")
    test_time_filter("day", "最近一天")
    test_time_filter("week", "最近一周")
    test_time_filter("month", "最近一月")
    
    # 测试新增的对比时间筛选
    test_time_filter("yesterday", "昨天")
    test_time_filter("last_week", "上周")
    test_time_filter("last_month", "上月")
    
    print("\n" + "=" * 50)
    print("🎯 测试完成！")
    print("\n📝 注意事项:")
    print("1. 需要先启动后端服务 (python -m uvicorn backend.main:app --reload)")
    print("2. 需要替换 YOUR_TOKEN_HERE 为实际的认证token")
    print("3. 如果没有认证token，可以先注册/登录获取")

if __name__ == "__main__":
    main()
