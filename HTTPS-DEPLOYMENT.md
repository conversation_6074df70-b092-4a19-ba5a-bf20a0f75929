# HTTPS 自动化部署说明

本项目已配置完整的HTTPS自动化部署流程，支持Let's Encrypt证书的自动获取和更新。

## 🚀 自动化特性

- ✅ **自动SSL证书获取**: 首次部署时自动获取Let's Encrypt证书
- ✅ **智能证书检查**: 每次部署时检查证书有效性
- ✅ **自动证书更新**: 证书即将过期时自动更新
- ✅ **HTTPS强制重定向**: HTTP流量自动重定向到HTTPS
- ✅ **安全配置**: 包含现代SSL/TLS安全配置
- ✅ **健康检查**: 部署后自动验证服务状态

## 📁 文件结构

```
.
├── ssl-auto-setup.sh          # SSL证书自动获取脚本
├── ssl-renew-cron.sh         # 证书更新定时任务脚本
├── deploy.sh                 # 增强的部署脚本
├── nginx/default.conf        # HTTPS配置的nginx
├── data/certbot/            # 证书存储目录
│   ├── conf/               # 证书文件
│   ├── www/                # Let's Encrypt验证文件
│   └── logs/               # 证书操作日志
└── .github/workflows/       # GitHub Actions配置
```

## 🔧 配置参数

在 `ssl-auto-setup.sh` 中配置:
- `DOMAIN="dev.q.opensii.ai"` - 你的域名
- `EMAIL="<EMAIL>"` - Let's Encrypt注册邮箱

## 🚀 部署流程

### 自动部署（推荐）
通过GitHub Actions自动触发：
1. 推送代码到main分支
2. GitHub Actions自动构建镜像
3. 自动部署到服务器并处理SSL证书

### 手动部署
```bash
# 1. 克隆项目到服务器
git clone <your-repo>
cd <project-dir>

# 2. 执行部署脚本（包含SSL自动化）
bash deploy.sh
```

## 📋 首次部署要求

### 域名准备
- 确保域名 `dev.q.opensii.ai` 已正确解析到服务器IP
- 确保80和443端口对外开放

### 服务器要求
- Docker和Docker Compose已安装
- 具有sudo权限（用于安装必要工具）
- 网络连接正常（能访问Let's Encrypt服务）

## 🔄 证书自动更新

### 方案1：定时任务
在服务器上设置crontab定时任务：
```bash
# 每天凌晨2点检查并更新证书
0 2 * * * /path/to/your/project/ssl-renew-cron.sh
```

### 方案2：GitHub Actions定时
可以在GitHub Actions中添加定时触发，定期重新部署以检查证书。

## 🔍 故障排除

### 证书获取失败
1. 检查域名DNS解析是否正确
2. 确认80端口可以从外网访问
3. 查看证书获取日志：`data/certbot/logs/`

### HTTPS无法访问
1. 检查证书文件是否存在：`data/certbot/conf/live/dev.q.opensii.ai/`
2. 检查nginx容器日志：`docker logs dair-nginx`
3. 验证证书有效性：
   ```bash
   openssl x509 -in data/certbot/conf/live/dev.q.opensii.ai/fullchain.pem -text -noout
   ```

### 服务健康检查失败
```bash
# 检查所有容器状态
docker ps

# 检查nginx配置
docker exec dair-nginx nginx -t

# 手动测试HTTPS
curl -k https://dev.q.opensii.ai
```

## 📊 监控和日志

- **部署日志**: GitHub Actions构建日志
- **证书日志**: `data/certbot/logs/`
- **Nginx日志**: `docker logs dair-nginx`
- **应用日志**: `docker logs dair-backend`

## 🔐 安全特性

- **HSTS**: 强制HTTPS传输安全
- **安全头部**: 包含XSS保护、内容类型嗅探保护等
- **现代TLS**: 支持TLS 1.2和1.3
- **安全密码套件**: 使用推荐的加密算法

## 🎯 后续维护

1. **定期检查**: 建议每月检查证书状态
2. **日志监控**: 定期查看证书更新日志
3. **域名续费**: 确保域名不过期
4. **服务器维护**: 保持Docker和系统更新

---

**GitHub commit message**: `add complete https automation with ssl certificate management` 