import asyncio
import sys
import os
import argparse
sys.path.append('backend')
from motor.motor_asyncio import AsyncIOMotorClient
from dotenv import load_dotenv

async def check_fields(fields_to_check, collection_name='cognitions_new', limit=3, show_preview=True, show_all_fields=False):
    """
    通用字段检查函数
    
    Args:
        fields_to_check (list): 要检查的字段列表
        collection_name (str): 集合名称，默认 'cognitions_new'
        limit (int): 检查的记录数量限制，默认 3
        show_preview (bool): 是否显示字段内容预览，默认 True
        show_all_fields (bool): 是否显示所有字段列表，默认 False
    """

    # 获取MongoDB URI - 使用与后端一致的环境变量名
    mongo_uri = os.getenv('MONGO_URI')
    
    # 如果没有找到，尝试从docker-compose.yml中的连接字符串
    if not mongo_uri:
        # 使用与docker-compose.yml中相同的连接字符串
        mongo_uri = "mongodb://dair:<EMAIL>:3717,mongoreplica98081f54cfd51.mongodb.ap-southeast-1.ivolces.com:3717/?authSource=admin&replicaSet=rs-mongo-replica-98081f54cfd5&retryWrites=true"
        print("使用默认的MongoDB集群连接字符串")
    
    print(f"连接到数据库: {mongo_uri[:50]}...")
    
    try:
        # 使用与后端相同的连接配置
        client = AsyncIOMotorClient(
            mongo_uri,
            serverSelectionTimeoutMS=5000,  # 5秒超时
            connectTimeoutMS=10000,        # 10秒连接超时
            socketTimeoutMS=45000,         # 45秒套接字超时
            maxPoolSize=100                # 连接池大小
        )
        
        # 使用与后端相同的数据库名称
        db = client.dair
        collection = db[collection_name]
        
        # 测试连接
        await client.admin.command('ping')
        print("MongoDB连接测试成功！")
        
        # 显示数据库中的所有集合
        collection_names = await db.list_collection_names()
        print(f"数据库中的所有集合: {collection_names}")
        
        if collection_name not in collection_names:
            print(f"❌ 警告：集合 '{collection_name}' 不存在！")
            print(f"可用的集合: {collection_names}")
            return
        
        # 查询指定数量的记录，检查字段
        print(f'\n=== 检查集合 {collection_name} 中的字段 ===')
        print(f'要检查的字段: {fields_to_check}')
        print(f'检查记录数量: {limit}')
        
        cursor = collection.find({}).limit(limit)
        docs = await cursor.to_list(length=None)
        
        if not docs:
            print(f"集合 {collection_name} 中没有找到任何记录！")
            return
        
        for i, doc in enumerate(docs, 1):
            print(f'\n--- 记录 {i} (ID: {doc.get("_id", "unknown")}) ---')
            
            # 检查指定字段
            for field in fields_to_check:
                value = doc.get(field, "")
                exists = field in doc
                has_content = bool(value and str(value).strip())
                
                if has_content:
                    status = "✓有内容"
                    length = len(str(value))
                elif exists:
                    status = "✓空值"
                    length = 0
                else:
                    status = "✗不存在"
                    length = 0
                
                print(f'{field}: {status} ({length} 字符)')
                
                # 显示内容预览
                if show_preview and has_content:
                    preview = str(value)[:80].replace('\n', ' ').replace('\r', ' ')
                    print(f'  预览: {preview}...')
                
                # 显示字段数据类型
                field_type = type(doc.get(field, None)).__name__
                print(f'  类型: {field_type}')
            
            # 显示所有字段（可选）
            if show_all_fields:
                all_keys = sorted(list(doc.keys()))
                print(f'总字段数: {len(all_keys)}')
                print(f'所有字段: {all_keys}')
                
                # 找出相关字段（包含检查字段中任一关键词的字段）
                related_fields = []
                for field in fields_to_check:
                    related = [k for k in all_keys if any(keyword in k.lower() for keyword in field.lower().split('_'))]
                    related_fields.extend(related)
                related_fields = list(set(related_fields))  # 去重
                if related_fields:
                    print(f'相关字段: {related_fields}')
        
        # 统计字段的记录数量
        print(f'\n=== 统计信息 ===')
        total_count = await collection.count_documents({})
        print(f'总记录数: {total_count}')
        
        for field in fields_to_check:
            exists_count = await collection.count_documents({field: {'$exists': True}})
            content_count = await collection.count_documents({field: {'$exists': True, '$ne': '', '$ne': None}})
            percentage = (content_count / total_count * 100) if total_count > 0 else 0
            
            print(f'{field}:')
            print(f'  - 有字段的记录数: {exists_count}')
            print(f'  - 有内容的记录数: {content_count}')
            print(f'  - 内容覆盖率: {percentage:.1f}%')
        
        client.close()
        
    except Exception as e:
        print(f"连接或查询数据库时出错: {e}")
        return

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='MongoDB字段检查工具')
    
    parser.add_argument('--fields', '-f', 
                       nargs='+', 
                       default=['cover_url','abstract_zh'],
                       help='要检查的字段列表，用空格分隔 (默认: cover_url)')
    
    parser.add_argument('--collection', '-c',
                       default='cognitions_new',
                       help='集合名称 (默认: cognitions_new)')
    
    parser.add_argument('--limit', '-l',
                       type=int,
                       default=3,
                       help='检查的记录数量限制 (默认: 3)')
    
    parser.add_argument('--no-preview',
                       action='store_true',
                       help='不显示字段内容预览')
    
    parser.add_argument('--show-all-fields',
                       action='store_true', 
                       help='显示每条记录的所有字段列表')
    
    return parser.parse_args()

async def main():
    """主函数"""
    args = parse_arguments()
    
    print(f"🔍 MongoDB字段检查工具")
    print(f"📋 检查字段: {args.fields}")
    print(f"📦 目标集合: {args.collection}")
    print(f"📊 记录限制: {args.limit}")
    print("-" * 50)
    
    await check_fields(
        fields_to_check=args.fields,
        collection_name=args.collection,
        limit=args.limit,
        show_preview=not args.no_preview,
        show_all_fields=args.show_all_fields
    )

if __name__ == "__main__":
    asyncio.run(main()) 